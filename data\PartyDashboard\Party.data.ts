import gql from 'graphql-tag';

export const GET_PARTY_FOR_PARTY_DASHBOARD_BY_ID = gql`
  query GetPartyById($getPartyByIdId: ID!) {
    getPartyById(id: $getPartyByIdId) {
      ... on PartyResponse {
        status
        message
        result {
          party {
            id
            name
            time
            serviceLocation {
              city
            }
            partyType {
              landscapeImage
            }
            weather {
              avgTempC
            }
            vendorTypes {
              id
              icon {
                iconSrc
                id
              }
              name
            }
          }
        }
      }
      ... on PartyErrorResponse {
        status
        message
        errors {
          field
          message
        }
      }
    }
  }`
