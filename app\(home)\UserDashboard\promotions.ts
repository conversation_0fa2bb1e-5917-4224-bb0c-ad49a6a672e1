interface Tag {
  id: string;
  name: string;
  slug: string;
}

interface MdPromotion {
  id: string;
  name: string;
  ctaLink: string;
  thumbnail: string;
  active: boolean;
  tags: Tag[];
  mediaUrl: string;
  partner: {
    id: string;
    name: string;
  };
}

interface MdPromotionByTagId {
  id: string;
  name: string;
  ctaLink: string;
  thumbnail: string;
  active: boolean;
  mediaUrl: string;
  partner: {
    id: string;
    name: string;
  };
}

interface MdPromotionsByTagIdResult {
  mdPromotions: MdPromotionByTagId[];
}

interface MdPromotionsByTagIdResponse {
  result: MdPromotionsByTagIdResult;
}

interface GetMdPromotionsByTagIdData {
  getMdPromotions: MdPromotionsByTagIdResponse;
}

interface MdPromotionsResult {
  mdPromotions: MdPromotion[];
}

interface MdPromotionsResponse {
  result: MdPromotionsResult;
}

interface GetMdPromotionsData {
  getMdPromotions: MdPromotionsResponse;
}

interface MdPromotionFilterInput {
  active?: boolean;
  tagIds?: string[];
  tag?: {
    id?: string;
    slug?: string;
  };
}

export interface GetMdPromotionsVars {
  pagination?: {
    limit: number;
    skip: number;
  };
  filter?: MdPromotionFilterInput;
  tagSlug?: string;
}

export type { 
  MdPromotion,
  MdPromotionsResponse,
  GetMdPromotionsData,
  MdPromotionFilterInput,
  Tag,
  MdPromotionByTagId,
  MdPromotionsByTagIdResponse,
  GetMdPromotionsByTagIdData
}; 