import { ThemedView } from "@/components/UI/ThemedView";
import { Pressable, StyleSheet, View, FlatList, RefreshControl, TextInput, NativeSyntheticEvent, NativeScrollEvent } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Text } from "react-native-paper";
import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { NewsCard } from "@/components/UI/NewsCard";
import { Article, UserArticleActivity } from "@/components/UI/NewsCardData";
import { GET_ARTICLES, GET_USER_ARTICLE_ACTIVITIES } from "@/components/UI/NewsCardData";
import { useQuery } from "@apollo/client";
import AppHeaderIcons from '@/components/AppHeaderIcons';
import { Colors, Spacing, Borders, Typography, Icons } from '@/constants/DesignSystem';
import { FastPartyActivityIndicator } from "@/components/FastPartyActivityIndicator";
import { Search } from "@/components/icons";

function formatRelativeTime(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMilliseconds = now.getTime() - date.getTime();
  const diffInHours = Math.floor(diffInMilliseconds / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMilliseconds / (1000 * 60 * 60 * 24));

  if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  }
  return `${diffInDays} days ago`;
}

const PAGE_SIZE = 100;

export default function TrendsScreen() {
  const [selectedTab, setSelectedTab] = useState<"discover" | "saved">(
    "discover"
  );

  const [feeds, setFeeds] = useState<Article[]>([]);
  const [loadingMoreDiscover, setLoadingMoreDiscover] = useState(false);
  const [hasMoreDiscover, setHasMoreDiscover] = useState(true);

  const [userArticleActivities, setUserArticleActivities] = useState<UserArticleActivity[]>([]);
  const [loadingMoreSaved, setLoadingMoreSaved] = useState(false);
  const [hasMoreSaved, setHasMoreSaved] = useState(true);

  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const scrollPositions = useRef<{ discover: number; saved: number }>({ discover: 0, saved: 0 });
  const flatListRef = useRef<FlatList>(null);

  const articlesQueryVariables = useMemo(() => ({
    pagination: { limit: PAGE_SIZE, skip: 0 },
    filter: { search: debouncedSearchTerm || null }
  }), [debouncedSearchTerm]);

  const activitiesQueryVariables = useMemo(() => ({
    pagination: { limit: PAGE_SIZE, skip: 0 },
    filter: { article: { search: debouncedSearchTerm || null } }
  }), [debouncedSearchTerm]);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setFeeds([]);
      setUserArticleActivities([]);
      setHasMoreDiscover(true);
      setHasMoreSaved(true);
      flatListRef.current?.scrollToOffset({ offset: 0, animated: false });
    }, 500);

    return () => clearTimeout(handler);
  }, [searchTerm]);

  const {
    loading: articlesLoading,
    error: articlesError,
    fetchMore: fetchMoreArticles,
    refetch: articlesRefetch
  } = useQuery(GET_ARTICLES, {
    variables: articlesQueryVariables,
    skip: selectedTab !== 'discover',
    fetchPolicy: "cache-and-network",
    notifyOnNetworkStatusChange: true,
    onCompleted: (data) => {
      if (data?.getArticles?.__typename === 'ArticlesResponse') {
          const fetchedArticles: Article[] = data.getArticles.result?.articles ?? [];
          const uniqueArticles = Array.from(new Map(fetchedArticles.map((article: Article) => [article.id, article])).values());
          setFeeds(uniqueArticles);

          const totalItems = data.getArticles.pagination?.totalItems ?? 0;
          setHasMoreDiscover(uniqueArticles.length < totalItems);
      }
    },
  });

  const {
    loading: userArticleActivitiesLoading,
    error: userArticleActivitiesError,
    fetchMore: fetchMoreActivities,
    refetch: userArticleActivitiesRefetch
  } = useQuery(GET_USER_ARTICLE_ACTIVITIES, {
    variables: activitiesQueryVariables,
    skip: selectedTab !== 'saved',
    fetchPolicy: "cache-and-network",
    notifyOnNetworkStatusChange: true,
    onCompleted: (data) => {
      if (data?.getUserArticleActivities?.__typename === 'UserArticleActivitiesResponse') {
          const fetchedActivities: UserArticleActivity[] = data.getUserArticleActivities.result?.activities ?? [];
          const uniqueActivities = Array.from(new Map(fetchedActivities.map((activity: UserArticleActivity) => [activity.articleId.id, activity])).values());
          setUserArticleActivities(uniqueActivities);

          const totalCount = data.getUserArticleActivities.result?.totalCount ?? 0;
          setHasMoreSaved(uniqueActivities.length < totalCount);
      }
    },
  });

  useEffect(() => {
    const hasData = selectedTab === 'discover' ? feeds.length > 0 : userArticleActivities.length > 0;
    if (flatListRef.current && hasData) {
        const targetScrollY = scrollPositions.current[selectedTab];
        setTimeout(() => {
            flatListRef.current?.scrollToOffset({ offset: targetScrollY, animated: false });
        }, 100);
    }
  }, [selectedTab, feeds, userArticleActivities]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    setHasMoreDiscover(true);
    setHasMoreSaved(true);
    setFeeds([]);
    setUserArticleActivities([]);
    try {
      if (selectedTab === 'discover') {
        await articlesRefetch(articlesQueryVariables);
      } else {
        await userArticleActivitiesRefetch(activitiesQueryVariables);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setRefreshing(false);
    }
  }, [selectedTab, articlesRefetch, userArticleActivitiesRefetch, articlesQueryVariables, activitiesQueryVariables]);

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentScrollY = event.nativeEvent.contentOffset.y;
    scrollPositions.current[selectedTab] = currentScrollY;
  };

  const handleLoadMore = useCallback(async () => {
    if (selectedTab === 'discover') {
      if (loadingMoreDiscover || !hasMoreDiscover || articlesLoading) return;
      setLoadingMoreDiscover(true);
      try {
        await fetchMoreArticles({
          variables: {
            ...articlesQueryVariables,
            pagination: { ...articlesQueryVariables.pagination, skip: feeds.length },
          },
          updateQuery: (prev, { fetchMoreResult }) => {
            if (fetchMoreResult?.getArticles?.__typename !== 'ArticlesResponse') {
               setHasMoreDiscover(false);
               return prev;
            }
            if (!fetchMoreResult?.getArticles?.result?.articles) return prev;

            const prevArticles = prev.getArticles?.__typename === 'ArticlesResponse' ? prev.getArticles.result.articles : [];
            const newArticles: Article[] = fetchMoreResult.getArticles.result.articles;
            const totalItems = fetchMoreResult.getArticles.pagination?.totalItems ?? 0;

            const existingArticleIds = new Set(prevArticles.map((article: Article) => article.id));
            const uniqueNewArticles = newArticles.filter((article: Article) => !existingArticleIds.has(article.id));

            const combinedArticles = [
              ...prevArticles,
              ...uniqueNewArticles,
            ];
            setHasMoreDiscover(combinedArticles.length < totalItems);

            return {
              ...prev,
              getArticles: {
                 ...fetchMoreResult.getArticles,
                 result: {
                   ...fetchMoreResult.getArticles.result,
                   articles: combinedArticles,
                 },
              },
            };
          },
        });
      } catch (error) {
        console.error(error);
        setHasMoreDiscover(false);
      } finally {
        setLoadingMoreDiscover(false);
      }
    } else {
      if (loadingMoreSaved || !hasMoreSaved || userArticleActivitiesLoading) return;
      setLoadingMoreSaved(true);
      try {
        await fetchMoreActivities({
          variables: {
            ...activitiesQueryVariables,
            pagination: { ...activitiesQueryVariables.pagination, skip: userArticleActivities.length },
          },
          updateQuery: (prev, { fetchMoreResult }) => {
            if (fetchMoreResult?.getUserArticleActivities?.__typename !== 'UserArticleActivitiesResponse') {
               setHasMoreSaved(false);
               return prev;
            }
            if (!fetchMoreResult?.getUserArticleActivities?.result?.activities) return prev;

            const prevActivities = prev.getUserArticleActivities?.__typename === 'UserArticleActivitiesResponse' ? prev.getUserArticleActivities.result.activities : [];
            const newActivities = fetchMoreResult.getUserArticleActivities.result.activities;
            const totalCount = fetchMoreResult.getUserArticleActivities.result?.totalCount ?? 0;

            const existingActivityArticleIds = new Set(prevActivities.map((act: UserArticleActivity) => act.articleId.id));
            const uniqueNewActivities = newActivities.filter((act: UserArticleActivity) => !existingActivityArticleIds.has(act.articleId.id));

            const combinedActivities = [
              ...prevActivities,
              ...uniqueNewActivities,
            ];
            setHasMoreSaved(combinedActivities.length < totalCount);

            return {
              ...prev,
              getUserArticleActivities: {
                ...fetchMoreResult.getUserArticleActivities,
                result: {
                  ...fetchMoreResult.getUserArticleActivities.result,
                  activities: combinedActivities,
                },
              },
            };
          },
        });
      } catch (error) {
        console.error(error);
        setHasMoreSaved(false);
      } finally {
        setLoadingMoreSaved(false);
      }
    }
  }, [
    selectedTab,
    articlesLoading, userArticleActivitiesLoading,
    loadingMoreDiscover, hasMoreDiscover, fetchMoreArticles, feeds.length, articlesQueryVariables,
    loadingMoreSaved, hasMoreSaved, fetchMoreActivities, userArticleActivities.length, activitiesQueryVariables
  ]);

  const renderNewsCard = ({ item }: { item: Article | UserArticleActivity }) => {
    const isArticle = (i: Article | UserArticleActivity): i is Article => 'headline' in i && !('userId' in i);

    if (selectedTab === 'discover' && isArticle(item)) {
        return (
            <NewsCard
              key={`discover-${item.id}`}
              articleId={item.id}
              articleUrl={item.source.link}
              imageUrl={item.image}
              title={item.headline}
              source={new URL(item.source.mainUrl).hostname}
              time={formatRelativeTime(item.date)}
              initialLiked={item.userArticleActivity?.liked ?? null}
              initialSaved={item.userArticleActivity?.saved ?? null}
            />
        );
    } else if (selectedTab === 'saved' && !isArticle(item)) {
        if (!item.saved) return null;
        return (
             <NewsCard
                key={`saved-${item.articleId.id}`}
                articleId={item.articleId.id}
                articleUrl={item.articleId.source.link}
                imageUrl={item.articleId.image}
                title={item.articleId.headline}
                source={new URL(item.articleId.source.mainUrl).hostname}
                time={formatRelativeTime(item.articleId.date)}
                initialLiked={item.liked ?? null}
                initialSaved={item.saved ?? null}
              />
        );
    }
    return null;
  };

  const keyExtractor = (item: Article | UserArticleActivity, index: number): string => {
     const isArticle = (i: Article | UserArticleActivity): i is Article => 'headline' in i && !('userId' in i);
     if (isArticle(item)) {
         return `discover-${item.id}-${index}`;
     } else {
         return `saved-${item.articleId.id}-${index}`;
     }
  };

  const currentData = selectedTab === 'discover' ? feeds : userArticleActivities.filter(a => a.saved);
  const isLoading = selectedTab === 'discover' ? articlesLoading : userArticleActivitiesLoading;
  const isLoadingMore = selectedTab === 'discover' ? loadingMoreDiscover : loadingMoreSaved;

  return (
    <SafeAreaView style={styles.safeArea} edges={['top']}>
      <ThemedView style={styles.container}>
        <View style={styles.headerContainer}>
          <Text variant="titleLarge" style={styles.headerTitle}>Trends</Text>
          <View style={styles.headerIconsContainer}>
            <AppHeaderIcons/>
          </View>
        </View>
        <View style={styles.mainContentContainer}>
          <View style={styles.searchBarContainer}>
             <Search size={Icons.size.md} color={Colors.mediumGray} />
              <TextInput
                placeholder="Find trends, stories, articles..."
                placeholderTextColor={Colors.mediumGray}
                style={styles.searchInput}
                value={searchTerm}
                onChangeText={setSearchTerm}
              />
          </View>
          <View style={styles.tabContainer}>
            <Pressable
                style={[
                styles.tabButton,
                selectedTab === 'discover' && styles.activeTabButton
                ]}
                onPress={() => setSelectedTab('discover')}
            >
                <Text style={[
                styles.tabButtonText,
                selectedTab === 'discover' ? styles.activeTabText : styles.inactiveTabText
                ]}>
                Discover
                </Text>
            </Pressable>
            <Pressable
                style={[
                styles.tabButton,
                selectedTab === 'saved' && styles.activeTabButton
                ]}
                onPress={() => setSelectedTab('saved')}
            >
                <Text style={[
                styles.tabButtonText,
                selectedTab === 'saved' ? styles.activeTabText : styles.inactiveTabText
                ]}>
                Saved
                </Text>
            </Pressable>
           </View>

          <FlatList
            ref={flatListRef}
            style={styles.contentScrollView}
            data={currentData}
            renderItem={renderNewsCard}
            keyExtractor={keyExtractor}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.5}
            onScroll={handleScroll}
            scrollEventThrottle={16}
            ListEmptyComponent={() => (
                !isLoading && !refreshing ? (
                     <View style={styles.emptyStateContainer}>
                        <Text>No {selectedTab === 'discover' ? 'articles' : 'saved articles'} found{debouncedSearchTerm ? ' for "' + debouncedSearchTerm + '"' : ''}.</Text>
                     </View>
                 ) : null
            )}
            ListFooterComponent={() => (
                isLoadingMore ? (
                    <View style={{ paddingVertical: 20 }}>
                        <FastPartyActivityIndicator size="large" fullScreen={false} />
                    </View>
                ) : null
            )}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
          />
          {isLoading && !refreshing && !isLoadingMore && currentData.length === 0 && (
             <FastPartyActivityIndicator size="large" />
          )}
          {selectedTab === 'discover' && articlesError && !isLoading && (
            <View style={styles.centerContainerAbsolute}>
                 <Text variant="bodyLarge" style={styles.errorText}>
                    Error loading articles: {articlesError.message}
                 </Text>
            </View>
          )}
          {selectedTab === 'saved' && userArticleActivitiesError && !isLoading && (
             <View style={styles.centerContainerAbsolute}>
                 <Text variant="bodyLarge" style={styles.errorText}>
                    Error loading saved articles: {userArticleActivitiesError.message}
                 </Text>
            </View>
          )}
        </View>
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  searchInput: {
    flex: 1,
    padding: 0,
    marginLeft: Spacing.sm,
  },
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  headerContainer: {
    padding: Spacing.xl,
    paddingBottom: Spacing.md,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  headerTitle: {
    fontWeight: Typography.fontWeight.bold,
    fontSize: Typography.fontSize.xl,
    color: Colors.secondary,
  },
  headerIconsContainer: {
    flexDirection: "row",
  },
  mainContentContainer: {
    flex: 1,
    alignItems: 'center',
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.tertiary,
    borderRadius: Borders.radius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    marginHorizontal: Spacing.xl,
    marginBottom: Spacing.md,
    width: '90%',
    alignSelf: 'center',
    height: 40,
  },
  searchIcon: {
    margin: 0,
    padding: 0,
  },
  searchInputText: {
    color: Colors.text.primary,
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    height: 40,
    width: '90%',
    marginVertical: Spacing.sm,
    borderRadius: Borders.radius.md,
    backgroundColor: Colors.background.tertiary,
    padding: Spacing.xs,
  },
  tabButton: {
    flex: 1,
    paddingVertical: Spacing.sm,
    borderRadius: Borders.radius.sm,
    alignItems: 'center',
  },
  activeTabButton: {
    backgroundColor: Colors.primary,
  },
  tabButtonText: {
    fontWeight: Typography.fontWeight.medium,
  },
  activeTabText: {
    color: Colors.text.tertiary,
  },
  inactiveTabText: {
    color: Colors.mediumGray,
  },
  contentScrollView: {
    width: '100%',
    marginTop: Spacing.sm,
  },
  emptyStateContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 50,
    paddingHorizontal: Spacing.xl,
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 50,
  },
  centerContainerAbsolute: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
  },
  errorText: {
    color: Colors.error,
    textAlign: 'center',
    marginHorizontal: Spacing.xl,
  },
  profileIcon: {
    marginLeft: -Spacing.sm,
  },
});
