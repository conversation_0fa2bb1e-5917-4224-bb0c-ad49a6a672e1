import { forwardRef, useMemo } from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { BottomSheetModal, BottomSheetBackdrop, BottomSheetView } from '@gorhom/bottom-sheet';
import { Copy, Delete, File } from '../../icons';
import { MuteEventButton } from './MuteEventButton';
import { DeleteOrNotGoingButton } from './DeleteOrNotGoingButton';
import { Colors, Icons, Spacing, Typography } from '@/constants/DesignSystem';

export interface PartyOptionsBottomSheetProps {
  onClose?: () => void;
  onMakeFlyer?: () => void;
  canMakeFlyer?: boolean;
  onMuteEvent?: () => void;
  isMuted?: boolean;
  isHost?: boolean;
  onDelete?: () => void;
  onNotGoing?: () => void;
  deleteOrNotGoingLoading?: boolean;
  deleteOrNotGoingDisabled?: boolean;
  onCopyLink?: () => void;
  copyLinkLoading?: boolean;
}

export const PartyOptionsBottomSheet = forwardRef<BottomSheetModal, PartyOptionsBottomSheetProps>(
  function PartyOptionsBottomSheet({ 
    onClose, 
    onMakeFlyer, 
    canMakeFlyer, 
    onMuteEvent, 
    isMuted, 
    isHost, 
    onDelete, 
    onNotGoing, 
    deleteOrNotGoingLoading, 
    deleteOrNotGoingDisabled,
    onCopyLink,
    copyLinkLoading
  }, ref) {
    const snapPoints = useMemo(() => ['32%'], []);

    return (
      <BottomSheetModal
        ref={ref}
        index={0}
        snapPoints={snapPoints}
        enablePanDownToClose
        onDismiss={onClose}
        backdropComponent={props => (
          <BottomSheetBackdrop {...props} appearsOnIndex={0} disappearsOnIndex={-1} pressBehavior="close" />
        )}
        backgroundStyle={styles.sheet}
      >
        <BottomSheetView>
          <TouchableOpacity 
            style={styles.option} 
            accessibilityRole="button" 
            accessibilityLabel="Copy Link"
            onPress={onCopyLink}
            disabled={copyLinkLoading}
          >
            <Copy size={Icons.size.lg} color={Colors.text.tertiary} />
            <Text style={styles.optionText}>Copy Link</Text>
          </TouchableOpacity>
          <View style={styles.divider} />
          {canMakeFlyer && (
            <TouchableOpacity style={styles.option} accessibilityRole="button" accessibilityLabel="Make Flyer" onPress={onMakeFlyer}>
              <File size={Icons.size.lg} color={Colors.text.tertiary} />
              <Text style={styles.optionText}>Make Flyer</Text>
            </TouchableOpacity>
          )}
          {canMakeFlyer && <View style={styles.divider} />}
          {onMuteEvent && (
            <MuteEventButton 
              isMuted={isMuted || false}
              onPress={onMuteEvent}
            />
          )}
          <View style={styles.divider} />
          <DeleteOrNotGoingButton
            isHost={!!isHost}
            onDelete={onDelete || (() => {})}
            onNotGoing={onNotGoing || (() => {})}
            loading={!!deleteOrNotGoingLoading}
            disabled={!!deleteOrNotGoingDisabled}
          />
        </BottomSheetView>
      </BottomSheetModal>
    );
  }
);

const styles = StyleSheet.create({
  sheet: {
    backgroundColor: 'white',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: 24,
    elevation: 8,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  optionText: {
    fontSize: Typography.fontSize.md,
    marginLeft: Spacing.md,
    fontFamily: 'Plus Jakarta Sans',
    color: '#222',
  },
  divider: {
    height: 1,
    backgroundColor: '#eee',
    marginHorizontal: 16,
  },
}); 