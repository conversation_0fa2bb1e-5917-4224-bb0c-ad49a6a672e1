import * as React from "react";
import Svg, {
  G,
  Path,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Rect,
} from "react-native-svg";
import { CommonProps } from ".";

const Refresh = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Refresh-1_svg__a)">
            <Path d="M9.79998 5.18184C9.3999 3.21081 7.65728 1.72729 5.56818 1.72729C3.18332 1.72729 1.25 3.66061 1.25 6.04548C1.25 8.43032 3.18332 10.3637 5.56818 10.3637C6.63368 10.3637 7.60906 9.97779 8.36215 9.338" stroke="url(#Refresh-1_svg__b)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M10.7501 2.59094L9.80006 5.18185L7.29553 4.75003" stroke="url(#Refresh-1_svg__c)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </G>
          <Defs>
            <LinearGradient id="Refresh-1_svg__b" x1="5.52499" y1="0.684975" x2="5.52499" y2="12.349" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="Refresh-1_svg__c" x1="9.02281" y1="2.27825" x2="9.02281" y2="5.77746" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <ClipPath id="Refresh-1_svg__a">
              <Rect width="12" height="12" fill="#fff"/>
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G clipPath="url(#Refresh_svg__a)">
            <Path d="M10.2 5.09089C9.75778 2.91239 7.83173 1.27271 5.52273 1.27271C2.88683 1.27271 0.75 3.40953 0.75 6.04543C0.75 8.68131 2.88683 10.8182 5.52273 10.8182C6.70039 10.8182 7.77843 10.3917 8.6108 9.68454" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M11.2501 2.22729L10.2 5.09093L7.43188 4.61366" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
          </G>
          <Defs>
            <ClipPath id="Refresh_svg__a">
                <Rect width="12" height="12" fill="#fff"/>
            </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  );
};
export default Refresh;
