type Query {
  getPartyById(id: ID!): PartyResponseOrError
}

union PartyResponseOrError = PartyResponse | PartyErrorResponse

type PartyResponse {
  message: String
  result: PartyWrapper
}

type PartyWrapper {
  party: Party
}

type Party {
  id: ID
  name: String
  weather: Weather
}

type Weather {
  weatherUrl: String
  avgTempC: Float
  avgTempF: Float
  condition: WeatherCondition
}

type WeatherCondition {
  icon: String
  text: String
}

type PartyErrorResponse {
  status: ResponseStatus
  errors: [Error]
}

type Error {
  field: String
  message: String
}

enum ResponseStatus {
  SUCCESS
  FAILURE
}