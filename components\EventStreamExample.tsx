import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useEventStream } from '@/services/eventStreamService';
import { Colors } from '@/constants/Colors';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';

interface EventStreamExampleProps {
  resourceId: string;
  title?: string;
  eventTypes?: string[];
}

/**
 * Example component that demonstrates how to use the useEventStream hook
 */
export function EventStreamExample({ 
  resourceId, 
  title = 'Event Stream', 
  eventTypes = [] 
}: EventStreamExampleProps) {
  const [events, setEvents] = useState<Array<{ id: string; data: any; timestamp: Date; type?: string }>>([]);
  const [isActive, setIsActive] = useState(true);
  
  // Handle incoming events
  const handleEvent = (data: any, eventType?: string) => {
    setEvents(prevEvents => [
      {
        id: `event-${Date.now()}`,
        data,
        timestamp: new Date(),
        type: eventType
      },
      ...prevEvents
    ].slice(0, 50)); // Keep only the last 50 events
  };
  
  // Connect to the event stream
  const { isConnected, close, reconnect } = useEventStream(
    resourceId,
    handleEvent,
    {
      eventTypes,
      autoReconnect: true,
      reconnectDelay: 3000,
      maxReconnectAttempts: 10
    }
  );
  
  // Toggle the connection
  const toggleConnection = () => {
    if (isActive) {
      close();
    } else {
      reconnect();
    }
    setIsActive(!isActive);
  };
  
  // Clear the events list
  const clearEvents = () => {
    setEvents([]);
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        <View style={styles.statusContainer}>
          <View 
            style={[
              styles.statusIndicator, 
              { backgroundColor: isConnected ? '#4CAF50' : '#F44336' }
            ]} 
          />
          <Text style={styles.statusText}>
            {isConnected ? 'Connected' : 'Disconnected'}
          </Text>
        </View>
      </View>
      
      <View style={styles.controls}>
        <TouchableOpacity 
          style={[styles.button, { backgroundColor: isActive ? '#F44336' : '#4CAF50' }]} 
          onPress={toggleConnection}
        >
          <Text style={styles.buttonText}>
            {isActive ? 'Disconnect' : 'Connect'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, { backgroundColor: '#2196F3' }]} 
          onPress={clearEvents}
        >
          <Text style={styles.buttonText}>Clear</Text>
        </TouchableOpacity>
      </View>
      
      {events.length === 0 && isConnected ? (
        <View style={styles.emptyState}>
          <FastPartyActivityIndicator />
          <Text style={styles.emptyText}>Waiting for events...</Text>
        </View>
      ) : events.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>No events received</Text>
        </View>
      ) : (
        <ScrollView style={styles.eventsList}>
          {events.map(event => (
            <View key={event.id} style={styles.eventItem}>
              <View style={styles.eventHeader}>
                <Text style={styles.eventType}>
                  {event.type || 'message'}
                </Text>
                <Text style={styles.eventTime}>
                  {event.timestamp.toLocaleTimeString()}
                </Text>
              </View>
              <Text style={styles.eventData}>
                {typeof event.data === 'object' 
                  ? JSON.stringify(event.data, null, 2) 
                  : String(event.data)
                }
              </Text>
            </View>
          ))}
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Plus Jakarta Sans',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 4,
  },
  statusText: {
    fontSize: 14,
    color: 'black',
    fontFamily: 'Plus Jakarta Sans',
  },
  controls: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  button: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
    marginRight: 8,
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Plus Jakarta Sans',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#757575',
    marginTop: 8,
    fontFamily: 'Plus Jakarta Sans',
  },
  eventsList: {
    flex: 1,
  },
  eventItem: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  eventType: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2196F3',
    fontFamily: 'Plus Jakarta Sans',
  },
  eventTime: {
    fontSize: 12,
    color: '#757575',
    fontFamily: 'Plus Jakarta Sans',
  },
  eventData: {
    fontSize: 14,
    color: 'black',
    fontFamily: 'Plus Jakarta Sans',
  },
});
