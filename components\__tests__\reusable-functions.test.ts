import { getFormattedDate, getDayFromDate } from "@/app/(home)/utils/reusableFunctions";

describe('reusableFunctions', () => {
  describe('getFormattedDate', () => {
    it('should format a valid UTC date string correctly', () => {
      const result = getFormattedDate('2023-04-15T12:00:00Z');
      expect(result).toBe('15th Apr \'23');
    });

    it('should handle different months correctly', () => {
      expect(getFormattedDate('2023-01-01T00:00:00Z')).toBe('1st Jan \'23');
      expect(getFormattedDate('2023-12-31T23:59:59Z')).toBe('31st Dec \'23');
    });

    it('should return "--" for invalid date strings', () => {
      expect(getFormattedDate('invalid-date')).toBe('--');
    });
  });

  describe('getDayFromDate', () => {
    it('should return the correct day for a valid UTC date string', () => {
      const result = getDayFromDate('2023-04-15T12:00:00Z');
      expect(result).toBe('Sat');
    });

    it('should handle different days of the week correctly', () => {
      expect(getDayFromDate('2023-04-16T12:00:00Z')).toBe('Sun');
      expect(getDayFromDate('2023-04-17T12:00:00Z')).toBe('Mon');
      expect(getDayFromDate('2023-04-18T12:00:00Z')).toBe('Tue');
      expect(getDayFromDate('2023-04-19T12:00:00Z')).toBe('Wed');
      expect(getDayFromDate('2023-04-20T12:00:00Z')).toBe('Thur');
      expect(getDayFromDate('2023-04-21T12:00:00Z')).toBe('Fri');
    });

    it('should return "--" for invalid date strings', () => {
      expect(getDayFromDate('invalid-date')).toBe('--');
    });
  });
});