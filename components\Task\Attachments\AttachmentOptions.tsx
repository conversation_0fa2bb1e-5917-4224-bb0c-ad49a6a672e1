import { View, Pressable } from 'react-native';
import { Text } from 'react-native-paper';
import { styles } from '../TaskList/CreateTask.styles';
import { useTheme } from 'react-native-paper';
import { File, Photos, TakePhoto } from '@/components/icons';
import { Colors, Icons } from '@/constants/DesignSystem';

interface AttachmentOptionsProps {
  isPickerActive: boolean;
  isCameraSessionActive: boolean;
  handleDocumentPick: () => void;
  handlePhotoGallery: () => void;
  handleCamera: () => void;
}

export function AttachmentOptions({
  isPickerActive,
  isCameraSessionActive,
  handleDocumentPick,
  handlePhotoGallery,
  handleCamera,
}: AttachmentOptionsProps) {
  const theme = useTheme();

  return (
    <View style={styles.bottomSheetContent}>
      <Pressable 
        style={[
          styles.attachmentOption,
          isPickerActive && styles.attachmentOptionDisabled
        ]} 
        onPress={() => {
          if (!isPickerActive) {
            handleDocumentPick();
          }
        }}
        disabled={isPickerActive}
      >
        <File 
          size={Icons.size.md} 
          color={isPickerActive ? Colors.background.secondary : Colors.primary} 
        />
        <Text style={[
          styles.attachmentOptionText,
          isPickerActive && styles.attachmentOptionTextDisabled
        ]}>
          {isPickerActive ? 'Selecting...' : 'Document'}
        </Text>
      </Pressable>
      
      <Pressable 
        style={styles.attachmentOption} 
        onPress={handlePhotoGallery}
      >
        <Photos 
          size={Icons.size.md} 
          color={Colors.primary} 
        />
        <Text style={styles.attachmentOptionText}>Photo Gallery</Text>
      </Pressable>
      
      <Pressable 
        style={[
          styles.attachmentOption,
          isCameraSessionActive && styles.attachmentOptionDisabled
        ]} 
        onPress={handleCamera}
        disabled={isCameraSessionActive}
      >
        <TakePhoto 
          size={Icons.size.md} 
          color={isCameraSessionActive ? Colors.background.secondary : Colors.primary} 
        />
        <Text style={[
          styles.attachmentOptionText,
          isCameraSessionActive && styles.attachmentOptionTextDisabled
        ]}>
          {isCameraSessionActive ? 'Taking Photos...' : 'Camera'}
        </Text>
      </Pressable>
    </View>
  );
} 