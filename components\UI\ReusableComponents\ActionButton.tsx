import React from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import { Text } from 'react-native-paper';

interface ActionButtonProps {
  onPress: () => void;
  label: string;
  disabled?: boolean;
  variant?: 'primary' | 'secondary';
  style?: object;
  labelStyle?: object;
}

export function ActionButton({
  onPress,
  label,
  disabled = false,
  variant = 'primary',
  style,
  labelStyle,
}: ActionButtonProps) {
  return (
    <View style={[styles.buttonContainer, style]}>
      <Pressable
        onTouchStart={onPress}
        style={({ pressed }) => [
          styles.button,
          variant === 'primary' ? styles.primaryButton : styles.secondaryButton,
          pressed && styles.buttonPressed,
          disabled && styles.buttonDisabled,
          styles.buttonContainer
        ]}
        disabled={disabled}
      >
        <Text
          style={[
            styles.buttonLabel,
            variant === 'primary' ? styles.primaryLabel : styles.secondaryLabel,
            labelStyle,
          ]}
        >
          {label}
        </Text>
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  buttonContainer: {
    width: '100%',
  },
  button: {
    paddingVertical: 10,
    borderRadius: 100,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#E2E7FB',
  },
  secondaryButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  buttonPressed: {
    opacity: 0.8,
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  primaryLabel: {
    color: '#2E52E2',
  },
  secondaryLabel: {
    color: '#374151',
  },
}); 