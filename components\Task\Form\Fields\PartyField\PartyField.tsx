import { View, Pressable } from 'react-native';
import { Text, TextInput } from 'react-native-paper';
import { partyFieldStyles } from './PartyField.styles';
import { useToast } from '@/components/Toast/useToast';
import { BottomSheetModal, BottomSheetBackdrop, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useRef, useMemo, useState, useCallback } from 'react';
import { useKeyboard } from '@/commons/KeyboardContext';
import { Icons, Colors } from '@/constants/DesignSystem';
import { Check } from '@/components/icons';

export interface Party {
  id: string;
  name: string;
}
interface PartyFieldProps {
  selectedParty: Party | null;
  parties: Party[];
  onPartySelect: (party: Party) => void;
  onSaveParty?: (partyId: string | null) => Promise<void>;
  taskId?: string;
  inputTheme: any;
  isLoading?: boolean;
  hasError?: boolean;
}

// Custom radio button component
const CustomRadioButton = ({ isSelected, onPress }: { isSelected: boolean; onPress: () => void }) => (
  <Pressable 
    onTouchEnd={onPress}
    style={{
      width: 27,
      height: 27,
      borderRadius: 20,
      borderWidth: 2,
      borderColor:  isSelected ? '#768DEC' : '#CFCECE',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: isSelected ? '#BEC9F6' : 'transparent',
      marginLeft: 16,
    }}
  >
    {isSelected && (
      <Check 
        size={Icons.size.md} 
        color={Colors.primary}
      />
    )}
  </Pressable>
);

export function PartyField({ 
  selectedParty,
  parties,
  onPartySelect,
  onSaveParty,
  taskId,
  inputTheme,
  isLoading,
  hasError,
}: PartyFieldProps) {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['80%'], []);
  const toast = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const { isKeyboardActive } = useKeyboard();

  const filteredParties = useMemo(() => 
    parties.filter((party) =>
      party.name.toLowerCase().includes(searchQuery.toLowerCase())
    ),
    [parties, searchQuery]
  );

  const handlePress = () => {
    if (isKeyboardActive) return;
    bottomSheetRef.current?.present();
  };

  const handlePartySelect = async (party: Party) => {
    if (!party) return;
    
    const previousParty = selectedParty;
    
    onPartySelect(party);
    
    bottomSheetRef.current?.dismiss();
    
    if (onSaveParty && taskId) {
      try {
        await onSaveParty(party.id);
      } catch (error: any) {
        console.log(error);
        if (previousParty) {
          onPartySelect(previousParty);
        }
        toast.error('Failed to update party');
      }
    }
  };

  const getPartyNameLength = (selectedPartyname: string) => {
    if (selectedPartyname.length <= 0)
      return 1;
      
    return selectedPartyname.length;
  }

  const handleDismiss = useCallback(() => {
    bottomSheetRef.current?.dismiss();
  }, []);

  return (
    <>
      <View style={[partyFieldStyles.inputWrapper]}>
        <Pressable 
          onTouchEnd={handlePress}
          disabled={isKeyboardActive}
          style={[
            partyFieldStyles.inputWrapper, 
            { 
              width: hasError ? '120%' : selectedParty ? 
                ((getPartyNameLength(selectedParty.name) * 1) < 25 ? 
                (getPartyNameLength(selectedParty.name) * 10 ) : '100%')
                : '40%',
            }
          ]}
        >
          <Text style={partyFieldStyles.labelText}>Party *</Text>
          <Text 
            style={selectedParty ? partyFieldStyles.valueText : partyFieldStyles.selectText} 
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {selectedParty ? selectedParty.name : 'Select'}
          </Text>
        </Pressable>
      </View>

      <BottomSheetModal
        ref={bottomSheetRef}
        index={1}
        snapPoints={snapPoints}
        enablePanDownToClose
        onDismiss={handleDismiss}
        style={{
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
          elevation: 5,
        }}
        backdropComponent={(props) => (
          <BottomSheetBackdrop
            {...props}
            appearsOnIndex={0}
            disappearsOnIndex={-1}
            pressBehavior="close"
            onPress={handleDismiss}
          />
        )}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'white',
        }}>
          <View style={{
            padding: 16,
            borderBottomWidth: 1,
            borderBottomColor: '#E5E5E5',
            alignItems: 'center',
          }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#344054',
            }}>
              Select Party
            </Text>
          </View>

          <TextInput
            placeholder="Search parties..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={{
              backgroundColor: '#ffffff',
              height: 50,
              borderBottomWidth: 1,
              borderColor: '#E5E5E5',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 1,
              elevation: 2,
            }}
            mode="flat"
            underlineColor="transparent"
            activeUnderlineColor="transparent"
            cursorColor="#000000"
          />

          <BottomSheetScrollView contentContainerStyle={{
            paddingHorizontal: 16,
          }}>
            {!filteredParties || filteredParties.length === 0 ? (
              <Text style={{
                padding: 16,
                textAlign: 'center',
                color: '#666',
              }}>
                No parties found
              </Text>
            ) : (
              filteredParties.map((party) => (
                <Pressable
                  key={party.id}
                  onTouchEnd={() => handlePartySelect(party)}
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingVertical: 16,
                    borderBottomWidth: 1,
                    borderBottomColor: '#E5E5E5',
                  }}
                >
                  <Text style={{
                    fontSize: 16,
                    color: '#344054',
                    flex: 1,
                  }} numberOfLines={4}>
                    {party.name}
                  </Text>
                  <CustomRadioButton
                    isSelected={selectedParty?.id === party.id}
                    onPress={() => handlePartySelect(party)}
                  />
                </Pressable>
              ))
            )}
          </BottomSheetScrollView>
        </View>
      </BottomSheetModal>
    </>
  );
} 