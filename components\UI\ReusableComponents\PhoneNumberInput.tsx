import * as React from 'react';
import { View, TouchableOpacity, Modal, FlatList } from 'react-native';
import { TextInput, Text, List, Searchbar } from 'react-native-paper';
import { StyleSheet, StyleProp, ViewStyle, TextStyle } from 'react-native';
import { countries, Country } from '../../../constants/countryCodes';
import { regexValidators } from '@/app/(home)/utils/reusableFunctions';
import { Colors, Spacing, Shadows, Typography, Borders, Icons } from '@/constants/DesignSystem';
import { DropDown } from '@/components/icons';

interface PhoneNumberInputProps {
  value: string;
  onChangeText: (rawValue: string, formattedValue: string) => void;
  error?: boolean;
  errorText?: string;
  onFocus?: () => void;
  label?: string;
  placeholder?: string;
  mode?: 'flat' | 'outlined';
  style?: StyleProp<TextStyle>;
}

export function PhoneNumberInput({
  value,
  onChangeText,
  error,
  errorText,
  onFocus,
  label = 'Phone Number',
  placeholder = 'Enter phone number',
  mode,
  style,
}: PhoneNumberInputProps) {
  const [modalVisible, setModalVisible] = React.useState(false);
  const [selectedCountry, setSelectedCountry] = React.useState(countries[0]);
  const [searchQuery, setSearchQuery] = React.useState('');

  const filteredCountries = React.useMemo(() => {
    return countries.filter(country =>
      country.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      country.dialCode.includes(searchQuery)
    );
  }, [searchQuery]);

  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country);
    setModalVisible(false);
  };

  const handlePhoneNumberChange = (number: string) => {
    const sanitizedNumber = regexValidators.sanitizeNumberInput(number, 10);

    const fullNumber = selectedCountry.dialCode + sanitizedNumber;
    onChangeText(sanitizedNumber, fullNumber);
  };

  return (
    <View style={styles.container}>
      <View style={styles.inputContainer}>
        <TouchableOpacity
          onPress={() => setModalVisible(true)}
          style={styles.countrySelector}
        >
          <View style={styles.countrySelectorContent}>
            <Text style={{
              color: Colors.text.secondary,
              fontSize: Typography.fontSize.md
            }}>
              {selectedCountry.dialCode}
            </Text>
            <DropDown size={Icons.size.md} color={Colors.text.secondary} />
          </View>
        </TouchableOpacity>

        <TextInput
          mode={mode}
          style={[styles.phoneInput, style]}
          label={label}
          value={value}
          onChangeText={handlePhoneNumberChange}
          error={error}
          onFocus={onFocus}
          placeholder={placeholder}
          keyboardType="phone-pad"
          outlineColor={Colors.border.medium}
          activeOutlineColor={Colors.primary}
          outlineStyle={{ borderRadius: Borders.radius.md }}
          contentStyle={{ paddingHorizontal: Spacing.sm }}
          theme={{ colors: { background: Colors.background.primary } }}
        />
      </View>
      {error && errorText ? (
        <Text style={styles.errorText}>{errorText}</Text>
      ) : null}

      <Modal
        visible={modalVisible}
        onDismiss={() => setModalVisible(false)}
        animationType="slide"
      >
        <View style={styles.modalContainer}>
          <Searchbar
            placeholder="Search countries"
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchbar}
            iconColor={Colors.text.secondary}
            inputStyle={{ color: Colors.text.primary }}
          />

          <FlatList
            data={filteredCountries}
            keyExtractor={(item) => item.code}
            renderItem={({ item }) => (
              <List.Item
                title={`${item.name} (${item.dialCode})`}
                titleStyle={{ color: Colors.text.primary }}
                onPress={() => handleCountrySelect(item)}
                left={props => <List.Icon {...props} icon="flag" color={Colors.primary} />}
                style={{
                  borderBottomWidth: Borders.width.thin,
                  borderBottomColor: Colors.border.light
                }}
              />
            )}
          />
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.tertiary,
  },
  countrySelector: {
    position: 'absolute',
    left: Spacing.md,
    zIndex: 1,
    justifyContent: 'center',
  },
  phoneInput: {
    flex: 1,
    paddingLeft: 48,
    backgroundColor: Colors.background.tertiary,
  } as TextStyle,
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.background.tertiary,
    paddingTop: 50,
  },
  searchbar: {
    margin: Spacing.sm,
    backgroundColor: Colors.background.tertiary,
  },
  countrySelectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  errorText: {
    color: Colors.error,
    fontSize: Typography.fontSize.xs,
    marginTop: Spacing.xs,
    marginLeft: '20%',
  },
});