import React from 'react';
import { View, StyleSheet, Pressable, TouchableOpacity } from 'react-native';
import { Avatar, Card, Text } from 'react-native-paper';
import { formatTimestamp } from '@/lib/utils/dateUtils';
import { useMutation } from '@apollo/client';
import { InAppNotification, GetInAppNotificationsQueryResult as GetInAppNotificationsListQueryResult, MarkNotificationAsReadMutationResult, DeleteInAppNotificationMutationResult } from '@/data/InAppNotificationsData/InAppNotificationsModel';
import { MARK_NOTIFICATION_AS_READ, GET_IN_APP_NOTIFICATIONS as GET_IN_APP_NOTIFICATIONS_LIST, DELETE_IN_APP_NOTIFICATION } from '@/data/InAppNotificationsData/InAppNotificationsGraphQL';
import { GET_IN_APP_NOTIFICATIONS as GET_UNREAD_COUNT_QUERY } from '@/graphql/queries/InAppNotificationCount';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS
} from 'react-native-reanimated';
import { GestureDetector, Gesture } from 'react-native-gesture-handler';
import { Colors, Typography, Spacing, Borders, Icons } from '@/constants/DesignSystem';
import { useNavigation } from 'expo-router';
import { NavigationProp } from '@react-navigation/native';
import { HomeRootStackList } from '@/app/Home/HomeNavigation';
import { Delete } from '@/components/icons';

interface InAppNotificationCardProps {
  notification: InAppNotification;
  onDelete: (id: string) => void;
}

const DELETE_BUTTON_WIDTH = 80;
const DELETE_SWIPE_THRESHOLD = DELETE_BUTTON_WIDTH * 0.6;

export function InAppNotificationCard({ notification, onDelete }: InAppNotificationCardProps) {
  const { id, sender, message, createdAt, read } = notification;

  const navigation = useNavigation<NavigationProp<HomeRootStackList>>();

  const translateX = useSharedValue(0);
  const itemHeight = useSharedValue(70);

  const [markAsRead, { loading: markingRead }] = useMutation<MarkNotificationAsReadMutationResult>(
    MARK_NOTIFICATION_AS_READ,
    {
      update(cache, { data: mutationResult }) {
        if (!mutationResult?.markNotificationAsRead || !('result' in mutationResult.markNotificationAsRead)) {
          return;
        }
        const updatedNotification = mutationResult.markNotificationAsRead.result.inAppNotification;

        const existingListData = cache.readQuery<GetInAppNotificationsListQueryResult>({
          query: GET_IN_APP_NOTIFICATIONS_LIST,
          variables: {
            pagination: { limit: null, skip: null },
          }
        });

        if (existingListData?.getInAppNotifications && 'result' in existingListData.getInAppNotifications) {
          const updatedList = existingListData.getInAppNotifications.result.inAppNotifications.map(notif =>
            notif.id === updatedNotification.id ? { ...notif, read: true } : notif
          );

          cache.writeQuery<GetInAppNotificationsListQueryResult>({
            query: GET_IN_APP_NOTIFICATIONS_LIST,
            variables: {
              pagination: { limit: null, skip: null },
            },
            data: {
              getInAppNotifications: {
                ...existingListData.getInAppNotifications,
                result: {
                  ...existingListData.getInAppNotifications.result,
                  inAppNotifications: updatedList,
                },
              },
            },
          });
        }
      },
      refetchQueries: [
        {
          query: GET_UNREAD_COUNT_QUERY,
          variables: {
            filter: {
              read: false,
            },
          },
        },
      ],
    }
  );

  const [deleteNotification, { loading: deleting }] = useMutation<DeleteInAppNotificationMutationResult>(
    DELETE_IN_APP_NOTIFICATION,
    {
      variables: { deleteInAppNotificationId: id },
      onCompleted: (data) => {
        if (data?.deleteInAppNotification && 'result' in data.deleteInAppNotification) {
          // Notification deleted successfully
        } else {
          console.error('Error reported from delete mutation:', data?.deleteInAppNotification?.message);
          translateX.value = withTiming(0);
        }
      },
      onError: (error) => {
        console.error('Error performing delete mutation:', error);
        translateX.value = withTiming(0);
      },
      update(cache) {
        const existingListData = cache.readQuery<GetInAppNotificationsListQueryResult>({
          query: GET_IN_APP_NOTIFICATIONS_LIST,
          variables: {
            pagination: { limit: null, skip: null },
          }
        });

        if (existingListData?.getInAppNotifications && 'result' in existingListData.getInAppNotifications) {
          const updatedList = existingListData.getInAppNotifications.result.inAppNotifications.filter(
            notif => notif.id !== id
          );

          cache.writeQuery<GetInAppNotificationsListQueryResult>({
            query: GET_IN_APP_NOTIFICATIONS_LIST,
            variables: {
              pagination: { limit: null, skip: null },
            },
            data: {
              getInAppNotifications: {
                ...existingListData.getInAppNotifications,
                result: {
                  ...existingListData.getInAppNotifications.result,
                  inAppNotifications: updatedList,
                },
                pagination: {
                  ...existingListData.getInAppNotifications.pagination,
                  totalItems: existingListData.getInAppNotifications.pagination.totalItems - 1,
                },
              },
            },
          });
        }

        const existingCountData = cache.readQuery<{ getInAppNotifications: { pagination: { totalItems: number } } }>({
          query: GET_UNREAD_COUNT_QUERY,
          variables: {
            filter: {
              read: false,
            },
          },
        });

        if (existingCountData?.getInAppNotifications?.pagination) {
          const currentCount = existingCountData.getInAppNotifications.pagination.totalItems;
          const wasUnread = !read;

          cache.writeQuery({
            query: GET_UNREAD_COUNT_QUERY,
            variables: {
              filter: {
                read: false,
              },
            },
            data: {
              getInAppNotifications: {
                ...existingCountData.getInAppNotifications,
                pagination: {
                  ...existingCountData.getInAppNotifications.pagination,
                  totalItems: wasUnread ? currentCount - 1 : currentCount,
                },
              },
            },
          });
        }
      },
      refetchQueries: [
        {
          query: GET_UNREAD_COUNT_QUERY,
          variables: {
            filter: {
              read: false,
            },
          },
        },
      ],
    }
  );

  const handlePress = () => {
    if (translateX.value !== 0) {
      translateX.value = withTiming(0);
      return;
    }
    if (!read && !markingRead && !deleting) {
      runOnJS(markAsRead)({ variables: { markNotificationAsReadId: id } });
    }

    handleNavigation();
  };

  const parseLink = (link: string) => {
    const [pathWithId, queryString] = link.split('?');
    const pathSegments = pathWithId.startsWith('/') ? pathWithId.substring(1).split('/') : pathWithId.split('/');

    let screenName = '';
    let itemId = null;
    const params: Record<string, string> = {};
    
    if (pathSegments.length > 0) {
      screenName = pathSegments[0];
    }
    if (pathSegments.length > 1) {
      itemId = pathSegments[1];
    }
    
    if (queryString) {
      queryString.split('&').forEach(param => {
        const [key, value] = param.split('=');
        if (key && value) {
          params[key] = decodeURIComponent(value);
        }
      });
    }

    return { screenName, itemId, params };
  }

  const handleNavigation = () => {
    if (notification.link === '//') return;
    const { screenName, itemId } = parseLink(notification.link!);

    switch (screenName) {
      case 'party-details':
        navigation.navigate('PartyDetails', {
          screen: 'NewPartyDetailsScreen',
          params: { partyId: itemId! }
        });
        break;

      case 'rsvp':
        navigation.navigate('PartyDetails', {
          screen: 'RsvpScreen',
          params: { partyId: itemId! }
        });
        break;

      case 'circleDetail':
        navigation.navigate('AddCircleScreen', {
          screen: 'CircleDetails',
          params: { circleId: itemId! }
        });
        break;

      default:
        console.log('Unsupported screen name:', screenName);
        break;
    }    
  };

  const handleDelete = () => {
    if (!deleting) {
      runOnJS(deleteNotification)();

      itemHeight.value = withTiming(0, { duration: 200 }, (finished) => {
        if (finished) {
          runOnJS(onDelete)(id);
        }
      });
    }
  };

  const panGesture = Gesture.Pan()
    .activeOffsetX([-10, 10])
    .failOffsetY([-5, 5])
    .onUpdate((event) => {
      translateX.value = Math.min(0, Math.max(-DELETE_BUTTON_WIDTH, event.translationX));
    })
    .onEnd((event) => {
      if (event.translationX < -DELETE_SWIPE_THRESHOLD) {
        translateX.value = withTiming(-DELETE_BUTTON_WIDTH);
      } else {
        translateX.value = withTiming(0);
      }
    });

  const animatedCardStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const animatedContainerStyle = useAnimatedStyle(() => ({
    height: itemHeight.value,
    marginBottom: itemHeight.value > 0 ? 8 : 0,
    opacity: itemHeight.value > 0 ? 1 : 0,
    overflow: 'hidden',
  }));

  const formattedTime = formatTimestamp(createdAt);
  const backgroundColor = read ? Colors.selection.unselected : Colors.selection.selected;
  const profilePictureUri = sender?.profilePicture;
  const initials = `${sender?.firstName?.charAt(0) ?? 'S'}${sender?.lastName?.charAt(0) ?? ''}`;

  return (
    <Animated.View style={animatedContainerStyle}>
      <TouchableOpacity
        style={styles.deleteButtonContainer}
        onPress={handleDelete}
        disabled={deleting}
      >
        <Delete size={Icons.size.xl} color={Colors.white} />
      </TouchableOpacity>

      <GestureDetector gesture={panGesture}>
        <Animated.View style={animatedCardStyle}>
          <Pressable onPress={handlePress} disabled={markingRead || deleting}>
            <Card style={[styles.cardBase, { backgroundColor }]} elevation={0}>
              <Card.Content style={styles.content}>
                {profilePictureUri ? (
                  <Avatar.Image
                    size={40}
                    source={{ uri: profilePictureUri }}
                    style={[styles.avatar, {
                      borderWidth: Borders.width.thin,
                      borderColor: Colors.border.light
                    }]}
                  />
                ) : (
                  <Avatar.Text
                    size={40}
                    label={initials}
                    style={styles.avatar}
                    color={Colors.white}
                    labelStyle={{ fontFamily: Typography.fontFamily.primary }}
                    theme={{ colors: { primary: Colors.primary } }}
                  />
                )}
                <View style={styles.textContainer}>
                  <Text
                    variant="bodyMedium"
                    numberOfLines={2}
                    ellipsizeMode='tail'
                    style={{
                      fontFamily: Typography.fontFamily.primary,
                      fontSize: Typography.fontSize.md,
                      color: Colors.text.tertiary
                    }}
                  >
                    {message}
                  </Text>
                </View>
                <Text variant="labelSmall" style={styles.timeText}>
                  {formattedTime}
                </Text>
              </Card.Content>
            </Card>
          </Pressable>
        </Animated.View>
      </GestureDetector>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  cardBase: {
    borderRadius: 0,
    justifyContent: 'center',
    minHeight: 70,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 0,
    paddingHorizontal: Spacing.lg,
  },
  avatar: {
    marginRight: Spacing.md,
  },
  textContainer: {
    flex: 1,
    marginRight: Spacing.sm,
  },
  timeText: {
    color: Colors.text.secondary,
    minWidth: 50,
    textAlign: 'right',
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.sm,
  },
  deleteButtonContainer: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    width: DELETE_BUTTON_WIDTH,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.error,
  },
});
