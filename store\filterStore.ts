import { create } from 'zustand';

interface Assignee {
  id: string;
  firstName: string;
  lastName: string;
}

interface Filters {
  status: string[];
  date: {
    startDate: Date;
    endDate: Date;
  } | null;
  assignee: Assignee[];
}

type FilterStore = {
  filters: Filters;
  setFilters: (filters: Partial<Filters>) => void;
  clearFilter: (filterType: keyof Filters) => void;
  clearFilters: () => void;
};

export const useFilterStore = create<FilterStore>((set) => ({
  filters: {
    status: [],
    date: null,
    assignee: [],
  },
  setFilters: (newFilters) => set((state) => ({
    filters: {
      ...state.filters,
      ...newFilters,
    }
  })),
  clearFilter: (filterType) =>
    set((state) => ({
      filters: {
        ...state.filters,
        [filterType]: filterType === 'date' ? null : [],
      }
    })),
  clearFilters: () => set({
    filters: {
      status: [],
      date: null,
      assignee: [],
    }
  }),
}));
