import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { View, Text, Pressable, StyleSheet, Image, ActivityIndicator, ScrollView } from 'react-native';
import { DELETE_CIRCLE_BY_ID, GET_CIRCLE_INVITATIONS, GET_CIRCLE_INVITATIONS_COUNT } from '@/app/(home)/CirclesDashboard/CircleDetails.data';
import { useMutation, useQuery } from '@apollo/client';
import { BottomSheetBackdrop, BottomSheetModal, BottomSheetModalProvider, BottomSheetView } from '@gorhom/bottom-sheet';
import CustomAlert from '../CustomAlert';
import { useToast } from '../Toast';
import { EventsList } from '../HomePage/Components/EventsList';
import { GET_INVITATIONS } from '../HomePage/HomePage.data';
import { SEND_CIRCLE_INVITATION } from '@/components/user-circles/user-circles.data';
import { useInvitationStore } from '@/store/invitationStore';
import { getRandomDefaultImage } from '@/constants/HomePageDefaultImages';
import { Filters } from '../HomePage/Components/Filters';
import { Colors, Typography, Spacing, Borders, Shadows, Icons } from '@/constants/DesignSystem';
import { BackArrow, ProfileDetail, MoreVertical, Edit, Delete, CalenderPlus } from '@/components/icons';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import { AddCircleRootStackList } from '@/app/(home)/CirclesDashboard/AddCircleRootStackList';
import MembersTab, { CircleMember } from './MembersTab';
import AddContactsSection from '@/components/settings/UserProfile/AddContactsSection';
import { s } from 'react-native-size-matters';
import { HomeRootStackList } from '@/app/Home/HomeNavigation';
import { SuccessModal } from '../shared/SuccessModal';

type FilterStatus = 'ACCEPTED' | 'PENDING' | 'REJECTED';

type CircleDetailsProps = StackScreenProps<AddCircleRootStackList, 'CircleDetails'>;

const CircleDetails = ({ route }: CircleDetailsProps) => {
    const [circle, setCircle] = useState({
        name: '',
        id: '',
        description: '',
        members: [],
        createdBy: {
            id: '',
            firstName: '',
            lastName: '',
            phone: '',
            email: '',
            profilePicture: '',
        },
        createdAt: '',
        membersCount: 0,
        imageUrl: '',
        userRole: '',
        organizers: [],
        invitations: [],
    });
    const [formattedDate, setFormattedDate] = useState('');
    const [showModal, setShowModal] = useState(false);
    const [alertMessage, setAlertMessage] = useState('');
    const circleId = route?.params?.circleId ?? undefined;
    const bottomSheetRef = useRef<BottomSheetModal>(null);
    const contactSheetRef = useRef<any>(null);
    const navigation = useNavigation<StackNavigationProp<HomeRootStackList>>();
    const [selectedFilter, setSelectedFilter] = useState<'next' | 'past'>('next');
    const [isUpdating, setIsUpdating] = useState(false);
    const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
    const [selectedMemberFilter, setSelectedMemberFilter] = useState<FilterStatus>('ACCEPTED');
    const [searchQuery, setSearchQuery] = useState('');
    const [debouncedSearch, setDebouncedSearch] = useState('');
    const [isSearchVisible, setIsSearchVisible] = useState(false);
    const toast = useToast();
    const setInvitations = useInvitationStore(state => state.setInvitations);

    const [activeTab, setActiveTab] = useState('events');
    const [isSuccessGifVisible, setIsSuccessGifVisible] = useState(false);

    const { data: circleData, loading: detailsLoading, refetch } = useQuery(GET_CIRCLE_INVITATIONS, {
        variables: {
            getEventGroupByIdId: circleId,
            filter: { eventGroupId: circleId },
            pagination: { limit: 200, skip: 0 }
        },
        skip: !circleId,
        fetchPolicy: 'network-only'
    });

    const { data: countsData, refetch: refetchCounts } = useQuery(GET_CIRCLE_INVITATIONS_COUNT, {
        variables: {
          eventGroupId: circleId,
        },
        skip: !circleId,
        fetchPolicy: 'cache-and-network',
      });

    const { data: filteredMembersData, refetch: refetchFilteredMembers, loading: isFilterLoading } = useQuery(GET_CIRCLE_INVITATIONS, {
        variables: {
          filter: {
            eventGroupId: circleId,
            status: selectedMemberFilter,
            user: debouncedSearch ? {
              firstName: debouncedSearch,
              lastName: null
            } : undefined
          },
          pagination: {
            skip: 0,
            limit: 50
          },
          getEventGroupByIdId: circleId
        },
        skip: !circleId || activeTab !== 'members',
        fetchPolicy: 'cache-and-network',
      });

    const { data, loading, error, refetch: refetchInvitations } = useQuery(GET_INVITATIONS, {
        fetchPolicy: 'network-only',
        notifyOnNetworkStatusChange: true,
        variables: {
            filter: {
                eventGroupId: circleId,
            },
        },
    });

    const processedEvents = useMemo(() => {
        if (!data?.getUserEvents?.result?.events) return [];

        return data.getUserEvents.result.events.reduce((acc: Event[], event: any) => {
            const party = event.parties[0];
            const mediaUrl = party?.invitation?.media?.[0]?.url;
            const eventDate = new Date(party?.time || event.startDate);
            const currentDate = new Date();
            const isPastEvent = eventDate < currentDate;
            const types: ('next' | 'past')[] = [];

            if (types.length === 0) types.push(isPastEvent ? 'past' : 'next');

            const serviceLocation = party?.serviceLocation;
            const locationString = serviceLocation
                ? `${serviceLocation.city}${serviceLocation.state ? `, ${serviceLocation.state}` : ''}`
                : 'Location TBD';

            return [...acc, ...types.map(type => ({
                id: event.id,
                originalId: party?.id,
                title: party?.name || event.name,
                hostedBy: `${event.mainHost.userId.firstName} ${event.mainHost.userId.lastName}`,
                image: mediaUrl ? { uri: mediaUrl } : getRandomDefaultImage(),
                type,
                date: party?.time || event.startDate,
                location: locationString
            }))];
        }, []);
    }, [data]);

    useEffect(() => {
        if (processedEvents.length > 0) {
            setInvitations(processedEvents);
        }
    }, [processedEvents, setInvitations]);

    const filteredEvents = useMemo(() =>
        processedEvents.filter((event: Event) => event.type === selectedFilter),
        [processedEvents, selectedFilter]
    );

    const filterCounts = useMemo(() => ({
        next: processedEvents.filter((e: Event) => e.type === 'next').length,
        past: processedEvents.filter((e: Event) => e.type === 'past').length,
    }), [processedEvents]);

    const filters = useMemo(() => [
        { label: `Next ${filterCounts.next}`, type: 'next' },
        { label: `Past ${filterCounts.past}`, type: 'past' }
    ], [filterCounts]);

    const [deleteCircle] = useMutation(DELETE_CIRCLE_BY_ID);
    const [sendCircleInvitation] = useMutation(SEND_CIRCLE_INVITATION);

    useFocusEffect(
        useCallback(() => {
            if (circleId) {
                refetch();
                refetchInvitations();
                refetchCounts();
                refetchFilteredMembers();
            }
        }, [circleId, refetch, refetchInvitations, refetchCounts, refetchFilteredMembers])
    );

    useEffect(() => {
        if (circleData) {
            const eventGroup = circleData?.getEventGroupById?.result?.eventGroup;
            if (!eventGroup) return;

            const invitations = circleData?.getEventGroupInvitations?.result?.eventGroupInvitations || [];
            const invitationMap = new Map();

            invitations.forEach((inv: any) => {
                if (inv.user) {
                    invitationMap.set(inv.user.id, inv.id);
                }
            });

            const members = eventGroup.members || [];
            const enrichedMembers = members.map((member: any) => ({
                ...member,
                invitationId: invitationMap.get(member.id),
            }));

            const organizerIds = new Set((eventGroup.organizers || []).map((o: { id: string }) => o.id));
            const organizersWithDetails = enrichedMembers.filter((m: any) => organizerIds.has(m.id));
            const membersOnly = enrichedMembers.filter((m: any) => !organizerIds.has(m.id));

            const createdByDetails = members.find((m: any) => m.id === eventGroup.createdBy?.id) || eventGroup.createdBy;

            setCircle({
                name: eventGroup.name,
                id: eventGroup.id,
                createdBy: createdByDetails,
                createdAt: eventGroup.createdAt,
                members: membersOnly,
                membersCount: eventGroup.membersCount,
                description: eventGroup.description,
                imageUrl: eventGroup.imageUrl,
                userRole: eventGroup.userRole,
                organizers: organizersWithDetails,
                invitations: invitations,
            });

            const date = new Date(eventGroup.createdAt);
            const formatted = date.toLocaleString('en-US', {
                month: 'short',
                year: 'numeric',
            });
            setFormattedDate(formatted);
        }
    }, [circleData]);

    const toggleDescriptionExpansion = () => {
        setIsDescriptionExpanded(!isDescriptionExpanded);
    };

    const renderDescription = () => {
        if (!circle.description) {
            return null;
        }

        const maxLength = 150;
        if (circle.description.length <= maxLength || isDescriptionExpanded) {
            return (
                <View style={styles.descriptionContainer}>
                    <Text style={styles.descriptionText}>
                        {circle.description}
                        {circle.description.length > maxLength && (
                            <Text style={styles.moreLessText} onPress={toggleDescriptionExpansion}>
                                {' less'}
                            </Text>
                        )}
                    </Text>
                </View>
            );
        }

        return (
            <View style={styles.descriptionContainer}>
                <Text style={styles.descriptionText}>
                    {`${circle.description.substring(0, maxLength)}...`}
                    <Text style={styles.moreLessText} onPress={toggleDescriptionExpansion}>
                        {'more'}
                    </Text>
                </Text>
            </View>
        );
    };

    const getInitials = (name: string) => {
        if (!name) return '';
        const nameParts = name.trim().split(' ');
        if (nameParts.length > 1) {
            return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
        }
        return `${nameParts[0][0]}`.toUpperCase();
    };

    const handlePressEdit = async () => {
        if (circle.userRole === 'MEMBER') {
            toast.error('You are a member and cannot edit the circle');
            return;
        }

        navigation.navigate('AddCircleScreen', {
            screen: 'AddCircle',
            params: {
                fromScreen: 'CircleDetails',
                details: circle,
                onUpdate: () => refetch()
            }
        });
    }

    const onPressDeleteCircle = () => {
        const message = `Do you want to DELETE " ${circle.name} "?`;
        setAlertMessage(message);
        setShowModal(true);
    };

    const handleDeleteCircle = async () => {
        try {
            setShowModal(false);
            setIsUpdating(true);
            const { data } = await deleteCircle({
                variables: {
                    deleteEventGroupId: circle.id,
                },
            });

            if (data?.deleteEventGroup?.status === 'SUCCESS') {
                toast.success('Circle deleted successfully!');
                navigation.goBack();
            } else {
                toast.error(data?.deleteEventGroup?.message || 'Failed to delete circle');
            }
        } catch (error) {
            console.log('Delete error:', error);
            toast.error('Something went wrong while deleting the circle');
        } finally {
            setIsUpdating(false);
        }
    };

    const setMembers = async (updatedMembers: any) => {
        try {
            setIsUpdating(true);
            const originalMemberIds = new Set(circle.members.map((m: any) => m.id));
            const newMembers = updatedMembers.filter((m: any) => !originalMemberIds.has(m.id));

            if (newMembers.length > 0) {
                const sendCircleInvitationInput = {
                    input: {
                        eventGroupId: circle.id,
                        members: newMembers.map((contact: any) => ({
                            email: contact?.emails?.[0]?.email ?? contact?.email,
                            firstName: contact?.firstName ?? null,
                            lastName: contact?.lastName ?? null,
                            phone: contact?.phoneNumbers?.[0]?.number ?? contact?.phoneNumber,
                        })),
                    }
                };

                const { data } = await sendCircleInvitation({ variables: sendCircleInvitationInput });

                if (data?.sendEventGroupInvitation?.status === 'SUCCESS') {
                    toast.success('Invitations sent successfully!');
                    refetch();
                } else {
                    toast.error(data?.sendEventGroupInvitation?.message || 'Failed to send invitations');
                }
                refetchCounts();
                refetchFilteredMembers();
            }
        } catch (error) {
            console.log('Update members error:', error);
            toast.error('Something went wrong while sending invitations');
        } finally {
            setIsUpdating(false);
        }
    };

    const handlePressAddMembers = () => {
        contactSheetRef.current?.openBottomSheet();
    }

    const handlePressCreateEvent = () => {
        navigation.navigate('AddPartyScreen', { screen: 'AddParty', params: { eventGroupId: circle.id } });
    }

    const EventsTab = () => (
        <ScrollView style={{ flex: 1, padding: Spacing.lg }}>
            <Filters
                filters={filters}
                onFilterSelect={(type: 'next' | 'past' | 'host' | 'guest') => setSelectedFilter(type as 'next' | 'past')}
                selectedFilter={selectedFilter}
            />
            <EventsList
                fromScreen={'circleDetails'}
                selectedFilter={selectedFilter}
                circleId={circleId}
            />
        </ScrollView>
    );


    const getFooterContent = () => {
        if (activeTab === 'members' && (circle.userRole === 'ORGANIZER' || circle.userRole === 'CO-ORGANIZER')) {
            return {
                show: true,
                icon: <ProfileDetail size={Icons.size.md} color={Colors.text.tertiary} />,
                text: 'Add Members',
                onPress: handlePressAddMembers,
            };
        }

        if (activeTab === 'events' && (circle.userRole === 'ORGANIZER' || circle.userRole === 'CO-ORGANIZER')) {
            return {
                show: true,
                icon: <CalenderPlus size={Icons.size.md} color={Colors.text.tertiary} />,
                text: 'Create Event',
                onPress: handlePressCreateEvent,
            };
        }

        return { show: false };
    };

    const footerContent = getFooterContent();

    const memberFilterCounts = useMemo(() => {
        const counts: Record<FilterStatus, number> = {
            ACCEPTED: countsData?.accepted?.pagination?.totalItems || 0,
            PENDING: countsData?.pending?.pagination?.totalItems || 0,
            REJECTED: countsData?.rejected?.pagination?.totalItems || 0,
        };
        return counts;
    }, [countsData]);

    const membersToDisplay = useMemo(() => {
        const eventGroup = filteredMembersData?.getEventGroupById?.result?.eventGroup;
        const invitations = filteredMembersData?.getEventGroupInvitations?.result?.eventGroupInvitations || [];
    
        const processedMembers: CircleMember[] = [];
        const memberIds = new Set<string>();
    
        const organizerId = eventGroup?.createdBy?.id;
        const coorganizerIds = new Set(eventGroup?.organizers?.map((o: { id: string }) => o.id) || []);

        if (organizerId) {
            coorganizerIds.delete(organizerId);
        }

        let members: CircleMember[] = [];
        let coOrganizers: CircleMember[] = [];
        let organizers: CircleMember[] = [];

        invitations.forEach((invitation: any) => {
            const user = invitation.user;
            if (!user || memberIds.has(user.id)) return;

            const role = user.id === organizerId ? 'ORGANIZER' 
                : coorganizerIds.has(user.id) ? 'CO-ORGANIZER' 
                : 'MEMBER';

            const memberData: CircleMember = {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName || '',
                phone: user.phone,
                email: user.email,
                profilePicture: user.profilePicture,
                status: invitation.status as FilterStatus,
                role,
                invitationId: invitation.id,
            };

            if (role === 'ORGANIZER') organizers.push(memberData);
            else if (role === 'CO-ORGANIZER') coOrganizers.push(memberData);
            else members.push(memberData);

            memberIds.add(user.id);
        });

        processedMembers.push(...organizers, ...coOrganizers, ...members);

        if (selectedMemberFilter === 'ACCEPTED') {
            const eventGroup = filteredMembersData?.getEventGroupById?.result?.eventGroup;
            const createdById = eventGroup?.createdBy?.id;
            const members = eventGroup?.members || [];
            
            if (createdById && !memberIds.has(createdById)) {
                const creatorDetails = members.find((m: any) => m.id === createdById);
                if (creatorDetails) {
                    processedMembers.unshift({
                        id: creatorDetails.id,
                        firstName: creatorDetails.firstName || '',
                        lastName: creatorDetails.lastName || '',
                        phone: creatorDetails.phone,
                        email: creatorDetails.email,
                        profilePicture: creatorDetails.profilePicture || undefined,
                        role: 'ORGANIZER',
                        status: 'ACCEPTED',
                    });
                    memberIds.add(creatorDetails.id);
                }
            }
        }

        return processedMembers;
    }, [filteredMembersData, selectedMemberFilter]);

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedSearch(searchQuery);
        }, 500);

        return () => {
            clearTimeout(handler);
        };
    }, [searchQuery]);

    useEffect(() => {
        if (route.params?.showSuccessAnimation) {
            setIsSuccessGifVisible(true);
            setTimeout(() => setIsSuccessGifVisible(false), 5000);
        }
    }, [route.params?.showSuccessAnimation]);

    return (
        detailsLoading || isUpdating ? (
            <View style={{ justifyContent: 'center', alignItems: 'center', minHeight: 260 }}>
                <ActivityIndicator size="large" color="#000000" />
            </View>
        ) : (
            <BottomSheetModalProvider>
                <View style={styles.customHeader}>
                    <Pressable onPress={() => navigation.goBack()} style={styles.headerButton}>
                        <BackArrow size={Icons.size.md} color={Colors.primary} />
                    </Pressable>
                    <Pressable onPress={() => {
                        bottomSheetRef.current?.present();
                    }}
                        style={styles.headerButton}>
                        <MoreVertical size={Icons.size.md} color={Colors.primary} />
                    </Pressable>
                </View>
                <View style={styles.container}>
                    <View style={{ flex: 1 }}>
                        <View style={styles.headerDetailsContainer}>
                            <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                                <View style={styles.imageContainer}>
                                    {circle.imageUrl ? (
                                        <Image
                                            source={{ uri: circle.imageUrl }}
                                            style={styles.circleImage}
                                            resizeMode="cover" />
                                    ) : (
                                        <Text style={{
                                            fontSize: 60,
                                            fontWeight: Typography.fontWeight.regular,
                                            color: Colors.primary,
                                        }}>
                                            {getInitials(circle.name)}
                                        </Text>
                                    )}
                                </View>
                            </View>
                            <View style={styles.detailsContainer}>
                                <View>
                                    <Text style={styles.circleTitle}>
                                        {circle.name}
                                    </Text>
                                </View>
                                <View style={styles.circleDetails}>
                                    <View>
                                        <Text style={styles.headerText}>{'Created by'}</Text>
                                        <Text style={styles.valueText}>{circle.createdBy?.firstName}</Text>
                                    </View>
                                    <View>
                                        <Text style={styles.headerText}>{'Active since'}</Text>
                                        <Text style={styles.valueText}>{formattedDate}</Text>
                                    </View>
                                </View>
                                <View style={styles.circleDetails}>
                                    <View>
                                        <Text style={styles.headerText}>{'Members'}</Text>
                                        <Text style={styles.valueText}>{circle.membersCount}</Text>
                                    </View>
                                        <View style={styles.editContainer}>
                                            <Pressable onPress={() => {
                                                handlePressEdit();
                                            }} style={styles.editIconContainer}>
                                                <Edit color={Colors.text.secondary} size={Icons.size.sm} />
                                                <Text style={styles.editText}>{'Edit'}</Text>
                                            </Pressable>
                                        </View>

                                </View>
                            </View>
                        </View>
                        {renderDescription()}
                        <View style={styles.tabBarContainer}>
                            <Pressable
                                style={[styles.tabItem, activeTab === 'events' && styles.activeTabItem]}
                                onPress={() => setActiveTab('events')}
                            >
                                <Text style={[styles.tabText, activeTab === 'events' && styles.activeTabText]}>Events</Text>
                            </Pressable>
                            <Pressable
                                style={[styles.tabItem, activeTab === 'members' && styles.activeTabItem]}
                                onPress={() => setActiveTab('members')}
                            >
                                <Text style={[styles.tabText, activeTab === 'members' && styles.activeTabText]}>Members</Text>
                            </Pressable>
                        </View>
                        {activeTab === 'events' ? <EventsTab /> : <MembersTab 
                            circle={circle} 
                            refetch={refetch}
                            membersToDisplay={membersToDisplay}
                            filterCounts={memberFilterCounts}
                            isFilterLoading={isFilterLoading}
                            selectedFilter={selectedMemberFilter}
                            setSelectedFilter={setSelectedMemberFilter}
                            searchQuery={searchQuery}
                            setSearchQuery={setSearchQuery}
                            isSearchVisible={isSearchVisible}
                            setIsSearchVisible={setIsSearchVisible}
                            refetchFilteredMembers={refetchFilteredMembers}
                            refetchCounts={refetchCounts}
                        />}
                    </View>
                    {footerContent.show && (
                        <View style={styles.footerContainer}>
                            <Pressable style={styles.footerButton} onPress={footerContent.onPress}>
                                {footerContent.icon}
                                <Text style={styles.footerButtonText}>{footerContent.text}</Text>
                            </Pressable>
                        </View>
                    )}
                </View>
                <View>
                    <AddContactsSection ref={contactSheetRef} members={circle.members} setMembers={setMembers} />
                </View>
                <BottomSheetModal
                    ref={bottomSheetRef}
                    index={0}
                    snapPoints={['20%']}
                    enablePanDownToClose
                    backdropComponent={(props) => (
                        <BottomSheetBackdrop {...props} appearsOnIndex={0} disappearsOnIndex={-1} />
                    )}
                >
                    <BottomSheetView style={{ backgroundColor: Colors.background.primary, flex: 1 }}>
                        <View style={styles.bottomSheetContainer}>
                            <Pressable style={styles.row} onPress={() => {
                                bottomSheetRef.current?.dismiss();
                                handlePressEdit()
                            }}>
                                <Edit color={Colors.text.secondary} size={Icons.size.xl} />
                                <Text style={styles.editCircleText}>Edit Circle</Text>
                            </Pressable>
                            <Pressable style={styles.row} onPress={onPressDeleteCircle}>
                                <Delete color={Colors.error} size={Icons.size.xl} />
                                <Text style={styles.deleteCircleText}>Delete Circle</Text>
                            </Pressable>
                        </View>
                        <CustomAlert
                            visible={showModal}
                            onClose={() => setShowModal(false)}
                            onConfirm={handleDeleteCircle}
                            message={alertMessage}
                        />
                    </BottomSheetView>
                </BottomSheetModal>
                <SuccessModal
                    visible={isSuccessGifVisible}
                    onDismiss={() => setIsSuccessGifVisible(false)}
                    message="Circle created successfully!"
                />
            </BottomSheetModalProvider>
        )
    )
}

export default CircleDetails;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background.primary,
        padding: Spacing.md,
        gap: Spacing.md,
    },
    headerDetailsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: Spacing.xl,
        marginBottom: Spacing.md,
    },
    imageContainer: {
        width: 130,
        height: 130,
        borderRadius: 65,
        overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Colors.background.secondary
    },
    detailsContainer: {
        width: '100%',
        gap: Spacing.lg,
    },
    circleTitle: {
        fontWeight: Typography.fontWeight.bold,
        fontFamily: Typography.fontFamily.primary,
        fontSize: Typography.fontSize.lg,
    },
    editIconContainer: {
        flexDirection: 'row',
        borderColor: Colors.border.light,
        borderWidth: Borders.width.thin,
        borderRadius: Borders.radius.circle,
        paddingHorizontal: Spacing.md,
        paddingVertical: Spacing.xs,
        alignItems: 'center',
        gap: Spacing.sm,
        height: 24,
    },
    editText: {
        fontFamily: Typography.fontFamily.primary,
        fontWeight: Typography.fontWeight.regular,
        color: Colors.text.secondary,
        fontSize: Typography.fontSize.sm
    },
    circleDetails: {
        flexDirection: 'row',
        gap: Spacing.lg,
        alignItems: 'center',
    },
    headerText: {
        fontFamily: Typography.fontFamily.primary,
        fontSize: Typography.fontSize.md,
        color: Colors.border.light
    },
    valueText: {
        fontFamily: Typography.fontFamily.primary,
        fontSize: Typography.fontSize.md,
        color: Colors.text.tertiary
    },
    editContainer: {
        paddingRight: Spacing.sm,
        height: 24,
    },
    bottomSheetContainer: {
        padding: Spacing.md,
        gap: Spacing.lg
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        gap: Spacing.md,
        paddingHorizontal: Spacing.md
    },
    editCircleText: {
        fontFamily: Typography.fontFamily.primary,
        fontSize: Typography.fontSize.lg,
        color: Colors.text.tertiary,
        fontWeight: Typography.fontWeight.semibold,
    },
    deleteCircleText: {
        fontFamily: Typography.fontFamily.primary,
        fontSize: Typography.fontSize.lg,
        color: Colors.error,
        fontWeight: Typography.fontWeight.semibold,
    },
    memberRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: Spacing.lg,
    },
    profilePicContainer: {
        width: 80,
        height: 80,
        borderRadius: 40,
        overflow: 'hidden',
        marginRight: Spacing.md,
        backgroundColor: Colors.tertiary,
        justifyContent: 'center',
        alignItems: 'center'
    },
    profilePic: {
        width: 80,
        height: 80,
    },
    infoContainer: {
        flex: 1,
        justifyContent: 'center',
    },
    name: {
        fontWeight: Typography.fontWeight.medium,
        fontSize: Typography.fontSize.md,
    },
    role: {
        fontWeight: Typography.fontWeight.medium,
        color: Colors.border.light,
        fontSize: Typography.fontSize.md,
    },
    iconContainer: {
        paddingHorizontal: Spacing.sm,
    },
    footerContainer: {
        padding: Spacing.lg,
        backgroundColor: Colors.background.primary,
        borderTopWidth: Borders.width.thin,
        borderTopColor: Colors.border.medium,
    },
    footerButton: {
        backgroundColor: Colors.primary,
        paddingVertical: Spacing.md,
        borderRadius: Borders.radius.md,
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'center',
        gap: Spacing.md
    },
    footerButtonText: {
        color: Colors.text.tertiary,
        fontWeight: Typography.fontWeight.regular,
        fontSize: Typography.fontSize.md,
    },
    customHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: Spacing.lg,
        paddingVertical: Spacing.md,
        borderBottomWidth: Borders.width.thin,
        borderColor: Colors.border.medium,
        ...Shadows.sm,
        zIndex: 1,
        marginTop: 30,
    },
    headerTitle: {
        fontSize: Typography.fontSize.lg,
        fontWeight: Typography.fontWeight.semibold,
        fontFamily: Typography.fontFamily.primary,
        color: Colors.text.tertiary,
    },
    headerButton: {
        padding: Spacing.sm,
    },
    descriptionContainer: {
        paddingHorizontal: s(16),
        marginTop: s(0),
        marginBottom: s(16),
    },
    descriptionText: {
        fontSize: s(13),
        color: '#333',
        lineHeight: s(19),
        textAlign: 'left',
    },
    moreLessText: {
        color: Colors.text.primary,
        fontWeight: 'bold',
        fontSize: s(13),
    },
    headerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
    },
    backButton: {
        padding: 8,
    },
    circleInfoContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
    },
    circleImage: {
        width: 130,
        height: 130,
        borderRadius: 65,
    },
    circleTextContainer: {
        flex: 1,
    },
    circleName: {
        fontWeight: 'bold',
        fontSize: 20,
    },
    detailText: {
        fontWeight: 'bold',
        marginRight: 8,
    },
    detailTextBold: {
        fontWeight: 'bold',
    },
    editButton: {
        padding: 8,
        backgroundColor: '#FF8500',
        borderRadius: 8,
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'center',
        gap: s(10),
    },
    editButtonText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
    tabBarContainer: {
        flexDirection: 'row',
        backgroundColor: Colors.background.primary,
        borderTopWidth: Borders.width.thin,
        borderBottomWidth: Borders.width.thin,
        borderColor: Colors.border.medium,
    },
    tabItem: {
        flex: 1,
        alignItems: 'center',
        paddingVertical: Spacing.md,
    },
    activeTabItem: {
        borderBottomWidth: 2,
        borderBottomColor: Colors.secondary,
    },
    tabText: {
        color: Colors.border.medium,
        fontWeight: Typography.fontWeight.semibold,
        fontFamily: Typography.fontFamily.primary,
    },
    activeTabText: {
        color: Colors.secondary,
    },
})
