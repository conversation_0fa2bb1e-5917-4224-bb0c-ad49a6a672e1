import { useEffect, useRef, useCallback } from 'react';
import * as Notifications from 'expo-notifications';
import { registerForPushNotificationsAsync } from '../services/notificationService';
import { useNavigation } from '@react-navigation/native';
import { HomeRootStackList } from '@/app/Home/HomeNavigation';
import { NavigationProp } from '@react-navigation/native';
import { useMutation } from '@apollo/client';
import { UPDATE_DEVICE_TOKEN } from '@/app/auth/deviceToken.data';
import { useUserStore } from '@/app/auth/userStore';
import { Platform } from 'react-native';

interface NotificationProviderProps {
  children: React.ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const notificationListener = useRef<Notifications.Subscription>();
  const responseListener = useRef<Notifications.Subscription>();
  const lastNotificationResponse = Notifications.useLastNotificationResponse();
  const navigation = useNavigation<NavigationProp<HomeRootStackList>>();
  const lastHandledNotificationRef = useRef<string | null>(null);
  const { userData } = useUserStore();

  // Mutations for device token management
  const [updateDeviceToken] = useMutation(UPDATE_DEVICE_TOKEN);

  const handleDeviceToken = useCallback(async (token: string | undefined) => {
    if (!token || !userData?.id) {
      console.log('No token or user not logged in, skipping token update');
      return;
    }

    try {
      console.log('Updating device token for user:', userData.id);
      const { data } = await updateDeviceToken({
        variables: {
          input: {
            token,
            platform: Platform.OS.toUpperCase(),
            deviceId: Platform.OS === 'ios' ? 'ios' : 'android',
          },
        },
      });

      if (data?.updateDeviceToken?.status === 'SUCCESS') {
        console.log('Device token updated successfully');
      } else {
        console.error('Failed to update device token:', data?.updateDeviceToken?.message);
      }
    } catch (error) {
      console.error('Error updating device token:', error);
    }
  }, [userData?.id, updateDeviceToken]);

  useEffect(() => {
    let isSubscribed = true;

    // Register for push notifications
    registerForPushNotificationsAsync().then(async token => {
      if (token && isSubscribed) {
        console.log('Push notification token:', token);
        await handleDeviceToken(token);
      }
    });

    // Listen for incoming notifications while app is foregrounded
    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      console.log('Received notification (foreground):', {
        title: notification.request.content.title,
        body: notification.request.content.body,
        data: notification.request.content.data,
        trigger: notification.request.trigger,
        identifier: notification.request.identifier,
      });
    });

    // Listen for user interaction with notifications
    responseListener.current = Notifications.addNotificationResponseReceivedListener(async response => {
      const identifier = response.notification.request.identifier;
      const { screen, partyId } = response.notification.request.content.data || {};
      
      // Check if this notification was already handled
      if (lastHandledNotificationRef.current === identifier) {
        console.log('Notification already handled, skipping:', identifier);
        return;
      }

      if (!screen || !partyId) {
        console.error('Missing required data in notification:', { screen, partyId });
        return;
      }

      console.log('Notification clicked:', identifier);

      console.log('Notification response (foreground):', {
        actionIdentifier: response.actionIdentifier,
        notification: {
          title: response.notification.request.content.title,
          body: response.notification.request.content.body,
          data: response.notification.request.content.data,
          trigger: response.notification.request.trigger,
          identifier,
        },
      });

      try {
        await Notifications.dismissNotificationAsync(identifier);
        await Notifications.clearLastNotificationResponseAsync();
        console.log('Notification dismissed:', identifier);
      } catch (error) {
        console.error('Error dismissing notification:', error);
      }

      // Navigate to the specified screen
      navigation.navigate('PartyDetails', { 
        screen, 
        params: { partyId }
      });

      lastHandledNotificationRef.current = identifier;
    });

    // Cleanup subscriptions
    return () => {
      notificationListener.current &&
        Notifications.removeNotificationSubscription(notificationListener.current);
      responseListener.current &&
        Notifications.removeNotificationSubscription(responseListener.current);
    };
  }, [navigation, handleDeviceToken, userData]);

  // Handle background notifications
  useEffect(() => {
    const handleBackgroundNotification = async () => {
      if (!lastNotificationResponse) return;
  
      const identifier = lastNotificationResponse.notification.request.identifier;
      const { screen, partyId } = lastNotificationResponse.notification.request.content.data || {};
  
      // Check if already handled in this session
      if (lastHandledNotificationRef.current === identifier) {
        console.log('Notification already handled, skipping:', identifier);
        return;
      }

      if (!screen || !partyId) {
        console.error('Missing required data in notification:', { screen, partyId });
        return;
      }
  
      console.log('Background notification clicked:', identifier);

      console.log('Notification received in terminated/background state:', {
        actionIdentifier: lastNotificationResponse.actionIdentifier,
        notification: {
          title: lastNotificationResponse.notification.request.content.title,
          body: lastNotificationResponse.notification.request.content.body,
          data: lastNotificationResponse.notification.request.content.data,
          trigger: lastNotificationResponse.notification.request.trigger,
          identifier,
        },
      });
  
      // Immediately dismiss the notification
      try {
        await Notifications.dismissNotificationAsync(identifier);
        await Notifications.clearLastNotificationResponseAsync();
        console.log('Notification dismissed:', identifier);
      } catch (error) {
        console.error('Error dismissing notification:', error);
      }
  
      // Navigate to the specified screen
      navigation.navigate('PartyDetails', { 
        screen, 
        params: { partyId }
      });
  
      // Mark as handled (only for current session)
      lastHandledNotificationRef.current = identifier;
    };
  
    handleBackgroundNotification(); // Call the async function
  }, [lastNotificationResponse, navigation]);
  

  return <>{children}</>;
} 