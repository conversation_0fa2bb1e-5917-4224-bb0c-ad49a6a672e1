import { StyleSheet } from 'react-native'
import { Text } from 'react-native-paper'
import { ThemedView } from './UI/ThemedView'
import { AppWelcomeNames } from '@/constants/displayNames'
import { Colors, Typography, Spacing } from '@/constants/DesignSystem'

const WelcomeBanner = () => {
    return (
        <ThemedView style={styles.container}>
            <Text variant='headlineMedium' style={styles.title}>Welcome to Fast{'\u00A0'}Party</Text>
            <Text variant='bodyMedium' style={styles.tagline}>{AppWelcomeNames.PLAY_PARTY_AND_CELEBRATE_EFFORTLESSLY}</Text>
        </ThemedView>
    )
}

export default WelcomeBanner

const styles = StyleSheet.create({
    container: {
        padding: Spacing.md,
        flexDirection: 'column',
        gap: Spacing.sm,
        alignItems: 'flex-start', // Changed from 'center' to 'flex-start' to align items to the left
    },
    title: {
        fontWeight: Typography.fontWeight.bold,
        color: Colors.text.primary,
    },
    tagline: {
        color: Colors.text.secondary,
        alignSelf: 'flex-start', // Ensures the tagline aligns to the left
    }
})