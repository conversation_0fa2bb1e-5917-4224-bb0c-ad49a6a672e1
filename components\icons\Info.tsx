import * as React from "react";
import Svg, {
  G,
  <PERSON>,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Rect,
} from "react-native-svg";
import { CommonProps } from ".";

const Info = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Info-1_svg__a)">
            <Path 
              fillRule="evenodd" 
              clipRule="evenodd" 
              d="M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12ZM4.71429 8.03571C4.41842 8.03571 4.17857 8.27556 4.17857 8.57143C4.17857 8.86731 4.41842 9.10714 4.71429 9.10714H6H7.28571C7.58158 9.10714 7.82143 8.86731 7.82143 8.57143C7.82143 8.27556 7.58158 8.03571 7.28571 8.03571H6.53571V5.57143C6.53571 5.27556 6.29587 5.03571 6 5.03571H5.14286C4.84699 5.03571 4.60714 5.27556 4.60714 5.57143C4.60714 5.8673 4.84699 6.10714 5.14286 6.10714H5.46429V8.03571H4.71429ZM6.85714 3.42857C6.85714 3.90195 6.47338 4.28571 6 4.28571C5.52662 4.28571 5.14286 3.90195 5.14286 3.42857C5.14286 2.95519 5.52662 2.57143 6 2.57143C6.47338 2.57143 6.85714 2.95519 6.85714 3.42857Z" 
              fill="url(#Info-1_svg__b)"
            />
          </G>
          <Defs>
            <LinearGradient id="Info-1_svg__b" x1="6" y1="-1.44828" x2="6" y2="14.7586" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <ClipPath id="Info-1_svg__a">
                <Rect width="12" height="12" fill="white"/>
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G clipPath="url(#Info_svg__a)">
            <Path 
                d="M6.00002 11.5714C9.07707 11.5714 11.5714 9.07707 11.5714 6.00002C11.5714 2.923 9.07707 0.428589 6.00002 0.428589C2.923 0.428589 0.428589 2.923 0.428589 6.00002C0.428589 9.07707 2.923 11.5714 6.00002 11.5714Z" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
            <Path 
                d="M4.71429 8.57141H7.28572" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
            <Path 
                d="M6.00003 8.57141V5.57141H5.14288" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
            <Path 
                d="M5.99999 3.64287C5.88165 3.64287 5.78571 3.54693 5.78571 3.42858C5.78571 3.31023 5.88165 3.21429 5.99999 3.21429" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
            <Path 
                d="M6 3.64287C6.11835 3.64287 6.21429 3.54693 6.21429 3.42858C6.21429 3.31023 6.11835 3.21429 6 3.21429" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
          </G>
          <Defs>
            <ClipPath id="Info_svg__a">
                <Rect width="12" height="12" fill="white"/>
            </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  );
};
export default Info;
