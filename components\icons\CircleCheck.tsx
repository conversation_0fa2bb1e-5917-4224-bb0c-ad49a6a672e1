import * as React from "react";
import Svg, {
  G,
  <PERSON>,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Rect,
} from "react-native-svg";
import { CommonProps } from ".";

const CircleCheck = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#CircleCheck-1_svg__a)">
            <Path 
                d="M6 0.5C9.03761 0.5 11.5 2.96243 11.5 6C11.5 9.03761 9.03761 11.5 6 11.5C2.96243 11.5 0.5 9.03761 0.5 6C0.5 2.96243 2.96243 0.5 6 0.5ZM8.88281 3.03223C8.67525 2.86618 8.38411 2.87536 8.1875 3.03906L8.10938 3.11816L5.05664 6.93262L3.79199 5.9834C3.54899 5.80114 3.20374 5.85074 3.02148 6.09375C2.86241 6.30633 2.88022 6.59683 3.0498 6.78809L3.13184 6.86328L4.82422 8.13281C5.06182 8.31067 5.3985 8.2679 5.58398 8.03613L8.96875 3.80566L9.0293 3.71191C9.14557 3.48439 9.08988 3.19827 8.88281 3.03223Z" 
                fill="url(#CircleCheck-1_svg__b)"
            />
          </G>
          <Defs>
            <LinearGradient id="CircleCheck-1_svg__b" x1="6" y1="-0.827586" x2="6" y2="14.0287" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <ClipPath id="CircleCheck-1_svg__a">
              <Rect width="12" height="12" fill="white"/>
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G clipPath="url(#CircleCheck_svg__a)">
            <Path 
                d="M6 11C8.76146 11 11 8.76146 11 6C11 3.23858 8.76146 1 6 1C3.23858 1 1 3.23858 1 6C1 8.76146 3.23858 11 6 11Z" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
            <Path 
                d="M8.30771 3.69232L5.23078 7.53848L3.69232 6.38463" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
          </G>
          <Defs>
            <ClipPath id="CircleCheck_svg__a">
                <Rect width="12" height="12" fill="white"/>
            </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  );
};
export default CircleCheck;
