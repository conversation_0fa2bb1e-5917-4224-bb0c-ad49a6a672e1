import { gql } from '@apollo/client';

export const GET_IN_APP_NOTIFICATIONS = gql`
  query GetInAppNotifications($pagination: PaginationInput, $filter: InAppNotificationFilterInput) {
    getInAppNotifications(pagination: $pagination, filter: $filter) {
      ... on InAppNotificationsResponse {
        status
        message
        result {
          inAppNotifications {
            id
            user {
              id
              firstName
              lastName
              profilePicture
            }
            message
            media
            link
            read
            createdAt
            updatedAt
            sender {
              id
              firstName
              lastName
              profilePicture
            }
          }
        }
        pagination {
          totalItems
          totalPages
          pageSize
          currentPage
        }
      }
      ... on InAppNotificationErrorResponse {
        status
        message
        errors {
          field
          message
        }
      }
    }
  }
`;

export const MARK_NOTIFICATION_AS_READ = gql`
  mutation MarkNotificationAsRead($markNotificationAsReadId: ID!) {
    markNotificationAsRead(id: $markNotificationAsReadId) {
      ... on InAppNotificationResponse {
        status
        message
        result {
          inAppNotification {
            id
            user {
              id
              firstName
              lastName
              profilePicture
            }
            message
            media
            link
            read
            createdAt
            updatedAt
          }
        }
      }
      ... on InAppNotificationErrorResponse {
        status
        message
        errors {
          field
          message
        }
      }
    }
  }
`;

export const DELETE_IN_APP_NOTIFICATION = gql`
  mutation DeleteInAppNotification($deleteInAppNotificationId: ID!) {
    deleteInAppNotification(id: $deleteInAppNotificationId) {
      ... on InAppNotificationResponse {
        status
        message
        result { 
          inAppNotification {
            id 
          } 
        } 
      }
      ... on InAppNotificationErrorResponse {
        status
        message
        errors {
          field
          message
        }
      }
    }
  }
`; 