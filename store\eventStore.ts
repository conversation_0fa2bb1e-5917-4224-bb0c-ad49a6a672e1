import { create } from 'zustand';

interface EventStore {
  selectedEventId: string | null;
  setSelectedEventId: (id: string) => void;
  clearSelectedEventId: () => void;
  shouldRefreshParties: boolean;
  setShouldRefreshParties: (should: boolean) => void;
  eventName: string;
  setEventName: (name: string) => void;
}

export const useEventStore = create<EventStore>((set) => ({
  selectedEventId: null,
  setSelectedEventId: (id: string) => {
    console.log('Selected Event ID:', id);
    set({ selectedEventId: id });
  },
  clearSelectedEventId: () => set({ selectedEventId: null }),
  shouldRefreshParties: false,
  setShouldRefreshParties: (should) => set({ shouldRefreshParties: should }),
  eventName: 'Tasks',
  setEventName: (name: string) => set({ eventName: name }),
})); 