import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { useCallback, useMemo, useRef, useState, useEffect } from 'react';
import { View, Text, StyleSheet, SectionList, TextInput, TouchableOpacity, Image, Pressable } from 'react-native';
import CustomAlert from '../CustomAlert';
import AddContactsSection from '../settings/UserProfile/AddContactsSection';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { Colors, Typography, Spacing, Borders, Shadows, ComponentStyles, getButtonStyle, Icons } from '@/constants/DesignSystem';
import { Search, Cross, BackArrow } from '@/components/icons';
import { AddCircleRootStackList } from '@/app/(home)/CirclesDashboard/AddCircleRootStackList';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';

export interface Contact {
    id: string;
    invitationId?: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    emails?: Array<{ email: string }>;
    status?: 'ACCEPTED' | 'PENDING' | 'REJECTED';
    imageAvailable?: boolean;
    image?: { uri: string };
    phoneNumbers?: { number: string }[];
    phoneNumber?: string;
    phone?: string;
}

type EditMembersProps = StackScreenProps<AddCircleRootStackList, 'EditMembers'>;

const EditMembers = ({ route }: EditMembersProps) => {
    const contactSheetRef = useRef<any>(null);
    const { members = [], initialMembers = [], circleName = '', onGoBack } = route.params || {};
    const [searchText, setSearchText] = useState<string>('');
    const [showModal, setShowModal] = useState<boolean>(false);
    const [alertMessage, setAlertMessage] = useState('')
    const [selectedContacts, setSelectedContacts] = useState<Contact[]>(members);
    const [removableContactId, setRemovableContactId] = useState<string>('')
    const navigation = useNavigation<StackNavigationProp<AddCircleRootStackList>>();

    useEffect(() => {
        if (onGoBack) {
            onGoBack(selectedContacts);
        }
    }, [selectedContacts, onGoBack]);


    const sections = useMemo(() => {
        const allContacts = selectedContacts.filter((member: Contact) => {
            const name = `${member.firstName ?? ''} ${member.lastName ?? ''}`.toLowerCase();
            return name.includes(searchText.toLowerCase());
        });

        const newM = allContacts.filter(c => !(initialMembers || []).some(i => i.id === c.id));
        const existingM = allContacts.filter(c => (initialMembers || []).some(i => i.id === c.id));

        const data = [];
        if (newM.length > 0) {
            data.push({
                title: 'New Members',
                data: newM,
                isNew: true,
            });
        }
        if (existingM.length > 0) {
            data.push({
                title: 'Members',
                data: existingM,
                isNew: false,
            });
        }
        return data;

    }, [selectedContacts, initialMembers, searchText]);

    const handlePressRemoveMember = (name: string, id: string) => {
        setAlertMessage(`Do you want to remove the member "${name}" ?`)
        setShowModal(true);
        setRemovableContactId(id);
    }

    const setMembers = useCallback((contacts: Contact[]) => {
        setSelectedContacts(contacts);
        onGoBack?.(contacts);
        navigation.goBack();
    }, [onGoBack, navigation]);

    const renderContactItem = ({ item, isNew }: { item: Contact, isNew: boolean }) => {
        const initials = `${item.firstName?.[0] ?? ''}${item.lastName?.[0] ?? ''}`.toUpperCase();

        return (
            <View style={styles.contactRow}>
                <View style={[styles.contactCircle, isNew && styles.dottedBorder]}>
                    {item.imageAvailable && item.image ? (
                        <Image source={{ uri: item.image.uri }} style={styles.image} />
                    ) : (
                        <Text style={styles.monogram}>{initials}</Text>
                    )}
                </View>
                <View style={styles.nameAndNumber}>
                    <Text style={styles.contactName}>
                        {item.firstName} {item.lastName}
                    </Text>
                    <Text style={styles.contactNumber}>
                        {item.phoneNumbers?.[0]?.number ?? item?.phoneNumber ?? item?.phone}
                    </Text>
                </View>
                <View>
                    <Pressable
                        onPress={() => handlePressRemoveMember(item.firstName + (item.lastName ? ` ${item.lastName}` : ''), item.id)}
                        style={{ padding: Spacing.sm }}
                    >
                        <Cross color={Colors.text.tertiary} size={Icons.size.md} />
                    </Pressable>
                </View>
            </View>
        );
    };

    const handleAddMembers = () => {
        contactSheetRef.current?.openBottomSheet();
    }

    return (
        <BottomSheetModalProvider>
            <View style={styles.customHeader}>
                <Pressable onPress={() => {
                    onGoBack(selectedContacts)
                    navigation.goBack()
                }} style={styles.headerButton}>
                    <BackArrow size={Icons.size.md} color={Colors.text.secondary} />
                </Pressable>
                <Text style={styles.headerTitle}>Edit Members</Text>
                <View style={styles.headerButton} />
            </View>
            <View style={styles.container}>
                <View style={styles.searchContainer}>
                    <Search color={Colors.text.secondary} size={Icons.size.md} />
                    <TextInput
                        style={styles.searchInput}
                        placeholder="Find members..."
                        placeholderTextColor={Colors.text.secondary}
                        value={searchText}
                        onChangeText={setSearchText}
                    />
                </View>
                {sections.length === 0 ? (
                    <View style={styles.noMembersText}>
                        <Text style={{
                            fontFamily: Typography.fontFamily.primary,
                            fontSize: Typography.fontSize.md,
                            color: Colors.text.secondary
                        }}>No results found</Text>
                    </View>
                ) : (
                    <SectionList
                        style={{ flex: 1 }}
                        sections={sections}
                        keyExtractor={(item, index) => (item.id ?? index.toString())}
                        renderItem={({ item, section: { isNew } }) => renderContactItem({ item, isNew: isNew })}
                        renderSectionHeader={({ section: { title } }) => (
                            <Text style={styles.sectionHeader}>{title}</Text>
                        )}
                        contentContainerStyle={{ paddingBottom: 100 }}
                        keyboardShouldPersistTaps="handled"
                        showsVerticalScrollIndicator={false}
                    />
                )}
                <CustomAlert
                    visible={showModal}
                    onClose={() => setShowModal(false)}
                    onConfirm={() => {
                        setShowModal(false);
                        const updatedContacts = selectedContacts.filter(
                            (member) => member.id !== removableContactId
                        );
                        setSelectedContacts(updatedContacts);
                    }}

                    message={alertMessage}
                />

                <AddContactsSection ref={contactSheetRef} members={selectedContacts} setMembers={setMembers} />

                <View style={styles.footer}>
                    <Text style={{
                        fontWeight: Typography.fontWeight.bold,
                        fontFamily: Typography.fontFamily.primary,
                        fontSize: Typography.fontSize.md,
                        color: Colors.text.tertiary
                    }}>
                        {selectedContacts.length} {'members'}
                    </Text>
                    <Pressable onPress={handleAddMembers} style={getButtonStyle('primary')}>
                        <Text style={{
                            color: Colors.text.tertiary,
                            fontFamily: Typography.fontFamily.primary,
                            fontWeight: Typography.fontWeight.medium,
                            fontSize: Typography.fontSize.md,
                            textAlign: 'center'
                        }}>
                            {'Add New'}
                        </Text>
                    </Pressable>
                </View>
            </View>
        </BottomSheetModalProvider>
    )
}

export default EditMembers;

const styles = StyleSheet.create({
    customHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: Spacing.lg,
        paddingVertical: Spacing.md,
        borderBottomWidth: Borders.width.thin,
        borderColor: Colors.border.light,
        ...Shadows.sm,
        zIndex: 1,
        marginTop: 30,
    },
    headerTitle: {
        flex: 1,
        fontSize: Typography.fontSize.lg,
        fontWeight: Typography.fontWeight.semibold,
        fontFamily: Typography.fontFamily.primary,
        color: Colors.text.tertiary,
        textAlign: 'center',
    },
    headerButton: {
        padding: Spacing.sm,
    },
    container: {
        flex: 1,
        padding: Spacing.lg,
        backgroundColor: Colors.background.primary
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: Borders.width.thin,
        borderColor: Colors.border.light,
        borderRadius: Borders.radius.md,
        paddingHorizontal: Spacing.md,
        height: 40,
        backgroundColor: Colors.background.tertiary,
        marginBottom: Spacing.md,
    },
    searchInput: {
        flex: 1,
        height: '100%',
        fontSize: Typography.fontSize.md,
        color: Colors.text.tertiary,
        fontFamily: Typography.fontFamily.primary,
        marginLeft: Spacing.sm,
    },
    contactRow: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: Spacing.sm,
    },
    contactCircle: {
        width: 54,
        height: 54,
        borderRadius: 27,
        backgroundColor: Colors.background.tertiary,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: Spacing.md,
    },
    contactName: {
        flex: 1,
        fontSize: Typography.fontSize.lg,
        fontWeight: Typography.fontWeight.semibold,
        fontFamily: Typography.fontFamily.primary,
        color: Colors.text.secondary,
    },
    monogram: {
        color: Colors.text.secondary,
        fontWeight: Typography.fontWeight.bold,
        fontSize: Typography.fontSize.md,
        fontFamily: Typography.fontFamily.primary,
    },
    image: {
        width: 54,
        height: 54,
        borderRadius: 27,
    },
    nameAndNumber: {
        flex: 1,
        justifyContent: 'center',
        marginLeft: Spacing.md,
    },
    contactNumber: {
        fontSize: Typography.fontSize.md,
        color: Colors.text.tertiary,
        fontWeight: Typography.fontWeight.medium,
        fontFamily: Typography.fontFamily.primary,
        marginBottom: Spacing.sm,
    },
    footer: {
        position: 'absolute',
        left: 0,
        right: 0,
        bottom: 0,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingTop: Spacing.md,
        paddingBottom: Spacing.xl,
        paddingHorizontal: Spacing.lg,
        borderTopWidth: Borders.width.thin,
        borderTopColor: Colors.border.medium,
        backgroundColor: Colors.background.primary,
    },
    addNew: {
        paddingHorizontal: Spacing.xl,
        paddingVertical: Spacing.sm,
        borderRadius: Borders.radius.md,
        backgroundColor: Colors.primary,
    },
    noMembersText: {
        justifyContent: 'center',
        alignItems: 'center',
        padding: Spacing.lg,
        flex: 1,
    },
    sectionHeader: {
        fontSize: Typography.fontSize.md,
        fontWeight: Typography.fontWeight.semibold,
        fontFamily: Typography.fontFamily.primary,
        color: Colors.text.secondary,
        backgroundColor: Colors.background.primary,
        paddingTop: Spacing.md,
        paddingBottom: Spacing.sm,
    },
    dottedBorder: {
        borderStyle: 'dashed',
        borderWidth: 1,
        borderColor: Colors.primary,
    },
})