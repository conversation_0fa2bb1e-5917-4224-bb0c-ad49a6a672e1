import React, { useState, useRef } from 'react';
import { StyleSheet, Image, TouchableOpacity, LayoutRectangle, View } from 'react-native';
import { ThemedView } from '@/components/UI/ThemedView';
import { ThemedText } from '@/components/UI/ThemedText';
import { Colors } from '@/constants/Colors';
import { CalendarClock, EllipsisVertical, MapPin } from 'lucide-react-native';
import { Href, Link, router } from 'expo-router';
import { PartyOptionsModal } from './PartyOptionsModal';
import { useToast } from '../Toast';
import { useMutation } from '@apollo/client';
import { DELETE_PARTY_BY_ID } from '../party-dashboard/party-dashboard.data';
import { Dialog, Portal, Button } from 'react-native-paper';

interface PartyInfo {
  id: string;
  title: string;
  date: string;
  time: string;
  day?: string;
  location: string;
  imageSource: string;
}
interface PartyCardProps {
  partyInfo: PartyInfo;
  onDeleteSuccess: () => void;
}

export const PartyCard: React.FC<PartyCardProps> = ({
  partyInfo,
  onDeleteSuccess
}: PartyCardProps) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [iconLayout, setIconLayout] = useState<LayoutRectangle | null>(null);
  const iconRef = useRef<View | null>(null);
  const toast = useToast();
  const [deleteParty, { loading: deleteLoading, error: deleteError }] = useMutation(DELETE_PARTY_BY_ID, {
    onCompleted: (data) => {
      if (data.deleteParty.__typename === 'PartyResponse') {
        toast.success('Party deleted successfully');
        onDeleteSuccess();
        setShowDeleteDialog(false);
      } else {
        toast.error(data.deleteParty.message || 'Failed to delete party');
      }
    },
    onError: (error) => {
      toast.error(error.message || 'An error occurred while deleting the party');
    }
  });

  if (!partyInfo || Object.keys(partyInfo).length === 0) {
    return (
      <ThemedView style={styles.emptyContainer}>
        <ThemedText type="subtitle" style={styles.emptyText}>
          No parties available
        </ThemedText>
      </ThemedView>
    );
  }

  const handleEditPress = () => {
    setIsModalVisible(false);
    router.push(`/EventsDashboard/AddParty?partyId=${partyInfo.id}`);
  };

  const handleDeletePress = () => {
    setIsModalVisible(false);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    try {
      await deleteParty({
        variables: {
          deletePartyId: partyInfo.id
        }
      });
    } catch (error) {
      console.log('Delete party error:', deleteError);
      console.error('Delete party error:', error);
    } finally {
      setShowDeleteDialog(false);
    }
  };

  const handleIconPress = () => {
    iconRef.current?.measure((x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
      setIconLayout({ x: pageX, y: pageY, width, height });
      setIsModalVisible(true);
    });
  };

  return (
    <>
      <Link href={`/(tabs)/party-dashboard/${partyInfo.id}` as Href} key={partyInfo.id} asChild>
        <TouchableOpacity onPress={() => null}>
          <View style={styles.card}>
            <Image source={{ uri: partyInfo.imageSource }} style={styles.image} />
            <ThemedView style={styles.content}>
              <ThemedView style={styles.titleContainer}>
                <TouchableOpacity onPress={handleIconPress} ref={iconRef} style={styles.iconContainer}>
                  <EllipsisVertical color={Colors.light.icon} size={16} />
                </TouchableOpacity>
              </ThemedView>
              <ThemedView style={styles.secondayContent}>
                <ThemedText style={styles.title} type="subtitle" numberOfLines={2}>{partyInfo.title}</ThemedText>
                <ThemedView style={styles.row}>
                  <MapPin color={Colors.light.icon} size={18} style={[styles.icon, { top: 4 }]} />
                  <ThemedText style={styles.icon} type="default" numberOfLines={2}>{partyInfo?.location || 'Venue'}</ThemedText>
                </ThemedView>
                <ThemedView style={styles.row}>
                  <CalendarClock color={Colors.light.icon} size={18} style={[styles.icon, { top: 4 }]} />
                  <ThemedText style={styles.icon} type="default" numberOfLines={2}>{partyInfo.time}, {partyInfo.date}, {partyInfo?.day ? partyInfo.day : ''} </ThemedText>
                </ThemedView>
              </ThemedView>
            </ThemedView>
          </View>
        </TouchableOpacity>
      </Link>
      <Portal>
        <Dialog visible={showDeleteDialog} onDismiss={() => setShowDeleteDialog(false)}>
          <Dialog.Title>Delete Party</Dialog.Title>
          <Dialog.Content>
            <ThemedText type="default">
              Do you really want to delete the party "{partyInfo.title}"?
            </ThemedText>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowDeleteDialog(false)}>No</Button>
            <Button
              mode="contained"
              onPress={confirmDelete}
              loading={deleteLoading}
              disabled={deleteLoading}
            >
              Yes
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
      <PartyOptionsModal
        isVisible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        onEdit={handleEditPress}
        onDelete={handleDeletePress}
        iconLayout={iconLayout}
      />
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.light.background,
    borderRadius: 10,
    marginTop: '4%',
    flexDirection: 'row',
    shadowColor: Colors.custom.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
    position: 'relative',
    width: '100%',
  },
  image: {
    width: '40%',
    height: '100%',
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
    marginRight: '2%',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    padding: '4%',
    position: 'relative', // Allow positioning of alert container
    borderTopRightRadius: 10,
    borderBottomRightRadius: 10,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  title: {
    paddingBottom: '4%',
    flex: 1,
    color: "#705FE7",
    fontWeight: '700',
    // fontSize: 20
  },
  icon: {
    marginRight: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    paddingRight: 4
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    textAlign: 'center',
    color: Colors.light.text,
  },
  iconContainer: {
    position: 'absolute',
    right: -8,
    top: 0,
  },
  secondayContent: {
    justifyContent: 'space-between',
    width: '84%'
  }
});
