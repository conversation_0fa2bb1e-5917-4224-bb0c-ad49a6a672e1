import { StyleSheet } from 'react-native';
import { sharedStyles } from '../Shared.styles';

export const partyFieldStyles = StyleSheet.create({
  inputWrapper: {
    flex: 1,
    backgroundColor: '#fff',
    height: 45,
  },

  labelText: {
    ...sharedStyles.label,
  },

  valueText: {
    ...sharedStyles.value,
  },

  selectText: {
    ...sharedStyles.placeholder,
  },

  bottomSheetContainer: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
    alignItems: 'center',
    paddingBottom: 20,
  },

  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },

  scrollContent: {
    
  },

  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
    paddingHorizontal: 20,
  },

  listItemTitle: {
    flex: 1,
    fontSize: 16,
    marginRight: 8,
  },

  noDataText: {
    textAlign: 'center',
    padding: 16,
    color: '#999',
  },
});
