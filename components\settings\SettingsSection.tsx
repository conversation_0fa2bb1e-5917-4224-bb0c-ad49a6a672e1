import { StyleSheet, Text, View, Pressable } from 'react-native'
import React, { memo } from 'react'
import { List, Divider } from 'react-native-paper'
import { ExternalPathString, RelativePathString, router } from 'expo-router'
import { SettingsSection as SettingsSectionType } from '@/app/(home)/userProfile'
import { Colors, Icons } from '@/constants/DesignSystem'
import { Next } from '../icons'
interface SettingsSectionProps {
  section: SettingsSectionType
}

const SettingsSection = memo(({ section }: SettingsSectionProps) => {
  const handlePress = (route: RelativePathString | ExternalPathString, name: string) => {
    router.push({ pathname: route, params: { title: name } })
  }

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{section.title}</Text>
      {section.items.map((item: any, index: any) => (
        <React.Fragment key={item.id}>
          <List.Item
            title={item.name}
            left={() => (
              item.icon
            )}
            right={() => (
              <Next 
                size={Icons.size.lg} 
                color={Colors.secondary} 
              />
            )}
            onPress={() => handlePress(item.route, item.name)}
            style={styles.listItem}
          />
          {index < section.items.length - 1 && <Divider />}
        </React.Fragment>
      ))}
    </View>
  )
})

export default SettingsSection

const styles = StyleSheet.create({
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '600',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  icon: {
    marginRight: 8,
  },
  listItem: {
    paddingVertical: 12,
    paddingLeft:24
  },
})