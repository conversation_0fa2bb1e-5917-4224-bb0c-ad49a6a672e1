import { StyleSheet, View } from 'react-native'
import { Text } from 'react-native-paper'
import { Pressable, ScrollView } from 'react-native-gesture-handler'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import { Colors, Typography, Spacing, Borders, Icons } from '@/constants/DesignSystem'
import { router } from 'expo-router'
import { Image } from 'expo-image'
import { Photos, Next } from '@/components/icons'

interface Album {
  id: string
  name: string
  albumCover: string
}

interface AlbumsProps {
  albums: Album[]
  albumType: 'host' | 'guest'
}

export function Albums({ albums, albumType }: AlbumsProps) {
  const title = albumType === 'host' ? 'Host Albums' : 'Guest Albums'
  
  const redirectToAlbumView = (albumId: string) => {
    router.push({
      pathname: '/(home)/photosAlbumView/photosAlbumView',
      params: {
        albumId: albumId,
        albumType: 'ALBUM'
      }
    })
  }

  const redirectToAlbumsList = () => {
    router.push({
      pathname: '/(home)/albumsList/albumsList',
      params: { albumType: albumType }
    })
  }

  return (
    <View style={styles.container}>
      <View style={styles.sectionHeader}>
        <Text variant="titleMedium" style={styles.sectionTitle}>
          {title}
        </Text>
        <Pressable style={styles.viewAllContainer} onPress={redirectToAlbumsList}>
          <Text variant="bodyMedium" style={styles.sectionSubtitle}>View All</Text>
          <Next size={Icons.size.sm} color={Colors.mediumGray} style={styles.viewAllIcon} />
        </Pressable>
      </View>

      <GestureHandlerRootView style={styles.gestureContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          style={styles.scrollView}
        >
          {albums.map((album) => (
            <Pressable
              key={album?.id}
              onPress={() => redirectToAlbumView(album?.id)}
              style={styles.albumCard}
            >
              {album?.albumCover ? (
                <Image
                  source={album.albumCover}
                  style={styles.albumCover}
                  contentFit="cover"
                />
              ) : (
                <View style={styles.placeholderContainer}>
                  <Photos size={Icons.size.md} color={Colors.mediumGray} />
                </View>
              )}
              <View style={styles.albumInfo}>
                <Text style={styles.albumText} numberOfLines={1}>
                  {album?.name || 'Unnamed Album'}
                </Text>
              </View>
            </Pressable>
          ))}
        </ScrollView>
      </GestureHandlerRootView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginVertical: Spacing.md,
    height: 180,
  },
  gestureContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: Spacing.xs,
  },
  sectionHeader: {
    width: '99%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.semibold,
    paddingHorizontal: Spacing.xs,
    color: Colors.text.secondary,
  },
  sectionSubtitle: {
    paddingHorizontal: Spacing.xs,
    fontSize: Typography.fontSize.sm,
    color: Colors.mediumGray,
    fontWeight: Typography.fontWeight.bold,
  },
  viewAllContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllIcon: {
    marginLeft: -6,
  },
  albumCard: {
    width: 140,
    height: 140,
    marginRight: Spacing.md,
    borderRadius: Borders.radius.md,
    overflow: 'hidden',
    backgroundColor: Colors.background.tertiary,
  },
  albumCover: {
    width: '100%',
    height: '100%',
  },
  placeholderContainer: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.background.tertiary,
  },
  albumInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: Spacing.sm,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  albumText: {
    color: Colors.white,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    textAlign: 'center',
  },
})