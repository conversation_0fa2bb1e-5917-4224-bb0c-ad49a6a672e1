import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { ThemedText } from '@/components/UI/ThemedText';
import { Colors } from '@/constants/Colors';
import { EventTabType } from '@/constants/eventTabTypes';


interface EventTabsProps {
  activeTab: EventTabType;
  onTabChange: (tab: EventTabType) => void;
}

export const EventTabs: React.FC<EventTabsProps> = ({ 
  activeTab, 
  onTabChange 
}) => {
  return (
    <View style={styles.tabs}>
      <TouchableOpacity 
        style={[styles.tab, activeTab === EventTabType.UPCOMING && styles.activeTab]}
        onPress={() => onTabChange(EventTabType.UPCOMING)}
      >
        <ThemedText style={activeTab === EventTabType.UPCOMING ? styles.activeTabText : styles.tabText}>
          Upcoming
        </ThemedText>
      </TouchableOpacity>
      <TouchableOpacity 
        style={[styles.tab, activeTab === EventTabType.PAST && styles.activeTab]}
        onPress={() => onTabChange(EventTabType.PAST)}
      >
        <ThemedText style={activeTab === EventTabType.PAST ? styles.activeTabText : styles.tabText}>
          Past
        </ThemedText>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  tabs: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
    paddingHorizontal: 40,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    marginHorizontal: 20,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.custom.searchBarActive,
  },
  tabText: {
    color: Colors.light.text,
    fontSize: 16,
  },
  activeTabText: {
    color: Colors.custom.searchBarActive,
    fontWeight: '600',
    fontSize: 16,
  },
});