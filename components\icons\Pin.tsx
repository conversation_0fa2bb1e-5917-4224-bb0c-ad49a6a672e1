import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const Pin = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      fill="url(#Pin-1_svg__a)"
      d="M1.478 10.125a.954.954 0 1 0 1.456 1.233zm3.726-2.927-.728-.616-2.998 3.543.728.616.728.617 2.998-3.544z"
    />
    <Path
      fill="url(#Pin-1_svg__b)"
      stroke="url(#Pin-1_svg__c)"
      strokeWidth={0.954}
      d="M7.13 9.24 3.003 5.35a.818.818 0 0 1 .35-1.386l1.884-.502a.8.8 0 0 0 .417-.266l1.7-2.035a.82.82 0 0 1 1.17-.089l2.144 1.896c.325.287.37.777.101 1.118l-1.59 2.022a.8.8 0 0 0-.155.327L8.49 8.823a.818.818 0 0 1-1.358.417Z"
    />
    <Defs>
      <LinearGradient
        id="Pin-1_svg__a"
        x1={3.705}
        x2={3.705}
        y1={6.771}
        y2={11.556}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Pin-1_svg__b"
        x1={6.991}
        x2={5.868}
        y1={-0.731}
        y2={12.252}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Pin-1_svg__c"
        x1={6.991}
        x2={5.868}
        y1={-0.731}
        y2={12.252}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <Path
      fill={color}
      d="M1.153 10.965a.5.5 0 0 0 .743.67zm4.051-3.754-.371-.334-3.68 4.088.372.335.371.334 3.68-4.088z"
    />
    <Path
      stroke={color}
      d="m7.13 9.253-4.128-3.89a.818.818 0 0 1 .35-1.386l1.884-.502a.8.8 0 0 0 .417-.266l1.7-2.035a.82.82 0 0 1 1.17-.089l2.144 1.896c.325.287.37.777.101 1.118L9.178 6.12a.8.8 0 0 0-.155.327L8.49 8.836a.818.818 0 0 1-1.358.417Z"
    />
        </>
      )}
    </Svg>
  );
};
export default Pin;
