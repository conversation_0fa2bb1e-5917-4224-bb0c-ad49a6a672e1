import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Colors } from '@/constants/Colors';
import Animated from 'react-native-reanimated';
import { HeaderTopRow } from './HeaderTopRow';
import { HeaderInfoSection } from './HeaderInfoSection';
import { HeaderLocationRow } from './HeaderLocationRow';
import { HEADER_MAX_HEIGHT, useHeaderAnimations } from './headerAnimations';
import EventPreferencesScrollView from '../vendors/EventPreferencesScrollView';

interface PartyHeaderProps {
  title: string;
  date: string;
  time: string;
  temperature: string;
  weatherIcon: string;
  location: string;
  onBackPress: () => void;
  notificationCount?: number;
  scrollY: Animated.SharedValue<number>;
  setSelectedVendor: (vendor: string) => void;
  setActiveIndex: (index: number) => void;
  activeIndex: number;
  vendor_types: any[];
  image: string;
}

export const PartyHeader: React.FC<PartyHeaderProps> = (props) => {
  const {
    headerAnimatedStyle,
    imageAnimatedStyle,
    contentAnimatedStyle,
    scrollViewAnimatedStyle
  } = useHeaderAnimations(props.scrollY);

  return (
    <>
      <Animated.View style={[styles.container, headerAnimatedStyle]}>
        <Animated.Image
          source={{ uri: props.image }}
          style={[styles.backgroundImage, imageAnimatedStyle]}
        />
        <View style={styles.overlay}>
          <HeaderTopRow {...props} />
          <Animated.View style={contentAnimatedStyle}>
            <View style={styles.infoContainer}>
              <HeaderInfoSection
                date={props.date}
                time={props.time}
                temperature={props.temperature}
                weatherIcon={props.weatherIcon}
              />
              <HeaderLocationRow location={props.location} />
            </View>
          </Animated.View>
        </View>
      </Animated.View>
      
      <Animated.View style={[styles.scrollViewContainer, scrollViewAnimatedStyle]}>
        <EventPreferencesScrollView
          setSelectedVendor={props.setSelectedVendor}
          setActiveIndex={props.setActiveIndex}
          activeIndex={props.activeIndex}
          vendor_types={props.vendor_types}
        />
      </Animated.View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  backgroundImage: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: 16,
    paddingTop: 30,
  },
  infoContainer: {
    paddingTop: 16,
    gap: 16,
  },
  scrollViewContainer: {
    position: 'absolute',
    top: HEADER_MAX_HEIGHT - 220,
    left: 0,
    right: 0,
    backgroundColor: Colors.light.background,
    zIndex: 999,
    paddingTop: 10,
    paddingBottom: 10,
  },
});