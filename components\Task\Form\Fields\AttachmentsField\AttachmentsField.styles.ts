import { StyleSheet } from 'react-native';
import { Dimensions } from 'react-native';
import { sharedStyles } from '../Shared.styles';

const screenHeight = Dimensions.get('window').height;
export const attachmentsFieldStyles = StyleSheet.create({
  attachmentsSection: {
    marginTop: screenHeight * 0.02,
  },

  attachmentBox: {
    marginTop: 8,
    borderColor: '#5270E7',
    borderWidth: 2,
    borderRadius: 8,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#E2E7FB',
    height: screenHeight * 0.125,
  },
  fileListContainer: {
    marginTop: 20,
  },
  fileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  fileName: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#344054',
  },

  attachmentsSectionTitle: {
    ...sharedStyles.label,
  },
});