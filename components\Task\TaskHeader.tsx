import { TouchableOpacity, View } from 'react-native';
import { Button, useTheme } from 'react-native-paper';
import { Feather } from '@expo/vector-icons';
import { TaskStatus } from '@/constants/Task.constants';
import { BackArrow } from '@/components/icons';
import { Icons, Colors } from '@/constants/DesignSystem';

interface HeaderProps {
    onComplete: () => void;
    onSave: () => void;
    isLoading?: boolean;
    isCompleted: boolean;
    status?: string;
}

// Define color constants at the top of the component
const COMPLETED_COLOR = '#0B7B69';
const DEFAULT_COLOR = '#655F5F';
const COMPLETED_BG_COLOR = '#A4F4E7';

export default function TaskHeader({ onComplete, onSave, isLoading, isCompleted, status }: HeaderProps) {
    const theme = useTheme();
    
    const isStatusClosed = status === TaskStatus.CLOSED;
    
    return (
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        position: 'relative',
        padding: 16,
        paddingTop: 25, 
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.surfaceVariant,
        backgroundColor: '#ffffff',
        height: 98,
        zIndex: 1,
        overflow: 'visible',
      }}>
          <View style={{ 
              flex: 1, 
              alignItems: 'center',
              height: 45,
              marginTop: 35,
              justifyContent: 'center',
              position: 'relative',
          }}>
              {!isStatusClosed && (
                <Button
                    mode="outlined"
                    icon={isCompleted ? () => 
                      <Feather 
                          name="check" 
                          size={20}
                          color={COMPLETED_COLOR}
                          style={{
                              right: 10,
                          }}
                      /> 
                          : "check"
                    }
                    onPress={onComplete}
                    style={{ 
                        borderRadius: 20,
                        alignContent: 'center',
                        alignItems: 'center',
                        borderColor: isCompleted ? COMPLETED_COLOR : DEFAULT_COLOR,
                        width: 200,
                    }}
                    contentStyle={{
                        backgroundColor: isCompleted ? COMPLETED_BG_COLOR : 'transparent',
                        width: 198.5,
                    }}
                    textColor={isCompleted ? COMPLETED_COLOR : DEFAULT_COLOR}
                    labelStyle={{ 
                        color: isCompleted ? COMPLETED_COLOR : DEFAULT_COLOR,
                        textAlign: 'center',
                    }}
                >
                    {isCompleted ? 'Completed' : 'Mark as Complete'}
                </Button>
              )}

              <TouchableOpacity onPress={onSave} style={{ position: 'absolute', left: 0 }}>
                <BackArrow size={Icons.size.md} color={Colors.primary} />
              </TouchableOpacity>
          </View>
      </View>
    );
}