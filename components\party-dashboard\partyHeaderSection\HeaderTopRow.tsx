import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '@/constants/Colors';
import Animated from 'react-native-reanimated';
import { useTopRowAnimations } from './headerAnimations';
import AppHeaderIcons from '@/components/AppHeaderIcons';

interface HeaderTopRowProps {
  title: string;
  notificationCount?: number;
  onBackPress: () => void;
  scrollY: Animated.SharedValue<number>;
}

export const HeaderTopRow: React.FC<HeaderTopRowProps> = ({
  title,
  notificationCount = 0,
  onBackPress,
  scrollY
}) => {
  const { topRowStyle, titleStyle, iconStyle } = useTopRowAnimations(scrollY);

  return (
    <Animated.View style={[styles.topRow, topRowStyle]}>
      <TouchableOpacity onPress={onBackPress} style={styles.iconButton}>
        <Animated.Text style={iconStyle}>
          <Ionicons name="chevron-back" size={24} />
        </Animated.Text>
      </TouchableOpacity>
      <Animated.Text style={[styles.title, titleStyle]}
        numberOfLines={2}
        ellipsizeMode="tail"
      >
        {title}
      </Animated.Text>
      <View style={styles.rightIcons}>
        <AppHeaderIcons iconColor={Colors.custom.white} />
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    paddingHorizontal: 16,
  },
  rightIcons: {
    flexDirection: 'row',
  },
  iconButton: {
    padding: 8,
    paddingVertical: 16,
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: Colors.light.notificationBadge,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Colors.dark.text,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 10,
  },
});