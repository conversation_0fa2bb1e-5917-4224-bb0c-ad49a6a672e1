import gql from 'graphql-tag';

export const CREATE_GUESTS = gql`
mutation CreateGuestsFromContacts($contacts: [ContactInput!]!, $partyId: ID!) {
  createGuestsFromContacts(contacts: $contacts, partyId: $partyId) {
    ... on GuestsResponse {
      message
      result {
        guests {
          id
        }
      }
    }
    ... on GuestErrorResponse {
      errors {
        message
      }
      message
    }
  }
}
`
export const SEND_INVITE = gql`
mutation SendInvitationTo($sendInvitationToInput: SendInvitationToInput!) {
  sendInvitationTo(sendInvitationToInput: $sendInvitationToInput) {
    ... on InvitationResponse {
      message
    }
    ... on InvitationErrorResponse {
      message
    }
  }
}
`

export const DELETE_GUEST = gql`
  mutation DeleteGuest($deleteGuestId: ID!) {
  deleteGuest(id: $deleteGuestId) {
    ... on GuestResponse {
      message
    }
    ... on GuestErrorResponse {
      message
    }
  }
}
`