import 'react-native-reanimated';
import 'react-native-gesture-handler';
import React, { useEffect, useRef } from 'react';
import { View } from 'react-native';
import * as SplashScreen from 'expo-splash-screen';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { StyleSheet } from 'react-native';
import { PaperProvider, DefaultTheme } from 'react-native-paper';
import { useFonts } from 'expo-font';
import * as Notifications from 'expo-notifications';
import * as Linking from 'expo-linking';
import { Slot } from 'expo-router';

// Keep the app from closing immediately when showing a notification
SplashScreen.preventAutoHideAsync();

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

const theme = {
  ...DefaultTheme,
  fonts: {
    ...DefaultTheme.fonts,
    regular: {
      fontFamily: 'Plus Jakarta Sans',
      fontWeight: 'normal',
    },
    medium: {
      fontFamily: 'Plus Jakarta Sans',
      fontWeight: 'medium',
    },
    light: {
      fontFamily: 'Plus Jakarta Sans',
      fontWeight: 'light',
    },
    thin: {
      fontFamily: 'Plus Jakarta Sans',
      fontWeight: 'thin',
    },
  },
};

// Deep linking configuration
const linking = {
  prefixes: [Linking.createURL('/')],
  config: {
    screens: {
      '(home)': {
        screens: {
          'party-dashboard': {
            path: 'party/:partyId',
            parse: {
              partyId: (id: string) => id,
            },
          },
          'new-party': {
            path: 'new-party',
          },
          'rsvp': {
            path: 'rsvp/:partyId',
            parse: {
              partyId: (id: string) => id,
            },
          },
        },
      },
      'sign-in': 'sign-in',
      'sign-up': 'sign-up',
    },
  },
};

const App = () => {
  const [fontsLoaded] = useFonts({
    'Plus Jakarta Sans': require('./assets/fonts/PlusJakartaSans-VariableFont_wght.ttf'),
  });

  useEffect(() => {
    if (fontsLoaded) {
      // Hide the splash screen when fonts are loaded
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded]);

  if (!fontsLoaded) {
    // Show loading indication or splash screen while fonts are loading
    return <FastPartyActivityIndicator />;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <PaperProvider theme={theme}>
        <BottomSheetModalProvider>
          <Slot />
        </BottomSheetModalProvider>
      </PaperProvider>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fontText: {
    fontFamily: 'PlusJakartaSans',
    fontSize: 20,
  },
});

export default App;
