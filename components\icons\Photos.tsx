import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Photos = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Photos-1_svg__a)">
      <Path
        fill="url(#Photos-1_svg__b)"
        fillRule="evenodd"
        d="M11.5 10.321c0 .651-.528 1.179-1.179 1.179H1.68C1.028 11.5.5 10.972.5 10.321V1.68C.5 1.028 1.028.5 1.679.5h8.642c.651 0 1.179.528 1.179 1.179zm-8.054-8.25a1.375 1.375 0 1 0 0 2.75 1.375 1.375 0 0 0 0-2.75m3.655 3.614L2.464 10.32h7.857V7.18L8.113 5.6a.786.786 0 0 0-1.012.084"
        clipRule="evenodd"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Photos-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.828}
        y2={14.029}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Photos-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Photos_svg__a)"
    >
      <Path d="M1.77 11h8.46a.77.77 0 0 0 .77-.77V1.77a.77.77 0 0 0-.77-.77H1.77a.77.77 0 0 0-.77.77v8.46c0 .426.344.77.77.77" />
      <Path d="m1.962 11 5.615-5.292a.385.385 0 0 1 .492 0L11 7.808M4.077 5.23a1.154 1.154 0 1 0 0-2.307 1.154 1.154 0 0 0 0 2.308" />
    </G>
    <Defs>
      <ClipPath id="Photos_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Photos;
