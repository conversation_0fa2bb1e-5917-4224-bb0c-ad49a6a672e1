import React from 'react';
import { StyleSheet, Text, View, Image } from 'react-native';
import MapView, { <PERSON><PERSON>, MapViewProps } from 'react-native-maps';
import { LiveLocation } from './MapLiveLocations.model';
import { MapLocationProps } from '@/components/map-weather/MapLocation';

interface AndroidMapProps extends MapViewProps {
    mapViewRef: React.RefObject<MapView>;
    mapProps: MapLocationProps;
    liveLocations: LiveLocation[] | undefined;
    isWithinTime: boolean;
    onRegionChange?: () => void;
    onRegionChangeComplete?: () => void;
    styles: any; // Consider defining a more specific type later
}

const backgroundColors = ['#FCD9BD', '#DBD4EC', '#B0E5CD', '#B2E0FF', '#FCE7F6', '#FCF2B3'];

const AndroidMap: React.FC<AndroidMapProps> = ({
    mapViewRef,
    mapProps,
    liveLocations,
    isWithinTime,
    styles,
    ...rest
}) => {
    return (
        <MapView
            ref={mapViewRef}
            style={styles.map}
            {...rest}
        >
            <Marker
                coordinate={{
                    latitude: mapProps.party.latitude,
                    longitude: mapProps.party.longitude,
                }}
                anchor={{ x: 0.5, y: 1 }}
                calloutAnchor={{ x: 0.5, y: 0 }}
                title='VENUE LOCATION'
                pinColor='orange'
            >
            </Marker>
            {isWithinTime && liveLocations && liveLocations.map((location, index) => {
                const colorIndex = index % backgroundColors.length;
                const backgroundColor = backgroundColors[colorIndex];

                return (
                    <Marker
                        key={index}
                        coordinate={{ latitude: location.coordinates.latitude ?? 0.0, longitude: location.coordinates.longitude ?? 0.0 }}
                        anchor={{ x: 0.5, y: 1 }}
                        calloutAnchor={{ x: 0.5, y: 0 }}
                        title={location.guest.user.firstName}
                        description={`ETA: ${location.ETA || 'N/A'}`}
                    >
                        <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
                            {location.guest.user.profilePicture ? (
                                <Image source={{ uri: location.guest.user.profilePicture }} style={localStyles.profileImage} />
                            ) : (
                                <View style={[localStyles.noPic, { backgroundColor }]}>
                                    <Text style={localStyles.initialText}>{location.guest.user.firstName.slice(0, 2).toUpperCase()}</Text>
                                </View>
                            )}
                        </View>
                    </Marker>
                );
            })}
        </MapView>
    );
};

const localStyles = StyleSheet.create({
    profileImage: {
        width: 35,
        height: 35,
        borderRadius: 100,
    },
    noPic: {
        width: 35,
        height: 35,
        borderRadius: 100,
        alignItems: 'center',
        justifyContent: 'center',
    },
    initialText: {
        fontSize: 14,
        color: '#333',
        fontWeight: 'bold',
    }
});

export default AndroidMap; 