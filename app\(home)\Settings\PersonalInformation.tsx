import { StyleSheet } from 'react-native'
import React from 'react'
import { Colors } from '@/constants/Colors'
import { useUserStore } from '@/app/auth/userStore'
import PersonalInfoForm from '@/components/settings/UserProfile/PersonalInfoForm'
import ProfilePhotoSectionUpdated from '@/components/settings/UserProfile/ProfilePhotoSection.'
import { ThemedView } from '@/components/UI/ThemedView'
import { ScrollView } from 'react-native-gesture-handler'
import { useState } from 'react'
import { Text } from 'react-native-paper'


const PersonalInformation = () => {
  const userData = useUserStore((state) => state.userData)
  const [profileImageUrl, setProfileImageUrl] = useState<string | null | undefined>(userData?.profilePicture ? userData?.profilePicture : null)

  if (!userData) {
    return (
      <ThemedView style={[styles.container, styles.centerContent]}>
        <Text variant="titleMedium">Unable to fetch user data</Text>
        <Text variant="bodyMedium">Please try again later</Text>
      </ThemedView>
    )
  }

  return (
    <ThemedView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <ProfilePhotoSectionUpdated
          profileUrl={profileImageUrl ? profileImageUrl : null}
          setProfileImageUrl={setProfileImageUrl}
        />
        <PersonalInfoForm
          userData={{
            email: userData?.email,
            emailVerified: userData?.emailVerified,
            phone: userData?.phone,
            phoneVerified: userData?.phoneVerified,
            firstName: userData?.firstName,
            lastName: userData?.lastName,
            profilePicture: profileImageUrl,
            externalId: userData?.externalId,
            id: userData?.id,
            isActive: userData?.isActive,
            isRegistered: userData?.isRegistered
          }}
        />
      </ScrollView>
    </ThemedView>
  )
}

export default PersonalInformation

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.custom.white,
  },
  scrollContent: {
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
})