import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Check = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#check--check-form-validation-checkmark-success-add-addition-tick-1_svg__a)">
      <Path
        stroke="url(#check--check-form-validation-checkmark-success-add-addition-tick-1_svg__b)"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="m1 7.192 2.1 2.7a.77.77 0 0 0 1.2.023L11 1.808"
      />
    </G>
    <Defs>
      <LinearGradient
        id="check--check-form-validation-checkmark-success-add-addition-tick-1_svg__b"
        x1={6}
        x2={6}
        y1={0.796}
        y2={12.12}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="check--check-form-validation-checkmark-success-add-addition-tick-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <Path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="m1 7.192 2.1 2.7a.77.77 0 0 0 1.2.023L11 1.808"
    />
      )}
    </Svg>
  );
};
export default Check;
