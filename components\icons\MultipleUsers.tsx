import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const MultipleUsers = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      fill="url(#Multiple_users-1_svg__a)"
      stroke="url(#Multiple_users-1_svg__b)"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={0.909}
      d="M4.18 5.97a1.611 1.611 0 1 0 0-3.222 1.611 1.611 0 0 0 0 3.222"
    />
    <Path
      fill="url(#Multiple_users-1_svg__c)"
      stroke="url(#Multiple_users-1_svg__d)"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={0.909}
      d="M1 10.226a3.2 3.2 0 0 1 .413-1.536 3.22 3.22 0 0 1 2.769-1.62c1.135.003 2.21.63 2.769 1.62.265.469.407.997.413 1.536"
    />
    <Path
      fill="url(#Multiple_users-1_svg__e)"
      stroke="url(#Multiple_users-1_svg__f)"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={0.909}
      d="M8.272 4.6a1.381 1.381 0 1 0 0-2.761 1.381 1.381 0 0 0 0 2.762"
    />
    <Path
      fill="url(#Multiple_users-1_svg__g)"
      stroke="url(#Multiple_users-1_svg__h)"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={0.909}
      d="M10.646 7.317A2.76 2.76 0 0 0 8.273 5.93a2.74 2.74 0 0 0-1.364.372c.91.61 1.364 1.065 1.818 2.355C9.792 8.648 11 8.634 11 8.634a2.74 2.74 0 0 0-.354-1.317"
    />
    <Defs>
      <LinearGradient
        id="Multiple_users-1_svg__a"
        x1={4.181}
        x2={4.181}
        y1={2.359}
        y2={6.711}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Multiple_users-1_svg__b"
        x1={4.181}
        x2={4.181}
        y1={2.359}
        y2={6.711}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Multiple_users-1_svg__c"
        x1={4.182}
        x2={4.182}
        y1={6.69}
        y2={10.952}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Multiple_users-1_svg__d"
        x1={4.182}
        x2={4.182}
        y1={6.69}
        y2={10.952}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Multiple_users-1_svg__e"
        x1={8.272}
        x2={8.272}
        y1={1.505}
        y2={5.236}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Multiple_users-1_svg__f"
        x1={8.272}
        x2={8.272}
        y1={1.505}
        y2={5.236}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Multiple_users-1_svg__g"
        x1={8.955}
        x2={8.955}
        y1={5.601}
        y2={9.284}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Multiple_users-1_svg__h"
        x1={8.955}
        x2={8.955}
        y1={5.601}
        y2={9.284}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <Path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M4.18 5.97a1.611 1.611 0 1 0 0-3.222 1.611 1.611 0 0 0 0 3.222M1 10.226a3.2 3.2 0 0 1 .413-1.536 3.22 3.22 0 0 1 2.769-1.62c1.135.003 2.21.63 2.769 1.62.265.469.407.997.413 1.536M8.272 4.6a1.381 1.381 0 1 0 0-2.761 1.381 1.381 0 0 0 0 2.762M6.91 6.302c.409-.238.88-.371 1.363-.372.973.002 1.893.54 2.373 1.387.227.402.35.856.354 1.317l-2.727.023"
    />
        </>
      )}
    </Svg>
  );
};
export default MultipleUsers;
