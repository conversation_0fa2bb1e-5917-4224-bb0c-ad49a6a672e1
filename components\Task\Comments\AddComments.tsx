import { Platform, StyleSheet, View, ScrollView, Keyboard, TouchableOpacity } from 'react-native'
import React, { useEffect, useState } from 'react'
import { TextInput, IconButton, FAB } from 'react-native-paper'
import { CREATE_TASK_COMMENT } from './TaskComments.data'
import { useMutation } from '@apollo/client'
import { useUserStore } from '@/app/auth/userStore'
import { CollaboratorInterface } from './CollaboratorsModal'
import { useTheme } from 'react-native-paper'
import MentionsList from './MentionsList'
import { Colors } from '../../../constants/Colors'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import { Icons, Colors as ColorsDesignSystem } from '@/constants/DesignSystem';
import { Circle, Send } from '@/components/icons';

interface AddCommentsProps {
  handleShowCollaboratorsModal: () => void
  taskId?: string
  refetchComments?: () => void
  taskCollaborators?: CollaboratorInterface[]
  onCommentSent?: () => void
}

function formatMentionsForDisplay(text: string) {
  // This regex matches @FirstName LastName pattern
  const mentionRegex = /@[\w]+ [\w]+/g
  return text.replace(mentionRegex, (match) => `**${match}**`)
}

const AddComments = ({ handleShowCollaboratorsModal, taskId, refetchComments, taskCollaborators, onCommentSent }: AddCommentsProps) => {
  const [selectedCollaborators, setSelectedCollaborators] = useState<CollaboratorInterface[]>([])
  const [comment, setComment] = useState('')
  const theme = useTheme()
  const [createTaskComment] = useMutation(CREATE_TASK_COMMENT)
  const userData = useUserStore((state) => state.userData)
  const [showMentionList, setShowMentionList] = useState(false)
  const [keyboardVisible, setKeyboardVisible] = useState(false)
  const [mentionSearch, setMentionSearch] = useState('')
  const [mentionedUsers, setMentionedUsers] = useState<string[]>([])

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => setKeyboardVisible(true)
    )
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => setKeyboardVisible(false)
    )

    return () => {
      keyboardDidShowListener.remove()
      keyboardDidHideListener.remove()
    }
  }, [])

  useEffect(() => {
    if (taskCollaborators) {
      setSelectedCollaborators(taskCollaborators)
    }
  }, [taskCollaborators])

  const handleCommentChange = (text: string) => {
    setComment(text)

    // Check if any mentions were removed (backspace case)
    selectedCollaborators.forEach(collaborator => {
      const fullName = `@${collaborator?.user?.firstName} ${collaborator?.user?.lastName}`.trim()
      // If the name was in the previous comment but not in the new text
      if (comment.includes(fullName) && !text.includes(fullName)) {
        setMentionedUsers(prev => prev.filter(id => id !== collaborator.user?.id))
      }
    })

    // Check for new mentions
    const words = text.split(' ')
    const currentWord = words[words.length - 1]

    if (currentWord.startsWith('@')) {
      setShowMentionList(true)
      const searchTerm = currentWord.slice(1)
      setMentionSearch(searchTerm)

      // Check for full name matches
      const fullName = searchTerm.toLowerCase()
      const matchedCollaborator = selectedCollaborators.find(collaborator => {
        const collaboratorFullName = `${collaborator?.user?.firstName} ${collaborator?.user?.lastName}`.toLowerCase().trim()
        return collaboratorFullName === fullName
      })

      if (matchedCollaborator?.user?.id) {
        setMentionedUsers(prev => {
          if (!prev.includes(matchedCollaborator.user?.id || '')) {
            return [...prev, matchedCollaborator.user?.id || '']
          }
          return prev
        })
        setShowMentionList(false)
      }
    } else {
      setShowMentionList(false)
    }
  }

  const handleMentionSelect = (collaborator: CollaboratorInterface) => {
    const words = comment.split(' ')
    const firstName = collaborator?.user?.firstName || ''
    const lastName = collaborator?.user?.lastName || ''

    // Construct mention string with a single space at the end
    const mentionString = lastName
      ? `@${firstName} ${lastName}`
      : `@${firstName}`

    words[words.length - 1] = mentionString
    const newComment = words.join(' ') + ' '  // Add single space at the end

    setComment(newComment)
    setShowMentionList(false)

    // Only add to mentionedUsers if not already present
    if (!mentionedUsers.includes(collaborator?.user?.id || '')) {
      setMentionedUsers(prev => [...prev, collaborator?.user?.id || ''])
    }
  }

  const handleCreateTaskComment = async () => {
    const formattedComment = formatMentionsForDisplay(comment)
    await createTaskComment({
      variables: {
        taskId,
        input: {
          content: formattedComment,
          mentions: mentionedUsers?.length > 0 ? mentionedUsers : null,
          reactions: null,
        }
      }
    }).then((res) => {
      setComment('')
      setMentionedUsers([])
      refetchComments?.()
      onCommentSent?.()
      Keyboard.dismiss()
    }).catch((err) => {
      // Error handled silently
    })
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={[styles.surface]}>
        <View style={styles.inputContainer}>
          {showMentionList && (
            <MentionsList
              collaborators={selectedCollaborators || []}
              searchQuery={mentionSearch}
              onSelectMention={handleMentionSelect}
            />
          )}
          {Platform?.OS === 'android' ? (
            <ScrollView
              keyboardShouldPersistTaps="always"
              style={{ flex: 1 }}
              contentContainerStyle={{ flex: 1 }}
            >
              <TextInput
                mode='flat'
                placeholder='Add a comment'
                style={[styles.input, { minHeight: 40 }]}
                multiline
                maxLength={1000}
                dense
                value={comment}
                onChangeText={handleCommentChange}
                underlineColor="transparent"
                underlineStyle={{ height: 0 }}
                scrollEnabled={true}
                textAlignVertical="top"
                autoFocus={false}
              />
            </ScrollView>
          ) : (
            <TextInput
              mode='flat'
              placeholder='Add a comment'
              style={[styles.input, { minHeight: 40 }]}
              multiline
              maxLength={1000}
              dense
              value={comment}
              onChangeText={handleCommentChange}
              underlineColor="transparent"
              underlineStyle={{ height: 0 }}
              numberOfLines={4}
              scrollEnabled={true}
              textAlignVertical="top"
              autoFocus={false}
            />
          )}
        </View>
        <View style={[
          styles.actionRow,
        ]}>
          <View style={{ flexDirection: 'row' }}>
            <IconButton
              icon="at"
              size={20}
              style={{ marginBottom: 32 }}
              iconColor="#7f7f7f"
              onPress={() => setShowMentionList(!showMentionList)}
            />
            <TouchableOpacity onPress={handleShowCollaboratorsModal}>
              <Circle size={Icons.size.md} color={ColorsDesignSystem.text.tertiary} />
            </TouchableOpacity>
          </View>
          <View style={styles.rightActions}>
            <FAB
              icon={() => <Send size={Icons.size.md} color={ColorsDesignSystem.primary} />}
              customSize={32}
              mode='flat'
              onPress={handleCreateTaskComment}
              disabled={comment.length === 0}
              style={[
                styles.sendButton,
                {
                  backgroundColor: comment.length === 0 ? theme.colors.surfaceVariant : Colors.custom.tasksSecondaryBg,
                }
              ]}
              color={comment.length === 0 ? theme.colors.onSurfaceDisabled : 'white'}
            />
          </View>
        </View>
      </View>
    </GestureHandlerRootView>
  )
}

const styles = StyleSheet.create({
  surface: {
    flex: 1,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    backgroundColor: 'white',
    paddingHorizontal: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    zIndex: 1000,
    flex: 1,
    paddingTop: 4,
  },
  input: {
    flex: 1,
    backgroundColor: 'transparent',
    maxHeight: 100,
    marginTop: 8,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: Platform.OS === 'android' ? '20%' : 'auto',
  },
  rightActions: {
    borderLeftWidth: 1,
    borderLeftColor: 'rgba(0,0,0,0.1)',
    width: '16%',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    marginLeft: 8,
  },
  sendButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: 2,
    marginLeft: '34%'
  }
})

export default AddComments