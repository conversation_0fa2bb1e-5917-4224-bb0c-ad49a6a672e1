import { useRef, useEffect } from 'react';
import { EventSourceService } from '@/services/eventsource';
import { userStorage } from '@/app/auth/userStorage';

export function useEventStream(partyId: string, onEventReceived: (data: any) => void) {
    const eventSourceRef = useRef<EventSourceService | null>(null);

    useEffect(() => {
        const setupEventSource = async () => {
            const token = await userStorage.getToken();
            const apiUrl = `${process.env.EXPO_PUBLIC_API_URL}`;
            console.log('API URL:', apiUrl);
            const eventsUrl = `${apiUrl}/activity_events?partyId=${partyId}`;
            console.log('Connecting to event source:', token);
            eventSourceRef.current = new EventSourceService(eventsUrl, {
                Authorization: `Bearer ${token}`,
                Accept: 'text/event-stream',
                Connection: 'keep-alive',
            });
            eventSourceRef.current.connect();
            eventSourceRef.current.addEventListener('heartbeat', () => {
                console.log('Heartbeat received');
            });
            eventSourceRef.current.addEventListener('message', (event) => {
                try {
                    const data = JSON.parse(event.data);
                    console.log('Received event:', data);
                    onEventReceived(data);
                } catch (error) {
                    console.error('Error parsing event data:', error);
                }
            });
        };

        setupEventSource();

        return () => {
            if (eventSourceRef.current) {
                eventSourceRef.current.close();
            }
        };
    }, [partyId, onEventReceived]);
}