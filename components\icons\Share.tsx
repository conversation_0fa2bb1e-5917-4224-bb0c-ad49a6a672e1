import * as React from "react";
import Svg, { <PERSON>, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Share = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Share-1_svg__a)">
      <Path
        fill="url(#Share-1_svg__b)"
        stroke="url(#Share-1_svg__c)"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={0.987}
        d="M2.404 7.904a1.904 1.904 0 1 0 0-3.808 1.904 1.904 0 0 0 0 3.808"
      />
      <Path
        fill="url(#Share-1_svg__d)"
        stroke="url(#Share-1_svg__e)"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={0.987}
        d="M9.596 11.5a1.904 1.904 0 1 0 0-3.808 1.904 1.904 0 0 0 0 3.808"
      />
      <Path
        fill="url(#Share-1_svg__f)"
        stroke="url(#Share-1_svg__g)"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={0.987}
        d="M9.596 4.308a1.904 1.904 0 1 0 0-3.808 1.904 1.904 0 0 0 0 3.808"
      />
      <Path fill="url(#Share-1_svg__h)" d="m4.105 5.154 3.79-1.904Z" />
      <Path
        stroke="url(#Share-1_svg__i)"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={0.987}
        d="m4.105 5.154 3.79-1.904"
      />
      <Path fill="url(#Share-1_svg__j)" d="m4.105 6.846 3.79 1.904Z" />
      <Path
        stroke="url(#Share-1_svg__k)"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={0.987}
        d="m4.105 6.846 3.79 1.904"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Share-1_svg__b"
        x1={2.404}
        x2={2.404}
        y1={3.637}
        y2={8.779}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Share-1_svg__c"
        x1={2.404}
        x2={2.404}
        y1={3.637}
        y2={8.779}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Share-1_svg__d"
        x1={9.596}
        x2={9.596}
        y1={7.233}
        y2={12.375}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Share-1_svg__e"
        x1={9.596}
        x2={9.596}
        y1={7.233}
        y2={12.375}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Share-1_svg__f"
        x1={9.596}
        x2={9.596}
        y1={0.04}
        y2={5.183}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Share-1_svg__g"
        x1={9.596}
        x2={9.596}
        y1={0.04}
        y2={5.183}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Share-1_svg__h"
        x1={6}
        x2={6}
        y1={3.02}
        y2={5.592}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Share-1_svg__i"
        x1={6}
        x2={6}
        y1={3.02}
        y2={5.592}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Share-1_svg__j"
        x1={6}
        x2={6}
        y1={6.616}
        y2={9.188}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Share-1_svg__k"
        x1={6}
        x2={6}
        y1={6.616}
        y2={9.188}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Share-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Share_svg__a)"
    >
      <Path d="M2.73 7.73a1.73 1.73 0 1 0 0-3.46 1.73 1.73 0 0 0 0 3.46M9.27 11a1.73 1.73 0 1 0 0-3.462 1.73 1.73 0 0 0 0 3.462M9.27 4.462A1.73 1.73 0 1 0 9.27 1a1.73 1.73 0 0 0 0 3.462M4.277 5.23 7.723 3.5M4.277 6.77 7.723 8.5" />
    </G>
    <Defs>
      <ClipPath id="Share_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Share;
