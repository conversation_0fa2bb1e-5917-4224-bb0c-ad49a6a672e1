import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Hosting = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Hosting-1_svg__a)">
      <Path
        stroke="url(#Hosting-1_svg__b)"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={0.964}
        d="M10.864 5.646c.179-1.116.358-1.949-.039-3.021-.422-1.063-.482-.964-1.445-1.927"
      />
      <Path
        stroke="url(#Hosting-1_svg__c)"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={0.964}
        d="M8.208 2.225c.427.254.752.66.91 1.139.203.462.233.981.085 1.455"
      />
      <Path
        fill="url(#Hosting-1_svg__d)"
        d="m3.934 11.34-.61-1.83a.467.467 0 0 1 .442-.622H5.12a.5.5 0 0 1 .487.4l.313 1.568c.05.248-.1.488-.342.548l-1.056.264a.497.497 0 0 1-.587-.327"
        stroke="transparent"
      />
      <Path
        fill="url(#Hosting-1_svg__e)"
        d="M4.235 1.11c.634-.91 1.958-.646 2.33.341h.001l2.18 5.256.008.022c.36 1.002-.507 2.094-1.58 1.842l-2.635-.29-.006-.028-1.927-5.299-.059.02zM3.55 8.343l-.853.15c-.657.22-1.365-.111-1.668-.714L.97 7.65.308 5.922c-.26-.679.036-1.478.708-1.777l.81-.54z"
        stroke="transparent"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Hosting-1_svg__b"
        x1={9.284}
        x2={11.777}
        y1={0.068}
        y2={6.564}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Hosting-1_svg__c"
        x1={8.296}
        x2={9.639}
        y1={1.833}
        y2={5.335}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Hosting-1_svg__d"
        x1={4.539}
        x2={4.479}
        y1={8.521}
        y2={12.454}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Hosting-1_svg__e"
        x1={2.3}
        x2={6.402}
        y1={0.509}
        y2={11.202}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Hosting-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G clipPath="url(#Hosting_svg__a)">
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M10.866 5.646c.178-1.115.357-1.946-.039-3.016-.421-1.061-.481-.962-1.443-1.924M8.215 2.231c.426.253.75.658.909 1.136.202.462.232.98.084 1.453M2.533 3.732l-1.262.837c-.45.172-.674.71-.494 1.18l.662 1.725c.18.47.707.72 1.157.547l1.551-.274 3.117.345c.68.18 1.29-.519 1.044-1.203L6.132 1.642c-.245-.685-1.12-.815-1.506-.224z"
      />
      <Path
        stroke={color}
        d="m3.114 8.029.964 2.87a.59.59 0 0 0 .736.377l.585-.194a.555.555 0 0 0 .354-.69l-.697-2.363"
      />
      <Path
        fill={color}
        d="M3.685 7.693c.11.256.404.382.657.28a.487.487 0 0 0 .26-.65zM2.533 3.732l-.46.184 1.612 3.777.459-.185.459-.184-1.611-3.777z"
      />
    </G>
    <Defs>
      <ClipPath id="Hosting_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Hosting;
