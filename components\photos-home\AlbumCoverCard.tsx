import { StyleSheet, Pressable, View } from 'react-native'
import { Text } from 'react-native-paper'
import { Image } from 'expo-image'
import Animated, { FadeIn } from 'react-native-reanimated'
import { Colors, Typography, Spacing, Borders, Shadows, Icons } from '@/constants/DesignSystem'
import { Photos } from '../icons'

interface AlbumCoverCardProps {
    name: string
    albumCover: string
    onPress: () => void
}

export function AlbumCoverCard({ name, albumCover, onPress }: AlbumCoverCardProps) {
    return (
        <Pressable onPress={onPress} style={[styles.container, { borderColor: albumCover ? Colors.white : Colors.mediumGray + '40', borderWidth: Borders.width.thin, borderRadius: Borders.radius.md }]}>
            <Animated.View entering={FadeIn} style={styles.cardContainer}>
                {albumCover ? <Image
                    source={albumCover}
                    style={styles.image}
                    contentFit="cover"
                /> : <View style={[styles.image, { alignItems: 'center', marginTop: '30%' }]}>
                    <Photos size={Icons.size.lg} color={Colors.mediumGray + '40'} />
                </View>}
                <View style={styles.titleContainer}>
                    <Text variant="bodyMedium" style={styles.title} numberOfLines={1}>
                        {name}
                    </Text>
                </View>
            </Animated.View>
        </Pressable>
    )
}

const styles = StyleSheet.create({
    container: {
        width: 140,
        height: 140,
        marginRight: Spacing.md,
        marginVertical: Spacing.md,
    },
    cardContainer: {
        flex: 1,
        borderRadius: Borders.radius.md,
        overflow: 'hidden',
        ...Shadows.sm,
    },
    image: {
        width: '100%',
        height: '100%',
    },
    titleContainer: {
        position: 'absolute',
        left: Spacing.md,
        right: Spacing.md,
        bottom: Spacing.md,
        paddingHorizontal: Spacing.md,
        paddingVertical: Spacing.sm,
        backgroundColor: Colors.overlay,
        borderRadius: Borders.radius.pill,
    },
    title: {
        color: Colors.white,
        fontWeight: Typography.fontWeight.semibold,
        fontSize: Typography.fontSize.md,
        textAlign: 'center',
        fontFamily: Typography.fontFamily.primary,
    },
})
