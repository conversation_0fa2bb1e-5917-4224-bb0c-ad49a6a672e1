import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import MapLocation, { MapLocationProps } from './MapLocation';
import WeatherLocation from './WeatherLocation';
import { NewPartyDetailsResult } from '@/app/(home)/PartyDetails/NewPartyDetailsResponse.model';

interface MapWeatherProps {
  party: NewPartyDetailsResult;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 10,
  },
  componentContainer: {
    flex: 1,
  },
  spacer: {
    width: 8,
  },
  placeholderCard: {
    flex: 1,
    paddingHorizontal: 8,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%'
  },
  placeholderText: {
    fontFamily: 'Plus Jakarta Sans',
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
  },
});

const MapWeather: React.FC<MapWeatherProps> = ({ party }) => {
  const venueAddress = party.party.venueAddress;
  const coordinates = venueAddress?.coordinates;
  const addressBookLabel = party.party.addressBook?.label;
  const venueAddressString = venueAddress?.address || venueAddress?.name;

  const mapPartyData = {
    latitude: coordinates?.latitude,
    longitude: coordinates?.longitude,
    location: addressBookLabel || venueAddressString || "Venue Details Unavailable",
    partyId: party.party.id,
    partyTime: party.party.time as string,
    userRole: party.party.userRole,
  };

  const canDisplayMap =
    typeof mapPartyData.latitude === 'number' &&
    typeof mapPartyData.longitude === 'number';

  return (
    <View style={styles.container}>
      <View style={styles.componentContainer}>
        {canDisplayMap ? (
          <MapLocation party={mapPartyData as MapLocationProps['party']} />
        ) : (
          <View style={styles.placeholderCard}>
            <Text style={styles.placeholderText}>Please select location to view map</Text>
          </View>
        )}
      </View>
      <View style={styles.spacer} />
      <View style={styles.componentContainer}>
        <WeatherLocation party={party} />
      </View>
    </View>
  );
};

export default MapWeather;
