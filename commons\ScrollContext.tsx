import { createContext, useContext } from 'react';
import { SharedValue } from 'react-native-reanimated';

interface ScrollContextType {
  scrollY: SharedValue<number>;
}

export const ScrollContext = createContext<ScrollContextType | null>(null);

export function useScrollContext() {
  const context = useContext(ScrollContext);
  if (!context) {
    throw new Error('useScrollContext must be used within a ScrollProvider');
  }
  return context;
}