import { MdPromotion } from './promotions';

export enum PromotionCategory {
  SPECIAL_OFFERS = 'special-offers',
  FEATURED_PRODUCTS = 'featured-products',
  DEAL_OF_THE_DAY = 'deal-of-the-day',
  TRENDING_PRODUCTS = 'trending-products'
}

interface PromotionWithTagId extends MdPromotion {
  categoryTagId?: string; // The ID of the tag that caused this categorization
}

interface CategorizedPromotions {
  specialOffers: PromotionWithTagId[];
  featuredProducts: PromotionWithTagId[];
  dealOfTheDay: PromotionWithTagId[];
  trendingProducts: PromotionWithTagId[];
  other: PromotionWithTagId[];
}

export function processPromotions(promotions: MdPromotion[]): CategorizedPromotions {
  const categorizedPromotions: CategorizedPromotions = {
    specialOffers: [],
    featuredProducts: [],
    dealOfTheDay: [],
    trendingProducts: [],
    other: []
  };

  promotions.forEach(promotion => {
    let categorized = false;

    // Each promotion's tags are checked
    promotion.tags.forEach(tag => {
      const promotionWithTag: PromotionWithTagId = {
        ...promotion,
        categoryTagId: tag.id
      };

      switch (tag.slug) {
        case PromotionCategory.SPECIAL_OFFERS:
          if (!categorizedPromotions.specialOffers.find(p => p.id === promotion.id)) {
            categorizedPromotions.specialOffers.push(promotionWithTag);
          }
          categorized = true;
          break;
        case PromotionCategory.FEATURED_PRODUCTS:
          if (!categorizedPromotions.featuredProducts.find(p => p.id === promotion.id)) {
            categorizedPromotions.featuredProducts.push(promotionWithTag);
          }
          categorized = true;
          break;
        case PromotionCategory.DEAL_OF_THE_DAY:
          if (!categorizedPromotions.dealOfTheDay.find(p => p.id === promotion.id)) {
            categorizedPromotions.dealOfTheDay.push(promotionWithTag);
          }
          categorized = true;
          break;
        case PromotionCategory.TRENDING_PRODUCTS:
          if (!categorizedPromotions.trendingProducts.find(p => p.id === promotion.id)) {
            categorizedPromotions.trendingProducts.push(promotionWithTag);
          }
          categorized = true;
          break;
      }
    });

    // If promotion wasn't categorized by any tag, add to other
    if (!categorized && !categorizedPromotions.other.find(p => p.id === promotion.id)) {
      categorizedPromotions.other.push({ ...promotion }); // No categoryTagId for 'other'
    }
  });

  // Return categorized promotions

  return categorizedPromotions;
}

export function getCategoryTagId(promotions: PromotionWithTagId[], category: PromotionCategory): string {
  const promotion = promotions[0];
  if (!promotion) return '';

  const categoryTag = promotion.tags.find(tag => tag.slug === category);
  return categoryTag?.id || '';
}