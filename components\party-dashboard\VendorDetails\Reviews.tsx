import React from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import { Star } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';
import { ReviewCard } from './ReviewCard';
import { VendorDetails } from '@/constants/displayNames';
import { ThemedText } from '@/components/UI/ThemedText';
import { TouchableOpacity } from 'react-native';
interface Review {
  rating: number;
  comment: string;
  userId: string;
  userName: string;
  userImage: string;
  createdAt: string;
}

interface RatingAggregate {
  rating_avg: number;
  review_count: number;
}

interface ReviewsProps {
  reviews: Review[];
  ratingAggregate: RatingAggregate;
  onShowAllReviews: () => void;
}

export function Reviews({ reviews, ratingAggregate, onShowAllReviews }: ReviewsProps) {
  const displayRatingCount = (reviewCount: number) => {
    return reviewCount > 1000 ? `${(reviewCount / 1000).toFixed(1)}K` : reviewCount;
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Star color={Colors.light.text} size={24} fill={Colors.light.text} />
        <Text style={styles.ratingText}>
          {ratingAggregate.rating_avg.toFixed(1)}
        </Text>
        <Text style={styles.ratingText}>
          · {displayRatingCount(ratingAggregate.review_count)} {VendorDetails.REVIEWS}
        </Text>
      </View>
      <FlatList
        data={reviews}
        renderItem={({ item }) => <ReviewCard review={item} />}
        keyExtractor={(item) => item.userId}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.reviewList}
        getItemLayout={(data, index) => ({
          length: 300, // Initial card width
          offset: 316, // card width + marginRight
          index,
        })}
      />
      <TouchableOpacity 
        style={styles.showAllReviewsButton} 
        onPress={onShowAllReviews}
      >
        <ThemedText style={styles.showAllReviewsButtonText}>Show All Reviews</ThemedText>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
    paddingTop: 12,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  ratingText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginLeft: 8,
  },
  reviewCount: {
    fontSize: 16,
    color: Colors.light.text,
    marginLeft: 8,
  },
  reviewList: {
    padding: 16,
    alignItems: 'flex-start', // This helps with dynamic height
  },
  showAllReviewsButton: {
    marginTop: 4,
    borderWidth: 1,
    borderColor: Colors.custom.black,
    borderRadius: 8,
  },
  showAllReviewsButtonText: {
    fontSize: 16,
    // fontWeight: 'bold',
    color: Colors.custom.black,
    padding: 8,
    textAlign: 'center',
  },
});