export interface User {
  id: string;
  firstName: string;
  lastName: string;
  profilePicture?: string | null;
}

export interface InAppNotification {
  id: string;
  user: User;
  sender: User;
  message: string;
  media?: string | null;
  link: string | null;
  read: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PaginationInfo {
  totalItems: number;
  totalPages: number;
  pageSize: number;
  currentPage: number;
}

export interface InAppNotificationsResult {
  inAppNotifications: InAppNotification[];
}

export interface InAppNotificationsResponse {
  status: number;
  message: string;
  result: InAppNotificationsResult;
  pagination: PaginationInfo;
}

export interface GraphQLError {
  field: string;
  message: string;
}

export interface InAppNotificationErrorResponse {
  status: number;
  message: string;
  errors: GraphQLError[];
}

export interface InAppNotificationResult {
  inAppNotification: InAppNotification;
}

export interface InAppNotificationResponse {
  status: number;
  message: string;
  result: InAppNotificationResult;
}
export interface DateRangeFilterInput {
  start?: string | null;
  end?: string | null;
}

export interface InAppNotificationFilterInput {
  createdAt?: DateRangeFilterInput | null;
  read?: boolean | null;
}


export type GetInAppNotificationsQueryResult = 
  | { getInAppNotifications: InAppNotificationsResponse }
  | { getInAppNotifications: InAppNotificationErrorResponse };

export type MarkNotificationAsReadMutationResult = 
  | { markNotificationAsRead: InAppNotificationResponse } 
  | { markNotificationAsRead: InAppNotificationErrorResponse };

export type DeleteInAppNotificationMutationResult = 
  | { deleteInAppNotification: InAppNotificationResponse }
  | { deleteInAppNotification: InAppNotificationErrorResponse };