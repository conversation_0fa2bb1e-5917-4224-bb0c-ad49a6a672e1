import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Reaction } from '@/app/(home)/PartyDetails/NewPartyDetailsResponse.model';
import { useUserStore } from "@/app/auth/userStore";
import { useNavigation } from '@react-navigation/native';
import { NavigationProp } from '@react-navigation/native';
import { PartyDetailsRootList } from '../Navigation/PartyDetailsRootList';

interface ReactionContentProps {
  reactions: Reaction[];
  remove: (emoji: string) => void;
}

interface GroupedReactions {
  [key: string]: {
    count: number;
  };
}

const groupReactionsByType = (reactions: Reaction[]): GroupedReactions => {
  return reactions.reduce((acc: GroupedReactions, { reactionUnicode }) => {
    if (acc[reactionUnicode]) {
      acc[reactionUnicode].count += 1;
    } else {
      acc[reactionUnicode] = { count: 1 };
    }
    return acc;
  }, {});
}

const ReactionsRow: React.FC<ReactionContentProps> = ({ reactions, remove }) => {
  const [groupedReactions, setGroupedReactions] = useState<GroupedReactions>({});
  const userData = useUserStore((state) => state.userData);
  const navigation = useNavigation<NavigationProp<PartyDetailsRootList>>();

  useEffect(() => {
    const grouped = groupReactionsByType(reactions);
    setGroupedReactions(grouped);
  }, [reactions]);

  const showReactionsCard = (reactionUnicode: string) => {
    const filteredReactions = reactions.filter(
      (reaction) => reaction.reactionUnicode === reactionUnicode
    );
    navigation.navigate("ReactionsCard", { reactions: filteredReactions ?? [] });
  };


  return (
    <View style={styles.row}>
      {Object.entries(groupedReactions).map(([reactionUnicode, { count }], index) => {
        const isUserReaction = reactions.some(
          (reaction) => reaction.user.id === userData?.id && reaction.reactionUnicode === reactionUnicode
        );

        return (
          <TouchableOpacity
            key={index}
            activeOpacity={1}
            onPress={() => {
              if (isUserReaction) {
                console.log("User reaction. Can remove")
                remove(reactionUnicode)
              } else {
                console.log("Not a user reaction")
              }
            }}
            onLongPress={() => {
              showReactionsCard(reactionUnicode)
            }}
          >
            <View
              style={isUserReaction ? styles.reactionRowUser : styles.reactionRow}
            >
              <Text style={styles.icon}>{reactionUnicode}</Text>
              <Text style={styles.text}>{count}</Text>
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    flexWrap: 'wrap',
  },
  reactionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginRight: 15,
    marginBottom: 10,
    paddingHorizontal: 14,
    paddingVertical: 5,
    borderRadius: 30,
    backgroundColor: '#F4F4F4',
  },
  reactionRowUser: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginRight: 15,
    marginBottom: 10,
    paddingHorizontal: 14,
    paddingVertical: 5,
    borderRadius: 30,
    borderWidth: 1,
    borderColor: 'rgba(255, 165, 0, 1)',
    backgroundColor: 'rgba(255, 165, 0, 0.2)',
  },
  icon: {
    width: 20,
    height: 20,
    marginRight: 5,
  },
  text: {
    marginLeft: 5,
    fontFamily: 'Plus Jakarta Sans',
    fontSize: 12,
    fontWeight: '800',
    color: '#C2C2C2',
  },
});

export default ReactionsRow;