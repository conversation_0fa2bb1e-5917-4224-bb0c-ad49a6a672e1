import { useColorScheme } from 'react-native';
import { SharedValue, useAnimatedReaction, runOnJS } from 'react-native-reanimated';

const updateStatusBarStyle = (shouldChange: boolean, colorScheme: 'light' | 'dark') => {
  if (shouldChange) {
    return colorScheme === 'dark' ? 'light' : 'dark';
  }
  return colorScheme === 'dark' ? 'light' : 'dark';
};

export const useStatusBarStyle = (scrollY: SharedValue<number>) => {
  const colorScheme = useColorScheme();

  useAnimatedReaction(
    () => scrollY.value > 20,
    (shouldChange) => {
      runOnJS(updateStatusBarStyle)(shouldChange, colorScheme || 'light');
    },
    [scrollY, colorScheme]
  );

  return colorScheme === 'dark' ? 'light' : 'dark';
};