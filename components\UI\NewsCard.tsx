import { View, StyleSheet, Image, Alert, Share } from "react-native";
import { Card, Text, IconButton } from "react-native-paper";
import * as Linking from 'expo-linking';
import { useMutation } from '@apollo/client';
import {
  UPDATE_USER_ARTICLE_ACTIVITY,
  UserArticleActivityInput,
} from './NewsCardData';
import { useState, useEffect } from 'react';
import { gql } from '@apollo/client';
import { Colors, Spacing, Borders, Shadows, Typography, Icons } from '@/constants/DesignSystem';
import { Like, Dislike, Share as ShareIcon, Save } from '@/components/icons';

interface NewsCardProps {
  articleId: string;
  articleUrl: string;
  imageUrl: string;
  title: string;
  source: string;
  time: string;
  initialLiked: boolean | null;
  initialSaved: boolean | null;
}

export function NewsCard({
  articleId,
  articleUrl,
  imageUrl,
  title,
  source,
  time,
  initialLiked,
  initialSaved,
}: NewsCardProps) {

  const [isLiked, setIsLiked] = useState(initialLiked);
  const [isSaved, setIsSaved] = useState(initialSaved);

  useEffect(() => {
    setIsLiked(initialLiked);
  }, [initialLiked]);

  useEffect(() => {
    setIsSaved(initialSaved);
  }, [initialSaved]);

  const [updateActivity, { loading: mutationLoading }] = useMutation(UPDATE_USER_ARTICLE_ACTIVITY, {
    onError: () => {
      // Reset state and show error alert
      setIsLiked(initialLiked);
      setIsSaved(initialSaved);
      Alert.alert('Error', 'Could not update your reaction. Please try again.');
    },
    update(cache, { data: mutationData }) {
        // Check mutation success type (adjust type name if needed based on your schema)
        if (mutationData?.updateUserArticleActivity?.__typename !== 'UserArticleActivityResponse') {
            return;
        }
        const updatedActivity = mutationData.updateUserArticleActivity.result?.activity;
        if (!updatedActivity || !updatedActivity.id) {
            return;
        }

        const { id: activityId, liked, saved } = updatedActivity;
        const articleCacheId = cache.identify({ __typename: 'Article', id: articleId });
        const activityCacheId = cache.identify({ __typename: 'UserArticleActivity', id: activityId });

        if (!articleCacheId || !activityCacheId) {
            return;
        }

        // --- Modify Article Cache ---
        if (articleCacheId) {
            cache.modify({
                id: articleCacheId,
                fields: {
                userArticleActivity(existingActivityRef = {}) { // Field holds a reference
                    // Create a new reference or update existing one
                    // We merge liked/saved onto whatever might exist
                    const newActivityData = {
                        liked,
                        saved
                    };
                    // Read the existing referenced object to merge
                    const existingData = cache.readFragment<{
                        __typename: 'UserArticleActivity'
                        liked: boolean | null;
                        saved: boolean | null;
                        shared: boolean | null;
                        // include other fields if needed
                    }>({
                        id: cache.identify(existingActivityRef),
                        fragment: gql`
                            fragment ExistingActivityFieldsForMerge on UserArticleActivity {
                                __typename
                                liked
                                saved
                                shared
                                # include other fields needed by UI if merge logic requires them
                            }
                        `,
                    });

                    // Merge - careful not to overwrite fields not returned by this mutation (like 'shared')
                    return {
                        ...(existingData ?? {}), // Spread existing fields first
                        ...newActivityData, // Overwrite with new liked/saved
                         __typename: 'UserArticleActivity' // Ensure typename
                    };
                }
                }
            });
            // Article cache modified
        }

        // --- Modify UserArticleActivity Cache ---
        if (activityCacheId) {
            cache.modify({
                id: activityCacheId,
                fields: {
                liked() {
                    return liked; // Directly update the liked field
                },
                saved() {
                    return saved; // Directly update the saved field
                }
                // Do NOT modify articleId or userId here
                }
            });
             // UserArticleActivity cache modified
        }
    }
  });

  const handleInteraction = async (action: Partial<Pick<UserArticleActivityInput, 'liked' | 'saved' | 'shared'>>) => {
    try {
      await updateActivity({
        variables: { input: { articleId, ...action } },
      });
    } catch (error) {
      // Error already caught by mutation error handler
    }
  };

  const handleLike = () => {
    const newLikedState = isLiked === true ? null : true;
    setIsLiked(newLikedState);
    handleInteraction({ liked: newLikedState });
  };

  const handleDislike = () => {
    const newLikedState = isLiked === false ? null : false;
    setIsLiked(newLikedState);
    handleInteraction({ liked: newLikedState });
  };

  const handleSave = () => {
    const newSavedState = isSaved ? null : true;
    setIsSaved(newSavedState);
    handleInteraction({ saved: newSavedState });
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `${title}\n${articleUrl}`,
        url: articleUrl,
        title: title
      });

      handleInteraction({ shared: true });
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handlePress = async () => {
    const supported = await Linking.canOpenURL(articleUrl);

    if (supported) {
      await Linking.openURL(articleUrl);
    } else {
      Alert.alert(`Don't know how to open this URL: ${articleUrl}`);
    }
  };

  return (
    <Card style={styles.card} onPress={handlePress}>
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: imageUrl }}
          style={styles.coverImage}
          resizeMode="cover"
        />
      </View>
      <Card.Content style={styles.content}>
        <Text variant="titleMedium" style={styles.title}>{title}</Text>
        <View style={styles.metaContainer}>
          <Image
            style={styles.favicon}
            source={{ uri: `https://www.google.com/s2/favicons?domain=${source}&sz=128` }}
            accessibilityLabel={`${source} favicon`}
          />
          <Text variant="bodySmall" style={styles.metaText}>{source.replace(/^www\./, '')}</Text>
          <Text variant="bodySmall" style={styles.metaText}> • {time}</Text>
        </View>
      </Card.Content>
      <Card.Actions style={styles.actions}>
        <View style={styles.actionContainer}>
          <View style={styles.actionSubContainer}>
            <IconButton
              icon={() => isLiked ? (
                <Like size={Icons.size.lg} variant="filled" gradientStartColor={Colors.primary} gradientEndColor={Colors.gradient.orange} />
              ) : (
                <Like size={Icons.size.lg} color={Colors.secondary}/>
              )}
              onPress={handleLike}
              disabled={mutationLoading}
              hitSlop={{ top: 10, bottom: 10, left: 5, right: 5 }}
            />
            <IconButton
              icon={() => isLiked === false ? (
                <Dislike size={Icons.size.lg} variant="filled" gradientStartColor={Colors.primary} gradientEndColor={Colors.gradient.orange} />
              ) : (
                <Dislike size={Icons.size.lg} color={Colors.secondary}/>
              )}
              onPress={handleDislike}
              disabled={mutationLoading}
              hitSlop={{ top: 10, bottom: 10, left: 5, right: 5 }}
            />
            <IconButton
              icon={() => <ShareIcon size={Icons.size.lg} color={Colors.secondary} />}
              onPress={handleShare}
              disabled={mutationLoading}
              hitSlop={{ top: 10, bottom: 10, left: 5, right: 5 }}
            />
          </View>
          <IconButton
            icon={() => isSaved ? (
              <Save size={Icons.size.lg} variant="filled" gradientStartColor={Colors.primary} gradientEndColor={Colors.gradient.orange} />
            ) : (
              <Save size={Icons.size.lg} color={Colors.secondary} />
            )}
            onPress={handleSave}
            disabled={mutationLoading}
            hitSlop={{ top: 10, bottom: 10, left: 5, right: 5 }}
          />
        </View>
      </Card.Actions>
    </Card>
  );
}

const styles = StyleSheet.create({
  actionContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  actionSubContainer: {
    flex: 1,
    flexDirection: 'row'
  },
  card: {
    marginVertical: Spacing.md,
    marginHorizontal: Spacing.lg,
    borderRadius: Borders.radius.md,
    ...Shadows.md,
  },
  imageContainer: {
    width: '100%',
    height: 180,
    overflow: 'hidden',
    borderTopLeftRadius: Borders.radius.md,
    borderTopRightRadius: Borders.radius.md,
  },
  coverImage: {
    width: '100%',
    height: '100%',
  },
  content: {
    paddingTop: Spacing.md,
    paddingBottom: 0,
    backgroundColor: Colors.background.primary,
  },
  title: {
    marginBottom: Spacing.sm,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.tertiary,
    fontSize: Typography.fontSize.md,
  },
  metaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  favicon: {
    width: 16,
    height: 16,
    marginRight: Spacing.xs,
  },
  metaText: {
    color: Colors.border.dark,
    fontSize: Typography.fontSize.sm,
  },
  actions: {
    paddingTop: 0,
    flex: 1,
    width: "100%",
    backgroundColor: Colors.background.primary,
  },
});