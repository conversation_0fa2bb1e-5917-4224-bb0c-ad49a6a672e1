import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle } from 'react-native';
import * as LucideIcons from 'lucide-react-native';

interface IconButtonProps {
  iconName: keyof typeof LucideIcons;
  onPress: () => void;
  style?: ViewStyle | ViewStyle[];
}

const ImageButton: React.FC<IconButtonProps> = ({ iconName, onPress, style }) => {
  const IconComponent = LucideIcons[iconName] as React.ElementType;

  return (
    <TouchableOpacity onPress={onPress} style={[styles.button, style]} activeOpacity={1}>
      {IconComponent && (
        <IconComponent size={18} style={styles.icon} />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F4F4F4',
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 30,
    margin: 5,
  },
  icon: {
    marginRight: 4,
    color: "#949494"
  },
});

export default ImageButton;