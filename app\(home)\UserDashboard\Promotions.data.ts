import gql from 'graphql-tag';

// Single reusable query
export const GET_PROMOTIONS_BY_TAG = gql`
query GetPromotionsByTag($pagination: PaginationInput, $tagSlug: String!) {
  getMdPromotions(
    filter: { 
      active: true,
      tag: { slug: $tagSlug }
    },
    pagination: $pagination
  ) {
    ... on MdPromotionsResponse {
      result {
        mdPromotions {
          id
          name
          ctaLink
          thumbnail
          mediaUrl
          active
          partner {
            id
            name
          }
          tags {
            id
            name
            slug
          }
        }
      }
    }
  }
}`;

// Constants for tag slugs
export const PROMOTION_TAG_SLUGS = {
  FEATURED_PRODUCTS: 'featured-products',
  TRENDING_PRODUCTS: 'trending-products',
  DEAL_OF_THE_DAY: 'deal-of-the-day',
  SPECIAL_OFFERS: 'special-offers'
} as const;
export const CREATE_EVENT_IMAGES = [
  { 
    id: '1', 
    image_src: require('@/assets/images/CreateEventImage1.png')
  },
  { 
    id: '2', 
    image_src: require('@/assets/images/CreateEventImage2.png')
  },
  { 
    id: '3', 
    image_src: require('@/assets/images/CreateEventImage3.png')
  }
];
export const SAMPLE_EVENT_IMAGE = "https://images.unsplash.com/5/unsplash-kitsune-4.jpg?ixlib=rb-0.3.5&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=400&fit=max&ixid=eyJhcHBfaWQiOjEyMDd9&s=dd060fe209b4a56733a1dcc9b5aea53a";

// Keep this query if still needed elsewhere
export const GET_PROMOTION_BY_CATEGORY_ID = gql`
query GetMdPromotions($filter: MdPromotionFilterInput) {
  getMdPromotions(filter: $filter) {
    ... on MdPromotionsResponse {
      result {
        mdPromotions {
          id
          name
          ctaLink
          thumbnail
          active
          mediaUrl
          partner {
            id
            name
          }
        }
      }
    }
  }
}`;