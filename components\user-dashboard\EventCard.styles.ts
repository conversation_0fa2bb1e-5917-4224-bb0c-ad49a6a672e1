import { StyleSheet, Platform, Dimensions } from 'react-native';
import { Colors } from '@/constants/Colors';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const CARD_ASPECT_RATIO = 1.8;
const cardWidth = SCREEN_WIDTH - 40;
const cardHeight = cardWidth / CARD_ASPECT_RATIO;

export const styles = StyleSheet.create({
  cardContainer: {
    width: cardWidth,
    height: cardHeight,
    marginHorizontal: 20,
    marginBottom: 12,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: Colors.custom.black,
    ...Platform.select({
      ios: {
        shadowColor: Colors.custom.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  pressed: {
    opacity: 0.9,
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  optionsButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    zIndex: 2,
  },
  optionsIconContainer: {
    width: 28,
    height: 28,
    borderTopRightRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  gradientContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '100%',
  },
  gradient: {
    flex: 1,
    justifyContent: 'flex-end',
    padding: 12,
  },
  contentContainer: {
    paddingBottom: 6,
    paddingLeft: 10,
    gap: 3,
  },
  title: {
    color: Colors.dark.text,
    fontSize: 22,
    fontWeight: '800',
    paddingBottom: 1,
  },
  upNextContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 4,
  },
  upNextContentContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    gap: 2,
    width: '70%',
  },
  upNextText: {
    color: Colors.dark.text,
    fontSize: 13,
    marginTop: 0,
    lineHeight: 14,
  },
  partyText: {
    color: Colors.custom.eventCardSecondaryText,
    fontSize: 12,
    fontWeight: '600',
    marginTop: 0,
    lineHeight: 14,
  },
  dateText: {
    color: Colors.custom.eventCardSecondaryText,
    fontSize: 12,
    marginTop: 0,
    lineHeight: 14,
  },
  statusContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    padding:10,
    paddingHorizontal: 13,
    paddingVertical: 7,
    borderBottomRightRadius: 16,
    borderTopLeftRadius : 16
  },
  hostStatus: {
    backgroundColor: Colors.custom.hostBackground,
  },
  guestStatus: {
    backgroundColor: Colors.custom.guestBackground,
  },
  statusText: {
    fontFamily: 'PlusJakartaSans-SemiBold',
    fontSize: 12,
    lineHeight: 14.4,
    fontWeight: '600',
    color: Colors.dark.text,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginTop: 2,
  },
  locationText: {
    color: Colors.custom.eventCardSecondaryText,
    fontSize: 12,
    fontFamily: 'PlusJakartaSans-Regular',
    lineHeight: 14.4,
  },
  combinedEventInfoText: {
    color: Colors.custom.white,
    fontSize: 13,
    fontFamily: 'PlusJakartaSans-Regular',
    lineHeight: 14.4,
    flexShrink: 1,
  },
});
