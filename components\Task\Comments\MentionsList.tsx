import React from 'react';
import { View, FlatList, StyleSheet } from 'react-native';
import { Text, List, Avatar } from 'react-native-paper';
import { CollaboratorInterface } from './CollaboratorsModal';
import { Colors } from '../../../constants/Colors';

interface MentionsListProps {
  collaborators: CollaboratorInterface[];
  searchQuery: string;
  onSelectMention: (collaborator: CollaboratorInterface) => void;
}

function getInitials(firstName: string, lastName: string): string {
  const firstInitial = firstName.charAt(0).toUpperCase();
  const lastInitial = lastName ? lastName.charAt(0).toUpperCase() : '';
  return `${firstInitial}${lastInitial}`;
}

const MentionsList = ({ collaborators, searchQuery, onSelectMention }: MentionsListProps) => {
  console.log('collaborators', JSON.stringify(collaborators, null, 2))
  let filteredCollaborators: CollaboratorInterface[] = [];

  filteredCollaborators = collaborators.filter(collaborator =>
    collaborator?.user?.firstName?.toLowerCase().includes(searchQuery?.toLowerCase()) ||
    collaborator?.user?.lastName?.toLowerCase().includes(searchQuery?.toLowerCase())
  );
  if (searchQuery === '') {
    filteredCollaborators = collaborators
  }
  if (collaborators.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.message}>Please add task collaborators</Text>
      </View>
    );
  }

  if (filteredCollaborators?.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.message}>No results found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        keyboardShouldPersistTaps="always"
        keyboardDismissMode="none"
        data={filteredCollaborators}
        keyExtractor={(item) => item?.id}
        renderItem={({ item }) => (
          <List.Item
            title={`${item?.user?.firstName} ${item?.user?.lastName ? item?.user?.lastName : ''}`}
            onPress={() => onSelectMention(item)}
            left={props => <Avatar.Text
              size={28}
              label={getInitials(item?.user?.firstName || '', item?.user?.lastName || '')}
              style={styles.avatar}
              theme={{ colors: { primary: Colors.custom.tasksSecondaryBg } }}
            />}
            style={styles.listItem}
          />
        )}
        nestedScrollEnabled={true}
        scrollEnabled={true}
        style={styles.flatList}
        pointerEvents="auto"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: '100%',
    left: 0,
    right: 0,
    maxHeight: '300%',
    backgroundColor: 'white',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    zIndex: 1000,
    marginBottom: '4%',
    // iOS shadow
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    // Android shadow
    elevation: 10,
  },
  listItem: {
    paddingVertical: 4,
  },
  message: {
    padding: 16,
    textAlign: 'center',
    color: 'rgba(0,0,0,0.6)',
  },
  avatar: {
    marginLeft: 16,
  },
  flatList: {
    flex: 1,
  }
});

export default MentionsList;