import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Dimensions, StyleSheet, Text, View, TouchableOpacity, Platform } from 'react-native';
import MapView, { LatLng } from 'react-native-maps';
import { StackScreenProps } from '@react-navigation/stack';
import { useQuery } from '@apollo/client';
import { PartyDetailsRootList } from '@/components/create-event/Navigation/PartyDetailsRootList';
import { MapLocationProps } from '@/components/map-weather/MapLocation';
import { GET_LIVE_LOCATIONS } from './MapLiveLocations.data';
import { LiveLocation } from './MapLiveLocations.model';
import { useFocusEffect } from 'expo-router';
import { useNavigation } from '@react-navigation/native';
import { NavigationProp } from '@react-navigation/native';
import AndroidMap from './AndroidMap';
import IOSMap from './IOSMap';
import { Colors, Icons } from '@/constants/DesignSystem';
import { DropDown } from '@/components/icons';

const { width, height } = Dimensions.get('window');
type FullScreenMapViewProps = StackScreenProps<PartyDetailsRootList, 'FullScreenMapView'> & {
    route: {
        params: {
            mapProps: MapLocationProps;
        }
    }
};

const LiveLocationMap: React.FC<FullScreenMapViewProps> = ({ route }) => {
    const { mapProps } = route.params;
    const mapViewRef = useRef<MapView>(null);
    const [isWithinTime, setIsWithinTime] = useState(false);
    const navigation = useNavigation<NavigationProp<PartyDetailsRootList>>();

    const { loading, error, data, refetch } = useQuery(GET_LIVE_LOCATIONS, {
        variables: { partyId: mapProps.party.partyId },
    });

    const checkTime = useCallback(() => {
        const datetime = mapProps.party.partyTime;
        if (datetime) {
            const partyTime = new Date(datetime.toString()); // Convert to primitive string
            const currentTime = new Date();
            const threeHoursBefore = new Date(
                partyTime.getTime() - 3 * 60 * 60 * 1000
            );
            const eightHoursAfter = new Date(
                partyTime.getTime() + 8 * 60 * 60 * 1000
            );

            setIsWithinTime(
                currentTime >= threeHoursBefore && currentTime <= eightHoursAfter
            );
        }
    }, [mapProps.party.partyTime]);

    useEffect(() => {
        checkTime(); // Check time initially
        if (!loading && !error && data?.getLiveLocationsByPartyId?.result?.liveLocations && mapViewRef.current) {
            const venueLocation: LatLng = {
                latitude: mapProps.party.latitude,
                longitude: mapProps.party.longitude,
            };

            const liveLocationsData: LiveLocation[] = data.getLiveLocationsByPartyId.result.liveLocations;

            let allCoordinates: LatLng[] = [venueLocation];

            if (isWithinTime && liveLocationsData) {
                const guestCoordinates: LatLng[] = liveLocationsData
                    .map(location => location.coordinates)
                    .filter(coord => coord && typeof coord.latitude === 'number' && typeof coord.longitude === 'number') // Filter out invalid coords
                    .map(coord => ({ latitude: coord.latitude!, longitude: coord.longitude! })); // Ensure non-null

                allCoordinates = [...allCoordinates, ...guestCoordinates];
            }

            // Fit map to all coordinates if there are any
            if (allCoordinates.length > 0) {
                 // Add slight delay to ensure map layout is complete on initial load
                setTimeout(() => {
                    mapViewRef.current?.fitToCoordinates(allCoordinates, {
                        edgePadding: {
                            top: 50,
                            right: 50,
                            bottom: 150,
                            left: 50,
                        },
                        animated: true,
                    });
                }, 200);
            }

        }
    }, [loading, error, data, mapProps.party.latitude, mapProps.party.longitude, isWithinTime, checkTime]);

    useFocusEffect(
        useCallback(() => {
            refetch();
            checkTime(); // Re-check time when screen comes into focus
        }, [refetch, checkTime])
    )

    if (loading) return <Text>Loading...</Text>;
    if (error) return <Text>Unable to load the map. Please try again later.</Text>;

    const liveLocations: LiveLocation[] | undefined = data.getLiveLocationsByPartyId.result.liveLocations;

    const handleManageTracking = () => {
        console.log('Manage Tracking Pressed');
        navigation.navigate('SendReminders', { locations: liveLocations || [], partyId: mapProps.party.partyId });
    }

    const mapComponentProps = {
        mapViewRef,
        mapProps,
        liveLocations,
        isWithinTime,
        styles: styles,
        showsUserLocation: true,
        followsUserLocation: false,
    };

    return (
        <>
            {Platform.OS === 'ios' ? (
                <IOSMap {...mapComponentProps} />
            ) : (
                <AndroidMap {...mapComponentProps} />
            )}
            {!isWithinTime ? (
                <View style={styles.bannerStyle}>
                    <Text style={styles.bannerText}>Live tracking is enabled 3 hours before of the party.Reminders are sent by Antsy</Text>
                </View>
            ) : (
                <View style={styles.bannerStyle}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Text style={styles.bannerText}>Tracking in Progress</Text>
                    </View>
                    {
                        mapProps.party.userRole === 'MAIN_HOST' && (
                            <TouchableOpacity onPress={handleManageTracking}>
                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                    <Text style={styles.calloutText}>Manage Tracking</Text>
                                    <DropDown size={Icons.size.md} color={Colors.primary} style={{ marginLeft: 5 }} />
                                </View>
                            </TouchableOpacity>)
                    }
                </View>
            )}
        </>
    );
};

const styles = StyleSheet.create({
    map: {
        width,
        height,
    },
    markerView: {
        alignItems: 'center',
        justifyContent: 'center',
        width: 100, // Ensure the marker view has a defined width
    },
    markerText: {
        fontFamily: 'Plus Jakarta Sans',
        fontSize: 20,
        fontWeight: 'bold',
        color: 'orange',
    },
    customMarker: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 5,
    },
    profilePic: {
        width: 40,
        height: 40,
        borderRadius: 20,
        marginBottom: 5,
    },
    noPic: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: '#ddd',
        alignItems: 'center',
        justifyContent: 'center',
    },
    initial: {
        fontSize: 18,
        color: 'black',
    },
    text: {
        fontSize: 12,
        color: 'black',
        fontFamily: 'Plus Jakarta Sans',
    },
    eta: {
        backgroundColor: 'orange',
        borderRadius: 5,
        paddingHorizontal: 8,
        paddingVertical: 5,
        fontSize: 12,
        fontFamily: 'Plus Jakarta Sans',
        fontWeight: '600',
        color: 'black',
    },
    recenterButton: {
        backgroundColor: 'white',
        borderRadius: 20,
        padding: 10,
        elevation: 5, // Android
        shadowOpacity: 0.3, // iOS
        shadowRadius: 5,
        shadowColor: '#000',
        shadowOffset: { height: 3, width: 0 },
    },
    bannerStyle: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        position: 'absolute',
        width: '100%',
        bottom: 0,
        left: 0,
        right: 0,
        paddingBottom: 75,
        borderRadius: 10,
        overflow: 'hidden',
        backgroundColor: 'white',
        padding: 10,
        elevation: 5,
        shadowOpacity: 0.3,
        shadowColor: '#000',
        shadowOffset: { height: 3, width: 0 },
    },
    bannerText: {
        textAlign: 'center',
        fontSize: 16,
        fontWeight: '700',
        color: 'black',
        fontFamily: 'Plus Jakarta Sans',
    },
    calloutText: {
        textAlign: 'center',
        fontSize: 12,
        fontWeight: '500',
        color: 'black',
        fontFamily: 'Plus Jakarta Sans',
    },
});

export default LiveLocationMap;
