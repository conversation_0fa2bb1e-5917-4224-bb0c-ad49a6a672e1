import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { View, StyleSheet, ActivityIndicator, TouchableOpacity, Platform, Dimensions, NativeScrollEvent, NativeSyntheticEvent, FlatList, Pressable } from 'react-native';
import { Appbar, Button, Text, useTheme, Dialog, Portal } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useRouter, useLocalSearchParams } from 'expo-router';
import * as FileSystem from 'expo-file-system';
import { Image } from 'expo-image';
import { useVideoPlayer, VideoView } from 'expo-video';
import AnimatedDotsCarousel from 'react-native-animated-dots-carousel';
import * as Sharing from 'expo-sharing';
import { AttachmentFile } from './Attachments.types';
import { getFileTypeIcon, getMimeType } from './Attachments.utils';
import { useAttachmentPreviewStore } from './attachmentPreview.store';
import * as IntentLauncher from 'expo-intent-launcher';
import { useRoute } from '@react-navigation/native';
import { RouteProp } from '@react-navigation/native';
import { BottomSheetModal, BottomSheetBackdrop, BottomSheetView, BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { Colors, Typography, Spacing, Borders, Shadows, Icons } from '@/constants/DesignSystem';
import { useMutation, useQuery } from '@apollo/client';
import { RESTORE_MEDIA, DELETE_MEDIA_FROM_MEDIA_FOLDER, PERMANENTLY_DELETE_MEDIA, REMOVE_MEDIA_FROM_FAVORITES, GET_FAVORITE_MEDIA } from '@/app/(home)/photosAlbumView/photosAlbum.data';
import { useToast } from '@/components/Toast/useToast';
import { GET_ARCHIVES_MEDIA } from '@/app/(home)/photosAlbumView/photosAlbum.data';
import { More, Share, Download, Info, Restore, Delete, StarOff, BackArrow } from '@/components/icons';

interface AttachmentPreviewScreenProps {
  file: AttachmentFile;
  onFileChange?: (index: number, activeFile: AttachmentFile) => void;
}

const FilePreview = React.memo(({ 
  attachment,
  onDownload
}: { 
  attachment: AttachmentFile;
  onDownload: (uri: string) => void;
}) => {
  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
  const videoHeight = screenHeight * 0.4;
  const videoWidth = screenWidth * 0.9;
  const [isDownloading, setIsDownloading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [localUri, setLocalUri] = useState<string | null>(null);

  const isExistingFile = 'documentUrl' in attachment;
  const isDeviceFile = !isExistingFile && 'uri' in attachment;
  const fileUri = isExistingFile ? attachment.documentUrl : attachment.uri;
  const fileName = attachment.name;
  const fileType = isExistingFile ? attachment.documentType : attachment.mimeType;

  // Check if file exists in local storage
  useEffect(() => {
    const checkLocalFile = async () => {
      if (isDeviceFile) {
        setLocalUri(fileUri);
        return;
      }

      if (isExistingFile) {
        const filename = fileUri.split('/').pop();
        const localPath = `${FileSystem.documentDirectory}${filename}`;
        
        try {
          const fileInfo = await FileSystem.getInfoAsync(localPath);
          if (fileInfo.exists) {
            setLocalUri(localPath);
          }
        } catch (error) {
          console.error('Error checking local file:', error);
        }
      }
    };

    checkLocalFile();
  }, [isDeviceFile, isExistingFile, fileUri]);

  const isImage = fileType?.toLowerCase().includes('image') || 
    /\.(jpg|jpeg|png|gif|webp)$/i.test(fileUri);
  const isVideo = fileType?.toLowerCase().includes('video') || 
    /\.(mp4|mov|avi|mkv)$/i.test(fileUri);

  const videoPlayer = useVideoPlayer(isVideo ? fileUri : null, player => {
    player.loop = true;
  });

  const downloadFile = useCallback(async () => {
    if (localUri) return;

    setIsDownloading(true);
    setError(null);
    try {
      const filename = fileUri.split('/').pop() || fileName;
      
      if (Platform.OS === 'ios') {
        // For iOS: Download to cache but don't share automatically
        const tempUri = FileSystem.cacheDirectory + filename;
        const result = await FileSystem.downloadAsync(fileUri, tempUri);
        setLocalUri(result.uri);
      } else {
        // For Android: Keep original behavior
        const localPath = `${FileSystem.documentDirectory}${filename}`;
        const { uri } = await FileSystem.downloadAsync(fileUri, localPath);
        setLocalUri(uri);
      }
    } catch (error) {
      console.error('Error downloading file:', error);
      setError('Failed to download file. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  }, [fileUri, fileName, localUri]);

  const handleOpenDocument = useCallback(async () => {
    if (!localUri) {
      await downloadFile();
      if (!localUri) return;
    }

    try {
      if (Platform.OS === 'ios') {
        // For iOS: Use share sheet only when explicitly requested
        await Sharing.shareAsync(localUri);
      } else {
        // For Android: Enhanced content URI opening logic
        const isContentUri = localUri.startsWith('content://');
        let mimeType = isContentUri ? fileType : getMimeType(localUri);
        
        // Enhanced MIME type detection for Office documents
        if (!mimeType || mimeType === 'application/octet-stream') {
          const extension = localUri.split('.').pop()?.toLowerCase();
          switch (extension) {
            case 'pdf':
              mimeType = 'application/pdf';
              break;
            case 'doc':
              mimeType = 'application/msword';
              break;
            case 'docx':
              mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
              break;
            case 'xls':
              mimeType = 'application/vnd.ms-excel';
              break;
            case 'xlsx':
              mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
              break;
            default:
              mimeType = 'application/octet-stream';
          }
        }

        const contentUri = isContentUri ? localUri : await FileSystem.getContentUriAsync(localUri);
        
        // Add FLAG_GRANT_READ_URI_PERMISSION for better compatibility
        await IntentLauncher.startActivityAsync('android.intent.action.VIEW', {
          data: contentUri,
          flags: 1 | 1 << 27, // Adding FLAG_GRANT_READ_URI_PERMISSION
          type: mimeType
        });
      }
    } catch (error) {
      console.error('Error opening file:', error);
      setError('Failed to open file. Make sure you have an app installed that can open this type of file.');
    }
  }, [localUri, fileType, downloadFile]);

  useEffect(() => {
    if (isImage && isExistingFile) {
      // Only download the file, don't share automatically
      downloadFile();
    }
  }, [downloadFile, isExistingFile, isImage]);

  if (isImage) {
    return (
      <Image
        source={{ uri: localUri || fileUri }}
        style={styles.mediaPreview}
        contentFit="contain"
      />
    );
  }

  if (isVideo) {
    return (
      <View style={[styles.videoContainer, { width: videoWidth, height: videoHeight }]}>
        <VideoView
          style={[styles.videoPlayer, { width: videoWidth, height: videoHeight }]}
          player={videoPlayer}
          allowsFullscreen
          allowsPictureInPicture
        />
      </View>
    );
  }

  // For documents
  const { icon, color } = getFileTypeIcon(attachment);
  return (
    <View style={styles.documentPreview}>
      <MaterialCommunityIcons 
        name={icon as keyof typeof MaterialCommunityIcons.glyphMap}
        size={80}
        color={color}
      />
      <Text style={styles.fileName}>{fileName}</Text>
      {!localUri && !isDeviceFile ? (
        <Button 
          mode="contained" 
          onPress={downloadFile}
          style={styles.downloadButton}
          icon={isDownloading ? () => <ActivityIndicator size="small" color="white" /> : "download"}
          loading={isDownloading}
          disabled={isDownloading}
        >
          {isDownloading ? 'Downloading...' : 'Download'}
        </Button>
      ) : (
        <Button 
          mode="contained" 
          onPress={handleOpenDocument}
          style={styles.openButton}
          icon="open-in-app"
        >
          Open File
        </Button>
      )}
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}
    </View>
  );
});

FilePreview.displayName = 'FilePreview';

export function AttachmentPreviewScreen({ 
  file, 
  onFileChange
}: AttachmentPreviewScreenProps) {
  const theme = useTheme();
  const router = useRouter();
  const params = useLocalSearchParams<{ taskId?: string; albumType?: string; mediaId?: string }>();
  const { albumType, mediaId } = params;
  const [currentIndex, setCurrentIndex] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['25%', '35%'], []);
  const { setSelectedFile, combinedAttachmentsByTask } = useAttachmentPreviewStore();
  const [isDeleteDialogVisible, setIsDeleteDialogVisible] = useState(false);
  const [isPermanentDeleteDialogVisible, setIsPermanentDeleteDialogVisible] = useState(false);
  const [isRestoreDialogVisible, setIsRestoreDialogVisible] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [restoreMedia] = useMutation(RESTORE_MEDIA);
  const [removeMediaFromFolder] = useMutation(DELETE_MEDIA_FROM_MEDIA_FOLDER);
  const [permanentlyDeleteMedia] = useMutation(PERMANENTLY_DELETE_MEDIA);
  const { refetch: refetchDeletedPhotos } = useQuery(GET_ARCHIVES_MEDIA, { 
    fetchPolicy: 'network-only',
    skip: albumType !== 'DELETED'
  });
  const toast = useToast();
  const [isDetailsDialogVisible, setIsDetailsDialogVisible] = useState(false);
  const [isUnfavoriteDialogVisible, setIsUnfavoriteDialogVisible] = useState(false);
  const [unfavoriteMedia] = useMutation(REMOVE_MEDIA_FROM_FAVORITES);
  const { refetch: refetchFavorites } = useQuery(GET_FAVORITE_MEDIA, { 
    fetchPolicy: 'network-only',
    skip: albumType !== 'FAVORITES'
  });

  const handlePresentPress = useCallback(() => {
    bottomSheetRef.current?.present();
  }, []);

  const handleSheetChanges = useCallback((index: number) => {
    if (index === -1) {
      bottomSheetRef.current?.dismiss();
    }
  }, []);

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        pressBehavior="close"
      />
    ),
    []
  );

  const keyExtractor = useCallback((item: AttachmentFile, index: number): string => {
    // For existing files with id
    if ('id' in item && item.id) {
      return item.id;
    }
    // For files with uri (like newly uploaded files)
    if ('uri' in item && item.uri) {
      return item.uri;
    }
    // Fallback to index-based id
    return `attachment-${index}`;
  }, []);

  const downloadFile = useCallback(async (fileUri: string) => {
    try {
      const filename = fileUri.split('/').pop();
      if (!filename) throw new Error('Invalid file URI');
      
      const localPath = `${FileSystem.documentDirectory}${filename}`;
      const { uri } = await FileSystem.downloadAsync(fileUri, localPath);
      return uri;
    } catch (error) {
      console.error('Error downloading file:', error);
      throw error;
    }
  }, []);

  // Get all attachments and find initial index
  const allAttachments = useMemo(() => {
    if (!params.taskId || !combinedAttachmentsByTask[params.taskId]) return [];
    const { existing, uploading } = combinedAttachmentsByTask[params.taskId];
    return [...existing, ...uploading];
  }, [params.taskId, combinedAttachmentsByTask]);

  // Find initial index based on current file
  useEffect(() => {
    const index = allAttachments.findIndex(attachment => {
      if (file.id && 'id' in attachment && attachment.id === file.id) return true;
      if ('uri' in file && 'uri' in attachment && file.uri === attachment.uri) return true;
      return false;
    });
    if (index !== -1) {
      setCurrentIndex(index);
    }
  }, [file, allAttachments]);

  const activeFile = allAttachments[currentIndex] || file;

  const handleScroll = useCallback((event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(offsetX / containerWidth);
    if (index !== currentIndex) {
      setCurrentIndex(index);
      const newFile = allAttachments[index];
      setSelectedFile(newFile);
      onFileChange?.(index, newFile);
    }
  }, [containerWidth, currentIndex, allAttachments, setSelectedFile, onFileChange]);

  const renderItem = useCallback(({ item: attachment, index }: { item: AttachmentFile, index: number }) => {
    // Only render items that are close to the current index
    const shouldRender = Math.abs(index - currentIndex) <= 2;
    return (
      <View 
        style={[
          styles.previewSlide, 
          { width: containerWidth }
        ]}
      >
        {shouldRender && (
          <FilePreview 
            attachment={attachment}
            onDownload={downloadFile}
          />
        )}
      </View>
    );
  }, [currentIndex, containerWidth, downloadFile]);

  const getItemLayout = useCallback((data: any, index: number) => ({
    length: containerWidth,
    offset: containerWidth * index,
    index,
  }), [containerWidth]);

  // Handle initial scroll position
  useEffect(() => {
    if (containerWidth > 0 && flatListRef.current && currentIndex > 0) {
      requestAnimationFrame(() => {
        flatListRef.current?.scrollToIndex({
          index: currentIndex,
          animated: false,
          viewPosition: 0
        });
      });
    }
  }, [containerWidth, currentIndex]);

  const handleShare = async () => {
    if (!activeFile) return;

    const fileUrl = 'documentUrl' in activeFile ? activeFile.documentUrl : activeFile.uri;
    if (!fileUrl) return;
    
    try {
      setIsSharing(true);
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        toast.error("Sharing isn't available on your platform");
        return;
      }

      let shareableUri = fileUrl;
      if (Platform.OS === 'android' && fileUrl.startsWith('http')) {
        const filename = fileUrl.split('/').pop() || `shared-file-${Date.now()}`;
        const localPath = `${FileSystem.cacheDirectory}${filename}`;
        const fileInfo = await FileSystem.getInfoAsync(localPath);
        
        if (fileInfo.exists) {
          shareableUri = fileInfo.uri;
        } else {
          const { uri } = await FileSystem.downloadAsync(fileUrl, localPath);
          shareableUri = uri;
        }
      }

      await Sharing.shareAsync(shareableUri, {
        mimeType: 'documentType' in activeFile && activeFile.documentType 
          ? activeFile.documentType 
          : 'mimeType' in activeFile && activeFile.mimeType 
            ? activeFile.mimeType 
            : undefined,
        dialogTitle: 'Share Photo',
        UTI: 'public.jpeg'
      });
    } catch (error) {
      console.error('Failed to share photo:', error);
      toast.error('Failed to share photo');
    } finally {
      setIsSharing(false);
    }
  };

  const handleDownload = async () => {
    if (!activeFile) return;
    const fileUrl = 'documentUrl' in activeFile ? activeFile.documentUrl : activeFile.uri;
    if (!fileUrl) return;
    
    try {
      setIsDownloading(true);
      const filename = fileUrl.split('/').pop() || `photo_${Date.now()}.jpg`;
      const localPath = `${FileSystem.documentDirectory}${filename}`;
      const { uri } = await FileSystem.downloadAsync(fileUrl, localPath);
      await FileSystem.getContentUriAsync(uri);
      toast.success('Photo saved successfully');
    } catch (error) {
      console.error('Failed to download photo:', error);
      toast.error('Failed to download photo');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleDelete = async () => {
    if (!mediaId || !params.taskId) return;
    
    try {
      const response = await removeMediaFromFolder({
        variables: {
          mediaId: mediaId,
          mediaFolderId: params.taskId
        }
      });

      const errors = response.data?.removeMediaFromMediaFolder?.errors;
      if (errors?.length > 0) {
        const errorMessage = errors[0].message || 'Failed to delete photo';
        toast.error(errorMessage);
        return;
      }

      toast.success('Photo moved to deleted folder');
      router.back();
    } catch (error) {
      console.error('Failed to delete photo:', error);
      toast.error('Failed to delete photo');
    }
  };

  const handlePermanentDelete = async () => {
    if (!mediaId) return;
    
    try {
      const response = await permanentlyDeleteMedia({
        variables: {
          deleteMediaId: mediaId
        }
      });

      const errors = response.data?.deleteMedia?.errors;
      if (errors?.length > 0) {
        const errorMessage = errors[0].message || 'Failed to delete photo';
        toast.error(errorMessage);
        return;
      }

      toast.success('Photo permanently deleted');
      
      // Refetch the deleted photos data
      if (albumType === 'DELETED') {
        await refetchDeletedPhotos();
      }
      
      router.back();
    } catch (error) {
      console.error('Failed to delete photo:', error);
      toast.error('Failed to delete photo');
    }
  };

  const handleRestore = async () => {
    if (!mediaId) return;
    
    try {
      const response = await restoreMedia({
        variables: {
          mediaId: mediaId
        }
      });

      const errors = response.data?.restoreMediaFromArchives?.errors;
      if (errors?.length > 0) {
        const errorMessage = errors[0].message || 'Failed to restore photo';
        toast.error(errorMessage);
        return;
      }

      toast.success('Photo restored successfully');
      router.back();
    } catch (error) {
      console.error('Failed to restore photo:', error);
      toast.error('Failed to restore photo');
    }
  };

  const handleUnfavorite = async () => {
    if (!mediaId) return;
    
    try {
      const response = await unfavoriteMedia({
        variables: {
          unfavoriteMediaId: mediaId
        }
      });

      const errors = response.data?.unfavoriteMedia?.errors;
      if (errors?.length > 0) {
        const errorMessage = errors[0].message || 'Failed to remove from favorites';
        toast.error(errorMessage);
        return;
      }

      toast.success('Photo removed from favorites');
      
      // Refetch the favorites data
      if (albumType === 'FAVORITES') {
        await refetchFavorites();
      }
      
      router.back();
    } catch (error) {
      console.error('Failed to remove from favorites:', error);
      toast.error('Failed to remove from favorites');
    }
  };

  const showDeleteConfirmation = () => {
    setIsDeleteDialogVisible(true);
  };

  const hideDeleteDialog = () => {
    setIsDeleteDialogVisible(false);
  };

  const showPermanentDeleteConfirmation = () => {
    setIsPermanentDeleteDialogVisible(true);
  };

  const hidePermanentDeleteDialog = () => {
    setIsPermanentDeleteDialogVisible(false);
  };

  const showRestoreConfirmation = () => {
    setIsRestoreDialogVisible(true);
  };

  const hideRestoreDialog = () => {
    setIsRestoreDialogVisible(false);
  };

  const showUnfavoriteConfirmation = () => {
    setIsUnfavoriteDialogVisible(true);
  };

  const hideUnfavoriteDialog = () => {
    setIsUnfavoriteDialogVisible(false);
  };

  const confirmDelete = () => {
    hideDeleteDialog();
    handleDelete();
  };

  const confirmPermanentDelete = () => {
    hidePermanentDeleteDialog();
    handlePermanentDelete();
  };

  const confirmRestore = () => {
    hideRestoreDialog();
    handleRestore();
  };

  const confirmUnfavorite = () => {
    hideUnfavoriteDialog();
    handleUnfavorite();
  };

  const showDetails = useCallback(() => {
    bottomSheetRef.current?.dismiss();
    setIsDetailsDialogVisible(true);
  }, []);

  const hideDetails = useCallback(() => {
    setIsDetailsDialogVisible(false);
  }, []);

  return (
    <BottomSheetModalProvider>
      <View style={styles.container}>
        <Appbar.Header style={styles.header}>
          <View style={styles.headerLeft}>
            <TouchableOpacity 
              onPress={() => router.back()}
              style={styles.doneButtonContainer}
            >
              <BackArrow size={Icons.size.md} color={Colors.secondary} />
            </TouchableOpacity>
          </View>
          <Appbar.Content 
            title={allAttachments[currentIndex]?.name || ''} 
            style={styles.headerTitle}
            titleStyle={styles.headerTitleText}
          />
          <View style={styles.headerRight}>
            <TouchableOpacity onPress={handlePresentPress}>
              <More size={Icons.size.md} color={Colors.secondary} />
            </TouchableOpacity>
          </View>
        </Appbar.Header>

        <View 
          style={styles.previewContainer}
          onLayout={(event) => setContainerWidth(event.nativeEvent.layout.width)}
        >
          <FlatList<AttachmentFile>
            ref={flatListRef}
            data={allAttachments}
            horizontal
            pagingEnabled
            snapToInterval={containerWidth}
            snapToAlignment="center"
            decelerationRate="fast"
            showsHorizontalScrollIndicator={false}
            onScroll={handleScroll}
            scrollEventThrottle={16}
            contentContainerStyle={styles.scrollViewContent}
            removeClippedSubviews={true}
            maxToRenderPerBatch={3}
            windowSize={5}
            initialNumToRender={1}
            getItemLayout={getItemLayout}
            renderItem={renderItem}
            keyExtractor={keyExtractor}
            onScrollToIndexFailed={(info) => {
              const wait = new Promise(resolve => setTimeout(resolve, 500));
              wait.then(() => {
                flatListRef.current?.scrollToIndex({ index: info.index, animated: true });
              });
            }}
          />

          {/* Number Pagination */}
          <View style={styles.numberPaginationContainer}>
            <View style={styles.numberPaginationBox}>
              <Text style={styles.numberPaginationText}>
                {`${currentIndex + 1}/${allAttachments.length}`}
              </Text>
            </View>
          </View>

          {/* Animated Dots Carousel */}
          {allAttachments.length > 1 && (
            <View style={styles.dotsContainer}>
              <AnimatedDotsCarousel
                length={allAttachments.length}
                currentIndex={currentIndex}
                maxIndicators={4}
                interpolateOpacityAndColor
                activeIndicatorConfig={{
                  color: Colors.secondary,
                  margin: 3,
                  opacity: 1,
                  size: 8,
                }}
                inactiveIndicatorConfig={{
                  color: Colors.mediumGray,
                  margin: 3,
                  opacity: 0.5,
                  size: 8,
                }}
                decreasingDots={[
                  {
                    config: { color: Colors.mediumGray, margin: 3, opacity: 0.5, size: 6 },
                    quantity: 1
                  },
                  {
                    config: { color: Colors.text.primary, margin: 3, opacity: 0.3, size: 4 },
                    quantity: 1
                  },
                ]}
              />
            </View>
          )}
        </View>

        <BottomSheetModal
          ref={bottomSheetRef}
          index={1}
          snapPoints={snapPoints}
          onChange={handleSheetChanges}
          backdropComponent={renderBackdrop}
          handleIndicatorStyle={{ backgroundColor: Colors.border.light }}
          backgroundStyle={{ backgroundColor: Colors.background.primary }}
        >
          <BottomSheetView style={styles.bottomSheetContentContainer}>
            {albumType !== 'DELETED' && (
              <Pressable 
                style={styles.bottomSheetItem}
                onPress={() => {
                  bottomSheetRef.current?.dismiss();
                  handleShare();
                }}
              >
                <Share 
                  size={Icons.size.md} 
                  color={Colors.text.secondary} 
                />
                <Text style={styles.bottomSheetText}>Share</Text>
              </Pressable>
            )}
            <Pressable 
              style={styles.bottomSheetItem}
              onPress={() => {
                bottomSheetRef.current?.dismiss();
                showDetails();
              }}
            >
              <Info 
                size={Icons.size.md} 
                color={Colors.text.secondary} 
              />
              <Text style={styles.bottomSheetText}>Details</Text>
            </Pressable>
            {albumType === 'DELETED' && (
              <Pressable 
                style={styles.bottomSheetItem}
                onPress={() => {
                  bottomSheetRef.current?.dismiss();
                  showRestoreConfirmation();
                }}
              >
                <Restore 
                  size={Icons.size.md} 
                  color={Colors.secondary} 
                />
                <Text style={[styles.bottomSheetText, { color: Colors.secondary }]}>Restore</Text>
              </Pressable>
            )}
            {albumType === 'FAVORITES' && (
              <Pressable 
                style={styles.bottomSheetItem}
                onPress={() => {
                  bottomSheetRef.current?.dismiss();
                  showUnfavoriteConfirmation();
                }}
              >
                <StarOff 
                  size={Icons.size.md} 
                  variant='filled'
                  gradientStartColor={Colors.secondary}
                  gradientEndColor={Colors.gradient.orange}
                />
                <Text style={styles.bottomSheetText}>Remove from Favorites</Text>
              </Pressable>
            )}
            {albumType !== 'DELETED' && (
              <Pressable 
                style={styles.bottomSheetItem}
                onPress={() => {
                  bottomSheetRef.current?.dismiss();
                  handleDownload();
                }}
              >
                <Download  
                  size={Icons.size.md} 
                  color={Colors.text.secondary} 
                />
                <Text style={styles.bottomSheetText}>Download</Text>
              </Pressable>
            )}
            <Pressable 
              style={styles.bottomSheetItem}
              onPress={() => {
                bottomSheetRef.current?.dismiss();
                if (albumType === 'DELETED') {
                  showPermanentDeleteConfirmation();
                } else {
                  showDeleteConfirmation();
                }
              }}
            >
              <Delete  
                size={Icons.size.md} 
                color={Colors.error} 
              />
              <Text style={[styles.bottomSheetText, { color: Colors.error }]}>
                {albumType === 'DELETED' ? 'Delete Permanently' : 'Delete'}
              </Text>
            </Pressable>
          </BottomSheetView>
        </BottomSheetModal>

        <Portal>
          <Dialog visible={isDeleteDialogVisible} onDismiss={hideDeleteDialog}>
            <Dialog.Title>Delete Photo</Dialog.Title>
            <Dialog.Content>
              <Text>
                Are you sure you want to delete this photo? It will be moved to the deleted folder.
              </Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={hideDeleteDialog}>Cancel</Button>
              <Button onPress={confirmDelete} textColor={Colors.error}>Delete</Button>
            </Dialog.Actions>
          </Dialog>

          <Dialog visible={isPermanentDeleteDialogVisible} onDismiss={hidePermanentDeleteDialog}>
            <Dialog.Title>Delete Permanently</Dialog.Title>
            <Dialog.Content>
              <Text>
                Are you sure you want to permanently delete this photo? This action cannot be undone.
              </Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={hidePermanentDeleteDialog}>Cancel</Button>
              <Button onPress={confirmPermanentDelete} textColor={Colors.error}>Delete Permanently</Button>
            </Dialog.Actions>
          </Dialog>

          <Dialog visible={isRestoreDialogVisible} onDismiss={hideRestoreDialog}>
            <Dialog.Title>Restore Photo</Dialog.Title>
            <Dialog.Content>
              <Text>
                Are you sure you want to restore this photo? It will be moved back to its original location.
              </Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={hideRestoreDialog}>Cancel</Button>
              <Button onPress={confirmRestore} textColor={Colors.primary}>Restore</Button>
            </Dialog.Actions>
          </Dialog>

          <Dialog visible={isDetailsDialogVisible} onDismiss={hideDetails}>
            <Dialog.Title>Photo Details</Dialog.Title>
            <Dialog.Content>
              {activeFile ? (
                <View style={styles.detailsContainer}>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Name:</Text>
                    <Text style={styles.detailValue}>{activeFile.name}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Type:</Text>
                    <Text style={styles.detailValue}>
                      {'documentType' in activeFile && activeFile.documentType 
                        ? activeFile.documentType 
                        : 'mimeType' in activeFile && activeFile.mimeType 
                          ? activeFile.mimeType 
                          : 'N/A'}
                    </Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Size:</Text>
                    <Text style={styles.detailValue}>
                      {'size' in activeFile && activeFile.size ? `${(activeFile.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown'}
                    </Text>
                  </View>
                  {'owner' in activeFile && activeFile.owner ? (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Owner:</Text>
                      <Text style={styles.detailValue}>
                        {`${(activeFile.owner as any).firstName || ''} ${(activeFile.owner as any).lastName || ''}`.trim()}
                      </Text>
                    </View>
                  ) : null}
                </View>
              ) : (
                <Text>No details available.</Text>
              )}
            </Dialog.Content>
            <Dialog.Actions>
              <Button style={{borderWidth: 1, borderColor: Colors.mediumGray, borderRadius: Borders.radius.lg}} onPress={hideDetails}><Text style={{color: Colors.text.secondary}}>Close</Text></Button>
            </Dialog.Actions>
          </Dialog>

          <Dialog visible={isUnfavoriteDialogVisible} onDismiss={hideUnfavoriteDialog}>
            <Dialog.Title>Remove from Favorites</Dialog.Title>
            <Dialog.Content>
              <Text>
                Are you sure you want to remove this photo from your favorites?
              </Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={hideUnfavoriteDialog}>Cancel</Button>
              <Button onPress={confirmUnfavorite}>Remove</Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>
      </View>
    </BottomSheetModalProvider>
  );
} 
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    elevation: 0,
    backgroundColor: Colors.background.primary,
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: Borders.width.thin,
    borderBottomColor: Colors.border.light,
  },
  headerLeft: {
    width: 50,
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingLeft: Spacing.md,
  },
  headerRight: {
    width: 50,
    justifyContent: 'center',
    alignItems: 'flex-end',
    paddingRight: Spacing.md,
  },
  headerTitle: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitleText: {
    textAlign: 'center',
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.primary,
  },
  doneButton: {
    color: Colors.primary,
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.medium,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
  },
  doneButtonContainer: {
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  previewContainer: {
    flex: 1,
    backgroundColor: Colors.background.transparent,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  previewSlide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
  },
  mediaPreview: {
    width: '100%',
    height: '80%',
    borderRadius: Borders.radius.md,
    backgroundColor: Colors.background.transparent,
  },
  videoContainer: {
    height: '80%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    borderRadius: Borders.radius.md,
    overflow: 'hidden',
    alignSelf: 'center',
    marginVertical: Spacing.lg,
  },
  videoPlayer: {
    width: '100%',
    height: '70%',
    backgroundColor: Colors.background.primary,
  },
  documentPreview: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
    width: '100%',
    maxWidth: 400,
  },
  fileName: {
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.primary,
    textAlign: 'center',
    color: Colors.text.primary,
  },
  downloadButton: {
    marginTop: Spacing.md,
  },
  numberPaginationContainer: {
    position: 'absolute',
    bottom: Spacing.lg,
    width: '100%',
    alignItems: 'flex-end',
    paddingRight: '4%',
  },
  numberPaginationBox: {
    backgroundColor: Colors.background.tertiary,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: Borders.radius.sm,
  },
  numberPaginationText: {
    color: Colors.text.secondary,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
    fontFamily: Typography.fontFamily.primary,
  },
  dotsContainer: {
    position: 'absolute',
    bottom: Spacing.lg,
    width: '100%',
    alignItems: 'center',
  },
  downloadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.md,
  },
  downloadingText: {
    marginLeft: Spacing.sm,
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
  openButton: {
    marginTop: Spacing.md,
  },
  errorText: {
    color: Colors.error,
    marginTop: Spacing.sm,
    textAlign: 'center',
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.sm,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: Spacing.sm,
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  retryButton: {
    marginTop: Spacing.sm,
  },
  bottomSheetContentContainer: {
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.md,
    paddingBottom: Spacing.xl,
  },
  bottomSheetItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.md,
  },
  bottomSheetIcon: {
    marginRight: Spacing.md,
  },
  bottomSheetText: {
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.lg,
    color: Colors.text.secondary,
    marginLeft: Spacing.md,
  },
  detailsContainer: {
    paddingVertical: Spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: Spacing.sm,
    alignItems: 'center',
  },
  detailLabel: {
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.md,
    color: Colors.text.secondary,
    width: 80,
  },
  detailValue: {
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.md,
    color: Colors.text.secondary,
    flex: 1,
  },
});