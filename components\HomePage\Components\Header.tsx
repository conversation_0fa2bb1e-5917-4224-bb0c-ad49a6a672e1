import React from 'react';
import { styles } from '@/components/HomePage/homepage.styles';
import { View, Image } from 'react-native';
import AppHeaderIcons from '@/components/AppHeaderIcons';

export function Header() {

  return (
    <View>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Image 
            source={require('@/assets/images/HomePageDefaultImages/Logo 4.png')}
            style={styles.headerLogo}
            resizeMode="cover"
          />
        </View>
        <View style={styles.headerRight}>
          <AppHeaderIcons/>
        </View>
      </View>
    </View>
  );
}