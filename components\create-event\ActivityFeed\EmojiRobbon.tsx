import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet, Text, ScrollView } from 'react-native';
import Popover from 'react-native-popover-view';
import { Platform } from 'react-native';

interface EmojiPopoverProps {
  isVisible: boolean;
  onEmojiSelected: (emoji: string) => void;
  remove: (emoji: string) => void;
  onClose: () => void;
  fromView: React.RefObject<View>;
  selectedEmoji: string | null;
}

const EmojiPopover: React.FC<EmojiPopoverProps> = ({
  isVisible,
  onEmojiSelected,
  remove,
  onClose,
  fromView,
  selectedEmoji,
}) => {
  const emojis = ['👍', '❤️', '👌', '🥳', '🥲', '🤩', '🔥', '🥂', '😁', '😋'];
  const [selected, setSelectedEmoji] = useState(selectedEmoji);

  return (
    <Popover
      popoverStyle={styles.popover}
      from={fromView}
      isVisible={isVisible}
      onRequestClose={onClose}
      arrowShift={Platform.OS === 'ios' ? 10 : 0}
      arrowSize={{ width: 0, height: 0 }}
    >
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.emojiContainer}
      >
        {emojis.map((emoji, index) => {
          const isSelectedEmoji = emoji === selectedEmoji;
          return (
            <TouchableOpacity
              key={index}
              onPress={() => {
                console.log('Selected Emoji:', selectedEmoji);
                console.log('Normal Emoji:', emoji);

                if (selectedEmoji === emoji) {
                  // If the selected emoji matches the current emoji, remove it
                  console.log('Removing the selected emoji');
                  remove(emoji);
                } else {
                  // If it's a different emoji, select it
                  console.log('New emoji selected');
                  onEmojiSelected(emoji);
                }
              }}
              style={isSelectedEmoji ? styles.selectedEmojiContainer : undefined}
            >
              <Text style={isSelectedEmoji ? styles.selectedEmoji : styles.emoji}>
                {emoji}
              </Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </Popover>
  );
};

const styles = StyleSheet.create({
  popover: {
    borderRadius: 37.5,
    paddingHorizontal: 20,
    paddingVertical: 10,
    zIndex: 1000,
  },
  emojiContainer: {
    flexDirection: 'row', // Horizontal scrolling
    alignItems: 'center',
    paddingVertical: 5,
  },
  emoji: {
    fontSize: 22,
    marginHorizontal: 4,
    padding: 5,
  },
  selectedEmoji: {
    fontSize: 22,
    marginHorizontal: 4,
    padding: 5,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 165, 0, 0.5)',
  },
  selectedEmojiContainer: {
    borderRadius: 20,
    padding: 4,
  },
});

export default EmojiPopover;