import { Platform, StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  filterPageContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '100%',
  },
  
  evenGridItem: {
    width: '50%',
    minHeight: 58,
    justifyContent: 'center',
    alignItems: 'flex-start',
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#BAB8B8',
  },
  oddGridItem: {
    width: '50%',
    minHeight: 58,
    justifyContent: 'center',
    alignItems: 'flex-start',
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#BAB8B8',
    paddingLeft: 34,
  },

  evenGridItemForAssignee: {
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    minHeight: 48,
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: '#BAB8B8',
  },
  oddGridItemForAssignee: {
    paddingTop: 16,
    paddingLeft: 35,
    backgroundColor: '#ffffff',
    minHeight: 48,
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: '#BAB8B8',
  },

  gridItemTextLabel: {
    fontFamily: 'Roboto',
    fontSize: 12,
    fontWeight: '500',
    color: '#101828',
    lineHeight: 22,
    textAlign: 'center',
  },
  
  gridItemTextLabelReset: {
    fontFamily: 'Roboto',
    fontSize: 8,
    fontWeight: '500',
    color: '#768DEC',
    lineHeight: 22,
    textAlign: 'center',
  },

  // Icon
  icon: {
    marginRight: 5,
  },

  // Date Button

  dateButton: {
    minWidth: 68,
    minHeight: 18,
    borderRadius: 100,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E2E7FB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dateButtonPressed: {
    opacity: 0.8,
  },
  dateButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  dateButtonText: {
    fontFamily: 'Roboto',
    fontSize: 8,
    fontWeight: '500',
    lineHeight: 22,
    textAlign: 'center',
    color: '#000000',
    paddingHorizontal: 5,
  },

  // Status Button
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 5,
  },
  statusButton: {
    minWidth: 55,
    minHeight: 18,
    borderRadius: 100,
    backgroundColor: '#E2E7FB',
    borderWidth: 1,
    borderColor: '#E2E7FB',
    alignItems: 'center',
  },
  statusButtonPressed: {
    backgroundColor: '#9AABF1',
  },
  statusButtonTextSelected: {
    color: '#000000',
  },
  statusButtonText: {
    fontSize: 8,
    lineHeight: 22,
    color: '#000000',
    paddingRight: 7,
    paddingLeft: 5,
  },

  // Assignee Button
  assigneeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 47,
    minHeight: 15,
    backgroundColor: 'white',
    borderRadius: 100,
    borderWidth: 1,
    borderColor: '#E2E7FB',
    opacity: 1,
  },
  assigneeButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  assigneeButtonText: {
    fontSize: 8,
    lineHeight: 22,
    color: '#000000',
    paddingRight: 7,
    paddingLeft: 5,
  },

  buttonContainer: {
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 50 : 130,
    gap: 12,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#ffffff',
  },

  selectedAssigneesContainer: {
    marginTop: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },

  selectedAssigneeChip: {
    backgroundColor: '#9AABF1',
    borderRadius: 16,
    paddingVertical: 4,
    paddingHorizontal: 8,
    height: 24,
    justifyContent: 'center',
  },

  selectedAssigneeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
    height: 24,
  },

  selectedAssigneeText: {
    fontSize: 8,
    fontWeight: '500',
    lineHeight: 16,
    textAlignVertical: 'center',
  },

  removeIcon: {
    color: '#768DEC',
    fontSize: 18,
    marginLeft: 4,
    marginTop: -2,
  },

  iconContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: 16
  },

  selectedAssigneeChipPressed: {
    opacity: 0.7,
  },

  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 1,
  },

  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    height: 50,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    backgroundColor: 'white',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    elevation: 5,
    zIndex: 1000,
    marginBottom: 16,
    paddingTop: 16,
  },

  headerTitle: {
    fontSize: 24,
    fontWeight: '400',
    textAlign: 'center',
    flex: 1,
  },

  headerButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },

  backButton: {
    padding: 8,
  },

  rightPlaceholder: {
    width: 40,
  },
}); 