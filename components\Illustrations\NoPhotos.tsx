import * as React from "react";
import Svg, {
  <PERSON>,
  <PERSON>,
  De<PERSON>,
  <PERSON>ar<PERSON>radient,
  Stop,
  ClipPath,
} from "react-native-svg";
import type { SvgProps } from "react-native-svg";
const SvgNoPhotos = (props: SvgProps) => (
  <Svg
    width={300}
    height={300}
    fill="none"
    viewBox="0 0 300 300"
    {...props}
  >
    <Path
      fill="url(#No_photos_svg__a)"
      d="M144.701 264.402c63.348 0 114.701-51.353 114.701-114.701S208.049 35 144.701 35 30 86.353 30 149.701s51.353 114.701 114.701 114.701"
    />
    <Path
      fill="url(#No_photos_svg__b)"
      d="M51.757 162.36a.61.61 0 0 0-.093-.715.6.6 0 0 0-.21-.145c-2.316-.916-10.547-3.663-15.043 1.831-3 3.64-7.239 6.043-11.903 6.748a.59.59 0 0 0-.492.443.59.59 0 0 0 .236.619c3.946 2.857 17.067 10.035 27.505-8.781"
    />
    <Path
      fill="#2F3C45"
      d="M178.387 117.051c3.483-3.547 3.845-8.93.808-12.023s-8.323-2.724-11.806.823c-3.484 3.547-3.845 8.93-.808 12.023s8.322 2.724 11.806-.823"
    />
    <Path
      stroke="#2F3C45"
      strokeMiterlimit={10}
      strokeWidth={1.14}
      d="m178.999 118.24-1.3 4.642-3.367-1.556"
    />
    <Path
      fill="#2F3C45"
      d="M177.444 118.865c1.438 2.598 5.153 3.241 8.3 1.434 3.148-1.806 4.533-5.374 3.095-7.973-.839-1.517-2.481-2.089-2.957-2.246-2.622-.86-4.901.526-5.346.809-2.399 1.542-4.608 5.238-3.094 7.973z"
    />
    <Path
      fill="#2F3C45"
      d="M188.795 122.182c2.134-1.224 3.353-3.143 2.721-4.284-.631-1.141-2.873-1.073-5.008.152s-3.353 3.142-2.722 4.284 2.874 1.073 5.009-.152"
    />
    <Path
      fill="#2F3C45"
      d="M184.768 123.903s1.268-4.752 1.529-5.029c1.32-1.408 5.324-4.06 10.132-2.176 3.939 1.542 5.266 4.324 7.157 5.372-1.438 1.199-2.18 7.654-10.372 7.648-4.246-.003-7.876-2.392-8.446-5.815M166.377 110.478c-2.078-.738-4.48-.187-6.108 1.304-1.479 1.384-2.385 3.401-2.586 5.413l-1.152-.199c.915-3.265 3.238-6.381 6.664-7.003 1.084-.22 2.197-.208 3.308-.053l-.123.538zM166.161 108.877c-2.347-2.413-6-2.622-8.683-.666-.839.577-1.642 1.336-2.224 2.16l-.9-.759c.433-.434.856-.791 1.318-1.131 2.741-2.116 6.362-2.764 9.375-.83.474.289.912.628 1.324.997z"
    />
    <Path
      fill="#fff"
      d="M119.655 260.092s10.279-8.184 6.894-15.261c-3.387-7.077-9.489 8.131-9.489 8.131s3.209-17.365-3.077-18.276c-6.287-.908-2.379 18.925-2.379 18.925s-1.286-4.575-4.854-2.408c-3.566 2.167 2.034 8.553 2.034 8.553l10.871.339z"
    />
    <Path
      fill="url(#No_photos_svg__c)"
      d="M119.655 260.092s10.279-8.184 6.894-15.261c-3.387-7.077-9.489 8.131-9.489 8.131s3.209-17.365-3.077-18.276c-6.287-.908-2.379 18.925-2.379 18.925s-1.286-4.575-4.854-2.408c-3.566 2.167 2.034 8.553 2.034 8.553l10.871.339z"
    />
    <Path
      fill="url(#No_photos_svg__d)"
      d="M221.37 261.527h-96.945c-6.018 0-10.895-4.967-10.895-11.095V140.518c0-6.127 4.877-11.094 10.895-11.094l96.945-14.452c6.017 0 10.895 4.967 10.895 11.095v124.368c0 6.128-4.878 11.095-10.895 11.095z"
    />
    <Path
      fill="#63B3ED"
      d="M221.519 258.801h-93.327c-5.792 0-10.486-4.783-10.486-10.678V142.31c0-5.898 4.696-10.678 10.486-10.678l93.327-13.913c5.792 0 10.486 4.783 10.486 10.678v119.726c0 5.898-4.697 10.678-10.486 10.678"
    />
    <Path
      stroke="#34434D"
      strokeMiterlimit={10}
      strokeWidth={1.14}
      d="M184.207 122.897c.107 0 .193-.703.193-1.571s-.086-1.571-.193-1.571c-.106 0-.193.703-.193 1.571s.087 1.571.193 1.571ZM174.411 124.469c.106 0 .193-.704.193-1.572 0-.867-.087-1.571-.193-1.571s-.193.704-.193 1.571c0 .868.086 1.572.193 1.572Z"
    />
    <Path
      fill="#fff"
      d="m168.66 219.931 22.494 22.906c0 6.83-5.436 12.366-12.143 12.366h-44.986c-6.707 0-12.143-5.536-12.143-12.366v-45.81c0-6.83 5.436-12.365 12.143-12.365l34.638 35.272z"
    />
    <Path
      fill="url(#No_photos_svg__e)"
      d="m165.816 222.341 20.624 21.693c0 6.374-4.372 11.222-10.764 11.222h-42.861c-6.388 0-11.57-5.166-11.57-11.541v-42.75c0-6.375 1.675-15.11 8.066-15.11l36.505 36.483z"
    />
    <Path
      fill="#fff"
      d="m225.137 197.039-.158-32.394a11.99 11.99 0 0 0-17.173.086l-31.65 32.555c-4.72 4.854-4.679 12.684.084 17.487l31.97 32.231a11.99 11.99 0 0 0 17.172-.087l-.245-49.881z"
    />
    <Path
      fill="url(#No_photos_svg__f)"
      d="m220.89 196.03-.628-30.186c-4.448-4.484-10.907-4.731-15.405-.107L174.7 196.754c-4.494 4.624-4.535 12.008-.087 16.49l29.832 30.078c4.448 4.485 11.722 9.42 16.217 4.795l.225-52.087z"
    />
    <Path
      fill="#fff"
      d="M160.38 187.138c8.409 0 15.226-7.959 15.226-17.776s-6.817-17.776-15.226-17.776-15.226 7.959-15.226 17.776 6.817 17.776 15.226 17.776"
    />
    <Path
      fill="url(#No_photos_svg__g)"
      d="M156.949 187.138c8.409 0 15.226-7.959 15.226-17.776s-6.817-17.776-15.226-17.776-15.226 7.959-15.226 17.776 6.817 17.776 15.226 17.776"
    />
    <Path
      fill="url(#No_photos_svg__h)"
      d="m40.885 226.612 48.455-17.88c3.007-1.11 4.562-4.491 3.472-7.553l-19.549-54.941c-1.09-3.062-4.41-4.645-7.417-3.535L14.82 153.36c-3.008 1.11-4.562 4.491-3.472 7.553l22.12 62.164c1.09 3.062 4.41 4.645 7.417 3.535"
    />
    <Path
      fill="#63B3ED"
      d="m41.4 224.773 46.649-17.214c2.896-1.068 4.392-4.324 3.343-7.273l-18.818-52.89c-1.05-2.949-4.246-4.473-7.143-3.405l-49.124 10.265c-2.896 1.068-4.392 4.324-3.343 7.273l21.293 59.842c1.05 2.949 4.247 4.473 7.143 3.405z"
    />
    <Path
      fill="#fff"
      d="m59.835 196.099-7.169 15.597c1.216 3.414 4.916 5.179 8.268 3.94l22.485-8.297c3.352-1.238 5.085-5.005 3.87-8.419l-8.148-22.897c-1.216-3.414-4.916-5.179-8.268-3.941z"
    />
    <Path
      fill="url(#No_photos_svg__i)"
      d="m59.823 195.893-6.45 14.645c1.134 3.185 4.182 4.804 7.376 3.625l21.425-7.904c3.194-1.179 4.863-4.717 3.732-7.901l-7.604-21.368c-1.134-3.185-3.525-7.244-6.719-6.065l-11.757 24.968z"
    />
    <Path
      fill="#fff"
      d="m27.532 195.075-5.68-16.219c1.527-3.28 5.377-4.679 8.597-3.125l21.609 10.434c3.22 1.556 4.594 5.476 3.069 8.755L44.88 216.925c-1.528 3.28-5.377 4.678-8.598 3.125l-8.75-24.978z"
    />
    <Path
      fill="url(#No_photos_svg__j)"
      d="m28.217 194.795-5.056-15.204c1.426-3.063 4.611-4.378 7.68-2.896l20.589 9.943c3.069 1.482 4.401 5.166 2.978 8.226l-9.562 20.537c-1.427 3.063-4.185 6.869-7.254 5.387z"
    />
    <Path
      fill="#fff"
      d="M58.143 178.181c4.203-1.551 6.195-6.787 4.448-11.695-1.746-4.907-6.57-7.628-10.773-6.077s-6.195 6.787-4.449 11.695 6.57 7.629 10.774 6.077"
    />
    <Path
      fill="url(#No_photos_svg__k)"
      d="M57.125 178.554c4.204-1.552 6.195-6.788 4.449-11.696s-6.57-7.628-10.774-6.077-6.195 6.787-4.448 11.695 6.57 7.629 10.773 6.078"
    />
    <Path
      fill="#FCD9BD"
      d="M158.305 263c72.103 0 130.554-.654 130.554-1.461s-58.451-1.462-130.554-1.462c-72.102 0-130.553.655-130.553 1.462S86.202 263 158.305 263"
    />
    <Path
      stroke="#2F3C45"
      strokeMiterlimit={10}
      strokeWidth={0.855}
      d="m46.818 237.71-.932-4.342-4.036-6.012"
    />
    <Path
      stroke="#2F3C45"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={0.855}
      d="m46.315 239.48.518 10.839-1.76 10.131h3.034"
    />
    <Path
      fill="#2F3C45"
      d="M44.79 245.418s-1.412-4.384-1.669-4.631c-1.303-1.253-5.176-3.536-9.586-1.539-3.612 1.637-4.725 4.304-6.444 5.378 1.4 1.053 2.403 7.056 10.065 6.648 3.972-.211 7.257-2.624 7.631-5.853z"
    />
    <Path
      fill="#2F3C45"
      d="M47.81 242.994c0 1.227-1.862 2.224-4.156 2.224s-4.156-.994-4.156-2.224c0-1.229 1.862-2.223 4.156-2.223s4.156.994 4.156 2.223M62.006 234.439c1.42-3.836-.93-8.289-5.247-9.947s-8.97.107-10.39 3.943.93 8.289 5.247 9.947 8.97-.107 10.39-3.943"
    />
    <Path
      fill="#2F3C45"
      d="M52.081 238.463c0 2.794-2.744 5.059-6.128 5.059s-6.129-2.265-6.129-5.059c0-1.634 1.082-2.866 1.4-3.217 1.75-1.932 4.238-1.866 4.729-1.843 2.653.134 6.128 2.116 6.128 5.06"
    />
    <Path
      stroke="#2F3C45"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={0.855}
      d="M44.445 244.476v5.843l-1.76 10.131h3.268M47.903 238.274l1.48-6.415-2.183-6.341"
    />
    <Path
      fill="#2F3C45"
      d="M60.074 229.436c3.957-.637 7.154 1.465 8.61 5.193.204.497.371.992.505 1.542l-.867.137a8.7 8.7 0 0 0-.807-2.848c-1.248-2.863-4.358-4.553-7.359-3.616l-.085-.405zM57.64 227.225c3.002-2.699 6.915-2.422 9.951.057.418.327.804.672 1.19 1.083l-.684.556a8.9 8.9 0 0 0-2.098-2.05c-2.502-1.854-5.965-1.693-8.201.527l-.155-.173zM40.411 226.392c-.976.085-1.741.495-1.708.916.032.421.85.692 1.827.607.976-.086 1.74-.496 1.708-.916s-.85-.693-1.827-.607M45.618 225.812c-1.055.167-1.96.019-2.02-.331s.745-.769 1.8-.936c1.054-.167 1.959-.019 2.02.331s-.745.769-1.8.936"
    />
    <Path
      stroke="#2F3C45"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={1.14}
      d="m243.881 230.463-8.542-6.559a1.586 1.586 0 0 1-.003-2.5l6.739-5.208M247.841 239.903l-.517 10.836 1.637 9.411h-3.135"
    />
    <Path
      fill="#2F3C45"
      d="M255.943 225.813c1.501-3.803-.753-8.307-5.034-10.06-4.282-1.753-8.97-.09-10.471 3.713-1.502 3.803.752 8.307 5.034 10.06 4.281 1.753 8.969.09 10.471-3.713M245.775 239.662s3.183-3.286 3.519-3.39c1.713-.529 6.181-.818 9.256 2.97 2.519 3.104 2.346 5.997 3.416 7.738-1.716.307-5.237 5.229-11.921 1.384-3.463-1.991-5.348-5.643-4.27-8.702"
    />
    <Path
      fill="#2F3C45"
      d="M242.076 232.118c0 2.794 2.744 5.059 6.128 5.059s6.129-2.265 6.129-5.059c0-1.634-1.082-2.866-1.4-3.217-1.751-1.932-4.238-1.866-4.729-1.843-2.653.134-6.128 2.116-6.128 5.06"
    />
    <Path
      fill="#2F3C45"
      d="M249.853 239.588c2.216-.595 3.755-2.038 3.437-3.224s-2.373-1.665-4.59-1.07-3.755 2.038-3.437 3.224 2.373 1.665 4.59 1.07M252.906 217.154c4.121-2.494 8.256-1.113 11.141 2.542l-.994.628c-.774-1.509-2.057-2.831-3.583-3.56-2.011-.982-4.574-.684-6.248.843l-.313-.453zM249.823 216.13c1.762-3.699 5.448-5.026 9.25-4.008a10 10 0 0 1 1.543.506l-.555 1.047a7.4 7.4 0 0 0-1.272-.654c-3.369-1.447-7.212-.316-8.691 3.24l-.278-.131z"
    />
    <Path
      stroke="#2F3C45"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={1.14}
      d="m250.229 239.903-.517 10.836 1.956 9.269h-2.85M245.687 232.115l-1.645 7.101a4.3 4.3 0 0 1-.526 1.282l-4.211 6.875"
    />
    <Path
      stroke="#2F3C45"
      strokeMiterlimit={10}
      strokeWidth={1.14}
      d="M243.881 216.324c.911 0 1.648-.131 1.648-.292s-.737-.291-1.648-.291-1.648.13-1.648.291.738.292 1.648.292ZM238.161 248.176c.809-.423 1.407-.883 1.335-1.026s-.788.084-1.597.508c-.81.423-1.408.883-1.336 1.026.073.143.788-.084 1.598-.508Z"
    />
    <Path
      stroke="#2F3C45"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={1.14}
      d="m85.72 231.448 6.89-15.246-1.534-6.568"
    />
    <Path
      stroke="#2F3C45"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={0.855}
      d="m81.441 238.722.517 10.835-1.59 10.461h2.274"
    />
    <Path
      fill="#2F3C45"
      d="M83.79 230.085c4.28-1.753 6.534-6.257 5.033-10.06s-6.19-5.466-10.471-3.713c-4.282 1.753-6.535 6.257-5.034 10.06s6.19 5.466 10.471 3.713"
    />
    <Path
      fill="#2F3C45"
      d="M87.204 232.65c0 2.795-2.744 5.06-6.128 5.06s-6.129-2.265-6.129-5.06c0-1.633 1.082-2.865 1.4-3.217 1.75-1.931 4.238-1.866 4.729-1.842 2.653.134 6.128 2.116 6.128 5.059"
    />
    <Path
      fill="#2F3C45"
      d="M82.193 240.48s-3.183-3.285-3.52-3.389c-1.712-.53-6.18-.819-9.255 2.97-2.519 3.104-2.346 5.996-3.416 7.737 1.716.307 5.237 5.229 11.92 1.384 3.464-1.991 5.349-5.642 4.27-8.702"
    />
    <Path
      fill="#2F3C45"
      d="M83.595 238.457c0 1.226-1.862 2.223-4.156 2.223s-4.156-.994-4.156-2.223 1.862-2.223 4.156-2.223 4.156.994 4.156 2.223M76.18 217.964c-2.516-1.967-5.903-1.287-8.267.805a9.4 9.4 0 0 0-1.976 2.28l-.327-.591c.292-.441.622-.84.976-1.218 1.716-1.913 4.31-3.145 6.874-2.592 1 .2 1.952.622 2.8 1.203zM79.111 216.833c-1.262-2.952-4.183-4.185-7.267-3.563-.987.164-1.958.5-2.831.971l-.141-.691c.47-.235.95-.41 1.44-.562 2.423-.789 5.284-.536 7.23 1.241.77.684 1.374 1.544 1.81 2.479l-.07.033z"
    />
    <Path
      stroke="#2F3C45"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={0.855}
      d="M79.568 243.621v5.936l-1.757 10.461h3.128"
    />
    <Path
      stroke="#2F3C45"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={1.14}
      d="m83.244 229.942 5.921-12.541-4.819-6.568"
    />
    <Path
      fill="#2F3C45"
      stroke="#2F3C45"
      strokeMiterlimit={10}
      strokeWidth={0.285}
      d="M85.67 210.599c.94-.506 1.57-1.172 1.406-1.487s-1.059-.161-2 .345c-.94.506-1.57 1.172-1.406 1.487s1.059.161 2-.345ZM92.071 208.636c.598-.896.847-1.785.557-1.985s-1.009.364-1.606 1.26c-.597.897-.847 1.785-.557 1.986.29.2 1.01-.364 1.606-1.261Z"
    />
    <Path
      fill="#fff"
      d="M230.64 260.64s10.278-8.184 6.894-15.261c-3.387-7.077-9.49 8.13-9.49 8.13s3.209-17.365-3.077-18.275c-6.286-.908-2.379 18.924-2.379 18.924s-1.286-4.574-4.854-2.407c-3.565 2.166 2.034 8.553 2.034 8.553l10.872.339z"
    />
    <Path
      fill="url(#No_photos_svg__l)"
      d="M230.64 260.64s10.278-8.184 6.894-15.261c-3.387-7.077-9.49 8.13-9.49 8.13s3.209-17.365-3.077-18.275c-6.286-.908-2.379 18.924-2.379 18.924s-1.286-4.574-4.854-2.407c-3.565 2.166 2.034 8.553 2.034 8.553l10.872.339z"
    />
    <G fill="#7F9CF5" clipPath="url(#No_photos_svg__m)">
      <Path d="M251.792 208.322c.136.401 1.035.044 3.838-1.51 2.966-1.648 3.563-2.12 3.248-2.515-.44-.536-7.289 3.368-7.086 4.025M243.82 195.684c-1.152.517-1.216.784-.893 3.687.331 2.999.496 3.533 1.053 3.508 1.009-.049 1.171-.875.943-4.608-.169-2.754-.252-2.968-1.103-2.587M251.873 196.847c-.833.36-1.065.662-1.624 2.097-1.643 4.261-3.146 7.176-1.538 6.442.659-.296 1.044-1.057 3.127-5.978 1.139-2.726 1.153-3.036.035-2.561" />
    </G>
    <G fill="#7F9CF5" clipPath="url(#No_photos_svg__n)">
      <Path d="M156.416 91.679c.385-.166-.028-1.055-1.746-3.794-1.821-2.898-2.326-3.472-2.69-3.124-.495.484 3.808 7.17 4.436 6.918M144.593 100.658c.586 1.134.852 1.181 3.674.65 2.914-.545 3.426-.75 3.363-1.314-.119-1.021-.939-1.129-4.58-.637-2.686.364-2.89.463-2.457 1.301M145.17 92.396c.411.821.723 1.036 2.167 1.504 4.289 1.372 7.25 2.695 6.418 1.113-.335-.648-1.108-.987-6.074-2.76-2.75-.967-3.054-.959-2.511.143" />
    </G>
    <Defs>
      <LinearGradient
        id="No_photos_svg__a"
        x1={279.249}
        x2={215.873}
        y1={-62.234}
        y2={150.371}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_photos_svg__b"
        x1={58.603}
        x2={49.172}
        y1={175.656}
        y2={153.752}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F99" />
        <Stop offset={1} stopColor="#FFE7DA" />
      </LinearGradient>
      <LinearGradient
        id="No_photos_svg__c"
        x1={116.398}
        x2={116.398}
        y1={228.019}
        y2={260.095}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#fff" />
        <Stop offset={0.671} stopColor="#80D6AF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#00AC5E" stopOpacity={0.7} />
      </LinearGradient>
      <LinearGradient
        id="No_photos_svg__d"
        x1={172.897}
        x2={172}
        y1={261.53}
        y2={80.991}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_photos_svg__e"
        x1={153.843}
        x2={153.476}
        y1={255.256}
        y2={172.781}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_photos_svg__f"
        x1={196.096}
        x2={195.329}
        y1={249.973}
        y2={145.874}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_photos_svg__g"
        x1={156.949}
        x2={156.743}
        y1={187.138}
        y2={144.889}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_photos_svg__h"
        x1={52.08}
        x2={51.647}
        y1={226.967}
        y2={122.728}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_photos_svg__i"
        x1={69.817}
        x2={69.527}
        y1={214.548}
        y2={162.459}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_photos_svg__j"
        x1={39.069}
        x2={38.753}
        y1={221.118}
        y2={167.602}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_photos_svg__k"
        x1={57.125}
        x2={49.263}
        y1={178.554}
        y2={157.562}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_photos_svg__l"
        x1={227.382}
        x2={227.382}
        y1={228.567}
        y2={260.643}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#fff" />
        <Stop offset={0.671} stopColor="#80D6AF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#00AC5E" stopOpacity={0.7} />
      </LinearGradient>
      <ClipPath id="No_photos_svg__m">
        <Path
          fill="#fff"
          d="m236.032 202.797 12.962-15.73 15.223 13.007-12.962 15.731z"
        />
      </ClipPath>
      <ClipPath id="No_photos_svg__n">
        <Path
          fill="#fff"
          d="m152.104 108.074-16.314-12.07 11.68-16.372 16.315 12.07z"
        />
      </ClipPath>
    </Defs>
  </Svg>
);
export default SvgNoPhotos;
