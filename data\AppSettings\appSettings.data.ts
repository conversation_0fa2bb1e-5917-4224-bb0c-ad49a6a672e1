import { gql } from "@apollo/client";

export const GET_APP_SETTINGS = gql`
query MdAppSettings($version: String!) {
  getMdAppSettingsByVersion(version: $version) {
    ... on MdAppSettingsResponse {
            result {
        mdAppSettings {
          navBars {
            hostEventDashboard {
              tabs {
                tasks {
                  id
                  name
                  iconSrc
                }
                apps {
                  iconSrc
                  id
                  name
                }
                eventsDb {
                  iconSrc
                  id
                  name
                }
                guests {
                  iconSrc
                  id
                  name
                }
                messages {
                  iconSrc
                  id
                  name
                }
              }
            }
            userDashboard {
              tabs {
                home {
                  iconSrc
                  id
                  name
                }
                myEvents {
                  iconSrc
                  id
                  name
                }
                publicEvents {
                  id
                  iconSrc
                  name
                }
              }
            }
          }
          themes {
            dark {
              backgroundColor
              primaryColor
              secondaryColor
              textColor
            }
            light {
              backgroundColor
              primaryColor
              secondaryColor
              textColor
            }
          }
          version
        }
      }
    }
  }
}
`;