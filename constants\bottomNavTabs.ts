export enum HOST_TABS {
    EVENTS_DB = 'eventsDb',
    TASKS = 'tasks',
    GUESTS = 'guests',
    APPS = 'apps',
    MESSAGES = 'messages'
  }

export enum USER_TABS {
    HOME = 'home',
    MY_EVENTS = 'homePage',
    PUBLIC_EVENTS = 'publicEvents',
    PHOTOS = 'photos'
  }

export enum NAV_BAR_VIEW_TYPE {
    HOST = 'host_event_dashboard',
    USER = 'user_dashboard',
    GUEST = 'guest_dashboard'
  }
export enum APP_SETTINGS_VERSION {
    VERSION = '01.00.00'
  }
  export enum DASHBOARD_TYPE {
    HOST = 'hostEventDashboard',
    USER = 'userDashboard'
}
