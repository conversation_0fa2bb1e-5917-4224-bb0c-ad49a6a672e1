import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors, Typography, Spacing, Borders, Icons } from '@/constants/DesignSystem';
import { Alert, CircleCheck, Info } from '@/components/icons';

type InfoLabelType = 'error' | 'success' | 'info';

interface InfoLabelProps {
  message: string;
  type?: InfoLabelType;
  visible: boolean;
  style?: object;
}

export const InfoLabel: React.FC<InfoLabelProps> = ({
  message,
  type = 'error',
  visible,
  style,
}) => {
  if (!visible || !message) return null;

  const getBackgroundColor = () => {
    switch (type) {
      case 'error':
        return `${Colors.error}20`;
      case 'success':
        return `${Colors.success}20`;
      case 'info':
      default:
        return `${Colors.primary}20`;
    }
  };

  const getTextColor = () => {
    switch (type) {
      case 'error':
        return Colors.error;
      case 'success':
        return Colors.success;
      case 'info':
      default:
        return Colors.primary;
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'error':
        return <Alert size={Icons.size.md} color={Colors.error} />;
      case 'success':
        return <CircleCheck size={Icons.size.md} color={Colors.success} />;
      case 'info':
      default:
        return <Info size={Icons.size.md} color={Colors.primary} />;
    }
  };

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: getBackgroundColor() },
        style,
      ]}
    >
      <View style={styles.iconContainer}>{getIcon()}</View>
      <Text style={[styles.message, { color: getTextColor() }]}>{message}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.md,
    borderRadius: Borders.radius.md,
    marginVertical: Spacing.md,
  },
  iconContainer: {
    marginRight: Spacing.sm,
  },
  message: {
    flex: 1,
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
});
