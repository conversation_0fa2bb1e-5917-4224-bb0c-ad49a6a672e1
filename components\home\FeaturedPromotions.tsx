import React from 'react';
import { StyleSheet, View, Linking } from 'react-native';
import { ImageCarousel } from '@/components/ImageCarousel';
import type { MdPromotion } from '@/app/(home)/UserDashboard/promotions';

interface FeaturedPromotionsProps {
  promotions: MdPromotion[];
  onPromotionPress?: (promotion: MdPromotion) => void;
}

async function openPromotionLink(ctaLink?: string) {
  if (!ctaLink) return;
  
  try {
    const canOpen = await Linking.canOpenURL(ctaLink);
    if (canOpen) {
      await Linking.openURL(ctaLink);
    } else {
      console.log('Cannot open URL:', ctaLink);
    }
  } catch (error) {
    console.error('Error opening URL:', error);
  }
}

export function FeaturedPromotions({ promotions, onPromotionPress }: FeaturedPromotionsProps) {
  const carouselImages = promotions.map(promotion => ({
    id: promotion.id,
    image_src: promotion.mediaUrl,
    onPress: () => {
      if (onPromotionPress) {
        onPromotionPress(promotion);
      } else if (promotion.ctaLink) {
        openPromotionLink(promotion.ctaLink);
      }
    }
  }));

  return (
    <View style={styles.container}>
      <ImageCarousel 
        images={carouselImages}
        showDotPagination={true}
        showNumberPagination={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
    borderRadius: 12,
    overflow: 'hidden',
  },
}); 