import { View, Text, Pressable } from 'react-native';
import { styles } from './StatusFilter.styles';
import { TaskStatus } from '@/constants/Task.constants';

interface StatusFilterProps {
  selectedStatus: Array<typeof TaskStatus[keyof typeof TaskStatus] | 'all'>;
  onStatusSelect: (status: typeof TaskStatus[keyof typeof TaskStatus] | 'all') => void;
}

export function StatusFilter({ selectedStatus, onStatusSelect }: StatusFilterProps) {
  const statusOptions = [
    { value: 'all' as const, label: 'All' },
    { value: TaskStatus.IN_PROGRESS, label: 'In Progress' },
    { value: TaskStatus.COMPLETED, label: 'Completed' },
    { value: TaskStatus.CLOSED, label: 'Closed' },
  ];

  return (
    <View style={styles.statusContainer}>
      {statusOptions.map((option) => (
        <Pressable
          key={option.value}
          onTouchStart={() => onStatusSelect(option.value)}
          style={({ pressed }) => [
            styles.statusButton,
            selectedStatus.includes(option.value) && styles.statusButtonPressed,
            pressed && styles.statusButtonPressed
          ]}
        >
          <Text
            style={[
              styles.statusButtonText,
              selectedStatus.includes(option.value) && styles.statusButtonTextSelected
            ]}
          >
            {option.label}
          </Text>
        </Pressable>
      ))}
    </View>
  );
} 