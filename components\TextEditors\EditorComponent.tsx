import React, { useState, useRef, useEffect } from "react";
import {
  Modal,
  View,
  TextInput,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Text,
  Keyboard,
  Image,
  Pressable,
  SafeAreaView,
} from "react-native";
import InitialsAvatar from "../Avatar/InitialsAvatar";
import * as ImagePicker from "expo-image-picker";
import * as FileSystem from "expo-file-system";
import * as DocumentPicker from "expo-document-picker";
import WebView from "react-native-webview";
import { uploadFiles } from "@/app/(home)/Task/Task.utils";
import { CREATE_MEDIA } from "@/app/(home)/PartyInviteTemplates/partyInviteTemplate.data";
import { useMutation } from "@apollo/client";
import { useUserStore } from "@/app/auth/userStore";
import { Switch } from "react-native-paper";
import { useNavigation } from "@react-navigation/native";
import { NavigationProp } from "@react-navigation/native";
import { PartyDetailsRootList } from "../create-event/Navigation/PartyDetailsRootList";
import { useFocusEffect } from "@react-navigation/native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { GifBottomSheet } from "../../src/components/gif-bottom-sheet/GifBottomSheet";
import type { TenorMediaFormat } from "../../src/api/tenor";
import { BottomSheetModalProvider, BottomSheetModal } from "@gorhom/bottom-sheet";
import { Borders, Spacing, Icons, Colors, Typography } from "@/constants/DesignSystem";
import { Send, Add, Keyboard as KeyboardIcon, Photos, TakePhoto, File, BackArrow, Gif } from "@/components/icons";
import { useToast } from "@/components/Toast";
export interface TextEditorProps {
  isVisible: boolean;
  parentId: string | undefined;
  toggleLocation: boolean;
  onSubmit: (
    text: string,
    parentId: string | undefined,
    mediaId: string | null
  ) => void;
  onClose: () => void;
  editedComment?: string | undefined;
  handleEditedComment: (
    commentText: string,
    parentId: string | undefined,
    mediaId: string | null
  ) => Promise<void>;
  onLocation: (isEnabled: boolean) => Promise<void>;
}

type TextEditorRouteProp = {
  params: {
    props: TextEditorProps;
  };
};

interface TextEditorScreenProps {
  route: TextEditorRouteProp;
}

const TextEditor: React.FC<TextEditorScreenProps> = ({ route }) => {
  const [textInput, setTextInput] = useState(
    route.params.props.editedComment ?? ""
  );
  const [showOverlay, setShowOverlay] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([]);
  const [uploadedMediaUrl, setUploadedMediaUrl] = useState<string | null>(null);
  const [mediaId, setMediaId] = useState<string | null>(null);
  const [documentUrl, setDocumentUrl] = useState<string | null>(null); // State for WebView modal
  const [isGif, setIsGif] = useState(false);
  const textInputRef = useRef<TextInput>(null);
  const [isEditorActive, setIsEditorActive] = useState(false);
  const userData = useUserStore((state) => state.userData);
  const [createMedia] = useMutation(CREATE_MEDIA);
  const maxChars = 1250;
  const [isEdited, setIsEdited] = useState(
    route.params.props.editedComment !== undefined
  );
  const [isLiveTrackingEnabled, setIsLiveTrackingEnabled] = useState(false);
  const navigation = useNavigation<NavigationProp<PartyDetailsRootList>>();
  const [isGifSheetVisible, setIsGifSheetVisible] = useState(false); // New state for GifBottomSheet
  const toast = useToast();
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  useFocusEffect(
    React.useCallback(() => {
      console.log("TextEditor screen is now focused");
      setTimeout(() => textInputRef.current?.focus(), 50);

      return () => {
        console.log("TextEditor screen is now unfocused");
      };
    }, [])
  );

  const handleSubmission = () => {
    setTimeout(() => {
      Keyboard.dismiss();
    }, 300);
    setTimeout(() => {
      if (navigation.canGoBack()) {
        if (!textInput.trim() && mediaId == null) return;
        console.log("Hi Before On submit", isEdited);
        if (isEdited) {
          route.params.props.handleEditedComment(
            textInput,
            route.params.props.parentId,
            mediaId
          );
        } else {
          route.params.props.onSubmit(textInput, undefined, mediaId);
        }

        console.log("Hi After On submit");
        setTextInput("");
        setUploadedFiles([]);
        setUploadedMediaUrl(null);
        setMediaId(null);
        setIsGif(false);
        navigation.goBack();
      }
    }, 600);
  };

  const handleLocation = async (isEnabled: boolean) => {
    setIsLiveTrackingEnabled(isEnabled);
    await route.params.props.onLocation(isEnabled);
    if (isEnabled) {
      setTimeout(() => textInputRef.current?.focus(), 50);
      route.params.props.onClose();
    }
  };

  const toggleOverlay = () => {
    if (isEditorActive) return;
    setShowOverlay(!showOverlay);
    if (!showOverlay) {
      setTimeout(() => Keyboard.dismiss(), 10);
    } else {
      setTimeout(() => textInputRef.current?.focus(), 10);
    }
  };

  const handleImagePicker = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsMultipleSelection: false,
      quality: 1,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      await uploadMedia(
        result.assets[0].uri,
        `photo_${Date.now()}.jpg`,
        "image/jpeg"
      );
    }
  };

  const handleCameraPicker = async () => {
    const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
    if (permissionResult.granted === false) {
      alert("You've refused to allow this app to access your camera!");
      return;
    }

    const result = await ImagePicker.launchCameraAsync();

    if (!result.canceled && result.assets && result.assets.length > 0) {
      await uploadMedia(
        result.assets[0].uri,
        `photo_${Date.now()}.jpg`,
        "image/jpeg"
      );
    }
  };

  const handleDocumentPicker = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: "application/pdf", // Restrict to PDF files only
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        // Validate that the file is a PDF
        if (
          asset.mimeType === "application/pdf" ||
          asset.name.toLowerCase().endsWith(".pdf")
        ) {
          await uploadMedia(
            asset.uri,
            asset.name || `document_${Date.now()}.pdf`,
            asset.mimeType || "application/pdf"
          );
        } else {
          alert("Please select a PDF file.");
        }
      }
    } catch (error) {
      console.error("Document picker failed:", error);
      alert("Failed to pick PDF document");
    }
  };

  const handleGifSelected = async (gif: TenorMediaFormat) => {
    setIsGifSheetVisible(false);

    try {
      const bestGifUrl = gif.url;

      const mediaResponse = await createMedia({
        variables: {
          input: {
            url: bestGifUrl, 
            title: "GIF from Tenor", 
          },
        },
      });

      if (mediaResponse.data?.createMedia?.result?.media?.url && mediaResponse.data?.createMedia?.result?.media?.id) {
        setUploadedMediaUrl(mediaResponse.data.createMedia.result.media.url);
        setMediaId(mediaResponse.data.createMedia.result.media.id);
        setIsGif(true);
        setShowOverlay(false);
      } else {
        console.error("Failed to register GIF with backend:", mediaResponse);
        alert("Failed to use selected GIF.");
      }
    } catch (error) {
      console.error("Error processing selected GIF:", error);
      alert("An error occurred while selecting the GIF.");
    }
  };

  const uploadMedia = async (uri: string, name: string, type: string) => {
    try {
      const fileInfo = await FileSystem.getInfoAsync(uri);
      if (!fileInfo.exists) {
        alert("File does not exist");
        throw new Error("File does not exist");
      }

      const file = {
        uri,
        type,
        name,
        size: fileInfo.size,
      };
      console.log("Uploading file:", file);
      const result = await uploadFiles([file], "media");
      console.log("Successful file:", file);
      const successfulUploads = result.successful.map((item: any) => item.url);
      setUploadedFiles((prev) => [...prev, ...successfulUploads]);
      console.log("Successful link:", successfulUploads);

      const mediaResponse = await createMedia({
        variables: {
          input: {
            url: successfulUploads[0],
            title: type.includes("image") ? "Image uploaded" : "PDF uploaded",
          },
        },
      });
      console.log("Media response:", mediaResponse);
      if (mediaResponse.data?.createMedia?.result?.media?.url) {
        setUploadedMediaUrl(mediaResponse.data.createMedia.result.media.url);
        setMediaId(mediaResponse.data.createMedia.result.media.id);
        setShowOverlay(false);
      }
    } catch (error) {
      console.error("Upload failed:", error);
      alert("Failed to upload file");
    }
  };

  const openDocument = (url: string) => {
    setDocumentUrl(url);
  };

  const closeDocument = () => {
    setDocumentUrl(null);
  };

  const renderOverlayContent = () => (
    <View style={styles.overlayStyle}>
      <View style={styles.overlayItem}>
        <Text style={styles.overlayText}>Allow Live Tracking</Text>
        <Switch
          value={isLiveTrackingEnabled}
          onValueChange={handleLocation}
          thumbColor={isLiveTrackingEnabled ? "orange" : "#f4f3f4"}
          trackColor={{ false: "#767577", true: "#81b0ff" }}
        />
      </View>
      <TouchableOpacity style={styles.overlayItem} onPress={handleImagePicker}>
        <Photos size={Icons.size.md} color={Colors.primary} />
        <Text style={styles.overlayText}>Photos</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.overlayItem} onPress={handleCameraPicker}>
        <TakePhoto size={Icons.size.md} color={Colors.primary} />
        <Text style={styles.overlayText}>Camera</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.overlayItem}
        onPress={handleDocumentPicker}
      >
        <File size={Icons.size.md} color={Colors.primary} />
        <Text style={styles.overlayText}>Documents</Text>
      </TouchableOpacity>
    </View>
  );

  const dismiss = () => {
    setTextInput("");
    setUploadedFiles([]);
    setUploadedMediaUrl(null);
    setMediaId(null);
    setDocumentUrl(null);
    setIsGif(false);
    setIsGifSheetVisible(false);
    if (navigation.canGoBack()) {
      navigation.goBack();
    }
  };
  const openImage = (url: string) => {
    setImageUrl(url);
  };
  const getPdfViewerUrl = (url: string) =>
    Platform.OS === "android"
      ? `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(`${url}?t=${Date.now()}`)}`
      : url;

  return (
    <Modal 
      animationType="slide" 
      transparent={true} 
      onRequestClose={isGifSheetVisible ? () => setIsGifSheetVisible(false) : dismiss}
    >
      <GestureHandlerRootView style={{ flex: 1 }}> 
        <BottomSheetModalProvider>
          {isGifSheetVisible ? (
            <GifBottomSheet
              visible={true}
              onDismiss={() => {
                setIsGifSheetVisible(false);
                textInputRef.current?.focus();
              }}
              onSelectGif={handleGifSelected}
            />
          ) : (
            <>
              <Pressable style={styles.overlay} onPress={dismiss}>
                <KeyboardAvoidingView
                  behavior={Platform.select({ ios: "padding", android: "height" })}
                  style={styles.halfSheetContainer}
                >
                  <Pressable style={styles.halfSheet} onPress={() => {}}>
                    <View style={styles.innerContainer}>
                      <TouchableOpacity onPress={toggleOverlay} style={styles.icon}>
                        {showOverlay ? (
                          <KeyboardIcon size={Icons.size.md} color={Colors.black} />
                        ) : (
                          <Add size={Icons.size.md} color={Colors.primary} />
                        )}
                      </TouchableOpacity>
                      {userData?.profilePicture ? (
                        <Image
                          source={{ uri: userData.profilePicture }}
                          style={styles.circle}
                        />
                      ) : (
                        <InitialsAvatar
                          firstName={userData?.firstName || ""}
                          lastName={userData?.lastName || ""}
                        />
                      )}
                      <View style={styles.textInputContainer}>
                        {uploadedMediaUrl && (
                          <View style={styles.uploadedMediaContainer}>
                            {uploadedMediaUrl.match(/\.(jpeg|jpg|png)$/i) || isGif ? (
                              <TouchableOpacity onPress={() => openImage(uploadedMediaUrl)}>
                                 <Image
                                source={{ uri: uploadedMediaUrl }}
                                style={styles.uploadedImage}
                               
                              />
                              </TouchableOpacity>
                            
                            ) : (
                              <TouchableOpacity
                                style={styles.documentPreview}
                                onPress={() => openDocument(uploadedMediaUrl)}
                              >
                                <File size={Icons.size.md} color={Colors.primary} />
                                <Text style={styles.documentName}>
                                  {uploadedFiles[0]?.split("/").pop()}
                                </Text>
                              </TouchableOpacity>
                            )}
                          </View>
                        )}
                        <TextInput
                          ref={textInputRef}
                          style={styles.textInput}
                          value={textInput}
                          onChangeText={setTextInput}
                          placeholder="Leave a comment..."
                          multiline={true}
                          maxLength={maxChars}
                          scrollEnabled={true}
                          onFocus={() => {
                            setShowOverlay(false);
                          }}
                        />
                        <Text style={styles.textcount}>
                          {textInput.length} / {maxChars}
                        </Text>
                      </View>
                      {textInput.trim() || mediaId ? (
                        <TouchableOpacity
                          onPress={handleSubmission}
                          style={styles.sendIcon}
                          activeOpacity={1}
                        >
                          <Send size={Icons.size.md} color={Colors.white} />
                        </TouchableOpacity>
                      ) : (
                        <>
                          <TouchableOpacity
                            onPress={() => {
                              Keyboard.dismiss(); 
                              setShowOverlay(false); 
                              setIsGifSheetVisible(true); 
                            }}
                            style={styles.icon}
                          >
                            <Gif size={Icons.size.xl} color={Colors.primary} />
                          </TouchableOpacity>
                        </>
                      )}
                    </View>
                    {showOverlay && renderOverlayContent()}
                  </Pressable>
                </KeyboardAvoidingView>
              </Pressable>
              <Modal
                visible={!!documentUrl}
                animationType="slide"
                onRequestClose={closeDocument}
              >
                <SafeAreaView style={styles.webViewContainer}>
                  <View style={styles.header}>
                    <TouchableOpacity style={styles.backButton} onPress={closeDocument}>
                      <BackArrow size={Icons.size.md} color={Colors.black} />
                    </TouchableOpacity>
                  </View>
                  {documentUrl && (
            <WebView
              source={{ uri: getPdfViewerUrl(documentUrl) }}
              onError={() => {
                setDocumentUrl(null);
                toast.error("Failed to load document. Try again.");
              }}
              style={styles.webView}
              startInLoadingState
            />
          )}
                </SafeAreaView>
              </Modal>
              <Modal
        visible={!!imageUrl}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setImageUrl(null)}
      >
        <SafeAreaView style={styles.imageModalContainer}>
          <View style={styles.imageContainer}>
          <View >
            <TouchableOpacity 
              style={styles.closeButton} 
              onPress={() => setImageUrl(null)}
            >
              <BackArrow size={Icons.size.lg} color={Colors.black} />
            </TouchableOpacity>
          </View>
          <Image
            source={{ uri: imageUrl || '' }}
            style={styles.fullSizeImage}
              resizeMode="contain"
            />
          </View>
        </SafeAreaView>
      </Modal>
            </>
          )}
        </BottomSheetModalProvider>
      </GestureHandlerRootView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  innerContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  halfSheet: {
    backgroundColor: Colors.white,
    borderTopLeftRadius: Borders.radius.lg,
    borderTopRightRadius: Borders.radius.lg,
    paddingRight: Spacing.xs,
    paddingVertical: Spacing.sm,
    paddingBottom: Platform.OS === 'ios' ? Spacing.lg : Spacing.sm,
  },
  textInputContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  textInput: {
    minHeight: 40,
    fontSize: Typography.fontSize.md,
    lineHeight: 20,
    paddingHorizontal: Spacing.sm,
    flexGrow: 1,
    flexShrink: 1,
    maxHeight: 120,
    paddingTop: Platform.OS === 'ios' ? Spacing.sm : 0,
    paddingBottom: Platform.OS === 'ios' ? Spacing.sm : 0,
    width: '100%',
  },
  uploadedMediaContainer: {
    marginBottom: Spacing.sm,
    marginHorizontal: Spacing.sm,
    alignSelf: 'flex-start',
  },
  uploadedImage: {
    width: 100,
    height: 100,
    borderRadius: Borders.radius.sm,
  },
  documentPreview: {
    flexDirection: "row",
    alignItems: "center",
    padding: Spacing.md,
  },
  documentName: {
    marginLeft: Spacing.sm,
    fontSize: Typography.fontSize.md,
    color: Colors.text.primary,
  },
  icon: {
    padding: Spacing.sm,
  },
  sendIcon: {
    padding: Spacing.sm,
    backgroundColor: Colors.primary,
    borderRadius: Borders.radius.pill,
    height: 40,
    width: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  overlay: {
    flex: 1,
    justifyContent: "flex-end",
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.25,
    shadowRadius: Borders.radius.sm,
    elevation: 5,
  },
  overlayStyle: {
    flexDirection: "row",
    padding: Spacing.sm,
    paddingBottom: Spacing.md,
  },
  halfSheetContainer: {
    flex: 1,
    justifyContent: "flex-end",
  },
  overlayItem: {
    alignItems: "center",
    marginHorizontal: Spacing.lg,
  },
  overlayText: {
    marginTop: Spacing.xs,
    fontSize: Typography.fontSize.sm,
  },
  circle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  textcount: {
    textAlign: "right",
    color: Colors.mediumGray,
    fontWeight: "500",
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.primary,
  },
  webViewContainer: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    backgroundColor: Colors.white,
    padding: Spacing.sm,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    padding: Spacing.sm,
  },
  webView: {
    flex: 1,
  },
  gifContainer: {
    borderWidth: Borders.width.thin,
    borderColor: Colors.border.medium,
    padding: Borders.width.thin,
  },
  gifText: {
    color: Colors.text.secondary,
    fontWeight: Typography.fontWeight.bold,
  },
  imageModalContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  imageContainer: {
    flex: 1,
    backgroundColor: Colors.white,
    position: 'relative',
    justifyContent: 'center',
    
  },

  closeButton: {
    padding: Spacing.sm,
    borderRadius: Borders.radius.lg,
  },
  fullSizeImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },  
});

export default TextEditor;
