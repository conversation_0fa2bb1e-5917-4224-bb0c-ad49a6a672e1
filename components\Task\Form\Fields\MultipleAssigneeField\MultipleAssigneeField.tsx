// components/Task/Form/Fields/AssigneeField/AssigneeField.tsx
import { View, Pressable } from 'react-native';
import { Text, TextInput, List } from 'react-native-paper';
import { styles } from './MultipleAssigneeField.styles';
import { Assignee } from '@/components/Task/SharedInterfaces';
import { getAssigneeName } from '@/app/(home)/utils/reusableFunctions';
import { useRef, useState, useMemo, useCallback, useEffect } from 'react';
import { BottomSheetModal, BottomSheetBackdrop, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useQuery } from '@apollo/client';
import { GET_EVENT_ASSIGNEES_BY_EVENT_ID } from '@/app/(home)/EventsDashboard/AddParty.data';
import { useEventStore } from '@/store/eventStore';
import { useFocusEffect } from '@react-navigation/native';
import { useKeyboard } from '@/commons/KeyboardContext';
import { ActionButton } from '@/components/UI/ReusableComponents/ActionButton';
import { Icons, Colors } from '@/constants/DesignSystem';
import { Check } from '@/components/icons';

interface MultipleAssigneeFieldProps {
  selectedAssignees: Assignee[];
  onAssigneesSelect: (assignees: Assignee[]) => void;
  isOpen?: boolean;
  onClose?: () => void;
}

const CustomRadioButton = ({ isSelected }: { isSelected: boolean }) => (
  <Pressable 
    style={{
      width: 27,
      height: 27,
      borderRadius: 20,
      borderWidth: 2,
      borderColor:  isSelected ? '#768DEC' : '#CFCECE',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: isSelected ? '#BEC9F6' : 'transparent',
      marginLeft: 16,
    }}
  >
    {isSelected && (
      <Check 
        size={Icons.size.md} 
        color={Colors.primary}
      />
    )}
  </Pressable>
);

export function MultipleAssigneeField({ 
  selectedAssignees, 
  onAssigneesSelect,
  isOpen,
  onClose
}: MultipleAssigneeFieldProps) {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['80%'], []);
  const [searchQuery, setSearchQuery] = useState('');
  
  const selectedEventId = useEventStore((state) => state.selectedEventId);

  const { data: assigneesData, loading , refetch} = useQuery(GET_EVENT_ASSIGNEES_BY_EVENT_ID, {
    variables: {
      eventId: selectedEventId
    },
    skip: !selectedEventId,
    fetchPolicy: 'network-only',
  });

  useFocusEffect(
    useCallback(() => {
      if (selectedEventId) {
        refetch();
      }
    }, [selectedEventId, refetch])
  );

  const assignees = assigneesData?.getEventAssignees?.result?.assignees?.map((assignee: any) => ({
    id: assignee.id,
    firstName: assignee.firstName,
    lastName: assignee.lastName,
    email: assignee.email,
  })) || [];

  const filteredAssignees = assignees.filter((assignee: Assignee) =>
    getAssigneeName(assignee).toLowerCase().includes(searchQuery.toLowerCase())
  );

  const { isKeyboardActive } = useKeyboard();

  const handlePress = () => {
    if (isKeyboardActive) return;
    bottomSheetRef.current?.present();
  };

  const [tempSelectedAssignees, setTempSelectedAssignees] = useState<Assignee[]>(selectedAssignees);

  useEffect(() => {
    setTempSelectedAssignees(selectedAssignees);
  }, [selectedAssignees]);

  const handleAssigneeSelect = async (assignee: Assignee) => {
    const isDeselecting = tempSelectedAssignees.some(a => a.id === assignee.id);
    
    if (isDeselecting) {
      setTempSelectedAssignees(prev => prev.filter(a => a.id !== assignee.id));
    } else {
      setTempSelectedAssignees(prev => [...prev, assignee]);
    }
  };

  const getDisplayName = (assignee?: Assignee) => {
    if (!assignee) return '';
    const firstName = assignee.firstName || '';
    const lastName = assignee.lastName || '';
    return `${firstName} ${lastName}`.trim() || '';
  };

  const handleDismiss = useCallback(() => {
    setTempSelectedAssignees(selectedAssignees);
    onClose?.();
    bottomSheetRef.current?.dismiss();
  }, [selectedAssignees, onClose]);

  const handleSave = () => {
    onAssigneesSelect(tempSelectedAssignees);
    onClose?.();
    bottomSheetRef.current?.dismiss();
  };

  useEffect(() => {
    if (isOpen) {
      bottomSheetRef.current?.present();
    }
  }, [isOpen]);

  return (
    <BottomSheetModal
      ref={bottomSheetRef}
      index={1}
      snapPoints={snapPoints}
      enablePanDownToClose
      style={styles.bottomSheet}
      onDismiss={handleDismiss}
      backdropComponent={(props) => (
        <BottomSheetBackdrop
          {...props}
          appearsOnIndex={0}
          disappearsOnIndex={-1}
          pressBehavior="close"
          onPress={handleDismiss}
        />
      )}
    >
      <View style={styles.bottomSheetContainer}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Select Assignee</Text>
        </View>

        <TextInput
          placeholder="Ex. name"
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={styles.searchInput}
          mode="flat"
          underlineColor="transparent"
          activeUnderlineColor="transparent"
          cursorColor="#000000"
        />
        
        <Text style={styles.infoText}>ⓘ You can only select one Assignee</Text>
        
        <BottomSheetScrollView contentContainerStyle={styles.scrollContent}>
          {loading ? (
            <Text style={styles.loadingText}>Loading...</Text>
          ) : filteredAssignees.length === 0 ? (
            <Text style={styles.noDataText}>No assignees found</Text>
          ) : (
            filteredAssignees.map((assignee: Assignee) => (
              <List.Item
                key={assignee.id}
                title={() => (
                  <View style={styles.titleContainer}>
                    <Text style={styles.listItemTitle} numberOfLines={3}>
                      {getAssigneeName(assignee)}
                    </Text>
                  </View>
                )}
                onTouchEnd={() => handleAssigneeSelect(assignee)}
                right={() => (
                  <CustomRadioButton
                    isSelected={tempSelectedAssignees.some(a => a.id === assignee.id)}
                  />
                )}
                style={styles.listItem}
              />
            ))
          )}
        </BottomSheetScrollView>

        <View style={styles.saveButtonContainer}>
          <ActionButton
            onPress={handleSave}
            label="Save"
            variant="primary"
            disabled={false}
          />
        </View>
      </View>
    </BottomSheetModal>
  );
}