import { Modal, View, Text } from 'react-native';
import { ResizeMode, Video } from 'expo-av';
import { Spacing, Typography,Colors, Borders } from '@/constants/DesignSystem';

interface SuccessModalProps {
  visible: boolean;
  onDismiss?: () => void;
  message?: string;
  videoSource?: any;
}

export function SuccessModal({
  visible,
  onDismiss,
  message = 'Success!',
}: SuccessModalProps) {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onDismiss}
    >
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.3)'
      }}>
        <View style={{
          backgroundColor: Colors.white,
          borderRadius: Borders.radius.md,
          padding: Spacing.md,
          alignItems: 'center',
          justifyContent: 'center',
          width: '75%',
          elevation: 4,
        }}>
          <Video
            source={require('@/assets/videos/Success.mp4')}
            style={{ width: 150, height: 150, borderRadius: Borders.radius.circle }}
            resizeMode={ResizeMode.COVER}
            shouldPlay
            isLooping={false}
            useNativeControls={false}
            isMuted={false}
            onError={e => console.log('Video error:', e)}
          />
          <Text
            style={{
                marginTop: Spacing.sm,
                fontSize: Typography.fontSize.lg,
                color: Colors.text.tertiary,
                fontWeight: Typography.fontWeight.semibold,
                textAlign: 'center',
                elevation: 1,
            }}
            accessibilityRole="alert"
          >
            {message}
          </Text>
        </View>
      </View>
    </Modal>
  );
} 