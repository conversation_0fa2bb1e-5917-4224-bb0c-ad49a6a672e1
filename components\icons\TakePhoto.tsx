import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const TakePhoto = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      fill="url(#Take_photo-1_svg__a)"
      fillRule="evenodd"
      d="M4.114 1.836a.4.4 0 0 1 .315-.157H7.57c.124 0 .24.058.315.157l1.06 1.414h1.375A1.18 1.18 0 0 1 11.5 4.429v4.714a1.18 1.18 0 0 1-1.179 1.178H1.68A1.18 1.18 0 0 1 .5 9.143V4.429A1.18 1.18 0 0 1 1.679 3.25h1.375zm3.82 4.723a1.934 1.934 0 1 1-3.868 0 1.934 1.934 0 0 1 3.868 0"
      clipRule="evenodd"
    />
    <Defs>
      <LinearGradient
        id="Take_photo-1_svg__a"
        x1={6}
        x2={6}
        y1={0.635}
        y2={12.308}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <Path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M11 4.462a.77.77 0 0 0-.77-.77H8.693L7.538 2.154H4.462L3.308 3.692H1.769a.77.77 0 0 0-.769.77v4.615a.77.77 0 0 0 .77.77h8.46a.77.77 0 0 0 .77-.77z"
    />
    <Path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M6 8.115a1.73 1.73 0 1 0 0-3.461 1.73 1.73 0 0 0 0 3.461"
    />
        </>
      )}
    </Svg>
  );
};
export default TakePhoto;
