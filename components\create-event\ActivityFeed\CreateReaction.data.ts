import { gql } from "@apollo/client";

export const CREATE_REACTION = gql`
mutation CreateReaction($input: ReactionInput!) {
  createReaction(input: $input) {
    ... on ReactionResponse {
      result {
        reaction {
          id
        }
      }
    }
  }
}
`
export const ADD_REACTION = gql`
mutation AddReactionToMessage($messageId: ID!, $reactionId: ID!) {
  addReactionToMessage(messageId: $messageId, reactionId: $reactionId) {
    ... on MessageResponse {
      result {
        message {
          id
        }
      }
    }
  }
}
`