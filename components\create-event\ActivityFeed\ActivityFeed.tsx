import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Keyboard, Image, Alert, Platform, Linking, ActivityIndicator } from 'react-native';
import { Activity } from '@/app/(home)/PartyDetails/NewPartyDetailsResponse.model';
import SecondaryButton from '@/components/Buttons/SecondayButton';
import ActivitiesContainer from './ActivityContainer';
import { useMutation } from '@apollo/client';
import { useToast } from '@/components/Toast';
import { POST_COMMENT } from '@/app/(home)/(tabs)/party-dashboard/partydetails.data';
import { useVenueInfo } from '@/hooks/useVenueInfo';
import { useNavigation } from '@react-navigation/native';
import { NavigationProp } from '@react-navigation/native';
import { useUserStore } from '@/app/auth/userStore';
import { EDIT_MESSAGE } from './ActivityFeed.data';
import { PartyDetailsRootList } from '../Navigation/PartyDetailsRootList';
import { Spacing, Colors, Typography, Borders, Icons } from '@/constants/DesignSystem';
import { LinearGradient } from 'expo-linear-gradient';
import AskAntsyModal from './AskAntsyModal';
import Markdown from 'react-native-markdown-display';
import { Pin, Cross } from '@/components/icons';
import * as Location from 'expo-location';

interface ActivityFeedProps {
  activities: Activity[];
  partyId: string;
  userRole: string;
  onLocation: (isEnabled: boolean) => Promise<void>;
  refreshMessages: (messageId: string, commentText: string) => void;
  venueAddress?: {
    coordinates?: {
      latitude: number;
      longitude: number;
    };
    address?: string;
    name?: string;
  };
}

const ActivityFeed: React.FC<ActivityFeedProps> = ({ activities, partyId, userRole, onLocation, refreshMessages, venueAddress }) => {
  const [displayedActivities, setDisplayedActivities] = useState<Activity[]>([]);
  const [showSystemMsgs, setSystemMsgs] = useState(false);
  const rotation = useRef(new Animated.Value(0)).current;
  const toast = useToast();
  const navigation = useNavigation<NavigationProp<PartyDetailsRootList>>();
  const [isEditorVisible, setEditorVisible] = useState(false);
  const [isCommentEdited, setCommentEdited] = useState(false);
  const [comment, setComment] = useState('');
  const [postComment] = useMutation(POST_COMMENT);
  const [editActivityMessage] = useMutation(EDIT_MESSAGE);
  const userData = useUserStore((state) => state.userData);
  const [isModalVisible, setModalVisible] = useState(false);
  const [queryResponse, setQueryResponse] = useState<{ data: string | null; loading: boolean; type?: string } | null>(null);
  const [isDirectionsLoading, setIsDirectionsLoading] = useState(false);
  const [isVenueResponseShort, setIsVenueResponseShort] = useState(false);
  const { fetchVenueInfo } = useVenueInfo(partyId);

  const handleCommentSubmission = (commentText: string, parentId: string | undefined, mediaId: string | null) => {
    setEditorVisible(false)
    postComment({
      variables: {
        input: {
          partyId: partyId,
          text: commentText,
          type: "USER",
          media: mediaId,
          parentMessageId: parentId ?? undefined,
        },
      }
    }).then(() => {
      Keyboard.dismiss();
    }).catch((error) => {
      const graphqlError = error?.graphQLErrors?.[0]?.message;
      const networkError = error?.networkError?.result?.errors?.[0]?.message;
      const errorMessage = graphqlError || networkError || 'Failed to update party. Please try again.';
      Keyboard.dismiss();
      toast.error(errorMessage);
    });
  };

  const handleLocationToggle = async (isEnabled: boolean) => {
    await onLocation(isEnabled);
  }

  useEffect(() => {
    if (activities) {
      const validActivities = [...activities];
      validActivities.sort((a, b) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

      if (showSystemMsgs) {
        setDisplayedActivities(validActivities.filter(activity => activity.type === "SYSTEM"));
      } else {
        setDisplayedActivities(validActivities);
      }
    }
  }, [activities, showSystemMsgs]);

  useEffect(() => {
    Animated.timing(rotation, {
      toValue: showSystemMsgs ? 1 : 0,
      duration: 100,
      useNativeDriver: true,
    }).start();
  }, [showSystemMsgs, rotation]);

  const spin = rotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '45deg'],
  });

  const handleEditComment = (id: string, message: string) => {
    navigation.navigate('KeyboardEditor', {
      props: {
        isVisible: true,
        parentId: id,
        toggleLocation: false,
        onSubmit: handleCommentSubmission,
        onClose: () => setEditorVisible(false),
        editedComment: message,
        handleEditedComment: handleEditedCommentSubmission,
        onLocation: handleLocationToggle,
      },
    });
  };

  const handleEditedCommentSubmission = async (commentText: string, parentId: string | undefined, mediaId: string | null) => {
    const activity = displayedActivities.find((message) => message.id === parentId);
    if (!activity) {
      return;
    }
    if (userRole !== 'MAIN_HOST' && userData?.id !== activity.createdBy.id) {
      return;
    }
    if (activity.type === 'SYSTEM') {
      return;
    }
    try {
      const input: { text?: string; media?: string | null } = {};
      if (commentText) {
        input.text = commentText;
      }
      if (mediaId) {
        input.media = mediaId;
      }

      if (Object.keys(input).length === 0) {
        return;
      }
      
      await editActivityMessage({
        variables: {
          editMessageId: activity.id,
          input
        }
      });
      refreshMessages(activity.id, commentText)
    } catch (error) {
      toast.error('Failed to edit comment');
    }
  }

  const handleAntsyPress = () => {
    setModalVisible(true);
  };

  const handleCloseQueryResponse = () => {
    setQueryResponse(null);
    setIsVenueResponseShort(false);
  };

  const handleEnableLocationServices = async () => {
    if (Platform.OS === 'ios') {
      await Linking.openURL('app-settings:');
    } else {
      await Linking.openSettings();
    }
  };

  const handleGetDirectionsPress = async () => {
    setIsDirectionsLoading(true);
    
    try {
      const coordinates = venueAddress?.coordinates;
      
      if (!coordinates?.latitude || !coordinates?.longitude) {
        Alert.alert(
          'Venue Not Decided',
          'Venue location is not yet decided. Please check back later.',
          [
            { text: 'OK', style: 'default' }
          ],
          { cancelable: true }
        );
        setIsDirectionsLoading(false);
        return;
      }

      const locationEnabled = await Location.hasServicesEnabledAsync();
      if (!locationEnabled) {
        Alert.alert(
          'Location Services Disabled',
          'Please enable location services to get directions.',
          [
            { text: 'OK', style: 'default' }
          ],
          { cancelable: true }
        );
        setIsDirectionsLoading(false);
        return;
      }

      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Denied',
          'Location permission is required to get directions.',
          [
            { text: 'OK', style: 'default' }
          ],
          { cancelable: true }
        );
        setIsDirectionsLoading(false);
        return;
      }

      const currentLocation = await Location.getCurrentPositionAsync({});
      const { latitude: currentLat, longitude: currentLng } = currentLocation.coords;

      const destination = `${coordinates.latitude},${coordinates.longitude}`;
      const origin = `${currentLat},${currentLng}`;
      
      const url = Platform.select({
        ios: `maps://app?saddr=${origin}&daddr=${destination}&dirflg=d`,
        android: `google.navigation:q=${destination}&mode=d`,
      }) || `https://maps.google.com/maps?saddr=${origin}&daddr=${destination}&dirflg=d`;

      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        const webUrl = `https://maps.google.com/maps?saddr=${origin}&daddr=${destination}&dirflg=d`;
        await Linking.openURL(webUrl);
      }
    } catch (error) {
      console.error('Error getting directions:', error);
      Alert.alert(
        'Error',
        'Unable to get directions. Please try again.',
        [
          { text: 'OK', style: 'default' }
        ],
        { cancelable: true }
      );
    } finally {
      setIsDirectionsLoading(false);
    }
  };

  const handleShowMoreVenueDetails = async () => {
    const isFullResponse = await fetchVenueInfo(false, setQueryResponse);
    if (isFullResponse) {
      setIsVenueResponseShort(false);
    }
  };

  const renderQueryResponse = () => {
    if (!queryResponse) return null;

    return (
      <LinearGradient
        colors={[Colors.tertiary, Colors.primary]}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={styles.queryResponseCard}
      >
        <View style={styles.queryHeaderSection}>
          <Image
            source={require('@/assets/images/AskAntsy/antsy.png')}
            style={styles.queryAntsyIcon}
            accessibilityLabel='Antsy mascot'
          />
          {queryResponse.loading && (
            <View style={styles.thinkingContainer}>
              <Text style={styles.thinkingText}>Thinking</Text>
              <Image
                source={require('@/assets/images/AskAntsy/thinking.gif')}
                style={styles.thinkingGif}
                resizeMode="contain"
              />
            </View>
          )}
          <View style={styles.spacer} />
          {!queryResponse.loading && (
            <TouchableOpacity style={styles.closeButton} onPress={handleCloseQueryResponse} accessibilityLabel='Close'>
              <Cross size={Icons.size.md} color={Colors.text.secondary} />
            </TouchableOpacity>
          )}
        </View>
        {!queryResponse.loading && (
          <View style={styles.queryResponseContent}>
            <Markdown>
              {queryResponse.data} 
            </Markdown>
            {queryResponse.type === 'venue' && isVenueResponseShort && (
                <Text onPress={handleShowMoreVenueDetails} style={styles.showMoreText}>Show More...</Text>
            )}
            {queryResponse.type === 'traffic' && (
              <TouchableOpacity 
                onPress={handleGetDirectionsPress}
                activeOpacity={0.7}
                style={[styles.directionsButton, isDirectionsLoading && styles.directionsButtonDisabled]}
                disabled={isDirectionsLoading}
              >
                {isDirectionsLoading ? (
                  <View style={styles.directionsButtonContent}>
                    <ActivityIndicator size="small" color={Colors.text.primary} />
                    <Text style={[styles.directionsButtonText, { marginLeft: Spacing.xs }]}>Opening...</Text>
                  </View>
                ) : (
                  <Text style={styles.directionsButtonText}>Get Directions</Text>
                )}
              </TouchableOpacity>
            )}
          </View>
        )}
      </LinearGradient>
    );
  };

  return (
    <>
      <View style={styles.container}>
        <View style={styles.row}>
          <View style={styles.leftContainer}>
            <Text style={[styles.subheading, styles.verticalPadding]}>Activity</Text>
            <TouchableOpacity
              style={styles.paddedButton}
              activeOpacity={1}
              onPress={() => setSystemMsgs(!showSystemMsgs)}>
              <Animated.View style={{ transform: [{ rotate: spin }] }}>
                <Pin size={Icons.size.md} color={Colors.primary} />
              </Animated.View>
            </TouchableOpacity>
          </View>
          <View style={styles.rightContainer}>
            <TouchableOpacity onPress={handleAntsyPress}>
              <Image source={require('@/assets/images/AskAntsy/antsy.png')} style={styles.antsyIcon} />
            </TouchableOpacity>
            <SecondaryButton
              onPress={() => {
                navigation.navigate('KeyboardEditor', {
                  props: {
                    isVisible: true,
                    parentId: undefined,
                    toggleLocation: false,
                    onSubmit: handleCommentSubmission,
                    onClose: () => setEditorVisible(false),
                    editedComment: undefined,
                    handleEditedComment: handleEditedCommentSubmission,
                    onLocation: handleLocationToggle,
                  },
                });
              }}
            />
          </View>
        </View>

        <View style={styles.spacerRow}>
          <Text style={[styles.callOut, styles.verticalPadding]}>
            {displayedActivities.length} updates
          </Text>
        </View>
        <View>
          {renderQueryResponse()}
          <ActivitiesContainer
            activities={displayedActivities}
            partyId={partyId}
            userRole={userRole}
            onLocation={handleLocationToggle}
            handleEdit={handleEditComment}
          />
        </View>
      </View>
      
      <AskAntsyModal
        isVisible={isModalVisible}
        onClose={() => setModalVisible(false)}
        partyId={partyId}
        onQueryResponse={(response) => {
          setQueryResponse(response);
          if (response.type === 'venue') {
            setIsVenueResponseShort(true); 
          }
        }}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'stretch',
    backgroundColor: Colors.background.primary,
  },
  paddedButton: {
    marginHorizontal: Spacing.sm,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.xs,
    backgroundColor: Colors.background.secondary,
    borderRadius: Borders.radius.pill,
    borderWidth: 1,
    borderColor: Colors.border.orange,
  },
  verticalPadding: {
    paddingVertical: Spacing.lg,
  },
  subheading: {
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.medium,
  },
  callOut: {
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    color: '##A4A4A4',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  spacerRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    marginTop: -Spacing.xxl,
  },
  antsyIcon: {
    width: 45,
    height: 45,
    marginTop: -Spacing.xs,
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  queryResponseCard: {
    flexDirection: 'column',
    borderRadius: Borders.radius.md,
    marginHorizontal: 0,
    marginTop: Spacing.md,
    marginBottom: Spacing.md,
    padding: Spacing.md,
    position: 'relative',
    width: '100%',
    minHeight: 100,
  },
  queryHeaderSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginBottom: Spacing.md,
    width: '100%',
    gap: Spacing.sm,
  },
  queryAntsyIcon: {
    width: 48,
    height: 48,
    resizeMode: 'contain',
  },
  queryResponseContent: {
    flex: 1,
    width: '100%',
  },
  thinkingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  thinkingText: {
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.tertiary,
    lineHeight: Typography.fontSize.sm,
  },
  thinkingGif: {
    width: 48,
    height: 48,
    marginLeft: -Spacing.sm,
    alignSelf: 'flex-start',
  },
  directionsButton: {
    backgroundColor: Colors.background.secondary,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: Borders.radius.pill,
    marginTop: Spacing.md,
    alignSelf: 'flex-start',
    borderWidth: Borders.width.thin,
    borderColor: Colors.primary,
  },
  directionsButtonText: {
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.primary,
    textAlign: 'center',
  },
  spacer: {
    flex: 1,
  },
  closeButton: {
    padding: Spacing.sm,
  },
  directionsButtonDisabled: {
    backgroundColor: Colors.background.secondary,
    borderColor: Colors.border.light,
    opacity: 0.6,
  },
  directionsButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  showMoreText: {
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.black,
    textDecorationLine: 'underline',
  },
});

export default ActivityFeed;