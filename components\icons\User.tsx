import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const User = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' ? (
        <Path
          fill={hasGradient ? "url(#User_svg__a)" : color}
          fillRule="evenodd"
          d="M3.462 3.038a2.538 2.538 0 1 1 5.076 0 2.538 2.538 0 0 1-5.076 0M6 6.423a4.23 4.23 0 0 0-4.23 4.23v.424c0 .233.189.423.422.423h7.616c.233 0 .423-.19.423-.423v-.423A4.23 4.23 0 0 0 6 6.424"
          clipRule="evenodd"
        />
      ) : (
        <Path
          stroke={color}
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={strokeWidth}
          d="M6.429 5.107a2.232 2.232 0 1 0 0-4.464 2.232 2.232 0 0 0 0 4.464M6.429 6.893A4.02 4.02 0 0 0 2.41 10.91v.446h8.035v-.446A4.02 4.02 0 0 0 6.43 6.893"
        />
      )}
      {variant === 'filled' && hasGradient && (
        <Defs>
          <LinearGradient
            id="User_svg__a"
            x1={6}
            x2={6}
            y1={-0.828}
            y2={14.029}
            gradientUnits="userSpaceOnUse"
          >
            <Stop stopColor={gradientStartColor} />
            <Stop offset={1} stopColor={gradientEndColor} />
          </LinearGradient>
        </Defs>
      )}
    </Svg>
  );
};
export default User;
