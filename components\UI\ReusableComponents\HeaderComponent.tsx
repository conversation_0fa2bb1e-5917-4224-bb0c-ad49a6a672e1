import React from 'react';
import { StyleSheet, View } from 'react-native';
import { ThemedText } from '@/components/UI/ThemedText';
import { Colors } from '@/constants/Colors';
import { useEventStore } from '@/store/eventStore';
import AppHeaderIcons from '@/components/AppHeaderIcons';
interface HeaderComponentProps {
  notificationCount?: number;
  isTaskScreen?: boolean;
}

export const HeaderComponent: React.FC<HeaderComponentProps> = ({
  notificationCount = 0,
  isTaskScreen = false,
}) => {
  const { eventName } = useEventStore();
  return (
    <View style={[styles.header]}>
      <ThemedText style={styles.headerTitle}
        numberOfLines={1}
        ellipsizeMode="tail"
        >
        {isTaskScreen ? eventName : 'My Events'}
      </ThemedText>
      <AppHeaderIcons />  
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 0,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.custom.searchBarActive,
    width: '73%',
  },
  headerIcons: {
    flexDirection: 'row',
  },
  iconButton: {
    padding: 8,
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: Colors.light.notificationBadge,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});