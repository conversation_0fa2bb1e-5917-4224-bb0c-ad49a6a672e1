import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '@/constants/Colors';
import { Star } from 'lucide-react-native';

interface MainFeature {
  name: string;
  value: string | null;
  icon_src: string;
}

interface RatingAggregate {
  rating_avg: number;
  review_count: number;
}

interface MainFeaturesProps {
  features: MainFeature[];
  ratingAggregate: RatingAggregate;
}

export function MainFeatures({ features, ratingAggregate }: MainFeaturesProps) {
  const displayRatingCount = (reviewCount: number) => {
    if (reviewCount > 1000) {
      return `${(reviewCount / 1000).toFixed(1)}K`;
    }
    return reviewCount;
  };

  return (
    <View style={styles.container}>
      <View style={styles.featuresColumn}>
        {features.map((feature, index) => (
          <View key={index} style={styles.featureItem}>
            <Text style={styles.featureText}>
              {feature.value ? `${feature.value} ${feature.name}` : feature.name}
            </Text>
            {index < features.length - 1 && <Text style={styles.dot}>, </Text>}
          </View>
        ))}
      </View>
      <View style={styles.ratingColumn}>
        <Star color={Colors.light.text} size={18} fill={Colors.light.text} />
        <Text style={styles.ratingText}>
          {ratingAggregate.rating_avg.toFixed(1)}
        </Text>
        <Text style={styles.reviewCount}>
          ({displayRatingCount(ratingAggregate.review_count)})
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  featuresColumn: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  ratingColumn: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 0,
    marginBottom: 4,
  },
  featureText: {
    fontSize: 14,
    color: Colors.light.text,
  },
  dot: {
    fontSize: 14,
    color: Colors.light.text,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginLeft: 4,
  },
  reviewCount: {
    fontSize: 14,
    color: Colors.light.text,
    marginLeft: 4,
  },
});