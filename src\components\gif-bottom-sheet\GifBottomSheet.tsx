import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { View, StyleSheet, ActivityIndicator, TouchableOpacity, TextInput } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { Text } from 'react-native-paper';
import { Image } from 'expo-image';
import { 
  fetchFeaturedTenorGifs, 
  searchTenorGifs, 
  TenorApiParams 
} from '../../api/tenor';
import type { TenorResult, TenorMediaFormat } from '../../api/tenor';
import {
  BottomSheetModal,
  BottomSheetView,
  BottomSheetBackdrop,
} from '@gorhom/bottom-sheet';
import { Search } from '@/components/icons';
import { Colors, Icons } from '@/constants/DesignSystem';

export interface GifBottomSheetProps {
  visible: boolean;
  onDismiss: () => void;
  onSelectGif: (gif: TenorMediaFormat) => void;
}

const FEATURED_QUERY_KEY = 'trending';
const DEBOUNCE_DELAY = 700;

const GifSheetContent = ({ onSelectGif }: { onSelectGif: (gif: TenorMediaFormat) => void; }) => {
  const textValueRef = useRef<string>('');
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [debouncedLocalInputValue, setDebouncedLocalInputValue] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [gifs, setGifs] = useState<TenorResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paginationNext, setPaginationNext] = useState<string | undefined>(undefined);
  const [currentListSource, setCurrentListSource] = useState<'featured' | 'search'>('featured');

  const inputRef = useRef<TextInput>(null);

  const loadGifs = useCallback(async (fetchType: 'featured' | 'search', params: TenorApiParams, loadMore = false) => {
    setIsLoading(true);
    setError(null);
    const queryForCall = params.q?.trim() || (fetchType === 'featured' ? FEATURED_QUERY_KEY : '');

    if (!loadMore) {
      setGifs([]);
      setPaginationNext(undefined);
      setSearchQuery(queryForCall);
    }
    setCurrentListSource(fetchType);

    try {
      let response;
      if (fetchType === 'featured') {
        response = await fetchFeaturedTenorGifs({ ...params, q: queryForCall });
      } else {
        if (!queryForCall) {
            console.log("Search query is empty for 'search' type, not calling API, clearing results.");
            setGifs([]);
            setPaginationNext(undefined);
            if (!loadMore) setSearchQuery('');
            setIsLoading(false);
            return;
        }
        response = await searchTenorGifs({ ...params, q: queryForCall });
      }
      setGifs(prevGifs => loadMore ? [...prevGifs, ...response.results] : response.results);
      setPaginationNext(response.next);
    } catch (e: any) {
      setError(e.message || 'Failed to load GIFs.');
      if (!loadMore) setGifs([]);
    }
    setIsLoading(false);
  }, []);

  useEffect(() => {
    loadGifs('featured', { q: FEATURED_QUERY_KEY, limit: 50 });
  }, [loadGifs]);

  useEffect(() => {
    const trimmedDebouncedVal = debouncedLocalInputValue.trim();

    if (trimmedDebouncedVal !== '') {
      if (trimmedDebouncedVal !== searchQuery || currentListSource !== 'search') {
        loadGifs('search', { q: trimmedDebouncedVal, limit: 50 });
      }
    } else { 
      if (currentListSource !== 'featured' || searchQuery !== FEATURED_QUERY_KEY) {
         loadGifs('featured', { q: FEATURED_QUERY_KEY, limit: 50 });
      }
    }
  }, [debouncedLocalInputValue, searchQuery, currentListSource, loadGifs]);

  const handleTextChange = useCallback((text: string) => {
    textValueRef.current = text;
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    debounceTimeoutRef.current = setTimeout(() => {
      setDebouncedLocalInputValue(textValueRef.current);
    }, DEBOUNCE_DELAY);
  }, []);

  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const handleLoadMore = () => {
    if (!isLoading && paginationNext) {
      const params: TenorApiParams = { 
        q: currentListSource === 'search' ? searchQuery : FEATURED_QUERY_KEY, 
        limit: 50, 
        pos: paginationNext 
      };
      loadGifs(currentListSource, params, true);
    }
  };

  const handleGifSelectAndDismiss = useCallback((gif: TenorMediaFormat) => {
    onSelectGif(gif);
  }, [onSelectGif]);
  
  const renderGifItem = useCallback(({ item }: { item: TenorResult }) => (
    <TouchableOpacity onPress={() => handleGifSelectAndDismiss(item.media_formats.gif)} style={styles.gifItemContainer}>
      <Image
        source={{ uri: item.media_formats.tinygif.url }}
        style={styles.gifImage}
        contentFit="cover"
        transition={200}
      />
    </TouchableOpacity>
  ), [handleGifSelectAndDismiss]);

  return (
    <BottomSheetView style={[styles.contentContainer, { backgroundColor: 'white' }]}>
      <View style={[styles.searchContainer, { backgroundColor: '#F4F4F4' }]}>
        <Search size={Icons.size.md} color={Colors.text.secondary} />
        <TextInput
          ref={inputRef}
          placeholder="Search for gifs.." 
          placeholderTextColor={Colors.text.secondary} 
          onChangeText={handleTextChange}
          defaultValue={textValueRef.current}
          style={styles.searchInput} 
          selectionColor="#333333"
          cursorColor="#777777"
          autoFocus={false}
        />
      </View>
      
      <View style={{ flex: 1 }}>
        {isLoading && gifs.length === 0 && <ActivityIndicator animating size="large" style={styles.loader} />}
        {error && <Text style={styles.errorText}>{error}</Text>}
        
        {!isLoading && !error && gifs.length === 0 && (
          <View style={styles.emptyStateContainer}>
            <Text style={styles.emptyStateText}>
              {searchQuery.trim() === '' && currentListSource === 'featured' ? 'No featured GIFs found. Try searching!' : 
               searchQuery.trim() === '' && currentListSource === 'search' ? 'Type to search for GIFs.':
               `No GIFs found for "${searchQuery}".`}
            </Text>
          </View>
        )}

        {(!error && (gifs.length > 0 || isLoading)) && (
          <FlatList
            data={gifs}
            renderItem={renderGifItem}
            keyExtractor={(item) => item.id}
            numColumns={4}
            style={styles.gifList} 
            contentContainerStyle={styles.gifListContent}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.5}
            ListFooterComponent={isLoading && gifs.length > 0 ? <ActivityIndicator animating /> : null}
          />
        )}
      </View>
    </BottomSheetView>
  );
};

export function GifBottomSheet({ visible, onDismiss, onSelectGif }: GifBottomSheetProps) {
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['90%'], []);

  useEffect(() => {
    if (visible) {
      const timer = setTimeout(() => {
        bottomSheetModalRef.current?.present();
      }, 200);
      return () => clearTimeout(timer);
    } else {
      bottomSheetModalRef.current?.dismiss();
    }
  }, [visible]);

  const handleSheetChanges = useCallback((index: number) => {
    if (index === -1) {
      onDismiss(); 
    }
  }, [onDismiss]);
  
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1} 
        appearsOnIndex={0}    
        pressBehavior="close" 
      />
    ),
    []
  );

  return (
    <BottomSheetModal
      ref={bottomSheetModalRef}
      index={0} 
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      backdropComponent={renderBackdrop}
      handleIndicatorStyle={{ backgroundColor: '#777777' }} 
      backgroundStyle={{ backgroundColor: 'white' }} 
    >
      <GifSheetContent onSelectGif={onSelectGif} />
    </BottomSheetModal>
  );
}

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  searchContainer: { 
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    height: 44, 
    marginBottom: 12, 
    borderRadius: 10, 
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    height: '100%',
    textAlignVertical: 'center',
    color: "#333333",
    marginLeft: 8,
  },
  loader: {
    marginVertical: 20,
    alignSelf: 'center',
  },
  errorText: {
    textAlign: 'center',
    color: 'red',
    marginVertical: 10,
  },
  gifList: {
    flex: 1,
  },
  gifListContent: {
    paddingBottom: 10, 
  },
  gifItemContainer: {
    flex: 1/4, 
    aspectRatio: 1, 
    margin: 2, 
    overflow: 'hidden',
    borderRadius: 6, 
  },
  gifImage: {
    width: '100%',
    height: '100%',
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 50,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#888',
    textAlign: 'center',
  },
}); 