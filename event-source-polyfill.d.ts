declare module 'event-source-polyfill' {
    export class EventSourcePolyfill {
      constructor(url: string, eventSourceInitDict?: EventSourceInit);
  
      onopen: ((this: EventSource, ev: MessageEvent) => any) | null;
      onmessage: ((this: EventSource, ev: MessageEvent) => any) | null;
      onerror: ((this: EventSource, ev: MessageEvent) => any) | null;
  
      close(): void;
    }
  
    interface EventSourceInit {
      withCredentials?: boolean;
      headers?: Record<string, string>;
      proxy?: string;
      https?: Record<string, any>;
    }
  }