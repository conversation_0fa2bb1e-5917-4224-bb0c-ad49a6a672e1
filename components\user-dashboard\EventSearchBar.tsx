import React, { useState } from 'react';
import { StyleSheet, View, TextInput, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '@/constants/Colors';
import { SearchBarNames } from '@/constants/displayNames';

interface EventSearchBarProps {
  onSearch?: (text: string) => void;
  placeholder?: string;
}

const INPUT_HEIGHT = Platform.select({
  ios: 36,
  android: 40,
  default: 38
});

export const EventSearchBar: React.FC<EventSearchBarProps> = ({
  onSearch,
  placeholder = SearchBarNames.USER_DASHBOARD_SEARCH_PLACEHOLDER
}) => {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <View style={styles.stickySearchContainer}>
      <View style={[
        styles.searchContainer,
        isFocused ? styles.searchContainerActive : styles.searchContainerInactive
      ]}>
        <Ionicons name="search" size={20} color={Colors.light.text} style={styles.searchIcon} />
        <TextInput 
          style={styles.searchInput}
          placeholder={placeholder}
          placeholderTextColor={Colors.light.text}
          onChangeText={onSearch}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          returnKeyType="search"
          enablesReturnKeyAutomatically
          textAlignVertical="center"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  stickySearchContainer: {
    backgroundColor: Colors.light.background,
    zIndex: 1,
    paddingBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    padding: 6,
    paddingVertical: Platform.select({ ios: 4, android: 2 }),
    borderRadius: 24,
    marginHorizontal: 16,
    borderWidth: 1,
    height: INPUT_HEIGHT,
  },
  searchIcon: {
    marginTop: Platform.select({ ios: 1, android: 0 }),
  },
  searchContainerActive: {
    borderColor: Colors.custom.searchBarActive,
  },
  searchContainerInactive: {
    borderColor: '#E2E8F0',
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 12,
    fontWeight: Platform.select({ ios: '200', android: '100' }),
    lineHeight: 12,
    height: '100%',
    textAlignVertical: 'center',
    ...Platform.select({
      android: {
        includeFontPadding: false,
        paddingVertical: 4,
      },
      ios: {
        paddingVertical: 3,
      }
    }),
  },
});