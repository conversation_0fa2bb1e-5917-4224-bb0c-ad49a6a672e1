import React from 'react';
import { Text, View, Image } from 'react-native';
import MapView, { <PERSON><PERSON>, Callout, MapViewProps } from 'react-native-maps';
import Animated from "react-native-reanimated";
import { LiveLocation } from './MapLiveLocations.model';
import { MapLocationProps } from '@/components/map-weather/MapLocation';
import { Location as LocationIcon } from '@/components/icons';
import { Colors, Icons } from '@/constants/DesignSystem';

interface IOSMapProps extends MapViewProps {
    mapViewRef: React.RefObject<MapView>;
    mapProps: MapLocationProps;
    liveLocations: LiveLocation[] | undefined;
    isWithinTime: boolean;
    onRegionChange?: () => void;
    onRegionChangeComplete?: () => void;
    styles: any;
}

const IOSMap: React.FC<IOSMapProps> = ({
    mapViewRef,
    mapProps,
    liveLocations,
    isWithinTime,
    styles,
    ...rest
}) => {
    return (
        <MapView
            ref={mapViewRef}
            style={styles.map}
            initialRegion={{
                latitude: mapProps.party.latitude,
                longitude: mapProps.party.longitude,
                latitudeDelta: 0.0922,
                longitudeDelta: 0.0421,
            }}
            {...rest}
        >
            <Marker
                coordinate={{
                    latitude: mapProps.party.latitude,
                    longitude: mapProps.party.longitude,
                }}
                anchor={{ x: 0.5, y: 1 }}
                calloutAnchor={{ x: 0.5, y: 0 }}
            >
                <Animated.View style={styles.markerView}>
                    <LocationIcon size={Icons.size.xl} color={Colors.primary} />
                    <Text style={styles.markerText}>Venue Location</Text>
                </Animated.View>
            </Marker>
            {isWithinTime && liveLocations && liveLocations.map((location, index) => (
                <Marker
                    key={index}
                    coordinate={{ latitude: location.coordinates.latitude ?? 0.0, longitude: location.coordinates.longitude ?? 0.0 }}
                    anchor={{ x: 0.5, y: 1 }}
                    calloutAnchor={{ x: 0.5, y: 0 }}
                >
                    <View style={[styles.customMarker]}>
                        {location.guest.user.profilePicture ? (
                            <Image source={{ uri: location.guest.user.profilePicture }} style={styles.profilePic} />
                        ) : (
                            <View style={styles.noPic}>
                                <Text style={styles.initial}>{location.guest.user.firstName[0]}</Text>
                            </View>
                        )}
                        <Text style={styles.eta}>{`ETA: ${location.ETA || 'N/A'}`}</Text>
                    </View>
                    <Callout>
                        <Text>{location.guest.user.firstName}</Text>
                    </Callout>
                </Marker>
            ))}
        </MapView>
    );
};

export default IOSMap; 