import { FastPartyClient } from '@/commons/apollo-client';
import { ApolloClient, InMemoryCache, HttpLink } from '@apollo/client';

describe('FastPartyClient', () => {
    it('should be an instance of ApolloClient', () => {
        expect(FastPartyClient).toBeInstanceOf(ApolloClient);
    });

    it('should have the correct link', () => {
        const httpLink = FastPartyClient.link as HttpLink;
        expect(httpLink.options.uri).toBe(process.env.EXPO_PUBLIC_API_URL);
    });

    it('should have an instance of InMemoryCache', () => {
        expect(FastPartyClient.cache).toBeInstanceOf(InMemoryCache);
    });
});