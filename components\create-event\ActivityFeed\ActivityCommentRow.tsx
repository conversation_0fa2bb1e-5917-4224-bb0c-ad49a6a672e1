import React, { useState } from 'react';
import { View, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { Activity } from '@/app/(home)/PartyDetails/NewPartyDetailsResponse.model';
import InitialsAvatar from '@/components/Avatar/InitialsAvatar';
import { ProfilePicture } from '@/app/Home/Invites/ProfilePicture';
import CommentAndMedia from './CommentAndMedia';
import ant from '../../../assets/images/ant.png';

const ActivityCommentRow: React.FC<{
  activity: Activity;
  canDelete: boolean;
  partyId: string;
  onAvatarLayout?: (activityId: string, x: number, y: number) => void; // New callback prop
}> = ({ activity, canDelete, partyId, onAvatarLayout }) => {
  const avatar = activity.type === 'SYSTEM' ? (
    <View
      style={styles.antContainer}
      onLayout={(event) => {
        // Send back coordinates from SYSTEM avatar
        if (onAvatarLayout) {
          const { x, y } = event.nativeEvent.layout;
          onAvatarLayout(activity.id, x, y);
        }
      }}
    >
      <Image source={ant} style={styles.antImage} />
    </View>
  ) : activity.createdBy?.profilePicture ? (
    <View
      onLayout={(event) => {
        // Send back coordinates from ProfilePicture avatar
        if (onAvatarLayout) {
          const { x, y } = event.nativeEvent.layout;
          onAvatarLayout(activity.id, x, y);
        }
      }}
    >
      <ProfilePicture url={activity.createdBy.profilePicture || ''} />
    </View>
  ) : (
    <View
      onLayout={(event) => {
        // Send back coordinates from InitialsAvatar
        if (onAvatarLayout) {
          const { x, y } = event.nativeEvent.layout;
          onAvatarLayout(activity.id, x, y);
        }
      }}
    >
      <InitialsAvatar
        firstName={activity.createdBy?.firstName ?? ''}
        lastName={activity.createdBy?.lastName ?? ''}
      />
    </View>
  );

  return (
    <TouchableOpacity style={styles.container} activeOpacity={1}>
      <View style={styles.container}>
        <View style={styles.row}>
          {avatar}
          <CommentAndMedia activity={activity} partyId={partyId} />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  antContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'orange',
  },
  antImage: {
    width: 46,
    height: 46,
    borderRadius: 23,
  },
});

export default ActivityCommentRow;
