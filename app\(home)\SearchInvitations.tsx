import React, { useMemo, useCallback, useEffect } from 'react';
import { StyleSheet, View, Image, FlatList, TouchableOpacity } from 'react-native';
import { Text, Searchbar } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useInvitationStore } from '@/store/invitationStore';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { Colors, Typography, Spacing, Borders, Icons } from '@/constants/DesignSystem';
import { useEventStore } from '@/store/eventStore';
import { useQuery } from '@apollo/client';
import { GET_EVENTS } from '@/components/HomePage/HomePage.data';
import { getRandomDefaultImage } from '@/constants/HomePageDefaultImages';
import { Pressable } from 'react-native';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { HomeRootStackList } from '@/app/Home/HomeNavigation';
import { useDebounce } from '@/app/hooks/useDebounce';
import { DueDate, Location, Search } from '@/components/icons';
import { useUserStore } from '@/app/auth/userStore';

type NavigationProp = NativeStackNavigationProp<HomeRootStackList>;

interface Event {
  id: string;
  parties: Array<{
    id: string;
    name: string;
    time: string;
    invitation?: {
      media?: Array<{
        url: string;
      }>;
    };
    serviceLocation?: {
      city: string;
      state?: string;
    };
    guests?: Array<{
      user: {
        id: string;
      };
    }>;
    rsvps?: Array<{
      id: string;
      status?: string;
      guest?: {
        user?: {
          id: string;
        };
      };
    }>;
  }>;

  name: string;
  status: string;
  mainHost: {
    userId: {
      id: string;
      firstName: string;
      lastName: string;
    };
  };
  startDate: string;
}

interface ProcessedEvent {
  id: string;
  originalId: string;
  title: string;
  hostedBy: string;
  image: { uri: string } | number;
  date: string;
  location: string;
  isCurrentUserHost: boolean;
  currentUserRsvpStatus?: string;
}

export default function SearchInvitations() {
  const navigation = useNavigation<NavigationProp>();
  const { searchQuery, setSearchQuery } = useInvitationStore();
  const [page, setPage] = React.useState(1);
  const [isLoadingMore, setIsLoadingMore] = React.useState(false);
  const pageSize = 10;
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  // Single query for all events with pagination and search
  const { data, loading, fetchMore } = useQuery(GET_EVENTS, {
    variables: {
      filter: {
        name: debouncedSearchQuery || undefined,
        sortBy: "startDate",
        sortOrder: "DESC"
      },
      pagination: {
        skip: 0,
        limit: pageSize
      }
    }
  });

  // Reset page when search query changes
  useEffect(() => {
    setPage(1);
  }, [debouncedSearchQuery]);

  // Clear search when screen loses focus
  useFocusEffect(
    React.useCallback(() => {
      return () => {
        setSearchQuery('');
        setPage(1);
      };
    }, [setSearchQuery])
  );

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };
  const userData = useUserStore((state) => state.userData);
  const processEvents = (events: Event[]): ProcessedEvent[] => {
    if (!events) return [];
    return events.map((event, index) => {
      const party = event.parties[0];
      const mediaUrl = party?.invitation?.media?.[0]?.url;
      const serviceLocation = party?.serviceLocation;
      const locationString = serviceLocation
        ? `${serviceLocation.city}${serviceLocation.state ? `, ${serviceLocation.state}` : ''}`
        : 'Location TBD';
        const isCurrentUserGuest = party?.guests?.some((guest: any) => guest.user.id === userData?.id);
        const rsvpStatus = party?.rsvps?.find((rsvp: any) => rsvp?.guest?.user?.id === userData?.id)?.status?.toLowerCase();
      return {
        id: `${event.id}-${index}`,
        originalId: party?.id,
        title: party?.name || event.name,
        hostedBy: `${event.mainHost.userId.firstName} ${event.mainHost.userId.lastName}`,
        image: mediaUrl ? { uri: mediaUrl } : getRandomDefaultImage(),
        date: party?.time || event.startDate,
        location: locationString,
        isCurrentUserHost: !isCurrentUserGuest,
        currentUserRsvpStatus: rsvpStatus
      };
    });
  };

  const allEvents = useMemo(() => {
    const events = data?.getUserEvents?.result?.events || [];
    return processEvents(events);
  }, [data]);

  const totalItems = data?.getUserEvents?.pagination?.totalItems || 0;
  const hasMoreData = allEvents.length < totalItems;

  const setSelectedEventId = useEventStore((state) => state.setSelectedEventId);


  const handleNavigateToPartyDetails = (partyId: string, isHost: boolean, rsvpStatus?: string , fromSearch?: boolean) => {
    if (!partyId) return;

    const selectedEvent = allEvents.find(event => event.originalId === partyId);

    if (selectedEvent) {
      setSelectedEventId(selectedEvent.id);
    }

    navigation.navigate('PartyDetails', {
      screen: 'NewPartyDetailsScreen',
      params: { partyId, isHosting: isHost, rsvp: rsvpStatus || '', fromSearch: fromSearch }
    });
  };

  const renderInvitation = ({ item: invitation }: { item: ProcessedEvent }) => (
    <Pressable
      style={styles.eventCard}
      onPress={() => {
        const isHost = invitation.isCurrentUserHost;
        const rsvpStatus = invitation.currentUserRsvpStatus?.toLowerCase();
        handleNavigateToPartyDetails(invitation.originalId, isHost, rsvpStatus, true);
      }}
    >
      <View style={styles.cardContent}>
        <Image 
          source={invitation.image} 
          style={styles.eventImage}
          resizeMode="cover"
        />
        <View style={styles.eventDetails}>
          <View style={styles.infoRow}>
            <DueDate size={Icons.size.md} color={Colors.text.tertiary} />
            <Text variant="bodySmall" style={styles.infoText}>
              {formatDate(invitation.date)}
            </Text>
          </View>
          <Text
            variant="titleMedium"
            style={styles.eventTitle}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {invitation.title}
          </Text>
          <View style={styles.infoRow}>
            <Location size={Icons.size.md} color={Colors.text.tertiary} />
            <Text variant="bodySmall" style={styles.infoText}>
              {invitation.location}
            </Text>
          </View>
          <Text variant="bodySmall" style={styles.hostedBy}>
            Hosted by <Text style={styles.hostName}>{invitation.hostedBy}</Text>
          </Text>
        </View>
      </View>
    </Pressable>
  );

  const handleLoadMore = useCallback(() => {
    if (loading || !hasMoreData || isLoadingMore) return;

    const nextPage = page + 1;
    setIsLoadingMore(true);
    
    fetchMore({
      variables: {
        filter: {
          name: debouncedSearchQuery || undefined,
          sortBy: "startDate",
          sortOrder: "DESC"
        },
        pagination: {
          skip: (nextPage - 1) * pageSize,
          limit: pageSize
        }
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;

        // Create a Map to store unique events by their ID
        const uniqueEvents = new Map();
        
        // Add existing events to the Map
        prev.getUserEvents.result.events.forEach((event: Event) => {
          uniqueEvents.set(event.id, event);
        });
        
        // Add new events to the Map (this will overwrite any duplicates)
        fetchMoreResult.getUserEvents.result.events.forEach((event: Event) => {
          uniqueEvents.set(event.id, event);
        });

        setIsLoadingMore(false);
        setPage(nextPage);

        return {
          getUserEvents: {
            ...fetchMoreResult.getUserEvents,
            result: {
              ...fetchMoreResult.getUserEvents.result,
              events: Array.from(uniqueEvents.values())
            }
          }
        };
      }
    }).catch(() => {
      setIsLoadingMore(false);
    });
  }, [page, loading, fetchMore, debouncedSearchQuery, hasMoreData, isLoadingMore]);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Searchbar
          placeholder="Find an Event..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          elevation={0}
          icon={() => <Search size={Icons.size.md} color={Colors.text.tertiary} />}
        />
      </View>

      {loading && page === 1 ? (
        <FastPartyActivityIndicator />
      ) : (
        <>
          {allEvents.length > 0 && (
            <View style={styles.countContainer}>
              {(loading || isLoadingMore) && (
                <View style={styles.countLoader}>
                  <FastPartyActivityIndicator size="small" />
                </View>
              )}
              <Text style={styles.countText}>
                Showing {allEvents.length} of {totalItems} events
              </Text>
            </View>
          )}
          <FlatList
            data={allEvents}
            renderItem={renderInvitation}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.content}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.5}
            ListEmptyComponent={
              <View style={styles.emptyState}>
                <Text style={styles.emptyStateText}>
                  {searchQuery ? 'No events found matching your search' : 'No events to display'}
                </Text>
              </View>
            }
            ListFooterComponent={
              isLoadingMore ? (
                <View style={styles.loadingMore}>
                  <FastPartyActivityIndicator />
                  <Text style={styles.loadingMoreText}>Loading more events...</Text>
                </View>
              ) : null
            }
          />
        </>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
    marginTop: -20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 0,
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.sm,
  },
  searchBar: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
    borderRadius: Borders.radius.md,
  },
  searchInput: {
    fontSize: Typography.fontSize.md,
    color: Colors.text.tertiary,
  },
  content: {
    padding: Spacing.lg,
  },
  eventCard: {
    backgroundColor: Colors.background.secondary,
    borderRadius: Borders.radius.lg,
    marginBottom: Spacing.md,
    overflow: 'hidden',
  },
  cardContent: {
    flexDirection: 'row',
    padding: Spacing.lg,
  },
  eventImage: {
    width: 100,
    height: 140,
    borderRadius: Borders.radius.md,
  },
  eventDetails: {
    flex: 1,
    marginLeft: Spacing.lg,
    justifyContent: 'center',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  infoText: {
    marginLeft: Spacing.xs,
    color: Colors.text.tertiary,
  },
  eventTitle: {
    marginBottom: Spacing.xs,
    color: Colors.text.primary,
  },
  hostedBy: {
    color: Colors.text.tertiary,
  },
  hostName: {
    color: Colors.text.primary,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  emptyStateText: {
    color: Colors.text.tertiary,
    textAlign: 'center',
  },
  loadingMore: {
    paddingVertical: Spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingMoreText: {
    marginTop: Spacing.sm,
    color: Colors.text.tertiary,
    fontSize: Typography.fontSize.sm,
  },
  countContainer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    backgroundColor: Colors.background.secondary,
    flexDirection: 'row',
    alignItems: 'center',
  },
  countLoader: {
    marginRight: Spacing.sm,
  },
  countText: {
    color: Colors.text.tertiary,
    fontSize: Typography.fontSize.sm,
  },
});
