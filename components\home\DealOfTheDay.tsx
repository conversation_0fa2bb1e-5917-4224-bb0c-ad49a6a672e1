import React from 'react';
import { StyleSheet, View, Pressable } from 'react-native';
import { Text } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

interface DealOfTheDayProps {
  timeRemaining: string;
  onViewAll?: () => void;
  tagSlug: string;
}

export function DealOfTheDay({ timeRemaining, onViewAll, tagSlug }: DealOfTheDayProps) {
  const router = useRouter();


  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>Deal of the Day</Text>
          <View style={styles.timeContainer}>
            <Ionicons name="time-outline" size={16} color="white" />
            <Text style={styles.timeText}>{timeRemaining} remaining</Text>
          </View>
        </View>
        <Pressable 
          onPress={() => router.push(`/(home)/ProductListView/deal-of-the-day?tagSlug=${tagSlug}` as any)}
        >
          <Text style={styles.viewAll}>View All</Text>
        </Pressable>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#768DEC',
    padding: 16,
    borderRadius: 12,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginBottom: 4,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  timeText: {
    fontSize: 12,
    color: 'white',
  },
  buttonContainer: {
    minWidth: 'auto',
  },
  viewAllButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderColor: 'white',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'none',
    color: 'white',
  },
  arrowIcon: {
    marginLeft: 4,
  },
  viewAll: {
    fontSize: 14,
    color: 'white',
    fontWeight: '500',
  },
}); 