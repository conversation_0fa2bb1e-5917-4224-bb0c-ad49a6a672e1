// Update interfaces to match the API response structure
export interface TabConfig {
    name: string;
    iconSrc?: string;
  }
  
  export interface DashboardTabs {
    home?: TabConfig;
    myEvents?: TabConfig;
    publicEvents?: TabConfig;
    events_db?: TabConfig;
    tasks?: TabConfig;
    guests?: TabConfig;
    apps?: TabConfig;
    messages?: TabConfig;
    [key: string]: TabConfig | undefined;
  }
  
  export interface NavBarConfig {
    tabs: DashboardTabs;
  }
  
  export interface NavBarsConfig {
    userDashboard?: NavBarConfig;
    hostEventDashboard?: NavBarConfig;
    [key: string]: NavBarConfig | undefined;
  }