import { ScrollView, RefreshControl, Pressable, View, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery} from '@apollo/client';
import { Header } from '@/components/HomePage/Components/Header';
import { Filters } from '@/components/HomePage/Components/Filters';
import { EventsList } from '@/components/HomePage/Components/EventsList';
import { CirclesList } from '@/components/HomePage/Components/CirclesList';
import { styles } from '@/components/HomePage/homepage.styles';
import { GET_CIRCLES, GET_EVENT_COUNTS, GET_EVENTS } from '@/components/HomePage/HomePage.data';
import { NavigationProp, useFocusEffect, useNavigation } from '@react-navigation/native';
import { getRandomDefaultImage } from '@/constants/HomePageDefaultImages';
import { useInvitationStore } from '@/store/invitationStore';
import { useEventRefreshStore } from '@/store/eventRefreshStore';
import { NotificationProvider } from '@/components/NotificationProvider';
import * as Linking from 'expo-linking';
import { HomeRootStackList } from '@/app/Home/HomeNavigation';
import { Colors } from '@/constants/DesignSystem';
import { useUserStore } from '@/app/auth/userStore';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';
import { Text } from 'react-native-paper';

//Home page
export default function HomePage() {
  const userData = useUserStore((state) => state.userData);
  const [selectedFilter, setSelectedFilter] = useState<'next' | 'host' | 'guest' | 'past'>('next');
  const [refreshing, setRefreshing] = useState(false);
  const { needsRefresh, setNeedsRefresh } = useEventRefreshStore();
  const [isRefetching, setIsRefetching] = useState(false);
  const setInvitations = useInvitationStore(state => state.setInvitations);

  const fullName = userData ? `${userData.firstName}` : 'Guest';

  // Get event counts from server
  const {
    data: countsData,
    loading: countsLoading,
    error: countsError,
    refetch: refetchCounts
  } = useQuery(GET_EVENT_COUNTS, {
    fetchPolicy: 'network-only',
    notifyOnNetworkStatusChange: true,
  });

  // Get filtered events based on selected filter
  const getFilterVariables = () => {
    switch (selectedFilter) {
      case 'next':
        return { presentAndUpcoming: true };
      case 'past':
        return { presentAndUpcoming: false };
      case 'host':
        return { userType: 'HOST' };
      case 'guest':
        return { userType: 'GUEST' };
      default:
        return { presentAndUpcoming: true };
    }
  };

  const {
    data,
    loading,
    error,
    refetch: refetchEvents
  } = useQuery(GET_EVENTS, {
    fetchPolicy: 'network-only',
    notifyOnNetworkStatusChange: true,
    variables: {
      filter: getFilterVariables(),
      pagination: {
        skip: 0,
        limit: 20,
      }
    }
  });

  const {
    data: circlesData,
    loading: circlesLoading,
    error: circlesError,
    refetch: refetchCircles,
    fetchMore
  } = useQuery(GET_CIRCLES, {
    fetchPolicy: 'network-only',
    notifyOnNetworkStatusChange: true,
    variables: {
      pagination: {
        skip: 0,
        limit: 10,
      }
    }
  });

  const navigation = useNavigation<NavigationProp<HomeRootStackList>>();

  useEffect(() => {
    // Handle cold start (app closed)
    const handleInitialURL = async () => {
      try {
        const initialUrl = await Linking.getInitialURL();
        if (initialUrl) {
          const { path } = Linking.parse(initialUrl);
          const partyId = path?.split('/').pop();
          if (partyId) {
            navigation.navigate('PartyDetails', {
              screen: 'NewPartyDetailsScreen',
              params: { partyId: partyId }
            });
          }
        }
      } catch (error) {
        // Keep this console.error as it's for an exception
        console.error('Error handling initial URL:', error);
      }
    };

    // Handle warm start (app in background)
    const handleURL = ({ url }: { url: string }) => {
      const { path } = Linking.parse(url);
      const partyId = path?.split('/').pop();
      if (partyId) {
        navigation.navigate('PartyDetails', {
          screen: 'NewPartyDetailsScreen',
          params: { partyId: partyId }
        });
      }
    };

    // Set up listeners
    handleInitialURL();
    const subscription = Linking.addEventListener('url', handleURL);

    return () => {
      subscription.remove();
    };
  }, [navigation]);

  // Function to handle data refetching
  const handleRefetch = useCallback(async () => {
    setIsRefetching(true);
    try {
      await Promise.all([refetchCounts(), refetchCircles()]);
    } catch (error) {
      // Keep this console.error as it's for an exception
      console.error('Error refetching data:', error);
    } finally {
      setIsRefetching(false);
      setNeedsRefresh(false);
    }
  }, [refetchCounts, refetchCircles, setNeedsRefresh]);

  const handleFetchMore = async () => {
    const currentPage = circlesData?.getEventGroups?.pagination?.currentPage;
    const totalPages = circlesData?.getEventGroups?.pagination?.totalPages;
    if (!circlesLoading && currentPage < totalPages) {
      try {
        await fetchMore({
          variables: {
            pagination: {
              skip: currentPage * 10,
              limit: 10,
            }
          },
          updateQuery: (prevResult, { fetchMoreResult }) => {
            if (!fetchMoreResult) return prevResult;

            return {
              getEventGroups: {
                ...fetchMoreResult.getEventGroups,
                result: {
                  ...prevResult.getEventGroups.result,
                  eventGroups: [
                    ...prevResult.getEventGroups.result.eventGroups,
                    ...fetchMoreResult.getEventGroups.result.eventGroups
                  ],
                }
              }
            };
          }
        });
      } catch (error) {
        // Keep this console.error as it's for an exception
        console.error("Error fetching more circles:", error);
      }
    }
  };

  useFocusEffect(
    useCallback(() => {
      handleRefetch();
    }, [handleRefetch])
  );

  useEffect(() => {
    if (needsRefresh) {
      handleRefetch();
    }
  }, [needsRefresh, handleRefetch]);

  // Process events data safely
  const processedEvents = useMemo(() => {
    try {
      if (!data?.getEvents?.result?.events) {
        console.log('No events data available');
        return [];
      }
      
      return data.getEvents.result.events.map((event: any) => ({
        ...event,
        image: event.image || getRandomDefaultImage(),
      }));
    } catch (error) {
      console.error('Error processing events:', error);
      return [];
    }
  }, [data]);

  useEffect(() => {
    if (processedEvents.length > 0) {
      setInvitations(processedEvents);
    }
  }, [processedEvents, setInvitations]);

  // Get counts from server response
  const filterCounts = useMemo(() => ({
    next: countsData?.upcomingEvents?.pagination?.totalItems || 0,
    past: countsData?.pastEvents?.pagination?.totalItems || 0,
    host: countsData?.hostEvents?.pagination?.totalItems || 0,
    guest: countsData?.guestEvents?.pagination?.totalItems || 0
  }), [countsData]);

  const filters = useMemo(() => [
    { label: `Next ${filterCounts.next}`, type: 'next' },
    { label: `Host ${filterCounts.host}`, type: 'host' },
    { label: `Guest ${filterCounts.guest}`, type: 'guest' },
    { label: `Past ${filterCounts.past}`, type: 'past' }
  ], [filterCounts]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await handleRefetch();
    } catch (error) {
      // Keep this console.error as it's for an exception
      console.error('Error refreshing:', error);
    } finally {
      setRefreshing(false);
    }
  }, [handleRefetch]);

  // Update query when filter changes
  useEffect(() => {
    refetchEvents({
      filter: getFilterVariables(),
      pagination: {
        skip: 0,
        limit: 20,
      }
    });
  }, [selectedFilter, refetchEvents]);

  // Handle loading states
  const isLoading = loading || countsLoading || circlesLoading;

  // Handle errors
  const hasError = error || countsError || circlesError;

  // Show loading indicator only for initial load
  if (isLoading && !data && !circlesData) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: Colors.background.primary }]} edges={["top"]}>
        <FastPartyActivityIndicator />
      </SafeAreaView>
    );
  }

  // Show error state
  if (hasError) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: Colors.background.primary }]} edges={["top"]}>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            backgroundColor: Colors.background.primary,
            justifyContent: 'center',
            alignItems: 'center',
            padding: 20
          }}
        >
          <Text style={{ color: Colors.error, textAlign: 'center', marginBottom: 16 }}>
            Something went wrong while loading the data. Please try again.
          </Text>
          <Pressable
            onPress={() => {
              refetchEvents();
              refetchCounts();
              refetchCircles();
            }}
            style={{
              backgroundColor: Colors.primary,
              padding: 12,
              borderRadius: 8,
            }}
          >
            <Text style={{ color: Colors.text.tertiary }}>Retry</Text>
          </Pressable>
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <NotificationProvider>
      <SafeAreaView style={[styles.container, { backgroundColor: Colors.background.primary }]} edges={["top"]}>
        <ScrollView
          contentContainerStyle={homePageStyles.contentContainerStyle}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[Colors.primary || '#43A5BE']}
              tintColor={Colors.primary || '#43A5BE'}
            />
          }
        >
          <View style={homePageStyles.containerStyles}>
            <Header />
          </View>
          <View style={homePageStyles.containerStyles}>
            <Text variant='titleLarge' style={styles.welcomeText}>
                Welcome {fullName}
            </Text>
            <Filters
              filters={filters}
              onFilterSelect={setSelectedFilter}
              selectedFilter={selectedFilter}
            />
            <EventsList
              selectedFilter={selectedFilter}
            />
          </View>
          <View style={homePageStyles.containerStyles}>
            <CirclesList
              circles={circlesData?.getEventGroups?.result?.eventGroups || []}
              isLoading={circlesLoading}
              error={circlesError}
              onFetchMore={handleFetchMore}
              isRefetching={isRefetching}
          />
          </View>
        </ScrollView>
      </SafeAreaView>
    </NotificationProvider>
  );
}

const homePageStyles = StyleSheet.create({
  contentContainerStyle: {
    flexGrow: 1,
    backgroundColor: Colors.background.primary,
    rowGap: 16
  },
  containerStyles: {
    paddingHorizontal: 8
  }
})