import { useFonts } from 'expo-font';
import { <PERSON><PERSON><PERSON><PERSON>, ClerkLoaded } from '@clerk/clerk-expo'
import * as SplashScreen from 'expo-splash-screen';
import { useEffect, useState } from 'react';
import { Slot } from 'expo-router';
import 'react-native-reanimated';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { PaperProvider, MD3LightTheme } from 'react-native-paper';
import { tokenCache } from './(home)/utils/tokenCache';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { ApolloProvider } from '@apollo/client';
import { FastPartyClient } from '@/commons/apollo-client';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { Toast } from '@/components/Toast';
import { View } from 'react-native';
import { useInitializeUser } from './auth/useInitializerUser';
import { useToastStore } from '@/store/toastStore';
import { Snackbar } from 'react-native-paper';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';

SplashScreen.preventAutoHideAsync();

function InitializedApp() {
  const isUserInitialized = useInitializeUser();

  if (!isUserInitialized) {
    return <FastPartyActivityIndicator />;
  }

  return <Slot />;
}

export default function RootLayout() {
  const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY || '';
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
    PlusJakartaSans: require('../assets/fonts/PlusJakartaSans-VariableFont_wght.ttf'),
  });

  const { message, isVisible, hideToast } = useToastStore();

  if (!loaded) {
    return <FastPartyActivityIndicator />;
  }

  if (!publishableKey) {
    throw new Error('Add EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY in your .env')
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <PaperProvider theme={MD3LightTheme}>
        <ThemeProvider value={DefaultTheme}>
          <SafeAreaProvider>
            <ApolloProvider client={FastPartyClient}>
              <ClerkProvider publishableKey={publishableKey} tokenCache={tokenCache}>
                <ClerkLoaded>
                  <BottomSheetModalProvider>
                    <InitializedApp />
                    <Toast />
                  </BottomSheetModalProvider>
                </ClerkLoaded>
              </ClerkProvider>
            </ApolloProvider>
          </SafeAreaProvider>
        </ThemeProvider>
      </PaperProvider>
      <Snackbar
        visible={isVisible}
        onDismiss={hideToast}
        duration={5000}
      >
        {message}
      </Snackbar>
    </GestureHandlerRootView>
  );
}
