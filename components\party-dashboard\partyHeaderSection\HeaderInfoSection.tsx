import React from 'react';
import { StyleSheet, Text, View, Image } from 'react-native';
import { Calendar, Clock, Thermometer } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';

interface HeaderInfoSectionProps {
  date: string;
  time: string;
  temperature: string;
  weatherIcon: string;
}

export const HeaderInfoSection: React.FC<HeaderInfoSectionProps> = ({
  date,
  time,
  temperature,
  weatherIcon
}) => {
  const weatherIconUrl = weatherIcon ? `https:${weatherIcon}` : '';
  return (
    <View style={styles.infoRow}>
      <View style={styles.infoItem}>
        <Calendar size={20} color={Colors.dark.text} />
        <Text style={styles.infoText}>{date}</Text>
      </View>
      <View style={styles.infoItem}>
        <Clock size={20} color={Colors.dark.text} />
        <Text style={styles.infoText}>{time}</Text>
      </View>
      <View style={styles.infoItem}>
        {weatherIcon ? (
          <Image 
            source={{ uri: weatherIconUrl }} 
            style={styles.weatherIcon} 
          />
        ) : (
          <Thermometer size={20} color={Colors.dark.text} />
        )}
        <Text style={styles.infoText}>{`${temperature}°C`}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  infoText: {
    color: Colors.dark.text,
    fontSize: 14,
  },
  weatherIcon: {
    width: 28,
    height: 28,
  },
});