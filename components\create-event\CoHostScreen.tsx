import React from 'react';
import { View } from 'react-native';
import { Text } from 'react-native-paper';
import { AddCohosts } from '../event-dashboard/AddParty/AddCohostsParent';
import { Cohost } from '../../app/(home)/CreateEvent/CreateEvent.models';
import { BottomSheetModal } from '@gorhom/bottom-sheet';

interface CohostsScreenProps {
  cohosts: Cohost[];
  onCohostChange: (cohosts: Cohost[]) => void;
  cohostSheetRef: React.RefObject<BottomSheetModal>;
}

export function CohostsScreen({ cohosts, onCohostChange, cohostSheetRef }: CohostsScreenProps) {
  return (
    <View style={{ padding: 16 }}>
      <Text variant="headlineMedium" style={{ marginBottom: 16 }}>
        Add Co-hosts
      </Text>
      <AddCohosts 
        cohosts={cohosts}
        onCohostChange={onCohostChange}
        cohostSheetRef={cohostSheetRef}
      />
    </View>
  );
}