# Event Stream Services

This directory contains services for handling Server-Sent Events (SSE) from the server.

## ⚠️ IMPORTANT: Current Implementation Status

**NOTE: The SSE implementation is currently disabled in the application due to 404 errors.**

The server doesn't have an endpoint at `/events/{resourceId}` which is required for this implementation to work. Instead, we're using a polling mechanism in `NewPartyDetailsScreen.tsx` to periodically refresh data.

To use this service in the future, the server needs to implement the appropriate SSE endpoints.

## Overview

Server-Sent Events (SSE) is a technology where a browser receives automatic updates from a server via HTTP connection. The server sends events to the client, which can then update the UI in real-time without requiring a page refresh.

## Files

- `eventStreamService.ts`: Implementation of SSE client with a React hook

## Usage

### Basic Usage

```tsx
import { useEventStream } from '@/services/eventStreamService';

function MyComponent() {
  // Handle incoming events
  const handleEvent = (data) => {
    console.log('Event received:', data);
  };

  // Connect to the event stream
  const { isConnected } = useEventStream(
    'my-resource-id',
    handleEvent,
    {
      eventTypes: ['custom_event'],
      autoReconnect: true
    }
  );

  return (
    <View>
      <Text>Connection status: {isConnected ? 'Connected' : 'Disconnected'}</Text>
    </View>
  );
}
```

### Advanced Usage with Event Types

```tsx
import { useEventStream } from '@/services/eventStreamService';

function MyComponent() {
  // Handle incoming events
  const handleEvent = (data, eventType) => {
    if (eventType === 'activity_added') {
      console.log('New activity:', data);
    } else if (eventType === 'activity_updated') {
      console.log('Activity updated:', data);
    } else {
      console.log('Other event:', data);
    }
  };

  // Connect to the event stream with custom event types
  const { isConnected, close, reconnect } = useEventStream(
    'my-resource-id',
    handleEvent,
    {
      eventTypes: [
        'activity_added',
        'activity_updated',
        'activity_deleted'
      ],
      autoReconnect: true,
      reconnectDelay: 3000,
      maxReconnectAttempts: 10
    }
  );

  return (
    <View>
      <Text>Connection status: {isConnected ? 'Connected' : 'Disconnected'}</Text>
      <Button title="Disconnect" onPress={close} />
      <Button title="Reconnect" onPress={reconnect} />
    </View>
  );
}
```

## Configuration Options

The `useEventStream` hook accepts the following options:

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `autoReconnect` | boolean | `true` | Whether to automatically reconnect on connection loss |
| `reconnectDelay` | number | `3000` | Delay in milliseconds before attempting to reconnect |
| `maxReconnectAttempts` | number | `5` | Maximum number of reconnection attempts |
| `headers` | object | `{}` | Custom headers to include with the request |
| `eventTypes` | string[] | `[]` | Event types to listen for (in addition to the default 'message' event) |
| `withCredentials` | boolean | `true` | Whether to include credentials with the request |

## Example Component

The `EventStreamExample.tsx` component demonstrates how to use the `useEventStream` hook in a real-world scenario. It displays a list of received events and provides controls for connecting, disconnecting, and clearing the event list.

## Implementation Details

The implementation uses the `EventSourcePolyfill` library to provide cross-platform support for SSE. It handles authentication, reconnection, and error handling automatically.

## Server Requirements

The server should implement the SSE protocol and send events in the following format:

```
event: event_type
data: {"key": "value"}

```

If no event type is specified, it will be treated as a 'message' event.
