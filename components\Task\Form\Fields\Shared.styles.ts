import { StyleSheet, Dimensions } from 'react-native';

const screenHeight = Dimensions.get('window').height;

export const sharedStyles = StyleSheet.create({
  label: {
    fontFamily: 'Plus Jakarta Sans',
    fontSize: 12,
    fontWeight: '500',
    lineHeight: 16,
    letterSpacing: 0.2,
    textAlign: 'left',
    color: '#000000',
  },

  placeholder: {
    fontFamily: 'Plus Jakarta Sans',
    fontSize: 14,
    fontWeight: 500,
    lineHeight: 16,
    letterSpacing: 0.5,
    textAlign: 'left',
    color: '#9F9C9C',
  },

  value: {
    fontFamily: 'Plus Jakarta Sans',
    fontSize: 14,
    fontWeight: 500,
    lineHeight: 16,
    letterSpacing: 0.5,
    textAlign: 'left',
    color: '#5270E7',
  },

  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
    backgroundColor: '#fff',
    top: screenHeight * 0.02829,
    flexGrow: 1,
    alignItems: 'stretch',
    flexWrap: 'wrap',
    height: 50,
  },

  saparator: {
    height: 45,
    paddingRight: 28,
  },
});