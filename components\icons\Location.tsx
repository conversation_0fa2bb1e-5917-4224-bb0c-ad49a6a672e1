import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const Location = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      fill="url(#Location-1_svg__a)"
      fillRule="evenodd"
      d="M5.834 1.204c2.182 0 4 1.818 4 4 0 .712-.245 1.414-.587 2.05-.344.639-.798 1.235-1.25 1.743-.452.509-.91.937-1.266 1.24a8 8 0 0 1-.453.36 2 2 0 0 1-.163.11 1 1 0 0 1-.************* 0 0 1-.337-.025 1 1 0 0 1-.082-.045 2 2 0 0 1-.162-.11 8 8 0 0 1-.454-.363A13 13 0 0 1 3.67 8.987c-.452-.509-.906-1.106-1.25-1.743-.341-.635-.586-1.334-.586-2.04 0-2.182 1.818-4 4-4m0 5.194a1.195 1.195 0 1 1 0-2.389 1.195 1.195 0 0 1 0 2.39"
      clipRule="evenodd"
    />
    <Defs>
      <LinearGradient
        id="Location-1_svg__a"
        x1={5.834}
        x2={5.834}
        y1={0.046}
        y2={13.002}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <Path
      d="M9.91 5.11c0 3.33-4 6-4 6s-4-2.67-4-6c0-2.21 1.8-4 4-4s4 1.79 4 4"
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M5.91 5.77a.67.67 0 1 0 0-********** 0 0 0 0 1.34"
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
        </>
      )}
    </Svg>
  );
};
export default Location;
