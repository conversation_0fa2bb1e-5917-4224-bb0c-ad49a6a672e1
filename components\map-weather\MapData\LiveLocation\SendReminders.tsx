import React from 'react';
import {
    View,
    Text,
    FlatList,
    Image,
    TouchableOpacity,
    StyleSheet,
    Dimensions,
    Alert,
} from 'react-native';
import InitialsAvatar from '@/components/Avatar/InitialsAvatar';
import { Modal } from 'react-native-paper';
import { PartyDetailsRootList } from '@/components/create-event/Navigation/PartyDetailsRootList';
import { StackScreenProps } from '@react-navigation/stack';
import { LiveLocation } from '../MapLiveLocations.model';
import { useMutation } from '@apollo/client';
import { SEND_REMINDERS } from './LiveLocation.data';
import { useToast } from '@/components/Toast';
import { Colors, Icons } from '@/constants/DesignSystem';
import { DropDown } from '@/components/icons';

const { height } = Dimensions.get('window');

type SendRemindersProps = StackScreenProps<PartyDetailsRootList, 'SendReminders'> & {
    route: {
        params: {
            locations: LiveLocation[],
            partyId: string
        }
    }
};

const SendReminders: React.FC<SendRemindersProps> = ({ route, navigation }) => {
    const [isVisible, setIsVisible] = React.useState(true);
    const { locations, partyId } = route.params;
    const [sendReminders] = useMutation(SEND_REMINDERS)
    const toast = useToast();
    const greenLight = require('./trackingIcon.png');

    const onClose = () => {
        navigation.goBack();
    }

    const getStatusStyle = (status: string) => {
        switch (status) {
            case 'ALLOW':
                return styles.statusAllow;
            case 'DISALLOW':
                return styles.statusDisallow;
            case 'PENDING':
                styles.statusPending;
            default:
                return styles.statusPending;
        }
    };

    const handleSendReminders = async () => {
        try {
            const response = await sendReminders({
                variables: {
                    partyId: partyId
                }
            });
            console.log('Reminders sent successfully:', response.data?.result);
            Alert.alert(
                'Success',
                'Reminders sent successfully!',
                [{ text: 'Dismiss', onPress: () => onClose() }],
            )
        } catch (error) {
            console.error('Error sending reminders:', error);
        }
    }

    const renderItem = ({ item }: { item: LiveLocation }) => {
        const user = item.guest.user;
        const status = item.allowLiveTracking
        return (
            <View style={styles.listItem}>
                {user?.profilePicture ? (
                    <Image source={{ uri: user.profilePicture }} style={styles.profilePicture} />
                ) : (
                    <InitialsAvatar
                        firstName={user?.firstName}
                        lastName={user?.lastName}
                    />
                )}
                <View style={styles.textContainer}>
                    <Text style={styles.name}>
                        {user?.firstName} {user?.lastName}
                    </Text>
                    {status === 'ALLOW' ? (
                        <View style={styles.insideStatusss}>
                            <Image source={greenLight} style={{ marginRight: 10 }} />
                            <Text style={[styles.statusText, getStatusStyle(status)]}>ON</Text>
                        </View>
                    ) : (
                        <Text style={[styles.statusText, getStatusStyle(status)]}>{status}</Text>
                    )}
                </View>
            </View>
        );
    };
    return (
        <Modal
            visible={isVisible}
            onDismiss={onClose}
            dismissable={true}
            contentContainerStyle={styles.modalContent}
        >
            <View style={styles.modalContent}>
                {/* Drag Indicator */}
                <View style={styles.dragIndicator} />

                {/* Header */}
                <View style={styles.header}>
                    <View style={styles.insideStatus}>
                        <View style={styles.otherInside}>
                            <Image source={greenLight} style={{ marginRight: 10 }} />
                            <Text style={styles.bannerText}>Tracking in Progress</Text>
                        </View>
                        {/* Add Space in between here */}
                        <View style={styles.otherInside}>
                            <Text style={styles.calloutText}>Manage Tracking</Text>
                            <DropDown size={Icons.size.md} color={Colors.primary} style={{ marginLeft: 5 }} />
                        </View>
                    </View>
                </View>

                <FlatList
                    data={locations}
                    keyExtractor={(item, index) => `${item.id}-${index}`}
                    renderItem={renderItem}
                    contentContainerStyle={styles.listContainer}
                />

                {/* Send Reminders Button */}
                <TouchableOpacity style={styles.sendButton} onPress={handleSendReminders} activeOpacity={1}>
                    <Text style={styles.sendButtonText}>Send Reminders</Text>
                </TouchableOpacity>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    modalContent: {
        position: 'absolute', // Position the modal absolutely
        bottom: 0, // Stick to the bottom
        left: 0,
        right: 0,
        height: height * 0.75, // 3/4th of the screen height
        backgroundColor: '#fff',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        padding: 16,
    },
    modal: {
        justifyContent: 'flex-end', // Position the modal at the bottom
        margin: 0, // Remove default margin
    },
    dragIndicator: {
        width: 40,
        height: 5,
        backgroundColor: '#ccc',
        borderRadius: 2.5,
        alignSelf: 'center',
        marginBottom: 12,
    },
    header: {
        alignItems: 'center',
        marginBottom: 16,
    },
    headerText: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    listContainer: {
        paddingBottom: 80,
    },
    bannerText: {
        textAlign: 'center',
        fontSize: 16,
        fontWeight: '700',
        color: 'black',
        fontFamily: 'Plus Jakarta Sans',
    },
    listItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        paddingBottom: 8,
    },
    profilePicture: {
        width: 40,
        height: 40,
        borderRadius: 20,
        marginRight: 12,
    },
    textContainer: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
    },
    name: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
    },
    sendButton: {
        position: 'absolute',
        bottom: 16,
        left: 16,
        right: 16,
        backgroundColor: 'orange',
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
    },
    calloutText: {
        textAlign: 'center',
        fontSize: 12,
        fontWeight: '500',
        color: 'black',
        fontFamily: 'Plus Jakarta Sans',
    },
    sendButtonText: {
        color: '#000',
        fontSize: 16,
        fontWeight: 'bold',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    errorText: {
        color: 'red',
        fontSize: 16,
    },
    statusText: {
        fontFamily: 'Plus Jakarta Sans',
        fontWeight: 'bold',
    },
    statusAllow: {
        color: 'green',
    },
    statusDisallow: {
        color: 'red',
    },
    statusPending: {
        color: 'grey',
    },
    insideStatus: {
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginTop: 16,
    },

    insideStatusss: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginTop: 16,
    }, otherInside: {
        flexDirection: 'row',
        alignItems: 'center'
    }
});

export default SendReminders;