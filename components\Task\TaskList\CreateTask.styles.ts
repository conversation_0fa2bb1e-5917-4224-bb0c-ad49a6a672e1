import { StyleSheet, Platform, ViewStyle, TextStyle } from 'react-native';
import { Dimensions } from 'react-native';
import { sharedStyles } from '../Form/Fields/Shared.styles';

const screenHeight = Dimensions.get('window').height;

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  surface: {
    backgroundColor: '#fff',
  },
  contentWrapper: {
    backgroundColor: '#fff',
  },
  scrollView: {
    marginBottom: '36%',
    backgroundColor: '#fff',
  },
  content: {
    flexGrow: 1,
    paddingBottom: 100,
    backgroundColor: '#fff',
  },

  errorContainer: {
    padding: 16,
    backgroundColor: '#FEE2E2',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
    zIndex: 1,
  },
  errorText: {
    color: '#EF4444',
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '500',
  },

  inputStyle: {
    backgroundColor: '#fff',
    marginVertical: 4,
    fontSize: 16,
    height: 72,
  },

  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
    marginTop: 24,
    marginBottom: 24,
    backgroundColor: '#fff',
    paddingHorizontal: 4,
  },
  inputWrapper: {
    flex: 1,
    backgroundColor: '#fff',
    minHeight: 72,
  },
  assignedToInput: {
    backgroundColor: '#fff',
    height: 72,
  },
  dueDateInput: {
    flex: 1,
    paddingLeft: 8,
    backgroundColor: '#fff',
    height: 72,
    ...(Platform.OS === 'ios' && {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
    }),
  } as TextStyle,
  dueDateWrapper: {
    flex: 1,
    backgroundColor: '#fff',
    position: 'relative',
    height: 72,
    borderRadius: 4,
    overflow: 'hidden',
    ...(Platform.OS === 'ios' && {
      transform: Platform.select({
        ios: [{ perspective: 1000 }, { translateY: 0 }],
        android: [],
      }),
    }),
  } as ViewStyle,
  calendarIcon: {
    marginLeft: 12,
    ...(Platform.OS === 'ios' && {
      zIndex: 1,
    }),
  },

  dropdownContainer: {
    marginBottom: 16,
    position: 'relative',
    backgroundColor: '#fff',
  },
  menuStyle: {
    marginTop: Platform.OS === 'ios' ? 0 : 60,
    width: Platform.OS === 'android' ? '100%' : undefined,
    marginLeft: Platform.OS === 'android' ? -16 : 0,
    backgroundColor: '#fff',
  },

  descriptionSection: {
    marginTop: 24,
    position: 'relative',
    backgroundColor: '#fff',
  },
  descriptionInput: {
    backgroundColor: '#fff',
    minHeight: 120,
    marginTop: 8,
    borderColor: '#E4E4E4',
    borderWidth: 2,
    borderRadius: 8,
    padding: 12,
    textAlignVertical: 'top',
    overflow: 'hidden',
    paddingRight: 50,
  },

  attachmentsSection: {
    marginTop: 26,
    backgroundColor: '#fff',
  },
  attachmentBox: {
    width: 120,
    height: screenHeight * 0.103,
    borderColor: '#768DEC',
    borderWidth: 1,
    borderRadius: 8,
    backgroundColor: '#E2E7FB',
    alignItems: 'center',
    justifyContent: 'center',
    borderStyle: 'dashed',
  },
  // attachmentBox: {
  //   marginTop: 8,
  //   borderColor: '#5270E7',
  //   borderWidth: 2,
  //   borderRadius: 8,
  //   borderStyle: 'dashed',
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   backgroundColor: '#E2E7FB',
  //   height: screenHeight * 0.125,
  // },
  attachmentBoxFullWidth: {
    width: '100%',
    marginRight: 0,
  },
  attachmentBoxWithFiles: {
    width: 120,
    flex: 0,
  },
  filePreviewBox: {
    width: 120,
    height: screenHeight * 0.103,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#F5F5F5',
  },
  filePreviewImage: {
    width: '100%',
    height: '100%',
  },
  fileIconContainer: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    backgroundColor: '#F5F5F5',
    paddingTop: 16,
    paddingLeft: 10,
  },
  filePreviewOverlay: {
    position: 'absolute',
    top: 4,
    right: 4,
    zIndex: 2,
  },
  removeFileButton: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fileNameOverlay: {
    position: 'absolute',
    bottom: 15,
    left: 10,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.0)',
    paddingVertical: 2,
    paddingHorizontal: 4,
    minHeight: 32,
    justifyContent: 'center',
  },
  fileNameText: {
    fontSize: 11,
    color: 'black',
    lineHeight: 13,
    textAlign: 'left',
    opacity: 0.9,
  },
  selectText: {
    position: 'absolute',
    right: 16,
    top: '50%',
    transform: [{ translateY: -8 }],
    color: '#655F5F',
    fontSize: 14,
    marginTop: 28,
  },
  assigneeSelectText: {
    left: 30,
    marginTop: 28,
  },
  dueDateSelectText: {
    position: 'absolute',
    right: 16,
    top: '50%',
    transform: [{ translateY: -8 }],
    color: '#655F5F',
    fontSize: 14,
    backgroundColor: 'transparent',
    ...(Platform.OS === 'ios' && {
      zIndex: 1,
    }),
  },
  partySelectText: {
    left: 30,
    marginTop: 8,
  },
  selectContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'absolute',
    width: '100%',
    paddingRight: 16,
    top: '50%',
    transform: [{ translateY: -8 }],
    marginTop: 23,
    backgroundColor: '#fff',
  },
  selectArrow: {
    position: 'absolute',
    left: 70,
    marginTop: 35,
    paddingTop: 20,
  },

  bottomButton: {
    paddingTop: 40,
    backgroundColor: '#fff',
    borderTopWidth: 0,
    borderTopColor: '#E5E7EB',
    marginBottom: '4%',
  },
  saveButton: {
    borderRadius: 100,
    backgroundColor: '#E2E7FB',
    width: '100%',
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButtonContent: {
    height: 56,
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  saveButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2E52E2',
    textAlign: 'center',
    marginTop: 8,
  },
  loadingBox: {
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0.7,
  },
  filePreviewContainer: {
    flex: 1,
    position: 'relative',
  },
  datePickerModal: {
    marginTop: 100,
    margin: 20,
    borderRadius: 12,
    backgroundColor: 'white',
    maxHeight: '70%',
    width: '90%',
    alignSelf: 'center',
  },
  datePickerBackdrop: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },

  noDataText: {
    color: '#6B7280',
    fontSize: 14,
    textAlign: 'center',
  },

  dueDateLabel: {
    position: 'absolute',
    left: 76,
    top: 8,
    fontSize: 12,
    color: '#344054',
    backgroundColor: 'transparent',
    ...(Platform.OS === 'ios' && {
      zIndex: 1,
    }),
  },
  dueDateValue: {
    position: 'absolute',
    left: 76,
    top: 28,
    fontSize: 14,
    color: '#9AABF1',
    backgroundColor: 'transparent',
    ...(Platform.OS === 'ios' && {
      zIndex: 1,
    }),
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  bottomSheet: {
    backgroundColor: 'white',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  bottomSheetContent: {
    gap: 20,
    padding: 16,
  },
  attachmentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 8,
  },
  attachmentOptionText: {
    fontSize: 16,
    color: '#344054',
  },
  attachmentOptionDisabled: {
    opacity: 0.5,
  },
  attachmentOptionTextDisabled: {
    color: '#9E9E9E',
  },
  attachmentsContainer: {
    marginTop: 26,
  },
  attachmentsLabel: {
    marginBottom: 8,
    ...sharedStyles.label,
    fontSize: 14,
  },
  attachmentsScrollContent: {
    height: screenHeight * 0.125,
    flexDirection: 'row',
    gap: 12,
  },
}); 