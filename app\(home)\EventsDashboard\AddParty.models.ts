export interface VendorType {
  id: string;
  name: string;
}

export interface BaseType {
  id: string;
  name: string;
}

export interface PartyType extends BaseType {
  vendorTypes: VendorType[];
}

export interface Cohost {
  id: string;
  firstName?: string | null;
  lastName?: string | null;
  phoneNumber?: string | null;
  email?: string | null;
}

export interface PartyFormData {
  partyType: string;
  partyName: string;
  date: Date;
  time: Date;
  eventId: string;
  area: LocationItem | null;
  guests: string;
  budget: string;
  services: string[];
  cohosts: Cohost[];
}

export interface StyledAccordionProps<T extends BaseType = BaseType> {
  label: string;
  value: string;
  options: T[];
  onSelect: (value: T) => void;
}

export interface LocationItem {
  id: string;
  title?: string;
  city?: string;
}

export interface FormErrors {
  partyName?: string;
  partyType?: string;
  date?: string;
  time?: string;
  area?: string;
  guests?: string;
  budget?: string;
  services?: string;
}

export interface GraphQLError {
  field: string;
  message: string;
}

export interface PartyErrorResponse {
  __typename: 'PartyErrorResponse';
  message: string;
  errors: GraphQLError[];
}

export interface PartySuccessResponse {
  __typename: 'PartyResponse';
  result: {
    party: {
      id: string;
    };
  };
}

export type CreatePartyResponse = {
  createParty: PartyErrorResponse | PartySuccessResponse;
};

export interface PartyUserResponse {
  id: string;
  email: string;
  firstName: string;
  lastName?: string | null;
  phone: string;
}

export interface PartyCoHostResponse {
  userId: PartyUserResponse;
  firstName: string;
  lastName?: string | null;
}

export interface PartyTypeResponse {
  id: string;
  name: string;
}

export interface ServiceLocationResponse {
  id: string;
  name: string;
  city: string;
}

export interface VendorTypeResponse {
  id: string;
  name: string;
}

export interface PartyResponse {
  getPartyById: {
  result: {
    party: {
      name: string;
      partyType: PartyTypeResponse;
      time: string;
      vendorTypes: VendorTypeResponse[];
      serviceLocation: ServiceLocationResponse;
      expectedGuestCount: number;
      totalBudget: number;
      coHosts: PartyCoHostResponse[];
      event: {
        id: string;
      };
        };
      };
    };
}
