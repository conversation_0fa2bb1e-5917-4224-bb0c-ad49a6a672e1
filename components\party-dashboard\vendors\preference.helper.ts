import { PREFERENCE_ITEM_ERROR_MESSAGES, PREFERENCE_ITEM_LABELS } from '@/constants/partyDashboard';
import { CoHost } from '../party-dashboard.model';

export interface FormattedValue {
    displayValue: string;
    error: boolean;
}

function getCoHostDisplayValue(coHost: CoHost): string {
    const { userId } = coHost;
    if (userId?.firstName){
        return `${userId.firstName}${userId.lastName ? ` ${userId.lastName}` : ''}`;
    }
    if (userId.email) return userId.email;
    if (userId.phone) return userId.phone;
    return 'No contact info';
}

export function formatPreferenceValue(
    label: string,
    value: string | CoHost[] | number,
    isError?: boolean
): FormattedValue {
    if (label === PREFERENCE_ITEM_LABELS.VENDOR_OVERVIEW_COHOST) {
        if (!Array.isArray(value) || value.length === 0) {
            return { displayValue: 'No co-hosts added', error: isError || false };
        }
        const formattedCoHosts = value
            .map(coHost => getCoHostDisplayValue(coHost))
            .filter(Boolean)
            .join(' | ');
        return { 
            displayValue: formattedCoHosts || 'No contact info available', 
            error: isError || false
        };
    }

    if (label.toLowerCase() === PREFERENCE_ITEM_LABELS.CO_HOST) {
        const coHosts = Array.isArray(value) ? value : [];
        
        if (coHosts.length === 0) {
            return {
                displayValue: PREFERENCE_ITEM_ERROR_MESSAGES.NO_CO_HOSTS,
                error: true
            };
        }

        const validEmails = coHosts
            .map(host => host.userId.email)
            .filter((email): email is string => email !== null && email !== '');

        if (validEmails.length === 0) {
            const pendingCount = coHosts.length;
            return {
                displayValue: `${pendingCount} co-host${pendingCount > 1 ? 's' : ''} (pending details)`,
                error: true
            };
        }

        return {
            displayValue: validEmails.join(', '),
            error: false
        };
    }

    if (value === null || value === undefined || value === '') {
        return {
            displayValue: 'Not available',
            error: true
        };
    }

    return {
        displayValue: typeof value === 'number' ? value.toString() : String(value),
        error: isError || false
    };
}