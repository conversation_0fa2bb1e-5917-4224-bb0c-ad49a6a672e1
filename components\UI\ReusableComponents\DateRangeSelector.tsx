import React from 'react';
import { View, Platform, StyleSheet } from 'react-native';
import { Modal, Text, Button, useTheme } from 'react-native-paper';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import { Portal } from 'react-native-paper';

interface DateRangeSelectorProps {
  visible: boolean;
  onDismiss: () => void;
  startDate: Date;
  endDate: Date;
  onConfirm: (startDate: Date, endDate: Date) => void;
  title: string;
  minimumDate: Date;
  maximumDate?: Date;
}

export function DateRangeSelector({
  visible,
  onDismiss,
  startDate,
  endDate,
  onConfirm,
  minimumDate,
  maximumDate,
}: DateRangeSelectorProps) {
  const theme = useTheme();
  const [selectingStartDate, setSelectingStartDate] = React.useState(true);
  const [tempStartDate, setTempStartDate] = React.useState(startDate);
  const [tempEndDate, setTempEndDate] = React.useState(endDate);
  const [showPicker, setShowPicker] = React.useState(visible);

  React.useEffect(() => {
    setShowPicker(visible);
    if (visible) {
      setSelectingStartDate(true);
      setTempStartDate(startDate);
      setTempEndDate(endDate);
    }
  }, [visible, startDate, endDate]);

  const handleChange = (_: DateTimePickerEvent, selectedDate?: Date) => {
    if (!selectedDate) {
      handleCancel();
      return;
    }

    const currentDate = new Date(selectedDate);

    if (Platform.OS === 'android') {
      setShowPicker(false);
    }

    if (selectingStartDate) {
      setTempStartDate(currentDate);
      // If end date becomes invalid, update it
      if (tempEndDate < currentDate) {
        setTempEndDate(currentDate);
      }
      if (Platform.OS === 'android') {
        // Show second picker for end date
        setTimeout(() => {
          setSelectingStartDate(false);
          setShowPicker(true);
        }, 100);
      } else {
        setSelectingStartDate(false);
      }
    } else {
      if (currentDate >= tempStartDate) {
        setTempEndDate(currentDate);
        if (Platform.OS === 'android') {
          onConfirm(tempStartDate, currentDate);
          onDismiss();
        }
      }
    }
  };

  const handleConfirm = () => {
    onConfirm(tempStartDate, tempEndDate);
    onDismiss();
  };

  const handleCancel = () => {
    setTempStartDate(startDate);
    setTempEndDate(endDate);
    setSelectingStartDate(true);
    setShowPicker(false);
    onDismiss();
  };

  const getDateHighlightStyle = () => {
    if (Platform.OS === 'ios') {
      return {
        accentColor: theme.colors.primary,
      };
    }
    return {};
  };

  if (Platform.OS === 'android') {
    if (!showPicker) return null;
    return (
      <DateTimePicker
        value={selectingStartDate ? tempStartDate : tempEndDate}
        mode="date"
        display="calendar"
        onChange={handleChange}
        minimumDate={selectingStartDate ? minimumDate : tempStartDate}
        maximumDate={maximumDate}
      />
    );
  }

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        dismissable={false}
        contentContainerStyle={[
          styles.modalContainer,
          { backgroundColor: theme.colors.background }
        ]}
      >
        <View>
          <Text variant="titleMedium" style={[styles.title, { color: theme.colors.onBackground }]}>
            {selectingStartDate ? 'Select Start Date' : 'Select End Date'}
          </Text>

          <Text variant="bodyMedium" style={[styles.dateRange, { color: theme.colors.onBackground }]}>
            {`${tempStartDate.toLocaleDateString()} - ${tempEndDate.toLocaleDateString()}`}
          </Text>

          <DateTimePicker
            value={selectingStartDate ? tempStartDate : tempEndDate}
            mode="date"
            display="inline"
            onChange={handleChange}
            minimumDate={selectingStartDate ? minimumDate : tempStartDate}
            maximumDate={maximumDate}
            textColor={theme.colors.onBackground}
            {...getDateHighlightStyle()}
          />

          <View style={styles.buttonContainer}>
            <Button 
              mode="outlined" 
              onPress={handleCancel}
              style={styles.button}
            >
              Cancel
            </Button>
            <Button 
              mode="contained" 
              onPress={handleConfirm}
              style={styles.button}
              disabled={selectingStartDate}
            >
              {selectingStartDate ? 'Next' : 'Confirm'}
            </Button>
          </View>
        </View>
      </Modal>
    </Portal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  title: {
    marginBottom: 16,
    textAlign: 'center',
  },
  dateRange: {
    marginBottom: 16,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 16,
  },
  button: {
    flex: 1,
  },
}); 