import { useCallback } from 'react';
import { Assignee } from '@/components/Task/SharedInterfaces';

interface UseAssigneeHandlerProps {
  onAssigneeSelect: (assignee: Assignee) => void;
}

export function useAssigneeHandler({
  onAssigneeSelect,
}: UseAssigneeHandlerProps) {
  const handleAssigneeSelect = useCallback((assignee: Assignee) => {
    onAssigneeSelect(assignee);
  }, [onAssigneeSelect]);

  return { handleAssigneeSelect };
}