import * as React from "react";
import Svg, {
  G,
  <PERSON>,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Rect,
} from "react-native-svg";
import { CommonProps } from ".";

const Orbit = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Orbit-1_svg__a)">
            <Path d="M9.9115 3.33008C10.4239 4.10458 10.698 5.01231 10.6999 5.94098C10.7019 6.86964 10.4315 7.7785 9.92229 8.55511C9.41309 9.33173 8.68739 9.94203 7.83496 10.3105" stroke="url(#Orbit-1_svg__b)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M1.98807 8.56988C1.47567 7.79517 1.20166 6.88722 1.19996 5.95839C1.19825 5.02956 1.46893 4.12062 1.97848 3.34403C2.48803 2.56744 3.2141 1.9573 4.06684 1.58911" stroke="url(#Orbit-1_svg__c)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M5.94968 7.37483C6.73663 7.37483 7.37459 6.73688 7.37459 5.94993C7.37459 5.16297 6.73663 4.52502 5.94968 4.52502C5.16273 4.52502 4.52478 5.16297 4.52478 5.94993C4.52478 6.73688 5.16273 7.37483 5.94968 7.37483Z" fill="url(#Orbit-1_svg__d)" stroke="url(#Orbit-1_svg__e)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M9.27452 3.57516C9.79915 3.57516 10.2245 3.14986 10.2245 2.62523C10.2245 2.10059 9.79915 1.67529 9.27452 1.67529C8.74989 1.67529 8.32458 2.10059 8.32458 2.62523C8.32458 3.14986 8.74989 3.57516 9.27452 3.57516Z" stroke="url(#Orbit-1_svg__f)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M2.62498 10.2247C3.14962 10.2247 3.57492 9.7994 3.57492 9.27476C3.57492 8.75013 3.14962 8.32483 2.62498 8.32483C2.10035 8.32483 1.67505 8.75013 1.67505 9.27476C1.67505 9.7994 2.10035 10.2247 2.62498 10.2247Z" stroke="url(#Orbit-1_svg__g)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </G>
          <Defs>
            <LinearGradient id="Orbit-1_svg__b" x1="9.26746" y1="2.48761" x2="9.26746" y2="11.9152" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="Orbit-1_svg__c" x1="2.63339" y1="0.746605" x2="2.63339" y2="10.1747" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="Orbit-1_svg__d" x1="5.94968" y1="4.18108" x2="5.94968" y2="8.02996" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="Orbit-1_svg__e" x1="5.94968" y1="4.18108" x2="5.94968" y2="8.02996" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="Orbit-1_svg__f" x1="9.27452" y1="1.446" x2="9.27452" y2="4.01191" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="Orbit-1_svg__g" x1="2.62498" y1="8.09553" x2="2.62498" y2="10.6615" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <ClipPath id="Orbit-1_svg__a">
              <Rect width="12" height="12" fill="#fff"/>
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G clipPath="url(#Orbit_svg__a)">
            <Path d="M9.91152 3.33008C10.4239 4.10458 10.698 5.01231 10.7 5.94098C10.7019 6.86964 10.4315 7.7785 9.92231 8.55511C9.41311 9.33173 8.68741 9.94203 7.83498 10.3105C6.98255 10.679 6.0408 10.7896 5.12622 10.6284" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M1.98807 8.56983C1.47567 7.79512 1.20166 6.88718 1.19996 5.95835C1.19825 5.02952 1.46893 4.12057 1.97848 3.34398C2.48803 2.5674 3.2141 1.95726 4.06684 1.58906C4.91958 1.22087 5.86156 1.11079 6.77622 1.27243" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M5.94968 7.37483C6.73663 7.37483 7.37459 6.73688 7.37459 5.94993C7.37459 5.16297 6.73663 4.52502 5.94968 4.52502C5.16273 4.52502 4.52478 5.16297 4.52478 5.94993C4.52478 6.73688 5.16273 7.37483 5.94968 7.37483Z" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M9.27452 3.57516C9.79915 3.57516 10.2245 3.14986 10.2245 2.62523C10.2245 2.10059 9.79915 1.67529 9.27452 1.67529C8.74989 1.67529 8.32458 2.10059 8.32458 2.62523C8.32458 3.14986 8.74989 3.57516 9.27452 3.57516Z" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M2.62498 10.2247C3.14962 10.2247 3.57492 9.7994 3.57492 9.27476C3.57492 8.75013 3.14962 8.32483 2.62498 8.32483C2.10035 8.32483 1.67505 8.75013 1.67505 9.27476C1.67505 9.7994 2.10035 10.2247 2.62498 10.2247Z" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
          </G>
          <Defs>
            <ClipPath id="Orbit_svg__a">
              <Rect width="12" height="12" fill="#fff"/>
            </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  );
};
export default Orbit;
