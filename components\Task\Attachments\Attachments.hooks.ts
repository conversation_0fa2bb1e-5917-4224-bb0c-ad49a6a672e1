import { useCallback } from 'react';
import { Animated, Platform, Alert, Linking } from 'react-native';
import * as DocumentPicker from 'expo-document-picker';
import { PermissionsHandler } from '../permissions';
import { ALLOWED_DOCUMENT_TYPES, MAX_FILE_SIZE_BYTES, MAX_FILE_SIZE_MB, getFileSize, type TaskAttachment } from './Attachments.utils';
import type { Dispatch, SetStateAction } from 'react';
import { ApolloClient, NormalizedCacheObject } from '@apollo/client';
import { deleteDocument, type UploadSuccess } from '@/app/(home)/Task/Task.utils';
import { 
  launchImageLibraryAsync, 
  MediaTypeOptions,
  launchCameraAsync,
} from 'expo-image-picker';

interface UploadStatus {
  [key: string]: {
    status: 'success' | 'error';
    response?: unknown;
  };
}

interface UseAttachmentHandlerProps {
  setIsBottomSheetOpen: (isOpen: boolean) => void;
  slideAnim: Animated.Value;
  overlayAnim: Animated.Value;
  bottomDrawerFiles: DocumentPicker.DocumentPickerAsset[];
  setBottomDrawerFiles: Dispatch<SetStateAction<DocumentPicker.DocumentPickerAsset[]>>;
  setUploadingFiles: Dispatch<SetStateAction<DocumentPicker.DocumentPickerAsset[]>>;
  uploadingFiles: DocumentPicker.DocumentPickerAsset[];
  isDocumentPickerOpen: boolean;
  setIsDocumentPickerOpen: (isOpen: boolean) => void;
  setIsPickerActive: (isActive: boolean) => void;
  documentPickerTimeout: React.MutableRefObject<NodeJS.Timeout | null>;
  MAX_FILES_LIMIT: number;
  MAX_TOTAL_FILES_LIMIT: number;
  setIsCameraSessionActive: (isActive: boolean) => void;
  isCameraSessionActive: boolean;
  apolloClient: ApolloClient<NormalizedCacheObject>;
  uploadedKeysMap: Map<number, string>;
  setUploadStatuses: Dispatch<SetStateAction<UploadStatus>>;
  onAttachmentsChange?: (changes: { existing: TaskAttachment[]; uploading: DocumentPicker.DocumentPickerAsset[] }) => void;
  existingAttachments?: TaskAttachment[];
}

function getTotalFilesCount(
  uploadingFiles: DocumentPicker.DocumentPickerAsset[], 
  bottomDrawerFiles: DocumentPicker.DocumentPickerAsset[]
): number {
  return uploadingFiles.length + bottomDrawerFiles.length;
}

export function useAttachmentHandler({
  setIsBottomSheetOpen,
  slideAnim,
  overlayAnim,
  bottomDrawerFiles,
  setBottomDrawerFiles,
  setUploadingFiles,
  uploadingFiles,
  isDocumentPickerOpen,
  setIsDocumentPickerOpen,
  setIsPickerActive,
  documentPickerTimeout,
  MAX_FILES_LIMIT,
  MAX_TOTAL_FILES_LIMIT,
  setIsCameraSessionActive,
  isCameraSessionActive,
  apolloClient,
  uploadedKeysMap,
  setUploadStatuses,
  onAttachmentsChange,
  existingAttachments,
}: UseAttachmentHandlerProps) {
  const handleAttachmentPress = useCallback(() => {
    setIsBottomSheetOpen(true);
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(overlayAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, [slideAnim, overlayAnim, setIsBottomSheetOpen]);

  const handleCloseBottomSheet = useCallback(() => {
    if (bottomDrawerFiles.length > 0) {
      setUploadingFiles(prev => [...prev, ...bottomDrawerFiles]);
      setBottomDrawerFiles([]);
    }

    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(overlayAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsBottomSheetOpen(false);
    });
  }, [bottomDrawerFiles, setBottomDrawerFiles, setUploadingFiles, slideAnim, overlayAnim, setIsBottomSheetOpen]);

  const handleDocumentPick = useCallback(async () => {
    if (isDocumentPickerOpen) return;

    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ALLOWED_DOCUMENT_TYPES,
        multiple: true,
        copyToCacheDirectory: Platform.OS === 'ios',
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const validatedAssets = await Promise.all(
          result.assets.map(async (asset) => {
            const fileSize = asset.size || await getFileSize(asset.uri);
            
            if (fileSize > MAX_FILE_SIZE_BYTES) {
              Alert.alert(
                'File Too Large',
                `File "${asset.name}" exceeds ${MAX_FILE_SIZE_MB}MB limit`
              );
              return null;
            }

            return {
              ...asset,
              name: Platform.OS === 'ios' 
                ? asset.name || asset.uri.split('/').pop() || 'document'
                : asset.name,
              type: asset.mimeType || '*/*',
              size: fileSize
            };
          })
        );

        const validFiles = validatedAssets.filter(asset => asset !== null);
        if (validFiles.length > 0) {
          setBottomDrawerFiles(prev => [...prev, ...validFiles]);
        }
      }
    } catch (error) {
      console.log(error);
    }
  }, [isDocumentPickerOpen, setBottomDrawerFiles]);

  const handlePhotoGallery = useCallback(async () => {
    try {
      const totalFiles = getTotalFilesCount(uploadingFiles, bottomDrawerFiles);
      const remainingTotalSlots = MAX_TOTAL_FILES_LIMIT - totalFiles;

      if (remainingTotalSlots <= 0) {
        Alert.alert(
          'Total File Limit Reached',
          `You can only attach up to ${MAX_TOTAL_FILES_LIMIT} files to a task.`,
          [{ text: 'OK' }]
        );
        return;
      }

      const currentSessionFiles = bottomDrawerFiles.length;
      const remainingSessionSlots = Math.min(
        MAX_FILES_LIMIT - currentSessionFiles,
        remainingTotalSlots
      );

      if (remainingSessionSlots <= 0) {
        Alert.alert(
          'Session Limit Reached',
          'Please save your current selection before adding more files.',
          [{ text: 'OK' }]
        );
        return;
      }

      setIsPickerActive(true);

      const { granted } = await PermissionsHandler.checkAndRequestPermission('MEDIA_LIBRARY');
      if (!granted) {
        setIsPickerActive(false);
        return;
      }

      const result = await launchImageLibraryAsync({
        mediaTypes: MediaTypeOptions.All,
        allowsMultipleSelection: true,
        quality: 1,
        videoMaxDuration: 300,
      });

      if (!result.canceled && result.assets) {
        if (result.assets.length > remainingSessionSlots) {
          Alert.alert(
            'Too Many Files',
            `You can only select ${remainingSessionSlots} more file${remainingSessionSlots === 1 ? '' : 's'} (Total limit: ${MAX_TOTAL_FILES_LIMIT}).`,
            [{ text: 'OK' }]
          );
          return;
        }

        const processedAssets = await Promise.all(
          result.assets.map(async (asset) => {
            const fileSize = await getFileSize(asset.uri);
            if (fileSize > MAX_FILE_SIZE_BYTES) {
              throw new Error(`File "${asset.fileName || 'media'}" exceeds ${MAX_FILE_SIZE_MB}MB limit`);
            }

            const isVideo = asset.type === 'video';
            const fileName = asset.fileName || `${isVideo ? 'video' : 'image'}-${Date.now()}${isVideo ? '.mp4' : '.jpg'}`;
            const mimeType = isVideo ? 'video/mp4' : (asset.type === 'image' ? 'image/jpeg' : 'application/octet-stream');

            return {
              uri: asset.uri,
              name: fileName,
              type: mimeType,
              size: fileSize,
              mimeType,
              width: asset.width,
              height: asset.height,
              duration: asset.duration
            } as DocumentPicker.DocumentPickerAsset;
          })
        );

        setBottomDrawerFiles(prev => [...prev, ...processedAssets]);
      }
    } catch (error) {
      console.error('Error selecting media:', error);
      if (error instanceof Error) {
        Alert.alert(
          'Media Selection Error',
          error.message,
          [{ text: 'OK' }]
        );
      }
    } finally {
      setIsPickerActive(false);
    }
  }, [
    uploadingFiles,
    bottomDrawerFiles,
    MAX_FILES_LIMIT,
    MAX_TOTAL_FILES_LIMIT,
    setIsPickerActive,
    setBottomDrawerFiles
  ]);

  const handleCamera = useCallback(async () => {
    try {
      const totalFiles = getTotalFilesCount(uploadingFiles, bottomDrawerFiles);
      if (totalFiles >= MAX_TOTAL_FILES_LIMIT) {
        Alert.alert(
          'Total File Limit Reached',
          `You can only attach up to ${MAX_TOTAL_FILES_LIMIT} files to a task.`,
          [{ text: 'OK' }]
        );
        return;
      }

      const currentSessionFiles = bottomDrawerFiles.length;
      if (currentSessionFiles >= MAX_FILES_LIMIT) {
        Alert.alert(
          'Session Limit Reached',
          'Please save your current selection before adding more files.',
          [{ text: 'OK' }]
        );
        return;
      }

      const { granted, canAskAgain } = await PermissionsHandler.checkAndRequestPermission('CAMERA');
      
      if (!granted) {
        if (!canAskAgain) {
          Alert.alert(
            'Camera Access Required',
            'Please enable camera access in your device settings to take photos.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Open Settings', onPress: () => Linking.openSettings() }
            ]
          );
          return;
        }
        return;
      }

      setIsCameraSessionActive(true);

      const takePicture = async () => {
        const result = await launchCameraAsync({
          mediaTypes: MediaTypeOptions.Images,
          quality: 0.7,
          allowsEditing: Platform.OS === 'ios',
          exif: false,
          base64: false,
        });

        if (!result.canceled && result.assets) {
          const asset = result.assets[0];
          const fileSize = asset.fileSize || await getFileSize(asset.uri);

          if (fileSize > MAX_FILE_SIZE_BYTES) {
            Alert.alert(
              'Image Too Large',
              `The captured photo exceeds ${MAX_FILE_SIZE_MB}MB limit. Please try taking another photo with lower quality.`,
              [{ text: 'OK' }]
            );
            return;
          }

          const processedAsset = {
            name: `camera_${Date.now()}.jpg`,
            uri: asset.uri,
            mimeType: 'image/jpeg',
            size: fileSize,
          };

          setBottomDrawerFiles(prev => [...prev, processedAsset]);

          const totalFilesAfterAdd = getTotalFilesCount(uploadingFiles, bottomDrawerFiles) + 1;
          if (totalFilesAfterAdd > MAX_TOTAL_FILES_LIMIT) {
            Alert.alert(
              'Total File Limit Reached',
              `You can only attach up to ${MAX_TOTAL_FILES_LIMIT} files to a task.`,
              [{ text: 'OK' }]
            );
            return;
          }

          setIsCameraSessionActive(false);
        } else {
          setIsCameraSessionActive(false);
        }
      };

      await takePicture();

    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert(
        'Error',
        'Failed to capture photo. Please try again.',
        [{ text: 'OK' }]
      );
      setIsCameraSessionActive(false);
    }
  }, [
    uploadingFiles,
    bottomDrawerFiles,
    MAX_FILES_LIMIT,
    MAX_TOTAL_FILES_LIMIT,
    setIsCameraSessionActive,
    setBottomDrawerFiles,
  ]);

  const handleFileRemove = useCallback((file: DocumentPicker.DocumentPickerAsset & { 
    id?: string;
    documentId?: string;
  }) => {
    const documentId = file.id || file.documentId;
    if (documentId) {
      deleteDocument(documentId, apolloClient)
        .then(() => {
          setUploadingFiles(prev => prev.filter(f => f.name !== file.name));
          if (onAttachmentsChange && existingAttachments) {
            onAttachmentsChange({
              existing: existingAttachments.filter(attachment => attachment.id !== documentId),
              uploading: uploadingFiles.filter(f => f.name !== file.name)
            });
          }
        })
        .catch(error => {
          console.error('Background deletion failed:', {
            documentId,
            error,
            fileName: file.name
          });
        });
    } else {
      setUploadingFiles(prev => prev.filter(f => f.name !== file.name));
    }
  }, [apolloClient, setUploadingFiles, onAttachmentsChange, existingAttachments, uploadingFiles]);

  const handleUploadComplete = useCallback((
    fileId: string,
    status: 'success' | 'error',
    response?: unknown
  ) => {
    console.log('Upload complete in CreateTaskForm:', { fileId, status, response });
    setUploadStatuses(prev => ({
      ...prev,
      [fileId]: { status, response }
    }));

    if (status === 'success' && response) {
      const uploadResponse = response as UploadSuccess;
      console.log('Upload success in CreateTaskForm:', { fileId, response });
      
      // Just update the file with the uploaded URL
      setUploadingFiles(prev => 
        prev.map(file => 
          file.name === fileId 
            ? { ...file, uploadedUrl: uploadResponse.url }
            : file
        )
      );
    }
  }, [setUploadStatuses, setUploadingFiles]);

  return { 
    handleAttachmentPress, 
    handleCloseBottomSheet,
    handleDocumentPick,
    handlePhotoGallery,
    handleCamera,
    handleFileRemove,
    handleUploadComplete
  };
}
