import { create } from 'zustand';

interface AssigneeFilter {
  id: string;
  firstName: string;
  lastName: string;
}

interface FilterState {
  filters: {
    status: string[];
    assignee: AssigneeFilter[];
    date?: {
      startDate: Date;
      endDate: Date;
    };
  } | null;
  setFilters: (filters: {
    status: string[];
    assignee: AssigneeFilter[];
    date?: {
      startDate: Date;
      endDate: Date;
    };
  }) => void;
  clearFilters: () => void;
}

interface Filters {
  status?: string[];
  date?: {
    startDate: Date;
    endDate: Date;
  };
  assignee?: AssigneeFilter[];
}

export const useFilterStore = create<FilterState>((set) => ({
  filters: null,
  setFilters: (filters) => set({ filters }),
  clearFilters: () => set({ filters: null }),
})); 