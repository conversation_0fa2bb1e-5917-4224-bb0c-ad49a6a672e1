import Svg, { Path } from 'react-native-svg';

const CURVE_CONTROL_POINT_OFFSET = 50;

const CurvedLineConnector = ({ start, end }: { start: { x: number; y: number }, end: { x: number; y: number } }) => {
  const d = `
    M ${start.x} ${start.y}
    Q ${start.x - CURVE_CONTROL_POINT_OFFSET}, ${(start.y + end.y) / 2}, ${end.x}, ${end.y}
  `;

  return (
    <Svg
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        height: '100%',
        width: '100%',
      }}
    >

      <Path
        d={d}
        stroke="black"
        strokeWidth={2}
        fill="none"
      />
    </Svg>
  );
};

export default CurvedLineConnector