import { Platform, StyleSheet } from 'react-native';

export const textModalStyles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },

  modalContent: {
    flex: 1,
    paddingHorizontal: 0,
    paddingTop: 0,
  },

  modalInput: {
    backgroundColor: 'white',
    fontSize: 15,
    lineHeight: 18,
    minHeight: 120,
    maxHeight: Platform.OS === 'ios' ? 400 : 'auto',
    top: Platform.OS === 'ios' ? 90 : 48,
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: '500',
    textAlign: 'left',
    paddingBottom: 16,
    textAlignVertical: 'top',
  },

  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 10,
    left: 0,
    right: 0,
    boxShadow: '0px 10px 10px 0px rgba(0, 0, 0, 0.05)',
    borderBottomWidth: 0,
    zIndex: 1000,
  },

  button: {
    minWidth: 80,
  },

  buttonText: {
    color: '#0A0A0B',
    fontSize: 12,
    fontWeight: '400',
    fontFamily: 'Plus Jakarta Sans',
  },

  characterCount: {
    color: '#0A0A0B',
    fontSize: 12,
    fontWeight: '400',
    fontFamily: 'Plus Jakarta Sans',
    marginBottom: Platform.OS === 'ios' ? 15 : 50,
    marginRight: 8,
  },
}); 