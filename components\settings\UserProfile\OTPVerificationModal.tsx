import { StyleSheet, View } from 'react-native'
import React, { useState, useEffect } from 'react'
import { Modal, Portal, Text, TextInput, Button } from 'react-native-paper'
import { SignUpNames } from '@/constants/displayNames'

interface OTPVerificationModalProps {
  visible: boolean
  onDismiss: () => void
  onVerify: (code: string) => Promise<void>
  type: 'phone' | 'email'
  isResending: boolean
  onResend: () => void
}

const OTPVerificationModal = ({
  visible,
  onDismiss,
  onVerify,
  type,
  isResending,
  onResend
}: OTPVerificationModalProps) => {
  const [code, setCode] = useState('')
  const [countdown, setCountdown] = useState(30)
  const [canResend, setCanResend] = useState(false)

  useEffect(() => {
    let timer: NodeJS.Timeout
    if (visible && countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            setCanResend(true)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }

    return () => {
      if (timer) clearInterval(timer)
    }
  }, [visible, countdown])

  const handleVerify = async () => {
    await onVerify(code)
    setCode('')
  }

  const handleResend = async () => {
    await onResend()
    setCountdown(30)
    setCanResend(false)
  }

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={styles.container}
      >
        <Text style={styles.title}>
          Enter verification code sent to your {type}
        </Text>
        <TextInput
          mode="outlined"
          label="Verification Code"
          value={code}
          onChangeText={setCode}
          keyboardType="number-pad"
          autoFocus={true}
          style={styles.input}
        />
        <View style={styles.buttonContainer}>
          <Button
            mode="contained"
            onPress={handleVerify}
            style={styles.button}
          >
            {SignUpNames.VERIFY_PHONE}
          </Button>
          {!canResend ? (
            <Text style={styles.countdownText}>
              Resend code in {countdown}s
            </Text>
          ) : (
            <Button
              mode="outlined"
              onPress={handleResend}
              loading={isResending}
              disabled={isResending}
              style={styles.button}
            >
              Resend Code
            </Button>
          )}
        </View>
      </Modal>
    </Portal>
  )
}

export default OTPVerificationModal

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  title: {
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  input: {
    marginBottom: 16,
  },
  buttonContainer: {
    gap: 12,
  },
  button: {
    borderRadius: 20,
  },
  countdownText: {
    textAlign: 'center',
    color: '#666',
  }
})