import { NavigatorScreenParams } from "@react-navigation/native";
import { PartyDetailsRootList } from "@/components/create-event/Navigation/PartyDetailsRootList";
import { AddPartyRootStackList } from "../(home)/EventsDashboard/AddPartyRootStackList";
import { AddCircleRootStackList } from "../(home)/CirclesDashboard/AddCircleRootStackList";

export type HomeRootStackList = {
  HomePage: undefined;
  PartyDetails: NavigatorScreenParams<PartyDetailsRootList> | undefined;
  AddPartyScreen: NavigatorScreenParams<AddPartyRootStackList> | undefined
  AddCircleScreen: NavigatorScreenParams<AddCircleRootStackList> | undefined;
  SearchInvitations: undefined;
  InAppNotification: undefined;
};
