import * as React from "react";
import Svg, {
  Path,
  Defs,
  LinearGradient,
  Stop,
} from "react-native-svg";
import { CommonProps } from ".";

const File = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path 
            fillRule="evenodd" 
            clipRule="evenodd" 
            d="M3.85715 0C3.50211 0 3.21429 0.287817 3.21429 0.642857C3.21429 0.997894 3.50211 1.28571 3.85715 1.28571H9.42858C9.54695 1.28571 9.64286 1.38165 9.64286 1.5V9.64286C9.64286 9.99789 9.93069 10.2857 10.2857 10.2857C10.6408 10.2857 10.9286 9.99789 10.9286 9.64286V1.5C10.9286 0.671573 10.257 0 9.42858 0H3.85715ZM1.28564 3.42857C1.28564 2.71849 1.86128 2.14286 2.57136 2.14286H7.28564C7.99573 2.14286 8.57136 2.71849 8.57136 3.42857V10.7143C8.57136 11.4243 7.99573 12 7.28564 12H2.57136C1.86128 12 1.28564 11.4243 1.28564 10.7143V3.42857ZM2.67824 4.28571C2.67824 3.98985 2.91809 3.75 3.21395 3.75H6.64252C6.93839 3.75 7.17824 3.98985 7.17824 4.28571C7.17824 4.58158 6.93839 4.82143 6.64252 4.82143H3.21395C2.91809 4.82143 2.67824 4.58158 2.67824 4.28571ZM3.21437 5.89286C2.9185 5.89286 2.67866 6.1327 2.67866 6.42857C2.67866 6.72444 2.9185 6.96429 3.21437 6.96429H6.64294C6.93881 6.96429 7.17866 6.72444 7.17866 6.42857C7.17866 6.1327 6.93881 5.89286 6.64294 5.89286H3.21437ZM2.678 8.57143C2.678 8.27556 2.91786 8.03571 3.21372 8.03571H4.928C5.22387 8.03571 5.46372 8.27556 5.46372 8.57143C5.46372 8.86731 5.22387 9.10714 4.928 9.10714H3.21372C2.91786 9.10714 2.678 8.86731 2.678 8.57143Z" 
            fill={hasGradient ? "url(#File-1_svg__a)" : color}
            />
          <Defs>
            <LinearGradient id="File-1_svg__a" x1="6.10711" y1="-1.44828" x2="6.10711" y2="14.7586" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
          </Defs>
        </>
      ) : (
        <>
            <Path 
              d="M7.61539 2.36542H2.76924C2.32317 2.36542 1.96155 2.72704 1.96155 3.17311V10.4423C1.96155 10.8884 2.32317 11.25 2.76924 11.25H7.61539C8.06147 11.25 8.42309 10.8884 8.42309 10.4423V3.17311C8.42309 2.72704 8.06147 2.36542 7.61539 2.36542Z" 
              stroke={color} 
              strokeWidth={strokeWidth} 
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
            <Path 
              d="M3.5769 4.38458H6.80767" 
              stroke={color} 
              strokeWidth={strokeWidth} 
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
            <Path 
              d="M3.5769 6.40387H6.80767" 
              stroke={color} 
              strokeWidth={strokeWidth} 
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
            <Path 
              d="M3.5769 8.42303H5.19229" 
              stroke={color} 
              strokeWidth={strokeWidth} 
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
            <Path 
              d="M3.98077 0.75H9.23077C9.44497 0.75 9.65045 0.835096 9.80189 0.986567C9.95334 1.13804 10.0385 1.34348 10.0385 1.55769V9.23077" 
              stroke={color} 
              strokeWidth={strokeWidth}
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
        </>
      )}
    </Svg>
  );
};
export default File;
