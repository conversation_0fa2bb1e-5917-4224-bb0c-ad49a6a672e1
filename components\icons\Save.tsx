import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const Save = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      fill="url(#Save-1_svg__a)"
      stroke="url(#Save-1_svg__b)"
      d="M2.333 1.258c0-.253.206-.458.459-.458h6.416c.253 0 .459.205.459.458v9.625a.458.458 0 0 1-.734.367L6.275 9.256a.46.46 0 0 0-.55 0L3.067 11.25a.458.458 0 0 1-.734-.367z"
    />
    <Defs>
      <LinearGradient
        id="Save-1_svg__a"
        x1={6}
        x2={6}
        y1={-0.528}
        y2={14.329}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Save-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.528}
        y2={14.329}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <Path
      stroke={color}
      d="M2.333 1.258c0-.253.206-.458.459-.458h6.416c.253 0 .459.205.459.458v9.625a.458.458 0 0 1-.734.367L6.275 9.256a.46.46 0 0 0-.55 0L3.067 11.25a.458.458 0 0 1-.734-.367z"
    />
        </>
      )}
    </Svg>
  );
};
export default Save;
