import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import type { SvgProps } from "react-native-svg";
const SvgRsvpCantBeDone = (props: SvgProps) => (
  <Svg
    width={300}
    height={300}
    fill="none"
    viewBox="0 0 300 300"
    {...props}
  >
    <Path
      fill="#FCD9BD"
      d="M39.815 246c10.991 0 19.901-1.044 19.901-2.333 0-1.288-8.91-2.333-19.901-2.333-10.992 0-19.902 1.045-19.902 2.333S28.823 246 39.815 246M175.95 247c48.649 0 88.086-1.044 88.086-2.333 0-1.288-39.437-2.333-88.086-2.333s-88.085 1.045-88.085 2.333S127.302 247 175.95 247"
    />
    <Path
      fill="#fff"
      d="M38.923 116.18h167.982a5.97 5.97 0 0 1 5.965 5.966v90.703a6.43 6.43 0 0 1-6.425 6.425H38.279a6.43 6.43 0 0 1-6.425-6.425v-89.595c0-3.9 3.169-7.069 7.069-7.069z"
    />
    <Path
      fill="url(#Rsvp_cant_be_done_svg__a)"
      d="M38.923 116.18h167.982a5.97 5.97 0 0 1 5.965 5.966v90.703a6.43 6.43 0 0 1-6.425 6.425H38.279a6.43 6.43 0 0 1-6.425-6.425v-89.595c0-3.9 3.169-7.069 7.069-7.069z"
    />
    <Path
      fill="#63B3ED"
      d="M206.441 219.275H38.275a6.424 6.424 0 0 1-6.425-6.426v-88.138l90.523 58.992 90.489-58.685v87.831a6.425 6.425 0 0 1-6.426 6.426z"
    />
    <Path
      fill="url(#Rsvp_cant_be_done_svg__b)"
      d="m35.767 116.925 172.477-.591-80.426-49.775a10.33 10.33 0 0 0-10.916 0L35.767 116.92z"
    />
    <Path
      fill="#2A363E"
      d="M173.983 125.115c2.912-2.912 3.214-7.333.674-9.873s-6.96-2.238-9.872.675-3.215 7.333-.675 9.873 6.96 2.238 9.873-.675"
    />
    <Path
      fill="#2A363E"
      d="M173.194 126.606c1.203 2.132 4.311 2.661 6.941 1.178s3.791-4.412 2.587-6.548c-.7-1.248-2.075-1.716-2.473-1.843-2.193-.705-4.097.433-4.469.665-2.004 1.265-3.856 4.303-2.586 6.548"
    />
    <Path
      fill="#2A363E"
      d="M182.687 129.32c1.783-1.005 2.801-2.58 2.273-3.517s-2.402-.882-4.186.123-2.801 2.58-2.273 3.517 2.402.882 4.186-.123"
    />
    <Path
      fill="#2A363E"
      d="M179.317 130.742s1.06-3.904 1.278-4.131c1.103-1.156 4.456-3.331 8.474-1.786 3.296 1.265 4.403 3.55 5.983 4.412-1.199.985-1.825 6.285-8.675 6.281-3.549 0-6.587-1.966-7.064-4.776zM163.945 119.695c-3.738-1.225-6.977 1.93-7.327 5.532l-.862-.148c1.059-3.874 4.167-6.5 8.281-5.778zM163.77 118.395c-2.779-2.779-6.994-1.764-9.17 1.204l-.674-.556c1.957-1.965 4.937-3.239 7.656-2.228.871.302 1.658.805 2.346 1.414z"
    />
    <Path
      stroke="#2A363E"
      strokeMiterlimit={10}
      strokeWidth={0.875}
      d="m174.494 126.09-1.09 3.816-2.815-1.278"
    />
    <Path
      fill="#fff"
      d="M42.11 216.648h160.462c3.213.013 4.443-4.534 1.729-6.399l-75.677-52.015c-3.537-2.381-8.014-2.377-11.546 0l-76.693 51.643c-2.635 1.773-1.379 6.754 1.725 6.767z"
    />
    <Path
      fill="url(#Rsvp_cant_be_done_svg__c)"
      d="M42.11 219.274h160.462c3.213.009 4.443-4.188 1.729-5.913l-75.677-48.071a10.93 10.93 0 0 0-11.546 0l-76.693 47.73c-2.635 1.641-1.379 6.241 1.725 6.254"
    />
    <Path
      fill="url(#Rsvp_cant_be_done_svg__d)"
      d="M57.857 141.659c-.573-8.601 6.71-12.694 14.794-12.89 8.5-.211 15.18 3.829 15.525 13.17L89.5 162.5z"
    />
    <Path
      stroke="#000"
      strokeMiterlimit={10}
      strokeWidth={1.751}
      d="M170.655 131.211c.09 0 .162-.578.162-1.292 0-.713-.072-1.291-.162-1.291-.089 0-.162.578-.162 1.291 0 .714.073 1.292.162 1.292ZM179.317 131.745c.089 0 .162-.578.162-1.291 0-.714-.073-1.292-.162-1.292s-.162.578-.162 1.292c0 .713.072 1.291.162 1.291Z"
    />
    <Path
      fill="#fff"
      d="M194.869 128.76c3.475.626 8.876 1.996 12.741 5.151 3.838 3.139 4.25 6.636 4.775 12.431a97.5 97.5 0 0 1 0 17.582q-.13 15.192-.267 30.385c.105 16.776-10.334 25.775-14.728 29.045-9.932 7.384-14.028 4.123-60.38 18.247-3.493 1.064-9.691 2.303-18.335 1.843-1.274-.066-13.993.875-21.74-4.285-4.591-3.064-6.583-9.494-6.583-13.319 0-30.888 0-45.867-1.852-75.84-.114-1.821-.5-13.5-4.5-17-3.939-3.125-7.676-3.973-8.963-4.236H194.86z"
    />
    <Path
      fill="url(#Rsvp_cant_be_done_svg__e)"
      d="M194.869 128.76c3.475.626 8.876 1.996 12.741 5.151 3.838 3.139 4.25 6.636 4.775 12.431a97.5 97.5 0 0 1 0 17.582q-.13 15.192-.267 30.385c.105 16.776-10.334 25.775-14.728 29.045-9.932 7.384-14.028 4.123-60.38 18.247-3.493 1.064-9.691 2.303-18.335 1.843-1.274-.066-13.993.875-21.74-4.285-4.591-3.064-6.583-9.494-6.583-13.319 0-30.888 0-45.867-1.852-75.84-.114-1.821-.5-13.5-4.5-17-3.939-3.125-7.676-3.973-8.963-4.236H194.86z"
    />
    <Path
      stroke="#fff"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={2.188}
      d="M110.083 194.545h42.561M148.075 173.781h43.174M148.075 160.921h43.174M110.083 208.074h30.183M107.767 232.304h30.183"
    />
    <Path
      fill="#fff"
      d="M119.27 243.466c40.74 0 82.238.052 122.978.052 2.565-2.131 5.944-5.41 8.36-9.791.814-1.479 2.679-5.191 2.788-16.825.097-10.373.062-15.599-3.983-17.577-1.247-.609-6.495-2.872-18.711 0-17.35 2.945-29.413 1.895-37.423.249-10.6-2.175-15.428-2.788-25.079-4.018-4.43-.565-8.881-1.593-13.543-.595-6.552 1.405-9.681 4.42-10.741 5.458-2.731 2.657-3.965 5.055-5.029 10.447-1.335 6.767-.118 8.378-.945 13.319-.429 2.552-1.685 8.089-5.638 11.906-4.438 4.285-9.274 5.523-13.034 7.375"
    />
    <Path
      fill="url(#Rsvp_cant_be_done_svg__f)"
      d="M119.27 243.466c40.74 0 82.238.052 122.978.052 2.565-2.131 5.944-5.41 8.36-9.791.814-1.479 2.679-5.191 2.788-16.825.097-10.373.062-15.599-3.983-17.577-1.247-.609-6.495-2.872-18.711 0-17.35 2.945-29.413 1.895-37.423.249-10.6-2.175-15.428-2.788-25.079-4.018-4.43-.565-8.881-1.593-13.543-.595-6.552 1.405-9.681 4.42-10.741 5.458-2.731 2.657-3.965 5.055-5.029 10.447-1.335 6.767-.118 8.378-.945 13.319-.429 2.552-1.685 8.089-5.638 11.906-4.438 4.285-9.274 5.523-13.034 7.375"
    />
    <Path
      fill="#fff"
      d="M159.437 194.304h117.424c7.856 0 15.126 4.451 18.413 11.586q.165.355.315.718c3.804 9.029.381 16.584-1.308 19.639l-109.366 1.182c.796-2.701 3.309-13.893-2.854-22.108-7.349-9.8-17.175-10.229-22.624-11.013z"
    />
    <Path
      fill="#63B3ED"
      d="M159.437 194.304h117.424c7.856 0 15.126 4.451 18.413 11.586q.165.355.315.718c3.804 9.029.381 16.584-1.308 19.639l-109.366 1.182c.796-2.701 3.309-13.893-2.854-22.108-7.349-9.8-17.175-10.229-22.624-11.013z"
    />
    <Path fill="#fff" d="M49.187 162.055H40.76v80.093h8.426z" />
    <Path
      fill="url(#Rsvp_cant_be_done_svg__g)"
      d="M49.187 162.055H40.76v80.093h8.426z"
    />
    <Path
      fill="#fff"
      d="M80.048 185.266 8.69 196.746c-1.304.211-2.552-.831-2.793-2.328L3.04 176.643c-.24-1.497.622-2.875 1.922-3.085l71.357-11.481a2.18 2.18 0 0 1 1.575.363l11.03 7.34c1.344.893 1.672 2.942.679 4.215l-8.172 10.43c-.359.456-.845.753-1.383.836z"
    />
    <Path
      fill="url(#Rsvp_cant_be_done_svg__h)"
      d="M80.048 185.266 8.69 196.746c-1.304.211-2.552-.831-2.793-2.328L3.04 176.643c-.24-1.497.622-2.875 1.922-3.085l71.357-11.481a2.18 2.18 0 0 1 1.575.363l11.03 7.34c1.344.893 1.672 2.942.679 4.215l-8.172 10.43c-.359.456-.845.753-1.383.836z"
    />
    <Path
      fill="url(#Rsvp_cant_be_done_svg__i)"
      d="M138.274 143.466h-32.761v34.906h32.761z"
    />
    <Path
      fill="#2A363E"
      d="M31.88 216.934c4.303-1.135 6.988-5.099 5.998-8.852s-5.282-5.877-9.585-4.742c-4.303 1.136-6.988 5.099-5.998 8.853s5.282 5.876 9.585 4.741"
    />
    <Path
      fill="#2A363E"
      d="M37.483 216.167c1.348 2.276.17 5.471-2.639 7.134-2.806 1.664-6.176 1.169-7.528-1.107-.788-1.331-.49-2.867-.394-3.309.52-2.434 2.613-3.607 3.033-3.825 2.267-1.195 6.106-1.292 7.528 1.107"
    />
    <Path
      fill="#2A363E"
      d="M33.03 225.551c2.194-.301 3.844-1.479 3.686-2.631s-2.065-1.842-4.259-1.54c-2.193.301-3.843 1.479-3.685 2.631s2.065 1.841 4.258 1.54"
    />
    <Path
      fill="#2A363E"
      d="M36.857 224.557s-3.462-2.665-3.8-2.718c-1.706-.271-6.009.044-8.46 4.001-2.004 3.243-1.47 5.935-2.267 7.708 1.676.061 5.677 4.219 11.568-.267 3.051-2.325 4.382-5.997 2.96-8.728zM26.698 204.643c-1.155-4.093-5.917-5.135-9.388-3.318l-.355-.801c2.858-.95 6.29-.757 8.456 1.51.705.705 1.248 1.549 1.668 2.464zM29.88 205.312c.828-4.197-2.573-7.349-6.573-7.572l.092-.871c2.932.569 5.86 2.486 6.578 5.524.25.963.267 1.974.131 2.954l-.227-.039z"
    />
    <Path
      stroke="#2A363E"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={0.875}
      d="m33.592 223.494.499 10.264-1.532 9.909h2.193M31.29 223.494l-.74 10.084-3.672 10.089h3.016"
    />
    <Path
      stroke="#2A363E"
      strokeMiterlimit={10}
      strokeWidth={0.875}
      d="m28.029 217.423-4.29 5.589 3.9 2.605M36.2 215.953l4.561 5.812-3.33 4.823"
    />
    <Path
      fill="#2A363E"
      stroke="#2A363E"
      strokeMiterlimit={10}
      strokeWidth={0.438}
      d="M39.841 229.135c.327-.288-.017-1.212-.767-2.065s-1.624-1.312-1.95-1.024c-.327.287.017 1.211.767 2.064s1.624 1.312 1.95 1.025Z"
    />
    <Path
      fill="#fff"
      d="M245.683 245.223s5.546-5.143 3.27-9.585-5.091 5.107-5.091 5.107 1.051-10.903-2.613-11.472-.503 11.884-.503 11.884-.95-2.871-2.906-1.51 1.567 5.37 1.567 5.37l6.276.21zM49.483 244.402s6.277-5.821 3.703-10.85c-2.578-5.029-5.764 5.782-5.764 5.782s1.19-12.347-2.96-12.991c-4.144-.643-.568 13.455-.568 13.455s-1.077-3.252-3.292-1.711 1.773 6.079 1.773 6.079l7.108.241z"
    />
    <Path
      fill="url(#Rsvp_cant_be_done_svg__j)"
      d="M245.683 245.223s5.546-5.143 3.27-9.585-5.091 5.107-5.091 5.107 1.051-10.903-2.613-11.472-.503 11.884-.503 11.884-.95-2.871-2.906-1.51 1.567 5.37 1.567 5.37l6.276.21z"
    />
    <Path
      fill="url(#Rsvp_cant_be_done_svg__k)"
      d="M49.483 244.402s6.277-5.821 3.703-10.85c-2.578-5.029-5.764 5.782-5.764 5.782s1.19-12.347-2.96-12.991c-4.144-.643-.568 13.455-.568 13.455s-1.077-3.252-3.292-1.711 1.773 6.079 1.773 6.079l7.108.241z"
    />
    <Path
      fill="url(#Rsvp_cant_be_done_svg__l)"
      d="M14.706 185.314a2.6 2.6 0 0 1-1.089-.067 2.8 2.8 0 0 1-.961-.495 2.945 2.945 0 0 1-1.1-1.913 2.97 2.97 0 0 1 .507-2.148q.318-.45.78-.744.463-.298 1.024-.38a2.54 2.54 0 0 1 1.094.071q.527.149.96.49.435.34.72.833.29.487.377 1.081.086.593-.05 1.147-.135.549-.453 1t-.78.748a2.6 2.6 0 0 1-1.029.377m-.173-1.187q.317-.047.575-.218a1.65 1.65 0 0 0 .695-1.023q.078-.323.027-.671a1.8 1.8 0 0 0-.218-.635 1.67 1.67 0 0 0-.958-.778 1.35 1.35 0 0 0-.613-.048 1.4 1.4 0 0 0-.578.222 1.6 1.6 0 0 0-.438.436q-.18.26-.258.583-.075.323-.024.671t.214.636q.168.287.415.489.248.198.544.292.3.09.617.044m6.188.309a2.6 2.6 0 0 1-1.09-.068 2.8 2.8 0 0 1-.96-.494 2.953 2.953 0 0 1-1.1-1.913 2.96 2.96 0 0 1 .507-2.148q.316-.45.78-.744.462-.299 1.023-.38a2.6 2.6 0 0 1 1.094.07q.528.15.961.491.435.34.72.832.29.488.376 1.081.087.594-.05 1.148-.134.549-.453 1-.318.45-.78.748a2.6 2.6 0 0 1-1.028.377m-.174-1.188q.317-.046.575-.217.26-.176.442-.436.18-.266.253-.587a1.8 1.8 0 0 0 .028-.671 1.8 1.8 0 0 0-.218-.635 1.67 1.67 0 0 0-.959-.778 1.35 1.35 0 0 0-.613-.048 1.4 1.4 0 0 0-.578.222 1.6 1.6 0 0 0-.438.436q-.18.26-.258.583-.074.322-.023.671.05.348.214.635.167.288.415.489.247.199.544.293.3.09.616.043m3.562.588-.81-5.541 2.09-.306q.456-.066.858.114a1.7 1.7 0 0 1 .68.531q.273.352.34.811.069.468-.1.884-.167.417-.505.7a1.5 1.5 0 0 1-.778.348l-.902.132.314 2.153zm.7-3.514.767-.112a.55.55 0 0 0 .277-.122.65.65 0 0 0 .178-.248q.06-.146.036-.308a.609.609 0 0 0-.363-.469.54.54 0 0 0-.301-.041l-.768.112zm5.856 2.662q-.467.068-.916-.016-.45-.088-.81-.319a1.64 1.64 0 0 1-.561-.601l1.056-.574a.5.5 0 0 0 .206.196q.155.087.375.127.22.036.476-.001.238-.035.438-.132a.9.9 0 0 0 .312-.244.39.39 0 0 0 .089-.304.33.33 0 0 0-.178-.253 1 1 0 0 0-.374-.123 2.5 2.5 0 0 0-.446-.036q-.615 0-1.122-.127a1.9 1.9 0 0 1-.833-.445q-.324-.312-.404-.854a1.48 1.48 0 0 1 .16-.95q.236-.434.683-.722.452-.288 1.01-.37.466-.067.916.02.453.084.814.318a1.6 1.6 0 0 1 .558.606l-1.057.566a.5.5 0 0 0-.21-.191q-.15-.088-.37-.124a1.5 1.5 0 0 0-.477.001q-.23.03-.43.131a1 1 0 0 0-.315.244.37.37 0 0 0-.095.297q.032.214.174.302a.8.8 0 0 0 .354.098q.216.013.47.012.583 0 1.093.144.51.143.85.458.342.314.416.824.075.519-.16.954a1.96 1.96 0 0 1-.679.717 2.5 2.5 0 0 1-1.013.371m2.654-2.191-.567-3.879.99-.144.566 3.879zm.756 1.692a.594.594 0 0 1-.682-.514.59.59 0 0 1 .508-.673.57.57 0 0 1 .441.11.57.57 0 0 1 .231.39.596.596 0 0 1-.498.687m6.81-6.758.173 1.187-1.584.231.636 4.354-1.187.173-.636-4.353-1.583.231-.174-1.188zm3.87 5.199a2.6 2.6 0 0 1-1.09-.067 2.8 2.8 0 0 1-.96-.495 2.945 2.945 0 0 1-1.1-1.913 2.97 2.97 0 0 1 .507-2.148q.317-.45.78-.744.462-.298 1.024-.38a2.54 2.54 0 0 1 1.093.071q.528.149.961.49.435.34.72.833.29.487.376 1.081.087.593-.05 1.147-.134.549-.453 1-.318.45-.78.748a2.6 2.6 0 0 1-1.028.377m-.174-1.187q.318-.047.575-.218a1.65 1.65 0 0 0 .696-1.023q.077-.323.027-.671a1.8 1.8 0 0 0-.218-.635 1.67 1.67 0 0 0-.959-.778 1.35 1.35 0 0 0-.613-.048 1.4 1.4 0 0 0-.578.222 1.6 1.6 0 0 0-.438.436q-.18.26-.258.583-.074.323-.023.671t.214.636q.167.286.415.489.247.198.544.292.3.09.616.044m6.188.309a2.6 2.6 0 0 1-1.089-.068 2.8 2.8 0 0 1-.961-.494 2.953 2.953 0 0 1-1.1-1.913 2.96 2.96 0 0 1 .507-2.148q.318-.45.78-.744.462-.299 1.024-.38a2.54 2.54 0 0 1 1.094.07q.526.15.96.491.435.34.72.832.29.487.377 1.081a2.95 2.95 0 0 1-.503 2.148q-.32.45-.78.748a2.6 2.6 0 0 1-1.029.377m-.173-1.188q.316-.046.575-.217.26-.176.441-.436.18-.266.254-.587a1.8 1.8 0 0 0 .027-.671 1.7 1.7 0 0 0-.218-.635 1.67 1.67 0 0 0-.958-.778 1.35 1.35 0 0 0-.614-.048 1.4 1.4 0 0 0-.578.222 1.6 1.6 0 0 0-.437.436q-.18.26-.259.583-.075.322-.023.671.05.348.214.635.168.288.415.489.247.199.544.293.3.09.617.043m5.579.293-.81-5.541 1.188-.173.636 4.353 2.177-.318.173 1.188zm5.078-6.401 1.338-.195 2.82 5.247-1.259.184-.443-.824-2.209.322-.19.917-1.258.184zm.509 3.483 1.338-.195-.941-1.738zm7.037-4.585.173 1.188-1.583.231.636 4.354-1.187.173-.636-4.354-1.583.231-.174-1.187zm1.6 5.426-.81-5.541 3.531-.516.174 1.188-2.343.342.115.791 1.805-.263.173 1.187-1.804.264.173 1.187 2.343-.342.174 1.187z"
    />
    <Path
      fill="#fff"
      d="M15.706 186.314a2.6 2.6 0 0 1-1.09-.067 2.8 2.8 0 0 1-.96-.495 2.947 2.947 0 0 1-1.1-1.913 2.97 2.97 0 0 1 .507-2.148q.317-.45.78-.744.462-.298 1.024-.38a2.54 2.54 0 0 1 1.093.071q.528.149.961.49.435.34.72.833.29.487.376 1.081.087.593-.05 1.147-.134.549-.453 1-.318.45-.78.748a2.6 2.6 0 0 1-1.028.377m-.173-1.187q.316-.047.574-.218a1.65 1.65 0 0 0 .696-1.023 1.8 1.8 0 0 0 .027-.671 1.8 1.8 0 0 0-.218-.635 1.67 1.67 0 0 0-.959-.778 1.35 1.35 0 0 0-.613-.048 1.4 1.4 0 0 0-.578.222 1.6 1.6 0 0 0-.437.436q-.18.26-.259.583-.075.323-.023.671.05.348.214.636.167.287.415.489.247.198.544.292.3.09.617.044m6.187.309a2.6 2.6 0 0 1-1.089-.068 2.8 2.8 0 0 1-.961-.494 2.953 2.953 0 0 1-1.1-1.913 2.962 2.962 0 0 1 .507-2.148q.318-.45.78-.744.462-.299 1.024-.38a2.6 2.6 0 0 1 1.094.07q.526.15.96.491.435.34.72.832.29.488.377 1.081.086.594-.051 1.148-.134.549-.452 1-.319.45-.78.748a2.6 2.6 0 0 1-1.029.377m-.173-1.188q.316-.046.575-.217a1.66 1.66 0 0 0 .695-1.023q.078-.323.027-.671a1.8 1.8 0 0 0-.218-.635 1.67 1.67 0 0 0-.958-.778 1.35 1.35 0 0 0-.614-.048 1.4 1.4 0 0 0-.578.222 1.6 1.6 0 0 0-.437.436q-.18.26-.259.583-.075.322-.023.671.05.348.214.635.168.288.415.489.247.199.544.293.3.09.617.043m3.561.588-.809-5.541 2.09-.306q.456-.066.857.114a1.7 1.7 0 0 1 .68.531q.274.352.34.811.07.468-.1.884-.165.417-.505.7a1.5 1.5 0 0 1-.777.348l-.903.132.315 2.153zm.7-3.514.768-.112a.55.55 0 0 0 .277-.122.65.65 0 0 0 .178-.248q.06-.146.036-.308a.609.609 0 0 0-.364-.469.54.54 0 0 0-.3-.041l-.769.112zm5.856 2.662q-.466.068-.916-.016-.45-.088-.81-.319a1.64 1.64 0 0 1-.56-.601l1.055-.574a.5.5 0 0 0 .207.196q.153.087.374.127.22.036.477-.001.237-.035.437-.132a.9.9 0 0 0 .312-.244.39.39 0 0 0 .09-.304.33.33 0 0 0-.179-.253 1 1 0 0 0-.374-.123 2.5 2.5 0 0 0-.446-.036q-.614 0-1.122-.127a1.9 1.9 0 0 1-.833-.445q-.324-.312-.403-.854a1.48 1.48 0 0 1 .16-.95 2 2 0 0 1 .683-.722q.45-.288 1.01-.37.465-.067.916.02.453.084.814.318a1.6 1.6 0 0 1 .557.606l-1.057.566a.5.5 0 0 0-.21-.191q-.15-.088-.37-.124a1.5 1.5 0 0 0-.476.001q-.23.03-.43.131a1 1 0 0 0-.316.244.37.37 0 0 0-.094.297q.03.214.174.302a.8.8 0 0 0 .353.098q.216.013.471.012.582 0 1.092.144.51.143.851.458.342.314.416.824.075.519-.16.954a1.96 1.96 0 0 1-.68.717 2.5 2.5 0 0 1-1.013.371m2.654-2.191-.566-3.879.99-.144.566 3.879zm.757 1.692a.594.594 0 0 1-.682-.514.59.59 0 0 1 .508-.673.57.57 0 0 1 .44.11q.196.145.232.39a.596.596 0 0 1-.498.687m6.809-6.758.173 1.187-1.583.231.636 4.354-1.188.173-.636-4.353-1.583.231-.173-1.188zm3.87 5.199a2.6 2.6 0 0 1-1.089-.067 2.8 2.8 0 0 1-.961-.495 2.945 2.945 0 0 1-1.1-1.913 2.97 2.97 0 0 1 .507-2.148q.318-.45.78-.744.462-.298 1.024-.38a2.54 2.54 0 0 1 1.094.071q.527.149.96.49.435.34.72.833.29.487.377 1.081.086.593-.05 1.147-.135.549-.453 1t-.78.748a2.6 2.6 0 0 1-1.029.377m-.173-1.187q.317-.047.575-.218a1.65 1.65 0 0 0 .695-1.023q.078-.323.027-.671a1.8 1.8 0 0 0-.218-.635 1.67 1.67 0 0 0-.958-.778 1.35 1.35 0 0 0-.613-.048 1.4 1.4 0 0 0-.578.222 1.6 1.6 0 0 0-.438.436q-.18.26-.258.583-.075.323-.024.671t.214.636q.168.286.415.489.247.198.544.292.3.09.617.044m6.188.309a2.6 2.6 0 0 1-1.09-.068 2.8 2.8 0 0 1-.96-.494 2.953 2.953 0 0 1-1.1-1.913 2.96 2.96 0 0 1 .507-2.148q.316-.45.78-.744.462-.299 1.023-.38a2.54 2.54 0 0 1 1.094.07q.528.15.961.491.435.34.72.832.29.487.376 1.081t-.05 1.148q-.134.549-.453 1-.318.45-.78.748a2.6 2.6 0 0 1-1.028.377m-.174-1.188q.317-.046.575-.217.26-.176.442-.436.18-.266.253-.587a1.8 1.8 0 0 0 .028-.671 1.7 1.7 0 0 0-.218-.635 1.67 1.67 0 0 0-.959-.778 1.35 1.35 0 0 0-.613-.048 1.4 1.4 0 0 0-.578.222 1.6 1.6 0 0 0-.438.436q-.18.26-.258.583-.074.322-.023.671.05.348.214.635.167.288.415.489.246.199.544.293.3.09.616.043m5.58.293-.81-5.541 1.188-.173.635 4.353 2.177-.318.174 1.188zm5.078-6.401 1.338-.195 2.82 5.247-1.26.184-.443-.824-2.209.322-.189.917-1.259.184zm.508 3.483 1.338-.195-.94-1.738zm7.038-4.585.173 1.188-1.583.231.636 4.354-1.188.173-.636-4.354-1.583.231-.173-1.187zm1.6 5.426-.81-5.541 3.53-.516.174 1.188-2.343.342.116.791 1.805-.263.173 1.187-1.805.264.173 1.187 2.344-.342.173 1.187z"
    />
    <Defs>
      <LinearGradient
        id="Rsvp_cant_be_done_svg__a"
        x1={122}
        x2={124.878}
        y1={116}
        y2={196.004}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="Rsvp_cant_be_done_svg__b"
        x1={122.006}
        x2={121.928}
        y1={116.925}
        y2={55.217}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="Rsvp_cant_be_done_svg__c"
        x1={122.374}
        x2={122.282}
        y1={219.275}
        y2={150.741}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="Rsvp_cant_be_done_svg__d"
        x1={73.663}
        x2={73.484}
        y1={162.5}
        y2={122.404}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="Rsvp_cant_be_done_svg__e"
        x1={143.909}
        x2={143.435}
        y1={243.542}
        y2={102.144}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="Rsvp_cant_be_done_svg__f"
        x1={186.344}
        x2={186.256}
        y1={243.518}
        y2={185.315}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="Rsvp_cant_be_done_svg__g"
        x1={44.974}
        x2={41.202}
        y1={242.148}
        y2={143.626}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="Rsvp_cant_be_done_svg__h"
        x1={46.59}
        x2={46.522}
        y1={196.773}
        y2={155.508}
        gradientUnits="userSpaceOnUse"
      >
        <Stop offset={0.22} stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.666} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="Rsvp_cant_be_done_svg__i"
        x1={121.894}
        x2={121.709}
        y1={178.372}
        y2={135.372}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="Rsvp_cant_be_done_svg__j"
        x1={243.374}
        x2={243.374}
        y1={225.088}
        y2={245.227}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#fff" />
        <Stop offset={0.671} stopColor="#80D6AF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#00AC5E" stopOpacity={0.7} />
      </LinearGradient>
      <LinearGradient
        id="Rsvp_cant_be_done_svg__k"
        x1={46.869}
        x2={46.869}
        y1={221.604}
        y2={244.407}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#fff" />
        <Stop offset={0.671} stopColor="#80D6AF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#00AC5E" stopOpacity={0.7} />
      </LinearGradient>
      <LinearGradient
        id="Rsvp_cant_be_done_svg__l"
        x1={43.417}
        x2={41.345}
        y1={184.048}
        y2={169.938}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default SvgRsvpCantBeDone;
