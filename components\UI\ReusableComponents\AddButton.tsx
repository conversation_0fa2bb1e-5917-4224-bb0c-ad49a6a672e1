import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Button } from 'react-native-paper';
import { router } from 'expo-router';
import { Colors } from '@/constants/Colors';

interface AddButtonProps {
  name: string;
  route: Readonly<string>;
  params?: Record<string, string>;
}

export function AddButton({ name, route, params }: AddButtonProps) {
  const handlePress = () => {
    const queryParams = params ? `?${new URLSearchParams(params)}` : '';
    router.push(route + queryParams as any);
  };

  return (
    <View style={styles.container}>
      <Button
        mode="elevated"
        onPress={handlePress}
        icon="plus"
        contentStyle={{ backgroundColor: Colors.custom.white }}
      >
        {name}
      </Button>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
});
