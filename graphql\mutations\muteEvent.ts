import { gql } from "@apollo/client";

export const MUTE_PARTY = gql`
mutation MuteParty($input: MutePartyInput!) {
    muteParty(input: $input) {
      ... on UserNotificationPreferenceResponse {
        status
        message
        result {
          userNotificationPreference {
            id
            mutedParties {
              party {
                id
              }
              mutedAt
              mutedUntil
            }
            userId
          }
        }
      }
      ... on UserNotificationPreferenceErrorResponse {
        status
        message
        errors {
          field
          message
        }
      }
    }
  }
`;

export const UNMUTE_PARTY = gql`
mutation UnmuteParty($input: UnmutePartyInput!) {
    unmuteParty(input: $input) {
      ... on UserNotificationPreferenceResponse {
        status
        message
        result {
          userNotificationPreference {
            id
            userId
            mutedParties {
              mutedAt
              mutedUntil
              party {
                id
                name
              }
            }
            createdAt
            updatedAt
          }
        }
      }
      ... on UserNotificationPreferenceErrorResponse {
        status
        message
        errors {
          field
          message
        }
      }
    }
  }
`;