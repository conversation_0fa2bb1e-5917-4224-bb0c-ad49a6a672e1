import * as React from "react";
import Svg, {
  G,
  Path,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Rect,
} from "react-native-svg";
import { CommonProps } from ".";

const Block = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Block-1_svg__a)">
            <Path 
                d="M6 10.75C3.37664 10.75 1.25 8.62336 1.25 6C1.25 3.37664 3.37664 1.25 6 1.25C8.62332 1.25 10.75 3.37664 10.75 6C10.75 8.62336 8.62332 10.75 6 10.75Z" 
                stroke="url(#Block-1_svg__b)" 
                strokeWidth="1.9" 
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
            <Path 
                d="M9.36162 9.36151L2.63861 2.63843" 
                stroke="url(#Block-1_svg__c)" 
                strokeWidth="1.9" 
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
          </G>
          <Defs>
            <LinearGradient id="Block-1_svg__b" x1="6" y1="0.103448" x2="6" y2="12.9339" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="Block-1_svg__c" x1="6.00011" y1="1.82702" x2="6.00011" y2="10.907" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <ClipPath id="Block-1_svg__a">
              <Rect width="12" height="12" fill="white"/>
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G clipPath="url(#Block_svg__a)">
            <Path 
                d="M6 11C3.23857 11 1 8.76143 1 6C1 3.23857 3.23857 1 6 1C8.76138 1 11 3.23857 11 6C11 8.76143 8.76138 11 6 11Z" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
            <Path 
                d="M9.53849 9.53841L2.46164 2.46149" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
          </G>
          <Defs>
              <ClipPath id="Block_svg__a">
                  <Rect width="12" height="12" fill="white"/>
              </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  );
};
export default Block;
