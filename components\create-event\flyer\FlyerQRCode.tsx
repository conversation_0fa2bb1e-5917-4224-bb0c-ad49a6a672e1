import { View, StyleSheet } from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import { Colors, Spacing, Borders } from '@/constants/DesignSystem';

interface FlyerQRCodeProps {
  value: string;
  size?: number;
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
}

export function FlyerQRCode({ 
  value, 
  size = 80,
  errorCorrectionLevel = 'H'
}: FlyerQRCodeProps) {
  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  if (!isValidUrl(value)) {
    console.warn('Invalid URL provided to FlyerQRCode:', value);
  }

  return (
    <View style={styles.container}>
      <View style={[styles.qrContainer, { width: size, height: size }]}>
        <QRCode
          value={value}
          size={size - 16}
          backgroundColor={Colors.white}
          color={Colors.black}
          ecl={errorCorrectionLevel}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  qrContainer: {
    backgroundColor: Colors.white,
    borderRadius: Borders.radius.md,
    padding: Spacing.xs,
    alignItems: 'center',
    justifyContent: 'center',
  },
}); 