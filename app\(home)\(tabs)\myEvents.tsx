import React from 'react';
import { ListRenderItem, StyleSheet, View } from 'react-native';
import { ThemedView } from '@/components/UI/ThemedView';
import Animated, {
  useAnimatedScrollHandler,
  useSharedValue
} from 'react-native-reanimated';
import { CreateEventButton } from '@/components/user-dashboard/CreateEventButton';
import { EventHeaderContainer } from '@/components/user-dashboard/EventHeaderContainer';
import { EventSearchBar } from '@/components/user-dashboard/EventSearchBar';
import { Colors } from '@/constants/Colors';
import { EventTabType } from '@/constants/eventTabTypes';
import {  GET_ALL_EVENTS } from '@/app/(home)/UserDashboard/MyEvents.data';
import { useQuery } from '@apollo/client';
import { router } from 'expo-router';
import { useEventStore } from '@/store/eventStore';
import { useEventRefreshStore } from '../../../store/eventRefreshStore';
import { LoadingIndicator } from '@/components/UI/ReusableComponents/LoadingIndicator';
import { getEventStatus } from '@/app/(home)/utils/reusableFunctions';
import { ThemedText } from '@/components/UI/ThemedText';
import { EventCard } from '@/components/user-dashboard/EventCard';
import { getFormattedDate } from '@/app/(home)/utils/reusableFunctions'; 
import { commonStyles } from '../../../styles/commonStyles';

interface Party {
  id: string;
  name: string;
  time: string; // ISO date string
}

interface Event {
  endDate: string;
  startDate: string;
  id: string;
  name: string;
  eventType: {
    bannerImage: string;
  };
  status: string[];
  parties: Party[];
}
interface Location {
  city: string;
  name: string;
  state: string;
}
interface EventItem {
  id: string;
  title: string;
  // date: string;
  eventPartyDate?: string;
  nextPartyName?: string;
  showNextParty?: boolean;
  status: string;
  image: string;
  location: Location;
}

export default function MyEventsScreen() {
  const scrollY = useSharedValue(0);
  const [activeTab, setActiveTab] = React.useState<EventTabType>(EventTabType.UPCOMING);
  const [errorMessage, setErrorMessage] = React.useState<string | null>(null);

  const filter = React.useMemo(() => getFilter(activeTab), [activeTab]);
  const { data, refetch, loading } = useQuery(GET_ALL_EVENTS, {
    fetchPolicy: 'network-only',
    variables: {
      filter,
      pagination: {
        skip: 0,
        limit: 100
      }
    },
    onError: () => {
      setErrorMessage('Unable to fetch your events. Please try again later.');
    },
    onCompleted: () => {
      setErrorMessage(null);
    },
  });

  function getFilter(tab: EventTabType) {

    if (tab === EventTabType.UPCOMING) {
      return {
        presentAndUpcoming: true,
      };
    } else if (tab === EventTabType.PAST) {
      return {
        presentAndUpcoming: false,
      };
    }
    return null;
  }

  const { needsRefresh, setNeedsRefresh } = useEventRefreshStore();

  React.useEffect(() => {
    if (needsRefresh) {
      refetch();
      setNeedsRefresh(false);
    }
  }, [needsRefresh, refetch, setNeedsRefresh]);

  const getPartyDetails = (parties: Party[]) => {
    const now = new Date();
    const futureParties = parties.filter(party => new Date(party.time) > now);
    const pastParties = parties.filter(party => new Date(party.time) <= now)
      .sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime());
  
    const showUpNextText = futureParties.length > 0;
    const nextParty = futureParties.sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime())[0];
  
    let eventPartyDate = '';
    if (nextParty) {
      eventPartyDate = getFormattedDate(nextParty.time);
    } else if (pastParties.length > 0) {
      if (pastParties.length === 1) {
        eventPartyDate = getFormattedDate(pastParties[0].time);
      } else {
        const firstDate = pastParties[0].time;
        const lastDate = pastParties[pastParties.length - 1].time;
        if (getFormattedDate(firstDate) === getFormattedDate(lastDate)) {
          eventPartyDate = getFormattedDate(firstDate);
        } else {
          eventPartyDate = `${getFormattedDate(firstDate)} - ${getFormattedDate(lastDate)}`;
        }
      }
    }
  
    return {
      showUpNextText,
      nextPartyName: nextParty ? nextParty.name : '',
      eventPartyDate
    };
  }


  const mappedEvents = data?.getUserEvents?.result?.events?.map((event: Event): EventItem => {
    const { showUpNextText, nextPartyName, eventPartyDate } = getPartyDetails(event.parties);
    return {
      id: event.id,
      title: event.name,
      status: getEventStatus(event.status),
      image: event.eventType.bannerImage,
      showNextParty: showUpNextText,
      nextPartyName,
      eventPartyDate,
      location: {
        city: '',
        name: '',
        state: ''
      }
    };
  }) || [];

  const scrollHandler = useAnimatedScrollHandler((event) => {
    scrollY.value = event.contentOffset.y;
  });

  const renderItem: ListRenderItem<EventItem> = ({ item }) => {
    return (
      <EventCard
        eventInfo={{
          id: item?.id,
          title: item?.title,
          nextPartyName: item?.nextPartyName,
          eventPartyDate: item?.eventPartyDate,
          showNextParty: item?.showNextParty,
          status: item?.status,
          image: item?.image
        }}
        onPress={() => handleEventPress(item.id) }
        onEdit={() => { }}
        onDelete={() => { }}
      />
    );
  };

  const setSelectedEventId = useEventStore((state) => state.setSelectedEventId);

  const handleEventPress = (id: string): void => {
    setSelectedEventId(id);
    router.push({
      pathname: '/(home)/(tabs)/eventsDB',
      params: { eventId: id }
    });
  };

  const handleCreateEvent = () => {
    router.push('../CreateEvent/CreateEvent');
  };

  const handleSearch = (text: string) => {
  };

  const renderEmptyComponent = () => {
    if (errorMessage) {
      return (
        <ThemedView style={styles.emptyContainer}>
          <ThemedText style={styles.emptyText}>
            Unable to fetch your events. Please try again later.
          </ThemedText>
        </ThemedView>
      );
    }

    return (
      <ThemedView style={styles.emptyContainer}>
        {loading ? <LoadingIndicator /> : (
          <ThemedText style={styles.emptyText}>
            You don't have any {activeTab.toLowerCase()} events yet.
          </ThemedText>
        )}
      </ThemedView>
    );
  };

  return (
    <ThemedView style={styles.container}>
      <EventHeaderContainer
        activeTab={activeTab}
        onTabChange={setActiveTab}
        notificationCount={6}
      />
      <Animated.FlatList
        data={mappedEvents}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={() => (
          <View style={{ paddingBottom: 16 }}>
            <EventSearchBar onSearch={handleSearch} />
          </View>
        )}
        ListEmptyComponent={renderEmptyComponent}
        stickyHeaderIndices={[0]}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        contentContainerStyle={[
          styles.listContainer,
          !mappedEvents.length && styles.emptyListContainer
        ]}
        showsVerticalScrollIndicator={false}
      />
      <CreateEventButton 
        onPress={handleCreateEvent} 
        style={commonStyles.fab}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // paddingLeft: 10,
    // paddingRight: 10,
    backgroundColor: Colors.light.background,
  },
  listContainer: {
    paddingBottom: 100,
  },
  emptyListContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    color: Colors.light.text,
    opacity: 0.8,
  },
});
