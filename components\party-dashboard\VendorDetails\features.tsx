import React, { useState } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { ChevronRight, ChevronLeft } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';
import { ButtonNames, VendorDetails } from '@/constants/displayNames';

interface Feature {
  name: string;
  value: string | null;
  icon_src: string;
}

interface FeaturesProps {
  features: Feature[];
}

export function Features({ features }: FeaturesProps) {
  const [showAll, setShowAll] = useState(false);

  const displayedFeatures = showAll ? features : features.slice(0, 6);

  const renderFeature = (feature: Feature, index: number) => (
    <View style={styles.featureItem} key={`${feature.name}-${index}`}>
      <Image source={{ uri: feature.icon_src }} style={styles.icon} />
      <Text style={styles.featureText}>
        {feature.value ? `${feature.value} ${feature.name}` : feature.name}
      </Text>
    </View>
  );

  const renderRow = (rowFeatures: Feature[], rowIndex: number) => (
    <View style={styles.row} key={`row-${rowIndex}`}>
      {rowFeatures.map((feature, index) => renderFeature(feature, rowIndex * 3 + index))}
    </View>
  );

  const rows = [];
  for (let i = 0; i < displayedFeatures.length; i += 3) {
    rows.push(renderRow(displayedFeatures.slice(i, i + 3), i / 3));
  }

  return (
    <View style={styles.container}>
      <Text style={styles.header}>{VendorDetails.AMENITIES}</Text>
      {rows}
      {features.length > 6 && (
        <TouchableOpacity
          style={styles.showMoreButton}
          onPress={() => setShowAll(!showAll)}
        >
          <Text style={styles.showMoreText}>
            {showAll ? ButtonNames.SHOW_LESS : ButtonNames.SHOW_MORE}
          </Text>
          {showAll ? (
            <ChevronLeft color={Colors.light.textSecondary} size={16} />
          ) : (
            <ChevronRight color={Colors.light.textSecondary} size={16} />
          )}
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: '8%',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    color: Colors.light.text,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginRight: 16,
    flex: 1,
  },
  icon: {
    width: 18,
    height: 18,
    marginRight: 8,
  },
  featureText: {
    fontSize: 14,
    color: Colors.light.text,
  },
  showMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  showMoreText: {
    fontSize: 14,
    marginRight: 4,
    color: Colors.light.textSecondary,
    textDecorationLine: 'underline',
  },
});
