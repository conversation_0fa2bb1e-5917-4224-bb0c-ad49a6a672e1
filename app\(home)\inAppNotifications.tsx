import { StyleSheet, View, FlatList, Pressable, RefreshControl } from 'react-native'
import { Text, Menu, Button } from 'react-native-paper'
import { ThemedView } from '@/components/UI/ThemedView'
import { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery } from '@apollo/client';
import { GET_IN_APP_NOTIFICATIONS } from '@/data/InAppNotificationsData/InAppNotificationsGraphQL';
import { InAppNotification, GetInAppNotificationsQueryResult, InAppNotificationsResponse, InAppNotificationFilterInput } from '@/data/InAppNotificationsData/InAppNotificationsModel';
import { InAppNotificationCard } from '@/components/InAppNotitificationCard'
import { getDateRangeFilter } from '@/lib/utils/dateUtils';
import { useApolloClient } from '@apollo/client';
import { Colors, Typography, Spacing, Borders, Shadows, Icons } from '@/constants/DesignSystem';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';
import { DropDown } from '@/components/icons';
import NoNotifications from '@/components/Illustrations/NoNotifications';

const PAGE_SIZE = 20;

export default function InAppNotifications() {

  const [selectedFilter, setSelectedFilter] = useState('all');
  const [menuVisible, setMenuVisible] = useState(false);
  const [selectedDateFilter, setSelectedDateFilter] = useState('Today');

  const [displayedNotifications, setDisplayedNotifications] = useState<InAppNotification[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const client = useApolloClient();

  const queryVariables = useMemo(() => {
    const dateRange = getDateRangeFilter(selectedDateFilter);
    const filter: InAppNotificationFilterInput = {
      createdAt: dateRange.start || dateRange.end ? dateRange : null,
    };
    if (!filter.createdAt) delete filter.createdAt;

    return {
      pagination: { limit: PAGE_SIZE, skip: 0 },
      filter: Object.keys(filter).length > 0 ? filter : null,
    };
  }, [selectedDateFilter]);

  const { loading, error, data, fetchMore, refetch } = useQuery<GetInAppNotificationsQueryResult>(GET_IN_APP_NOTIFICATIONS, {
    variables: queryVariables,
    fetchPolicy: 'cache-and-network',
    notifyOnNetworkStatusChange: true,
  });

  useEffect(() => {
    if (data?.getInAppNotifications && 'result' in data.getInAppNotifications) {
      const fetched = (data.getInAppNotifications as InAppNotificationsResponse).result.inAppNotifications;
      setDisplayedNotifications(fetched);
    } else if (!loading && !error) {
      setDisplayedNotifications([]);
    }
  }, [data, loading, error]);

  const openMenu = () => setMenuVisible(true);
  const closeMenu = () => setMenuVisible(false);

  const handleDateFilterSelect = (filter: string) => {
    setSelectedDateFilter(filter);
    closeMenu();

    const newDateRange = getDateRangeFilter(filter);
    const newFilter: InAppNotificationFilterInput = {
      createdAt: newDateRange.start || newDateRange.end ? newDateRange : null,
    };
    if (!newFilter.createdAt) delete newFilter.createdAt;

    refetch({
      pagination: { limit: PAGE_SIZE, skip: 0 },
      filter: Object.keys(newFilter).length > 0 ? newFilter : null,
    });
  };

  const handleReadFilterSelect = (filter: string) => {
    setSelectedFilter(filter);
  }

  const onRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await refetch(queryVariables);
    } catch (e) {
      console.error("Error during refresh:", e);
    } finally {
      setIsRefreshing(false);
    }
  }, [refetch, queryVariables]);

  const loadMore = useCallback(() => {
    const currentFilter = queryVariables.filter;

    if (loading || !data?.getInAppNotifications || !('result' in data.getInAppNotifications)) return;

    const paginationInfo = (data.getInAppNotifications as InAppNotificationsResponse).pagination;

    if (displayedNotifications.length >= paginationInfo.totalItems) {
      return;
    }
    fetchMore({
      variables: {
        pagination: {
          limit: PAGE_SIZE,
          skip: displayedNotifications.length
        },
        filter: currentFilter,
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult?.getInAppNotifications || !('result' in fetchMoreResult.getInAppNotifications)) {
          return prev;
        }

        const newNotifications = (fetchMoreResult.getInAppNotifications as InAppNotificationsResponse).result.inAppNotifications;

        const existingIds = new Set(displayedNotifications.map(n => n.id));
        const uniqueNewNotifications = newNotifications.filter(n => !existingIds.has(n.id));

        if (uniqueNewNotifications.length === 0) {
          return {
              getInAppNotifications: {
                  ...fetchMoreResult.getInAppNotifications,
                  result: {
                      ...(fetchMoreResult.getInAppNotifications as InAppNotificationsResponse).result,
                      inAppNotifications: displayedNotifications
                  }
              }
          };
        }
        setDisplayedNotifications(prevDisplayed => [...prevDisplayed, ...uniqueNewNotifications]);

        const combinedNotifications = [...displayedNotifications, ...uniqueNewNotifications];
        return {
          getInAppNotifications: {
            ...(fetchMoreResult.getInAppNotifications),
            result: {
              ...(fetchMoreResult.getInAppNotifications as InAppNotificationsResponse).result,
              inAppNotifications: combinedNotifications,
            },
          },
        };
      },
    }).catch(err => {
        console.error("Error fetching more notifications:", err);
    });
  }, [loading, data, fetchMore, displayedNotifications, queryVariables]);

  const filteredNotifications = displayedNotifications.filter(notif =>
    (selectedFilter === 'all' || !notif.read)
  );

  const handleDeleteNotification = useCallback((deletedId: string) => {
    setDisplayedNotifications(prev => prev.filter(notif => notif.id !== deletedId));

    const existingData = client.readQuery<GetInAppNotificationsQueryResult>({
      query: GET_IN_APP_NOTIFICATIONS,
      variables: queryVariables,
    });

    if (existingData?.getInAppNotifications && 'result' in existingData.getInAppNotifications) {
      const updatedList = existingData.getInAppNotifications.result.inAppNotifications.filter(
        notif => notif.id !== deletedId
      );

      client.writeQuery<GetInAppNotificationsQueryResult>({
        query: GET_IN_APP_NOTIFICATIONS,
        variables: queryVariables,
        data: {
          getInAppNotifications: {
            ...existingData.getInAppNotifications,
            result: {
              ...existingData.getInAppNotifications.result,
              inAppNotifications: updatedList,
            },
            pagination: {
              ...existingData.getInAppNotifications.pagination,
              totalItems: existingData.getInAppNotifications.pagination.totalItems - 1,
            },
          },
        },
      });
    }
  }, [client, queryVariables]);

  const renderNotificationCard = ({ item }: { item: InAppNotification }) => (
    <InAppNotificationCard
      notification={item}
      onDelete={handleDeleteNotification}
    />
  );

  const keyExtractor = (item: InAppNotification) => item.id;

  const renderListFooter = () => {
    if (loading && displayedNotifications.length > 0) {
        return <FastPartyActivityIndicator size="small" fullScreen={false} />;
    }
    return null;
  };

  const renderEmptyList = () => (
     <View style={styles.centeredEmptyList}>
        <NoNotifications/>
        <Text style={styles.noNotificationsText}>
        All quiet here! No updates match your current filter.
        </Text>
        <Text style={styles.noNotificationsSubText}>
        👉 Hint: "Try a different filter"        </Text>
    </View>
  );

  return (
    <ThemedView style={styles.container}>
      <View style={styles.filterContainer}>
        <View style={styles.leftFilters}>
          <Pressable
            style={[ styles.filterButton, selectedFilter === 'all' && styles.selectedFilter ]}
            onPress={() => handleReadFilterSelect('all')}
            disabled={loading}
          >
            <Text style={[ styles.filterText, selectedFilter === 'all' && styles.selectedFilterText ]}> All </Text>
          </Pressable>
          <Pressable
            style={[ styles.filterButton, selectedFilter === 'unread' && styles.selectedFilter ]}
            onPress={() => handleReadFilterSelect('unread')}
            disabled={loading}
          >
            <Text style={[ styles.filterText, selectedFilter === 'unread' && styles.selectedFilterText ]}> Unread </Text>
          </Pressable>
        </View>
        <Menu
          theme={{ colors: { primary: 'red' } }}
          visible={menuVisible}
          onDismiss={closeMenu}
          anchor={
            <Pressable onPress={openMenu} style={styles.dateFilterPressable} disabled={loading}>
              <View style={styles.dateFilterPressableContent}>
                <Text style={styles.dateFilterPressableLabel}>{selectedDateFilter}</Text>
                <DropDown size={Icons.size.md} color={loading ? Colors.background.secondary : Colors.text.secondary} />
              </View>
            </Pressable>
          }
        >
       
            <Menu.Item onPress={() => handleDateFilterSelect('Today')} title="Today" />
            <Menu.Item onPress={() => handleDateFilterSelect('Last 7 days')} title="Last 7 days" />
            <Menu.Item onPress={() => handleDateFilterSelect('Month')} title="Month" />
         
        </Menu>
      </View>

      {/* --- Content Area --- */}
      <View style={styles.listContainer}>
        {error && (
          <View style={styles.centeredEmptyList}>
            <Text style={styles.errorText}>Error loading notifications.</Text>
            <Text style={styles.errorTextDetails}>{error.message}</Text>
            <Button
              mode="contained"
              onPress={() => refetch(queryVariables)}
              buttonColor={Colors.primary}
              textColor={Colors.white}
              style={{ borderRadius: Borders.radius.pill }}
            >
              Retry
            </Button>
          </View>
        )}

        {!error && (
          <FlatList
            data={filteredNotifications}
            renderItem={renderNotificationCard}
            keyExtractor={keyExtractor}
            onEndReached={loadMore}
            onEndReachedThreshold={0.5}
            ListFooterComponent={renderListFooter}
            ListEmptyComponent={loading && !isRefreshing ? <FastPartyActivityIndicator size="large" fullScreen={false} /> : renderEmptyList}
            contentContainerStyle={styles.listContentContainer}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={onRefresh}
              />
            }
          />
        )}
      </View>
    </ThemedView>
  )
}

// --- Styles ---
const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%'
  },
  noNotificationsText: {
    fontSize: Typography.fontSize.xl,
    color: Colors.text.secondary,
    fontFamily: Typography.fontFamily.primary,
    textAlign: 'center',
    paddingTop: Spacing.lg,
  },
  noNotificationsSubText: { 
    fontSize: Typography.fontSize.lg,
    color: Colors.text.secondary,
    fontFamily: Typography.fontFamily.primary,
    textAlign: 'center',
    paddingTop: Spacing.sm,
  },
  filterContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.lg,
    paddingHorizontal: Spacing.lg
  },
  listContainer: {
    flex: 1,
    width: '100%',
  },
  listContentContainer: {
    paddingBottom: Spacing.lg,
    flexGrow: 1,
  },
  centeredEmptyList: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerLoader: {
    marginTop: Spacing.md,
    marginBottom: Spacing.md,
  },
  emptyText: {
    opacity: 0.6,
    textAlign: 'center',
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.md,
    color: Colors.text.secondary,
  },
  errorText: {
    color: Colors.error,
    fontSize: Typography.fontSize.lg,
    marginBottom: Spacing.xs,
    textAlign: 'center',
    fontFamily: Typography.fontFamily.primary,
  },
  errorTextDetails: {
    color: Colors.error,
    fontSize: Typography.fontSize.sm,
    textAlign: 'center',
    marginBottom: Spacing.md,
    fontFamily: Typography.fontFamily.primary,
  },
  leftFilters: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  filterButton: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    borderRadius: Borders.radius.pill,
    backgroundColor: Colors.tertiary,
  },
  filterText: {
    fontSize: Typography.fontSize.md,
    color: Colors.text.secondary,
    fontFamily: Typography.fontFamily.primary,
  },
  selectedFilter: {
    backgroundColor: Colors.primary,
  },
  selectedFilterText: {
    color: Colors.text.tertiary,
    fontWeight: Typography.fontWeight.medium,
  },
  dateFilterPressable: {
    borderRadius: Borders.radius.pill,
    borderColor: Colors.border.medium,
    borderWidth: Borders.width.thin,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
  },
  dateFilterPressableContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dateFilterPressableLabel: {
    fontSize: Typography.fontSize.md,
    color: Colors.text.secondary,
    marginRight: Spacing.xs,
    fontFamily: Typography.fontFamily.primary,
  },
});