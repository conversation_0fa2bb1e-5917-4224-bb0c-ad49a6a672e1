import React from 'react';
import { StyleSheet, FlatList } from 'react-native';
import { ThemedView } from "@/components/UI/ThemedView";
import VendorDetailsCard from "@/components/party-dashboard/VendorDetailsCard";
import vendors_marketplace from "@/data/vendors_marketplace.json";
import { GestureHandlerRootView } from 'react-native-gesture-handler';

export default function Name({ name }: { name: string }) {
    return (
        <GestureHandlerRootView style={{ flex: 1 }}>
            <ThemedView>
                <FlatList
                    data={vendors_marketplace}
                    keyExtractor={(item) => item.id}
                    renderItem={({ item }) => (
                            <ThemedView style={styles.cardContainer}>
                                <VendorDetailsCard
                                    vendorId={item.id}
                                    name={item.name}
                                    mainFeature={item.main_feature}
                                    rating={item.rating}
                                    images={item.images}
                                />
                            </ThemedView>
                    )}
                    contentContainerStyle={styles.listContent}
                />
            </ThemedView>
        </GestureHandlerRootView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    title: {
        padding: 20,
        textAlign: 'center',
    },
    listContent: {
        paddingHorizontal: 20,
        paddingBottom: 20,
    },
    cardContainer: {
        marginBottom: 20,
    },
});