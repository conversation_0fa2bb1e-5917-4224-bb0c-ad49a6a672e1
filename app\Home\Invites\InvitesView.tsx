import React, { useState, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  ScrollView,
  SafeAreaView,
  Alert,
} from 'react-native';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { StackScreenProps } from '@react-navigation/stack';
import { Rsvp, Party } from '@/app/(home)/PartyDetails/NewPartyDetailsResponse.model';
import InviteRow from './InvitesRow';
import { PartyDetailsRootList } from '@/components/create-event/Navigation/PartyDetailsRootList';
import { DELETE_GUEST } from './SaveOrSend/SaveOrSend.data';
import { useMutation } from '@apollo/client';
import { isHost } from '@/app/(home)/utils/reusableFunctions';
import { ProfileDetail, Search } from '@/components/icons';
import { Colors, Icons } from '@/constants/DesignSystem';
import NoInvitesSent from '@/components/Illustrations/NoInvitesSent';

type InvitesParams = {
  partyDetails: Party;
};

type InvitesViewProps = StackScreenProps<PartyDetailsRootList, 'InvitesView'> & InvitesParams;

const InvitesView: React.FC<InvitesViewProps> = ({ route }) => {
  const { partyDetails } = route.params;
  const invites: Rsvp[] = partyDetails?.rsvps ?? [];
  const navigation = useNavigation<NavigationProp<PartyDetailsRootList>>();

  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [searchText, setSearchText] = useState('');
  const [filteredInvites, setFilteredInvites] = useState<Rsvp[]>(invites);
  const [deleteGuest] = useMutation(DELETE_GUEST);

  const statusCounts = useMemo(() => {
    const counts: { [key: string]: number } = { ACCEPTED: 0, REJECTED: 0, MAYBE: 0, PENDING: 0 };
    invites.forEach((invite) => {
      if (invite.status in counts) {
        counts[invite.status]++;
      }
    });
    return counts;
  }, [invites]);

  useEffect(() => {
    navigation.setOptions({
      headerTitle: isHost(partyDetails?.userRole ?? '') ? 'Invite' : 'Guests'
    });
  })

  const filterOptions = ['ACCEPTED', 'REJECTED', 'MAYBE', 'PENDING'];

  const toggleSelectedStatus = (status: string) => {
    setSelectedStatus((prevStatus) => (prevStatus === status ? null : status));
  };

  const onNewGuestsInvite = () => {
    console.log('Send new Invites button');
    navigation.navigate('SendInvitesView', { partyDetails: partyDetails });
  };

  const onDelete = (rsvp: Rsvp) => {
    const guestName = `${rsvp.guest?.user?.firstName ?? ''}`;
    Alert.alert(
      '',
      `Are you sure you want to remove ${guestName} from the Guest List?`,
      [
        {
          text: 'No',
          style: 'cancel',
        },
        {
          text: 'Yes',
          style: 'destructive',
          onPress: () => deleteGuestId(rsvp), // Pass the RSVP to deleteGuestId
        },
      ]
    );
  };

  const deleteGuestId = async (rsvp: Rsvp) => {
    try {
      const { data } = await deleteGuest({
        variables: {
          deleteGuestId: rsvp.guest?.id, // Use optional chaining
        },
      });

      if (data?.deleteGuest) {
        console.log('Guest deleted successfully');
        // Remove the deleted guest from the filteredInvites state
        setFilteredInvites((prevInvites) =>
          prevInvites.filter((invite) => invite.guest?.id !== rsvp.guest?.id)
        );
      } else {
        console.error('Failed to delete guest');
      }
    } catch (error) {
      console.error('Error deleting guest:', error);
    }
  };

  const filteredAndSortedInvites = useMemo(() => {
    return filteredInvites
      .filter((invite) => {
        const fullName = `${invite.guest?.user?.firstName ?? ''} ${invite.guest?.user?.lastName ?? ''}`.toLowerCase();
        return searchText === '' || fullName.includes(searchText.toLowerCase());
      })
      .filter((invite) => {
        if (!selectedStatus) return true;
        return invite.status === selectedStatus;
      })
      .sort((a, b) => {
        const nameA = `${a.guest?.user?.firstName ?? ''} ${a.guest?.user?.lastName ?? ''}`.toLowerCase();
        const nameB = `${b.guest?.user?.firstName ?? ''} ${b.guest?.user?.lastName ?? ''}`.toLowerCase();
        return nameA.localeCompare(nameB);
      });
  }, [selectedStatus, filteredInvites, searchText]);

  const renderNoResultsMessage = () => (
    <Text style={styles.noResultsText}>No matching guests found.</Text>
  );

  const renderNoGuestsView = () => (
    <View style={styles.noGuestsContainer}>
      <NoInvitesSent />
      <Text style={styles.noGuestsText}>No invites sent. Time to wow your guests!</Text>
      <Text style={styles.noGuestsSubText}>👉 Button: "Send Invites"</Text>
    </View>
  );

  const renderGuestList = () => (
    <>
      <View style={styles.searchBarContainer}>
      <Search size={Icons.size.md} color={Colors.mediumGray} />
        <TextInput
          placeholder="Find a guest..."
          placeholderTextColor={Colors.mediumGray}
          style={styles.searchBar}
          value={searchText}
          onChangeText={setSearchText}
        />
      </View>
      <View style={styles.chipContainer}>
        {filterOptions.map((option, index) => {
          const isSelected = selectedStatus === option;
          return (
            <TouchableOpacity
              key={index}
              style={[
                styles.chip,
                isSelected ? styles.chipSelected : styles.chipUnselected,
              ]}
              onPress={() => toggleSelectedStatus(option)}
            >
              <Text style={isSelected ? styles.chipTextSelected : styles.chipTextUnselected}>
                {`${option} ${statusCounts[option]}`}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
      {filteredAndSortedInvites.length > 0
        ? filteredAndSortedInvites.map((invite, index) => (
          <InviteRow key={`invite-${index}`} rsvp={invite} onDelete={onDelete} />
        ))
        : renderNoResultsMessage()}
    </>
  );

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <KeyboardAwareScrollView style={styles.mainContainer}>
        <ScrollView style={{ flex: 1 }} contentContainerStyle={{ paddingBottom: 75 }}>
          {invites.length === 0 ? renderNoGuestsView() : renderGuestList()}
        </ScrollView>
      </KeyboardAwareScrollView>
      {isHost(partyDetails?.userRole ?? '') && (
        <TouchableOpacity
          onPress={() => onNewGuestsInvite()}
          style={styles.floatingActionButton}
          activeOpacity={1}
        >
          <ProfileDetail size={Icons.size.lg} color={Colors.black} />
          <Text style={styles.fabText}>INVITE NEW GUESTS</Text>
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  floatingActionButton: {
    position: 'absolute',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    borderRadius: 8,
    backgroundColor: Colors.background.orange,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    bottom: 20,
    left: 20,
    right: 20,
    height: 56,
    marginBottom: 20,
  },
  chipContainer: {
    flexDirection: 'row',
    padding: 10,
    flexWrap: 'wrap'
  },
  chip: {
    paddingHorizontal: 20,
    paddingVertical: 3,
    margin: 5,
    borderWidth: 1,
    borderRadius: 20
  },
  chipSelected: {
    backgroundColor: Colors.background.secondary,
    borderColor: Colors.border.orange,
  },
  chipUnselected: {
    backgroundColor: Colors.background.transparent,
    borderColor: Colors.border.medium,
  },
  chipTextSelected: {
    color: Colors.text.primary,
  },
  chipTextUnselected: {
    color: Colors.text.secondary,
  },
  noResultsText: {
    marginTop: 20,
    textAlign: 'center',
    fontSize: 16,
  },
  fabText: {
    marginLeft: 10,
    color: Colors.black,
    fontFamily: 'Plus Jakarta Sans',
    fontSize: 16,
    fontWeight: 'bold',
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 10,
    paddingHorizontal: 10,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#F4F4F4',
  },
  searchBar: {
    flex: 1,
    paddingHorizontal: 10,
    color: Colors.secondary,
  },
  searchIcon: {
    marginRight: 10,
  },
  noGuestsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  noGuestsText: {
    fontSize: 24,
    color: 'black',
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: '500',
    paddingTop: 10,
    textAlign: 'center'
  },
  noGuestsSubText: {
    fontSize: 18,
    color: 'black',
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: '500',
  },
});

export default InvitesView;