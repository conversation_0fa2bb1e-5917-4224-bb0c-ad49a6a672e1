import { Pressable, View } from 'react-native';
import { Text } from 'react-native-paper';
import { styles } from "../Filter.styles";
import { MultipleAssigneeField } from '@/components/Task/Form/Fields/MultipleAssigneeField/MultipleAssigneeField';
import { Assignee } from '@/components/Task/SharedInterfaces';
import { Icons, Colors } from '@/constants/DesignSystem';
import { DropDown, Cross } from '@/components/icons';

interface AssigneeFilterProps {
  selectedAssignees: Assignee[];
  setSelectedAssignees: (assignees: Assignee[]) => void;
  isAssigneeModalOpen: boolean;
  setIsAssigneeModalOpen: (isOpen: boolean) => void;
}

export function AssigneeFilter({
  selectedAssignees,
  setSelectedAssignees,
  isAssigneeModalOpen,
  setIsAssigneeModalOpen,
}: AssigneeFilterProps) {
  return (
    <>
      <View style={[styles.oddGridItemForAssignee, { width: '30%' }]}>
        <Text style={[styles.gridItemTextLabel]}>Assignee</Text>
      </View>
      <View style={[styles.evenGridItemForAssignee, { width: '70%' }]}>
        <Pressable
          onTouchStart={() => setIsAssigneeModalOpen(true)}
          style={({ pressed }) => [
            styles.assigneeButton,
            pressed && styles.dateButtonPressed
          ]}
        >
          <View style={styles.assigneeButtonContent}>
            <Text style={styles.assigneeButtonText}>
              Select
            </Text>
            <DropDown 
              style={styles.icon}
              size={Icons.size.md} 
              color={Colors.text.secondary} 
            />
          </View>
        </Pressable>

        {selectedAssignees.length > 0 && (
          <View style={styles.selectedAssigneesContainer}>
            {selectedAssignees.map((assignee) => (
              <Pressable
                key={assignee.id}
                style={({ pressed }) => [
                  styles.selectedAssigneeChip,
                  pressed && styles.selectedAssigneeChipPressed
                ]}
                onTouchStart={() => {
                  setSelectedAssignees(selectedAssignees.filter(a => a.id !== assignee.id));
                }}
              >
                <View style={styles.selectedAssigneeContent}>
                  <Text style={styles.selectedAssigneeText}>
                    {`${assignee.firstName} ${assignee.lastName}`.trim()}
                  </Text>
                  <View style={styles.iconContainer}>
                    <Cross size={Icons.size.md} color={Colors.text.secondary} />
                  </View>
                </View>
              </Pressable>
            ))}
          </View>
        )}
        
        <MultipleAssigneeField 
          selectedAssignees={selectedAssignees}
          onAssigneesSelect={setSelectedAssignees}
          isOpen={isAssigneeModalOpen}
          onClose={() => setIsAssigneeModalOpen(false)}
        />
      </View>
    </>
  );
} 