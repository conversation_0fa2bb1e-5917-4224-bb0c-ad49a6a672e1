import { EventSourcePolyfill } from 'event-source-polyfill';

const DEFAULT_TIMEOUT = 120000;
const RETRY_INTERVAL = 3000;

interface ExtendedEventSourcePolyfill extends EventSource {
    addEventListener<K extends keyof EventSourceEventMap>(
        type: K,
        listener: (this: EventSource, ev: EventSourceEventMap[K]) => any,
        options?: boolean | AddEventListenerOptions
    ): void;
    addEventListener(
        type: string,
        listener: (this: EventSource, event: MessageEvent) => any,
        options?: boolean | AddEventListenerOptions
    ): void;

    removeEventListener<K extends keyof EventSourceEventMap>(
        type: K,
        listener: (this: EventSource, ev: EventSourceEventMap[K]) => any,
        options?: boolean | EventListenerOptions
    ): void;
    removeEventListener(
        type: string,
        listener: (this: EventSource, event: MessageEvent) => any,
        options?: boolean | EventListenerOptions
    ): void;

    readyState: number; // Add the readyState property
}

// Extend EventSourceInit to include the custom heartbeatTimeout property
interface EventSourceInitWithHeartbeat extends EventSourceInit {
    heartbeatTimeout?: number;
}

export class EventSourceService {
    private eventSource: ExtendedEventSourcePolyfill | null = null;
    private url: string;
    private headers: Record<string, string>;
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 5;
    private listeners: Record<string, ((event: MessageEvent) => void)[]> = {};
    private reconnectTimer: NodeJS.Timeout | null = null;

    constructor(url: string, headers: Record<string, string> = {}) {
        this.url = url;
        this.headers = headers;
    }

    connect() {
        this.close(); // Ensure any existing connection is closed

        try {
            this.eventSource = new EventSourcePolyfill(this.url, {
                headers: this.headers,
                heartbeatTimeout: DEFAULT_TIMEOUT,
            } as EventSourceInitWithHeartbeat);

            this.eventSource.onopen = () => {
                console.log('EventSource connection opened');
                this.reconnectAttempts = 0; // Reset reconnect attempts
            };

            this.eventSource.onmessage = (event) => {
                console.log('Message received:', event.data);
            };

            this.eventSource.onerror = (error) => {
                console.error('EventSource error:', error);
                if (this.eventSource?.readyState === EventSource.CLOSED) {
                    console.log('Connection closed. Attempting to reconnect...');
                    this.handleReconnection();
                }
            };

            Object.keys(this.listeners).forEach(eventName => {
                this.listeners[eventName].forEach(listener => {
                    this.eventSource?.addEventListener(eventName, listener);
                });
            });

        } catch (error) {
            console.error('Failed to create EventSource:', error);
            this.handleReconnection();
        }
    }

    private handleReconnection() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            if (this.reconnectTimer) {
                clearTimeout(this.reconnectTimer);
            }
            this.reconnectTimer = setTimeout(() => {
                this.connect();
            }, RETRY_INTERVAL);
        } else {
            console.error('Max reconnection attempts reached');
        }
    }

    addEventListener(eventName: string, callback: (event: MessageEvent) => void) {
        if (!this.listeners[eventName]) {
            this.listeners[eventName] = [];
        }
        this.listeners[eventName].push(callback);
        this.eventSource?.addEventListener(eventName, callback);
    }

    removeEventListener(eventName: string, callback: (event: MessageEvent) => void) {
        this.eventSource?.removeEventListener(eventName, callback);
        if (this.listeners[eventName]) {
            this.listeners[eventName] = this.listeners[eventName].filter(
                listener => listener !== callback
            );
        }
    }

    close() {
        if (this.eventSource) {
            console.log('Closing EventSource connection');
            this.eventSource.close();
            this.eventSource = null;
        }
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    }
}