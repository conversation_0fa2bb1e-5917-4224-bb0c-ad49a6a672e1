import { gql } from "@apollo/client/core"

export const CREATE_VENUE_ADDRESS = gql`
mutation CreateVenueAddress($input: VenueAddressInput!) {
  createVenueAddress(input: $input) {
    ... on VenueAddressResponse {
      status
      message
      result {
        venueAddress {
          id
          name
          placeId
          address
        }
      }
    }
  }
}
`;

export const CREATE_ADDRESS_BOOK = gql`
mutation CreateAddressBook($input: AddressBookInput!) {
  createAddressBook(input: $input) {
    ... on AddressBookResponse {
      status
      message
      result {
        addressBook {
          id
          label
        }
      }
    }
  }
}
`;

export const UPDATE_VENUE_DETAILS = gql`
mutation UpdateVenueDetails($updateVenueAddressId: ID!, $input: VenueAddressUpdateInput!, $updateAddressBookId: ID!, $updateAddressBookInput2: AddressBookUpdateInput!) {
  updateVenueAddress(id: $updateVenueAddressId, input: $input) {
    ... on VenueAddressResponse {
      result {
        venueAddress {
          placeId
          name
          id
          address
        }
      }
      message
      status
    }
  }
  updateAddressBook(id: $updateAddressBookId, input: $updateAddressBookInput2) {
    ... on AddressBookResponse {
      result {
        addressBook {
          id
          label
        }
      }
      status
      message
    }
  }
}
`;

export const DELETE_VENUE_Details = gql`
mutation DeleteVenueDetails($deleteVenueAddressId: ID!, $deleteAddressBookId: ID!) {
  deleteVenueAddress(id: $deleteVenueAddressId) {
    ... on VenueAddressResponse {
      message
      result {
        venueAddress {
          id
          name
        }
      }
      status
    }
  }
  deleteAddressBook(id: $deleteAddressBookId) {
    ... on AddressBookResponse {
      message
      result {
        addressBook {
          id
          label
        }
      }
      status
    }
  }
}
`;
