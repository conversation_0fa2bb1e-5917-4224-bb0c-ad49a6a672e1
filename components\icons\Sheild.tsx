import * as React from "react";
import Svg, {
  G,
  <PERSON>,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Rect,
} from "react-native-svg";
import { CommonProps } from ".";

const Sheild = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Sheild_svg__a)">
            <Path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M0.5 1.67793C0.5 1.03304 1.03384 0.499756 1.67857 0.499756H10.3214C10.9662 0.499756 11.5 1.03304 11.5 1.67793V3.92043C11.5 7.16968 9.52794 10.2109 6.46257 11.4064C6.32821 11.4588 6.16876 11.501 5.99452 11.5001C5.82402 11.4993 5.66837 11.4574 5.53743 11.4064C2.4721 10.2109 0.5 7.16968 0.5 3.92043V1.67793ZM8.79761 4.04217C9.01384 3.79893 8.99192 3.42645 8.74866 3.21023C8.50541 2.99401 8.13297 3.01592 7.91675 3.25917L5.13413 6.38962L3.99648 5.53638C3.73611 5.34111 3.36675 5.39388 3.17148 5.65424C2.9762 5.9146 3.02897 6.28397 3.28933 6.47924L4.86076 7.65781C5.10594 7.8417 5.45115 7.80695 5.65477 7.57788L8.79761 4.04217Z"
              fill="url(#Sheild_svg__b)"
            />
          </G>
          <Defs>
            <LinearGradient
              id="Sheild_svg__b"
              x1="6"
              y1="-0.827874"
              x2="6"
              y2="14.0289"
              gradientUnits="userSpaceOnUse"
            >
              <Stop stopColor={gradientStartColor} />
              <Stop offset="1" stopColor={gradientEndColor} />
            </LinearGradient>
            <ClipPath id="Sheild_svg__a">
              <Rect width={12} height={12} fill="white" />
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G clipPath="url(#Sheild-1_svg__a)">
            <Path
              d="M6.28764 11.1959C6.10062 11.268 5.89345 11.268 5.70642 11.1959C4.24713 10.6356 2.99211 9.6459 2.10715 8.35735C1.22219 7.06883 0.748933 5.54217 0.749879 3.97902V1.55726C0.749879 1.34316 0.834929 1.13783 0.986318 0.98644C1.13771 0.83505 1.34304 0.75 1.55713 0.75H10.4369C10.651 0.75 10.8564 0.83505 11.0078 0.98644C11.1592 1.13783 11.2442 1.34316 11.2442 1.55726V3.97902C11.2451 5.54217 10.7719 7.06883 9.88695 8.35735C9.00194 9.6459 7.74695 10.6356 6.28764 11.1959Z"
              stroke={color}
              strokeWidth={strokeWidth}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <Path
              d="M8.4417 3.55817L5.18589 7.22096L3.55798 6.00003"
              stroke={color}
              strokeWidth={strokeWidth}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </G>
          <Defs>
            <ClipPath id="Sheild-1_svg__a">
              <Rect width={12} height={12} fill="white" />
            </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  );
};
export default Sheild;
