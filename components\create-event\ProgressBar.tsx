import React from 'react';
import { View } from 'react-native';
import { useTheme } from 'react-native-paper';

interface ProgressBarProps {
  currentStep: number;
  totalSteps: number;
}

export function ProgressBar({ currentStep, totalSteps }: ProgressBarProps) {
  const theme = useTheme();
  const progress = (currentStep / totalSteps) * 100;

  return (
    <View style={{ 
      height: 4, 
      backgroundColor: theme.colors.surfaceVariant,
      marginHorizontal: 16,
      marginVertical: 8,
    }}>
      <View 
        style={{
          height: '100%',
          width: `${progress}%`,
          backgroundColor: theme.colors.primary,
          borderRadius: 2,
        }}
      />
    </View>
  );
}