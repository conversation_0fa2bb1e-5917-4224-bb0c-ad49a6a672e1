import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { VendorDetails } from '@/constants/displayNames';
interface SpecialitiesProps {
    specialities: string;
}

export function Specialities({ specialities }: SpecialitiesProps) {
    return (
        <View style={styles.content}>
            <Text style={styles.title}>{VendorDetails.SPECIALITIES}</Text>
            <Text style={styles.description}>{specialities}</Text>
        </View>
    );
}

const styles = StyleSheet.create({
    content: {
        marginTop: '4%',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
        marginTop: 16,
    },
    description: {
        fontSize: 14,
        lineHeight: 24,
    },
});