import React, { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { View, Platform, SafeAreaView, KeyboardAvoidingView } from 'react-native';
import { useMutation, useQuery } from '@apollo/client';
import { Portal, useTheme, ActivityIndicator } from 'react-native-paper';
import { GET_PARTY_TYPES, GET_LOCATIONS, ADD_PARTY, GET_PARTY_BY_ID, UPDATE_PARTY } from './AddParty.data'
import { PartyFormData, LocationItem, PartyType, FormErrors, VendorType } from './AddParty.models'
import { DatePickerModal } from '@/components/UI/ReusableComponents/DatePickerModal';
import { TimePickerModal } from '@/components/UI/ReusableComponents/TimePickerModal';
import { SearchModal } from '@/components/UI/ReusableComponents/SerachModal';
import { AddPartyNames } from '@/constants/displayNames';
import { router, useLocalSearchParams } from 'expo-router';
import * as z from 'zod';
import { getCurrentDateTime } from '@/app/(home)/utils/reusableFunctions';
import AddPartyForm from '@/components/event-dashboard/AddPartyFormClone';
import { useToast } from '@/components/Toast/useToast';
import { handlePartySubmission, transformPartyData } from './handlePartySubmission';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useNavigation } from '@react-navigation/native';
import { useEventStore } from '@/store/eventStore';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { BottomSheetModalProvider, BottomSheetBackdrop, BottomSheetScrollView, BottomSheetView } from '@gorhom/bottom-sheet';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AddCohostsForm from '@/components/event-dashboard/AddParty/AddCohostsForm'
import { VendorTypes } from '@/constants/vendorTypes';

const AreaSearchModal = SearchModal<LocationItem>();

export function AddParty() {
  const theme = useTheme();
  const navigation = useNavigation();

  const { eventId, partyId } = useLocalSearchParams<{ eventId: string, partyId: string }>();
  const cohostSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ["90%", "90%"], []);

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: partyId ? 'Edit Party' : 'Add Party',
      headerTitleStyle: {
        fontSize: 24
      }
    });
  }, [navigation, partyId]);

  const { data: party, refetch } = useQuery(GET_PARTY_BY_ID, {
    variables: { getPartyByIdId: partyId },
    skip: !partyId,
    fetchPolicy: 'network-only'
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const { data: partyTypes } = useQuery(GET_PARTY_TYPES);
  const { data: locations } = useQuery(GET_LOCATIONS);
  const [addParty, { loading: addPartyLoading }] = useMutation(ADD_PARTY);
  const [updateParty, { loading: updatePartyLoading }] = useMutation(UPDATE_PARTY);
  const [formData, setFormData] = useState<PartyFormData>({
    partyType: '',
    partyName: '',
    date: new Date(),
    time: new Date(),
    area: null,
    eventId: eventId as string,
    guests: '',
    budget: '',
    services: [],
    cohosts: [],
  });
  useEffect(() => {
    if (partyId && party?.getPartyById) {
      const transformedData = transformPartyData(party);
      setFormData(prev => ({
        ...transformedData,
      }));
    }
  }, [partyId, party]);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [showAreaSearch, setShowAreaSearch] = useState(false);

  const toggleService = (serviceId: string) => {
    setFormData(prev => {
      const newServices = prev.services.includes(serviceId)
        ? prev.services.filter(id => id !== serviceId)
        : [...prev.services, serviceId];

      if (newServices.length > 0) {
        setFormErrors(prev => ({ ...prev, services: '' }));
      }
      return {
        ...prev,
        services: newServices
      };
    });
  };

  const availableServices = React.useMemo(() => {
    if (!partyTypes?.getMdPartyTypes?.result?.mdPartyTypes || !formData.partyType) {
      return [];
    }
      const selectedPartyType = partyTypes.getMdPartyTypes.result.mdPartyTypes
      .find((type: PartyType) => type.id === formData.partyType);
  
    if (!selectedPartyType) {
      return [];
    }
      const partyTypeVendors = selectedPartyType.vendorTypes || [];
      if (partyId) {
      const allVendorTypes = partyTypes.getMdPartyTypes.result.mdPartyTypes
        .flatMap((type: PartyType) => type.vendorTypes || []);
        const vendorMap = new Map();
      partyTypeVendors.forEach((vendor: VendorType) => {
        if (vendor.name?.toLowerCase() !== VendorTypes.VENUE) {
          vendorMap.set(vendor.id, vendor);
        }
      });
      allVendorTypes.forEach((vendor: VendorType) => {
        if (formData.services.includes(vendor.id) && vendor.name?.toLowerCase() !== VendorTypes.VENUE) {
          vendorMap.set(vendor.id, vendor);
        }
      });
      const initialSelectedVendors = allVendorTypes.filter((vendor: VendorType) =>
        formData.services.includes(vendor.id) && vendor.name?.toLowerCase() !== VendorTypes.VENUE
      );
      initialSelectedVendors.forEach((vendor: VendorType) => {
        vendorMap.set(vendor.id, vendor);
      });
  
      return Array.from(vendorMap.values());
    }
    return partyTypeVendors.filter((vendor: VendorType) => vendor.name?.toLowerCase() !== VendorTypes.VENUE);
  }, [partyTypes, formData.partyType, partyId, formData.services]);

  const locationItems: LocationItem[] = React.useMemo(() =>
    locations?.getMdServiceLocations?.result?.mdServiceLocations?.map((location: any) => ({
      id: location?.id,
      title: location?.city,
    })) || [],
    [locations]);
  const handlePartyTypeChange = (type: PartyType) => {
    const isOther = type.name.trim().toLowerCase() === AddPartyNames.PARTY_TYPE_OTHER;
    setFormData(prev => ({
      ...prev,
      partyType: type.id,
      partyName: isOther ? '' : type.name,
      services: []
    }));
    setFormErrors(prev => ({ ...prev, partyType: '' }));
  };

  const toast = useToast();

  const handleDateChange = (date: Date) => {
    setFormData(prev => ({ ...prev, date }));
    if (Platform.OS === 'ios') {
      setShowDatePicker(false);
    }
  };

  const handleTimeChange = (time: Date) => {
    setFormData(prev => ({ ...prev, time }));
    if (Platform.OS === 'ios') {
      setShowTimePicker(false);
    }
  };

  const formSchema = z.object({
    partyType: z.string().min(1, AddPartyNames.PARTY_TYPE_ERROR_MESSAGE),
    partyName: z.string().min(1, AddPartyNames.PARTY_NAME_ERROR_MESSAGE).max(300, 'Party Name must be at most 300 characters'),
    date: z.date(),
    time: z.date(),
    area: z.object({
      id: z.string(),
      title: z.string()
    }).nullable().refine(val => val !== null, AddPartyNames.AREA_ERROR_MESSAGE),
    guests: z.string().min(1, AddPartyNames.GUESTS_ERROR_MESSAGE),
    budget: z.string().min(1, AddPartyNames.BUDGET_ERROR_MESSAGE),
    services: z.array(z.string()).min(1, AddPartyNames.SERVICES_ERROR_MESSAGE)
  });

  const validateForm = () => {
    try {
      formSchema.parse(formData);
      setFormErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {};
        error.errors.forEach((err) => {
          if (err.path) {
            errors[err.path[0]] = err.message;
          }
        });
        setFormErrors(errors);
      }
      return false;
    }
  };

  const selectedEventId = useEventStore((state) => state.selectedEventId);

  const initialFormState: PartyFormData = {
    partyType: '',
    partyName: '',
    date: new Date(),
    time: new Date(),
    area: null,
    eventId: eventId as string,
    guests: '',
    budget: '',
    services: [],
    cohosts: [],
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    const success = await handlePartySubmission({
      formData,
      mutation: partyId ? updateParty : addParty,
      partyId,
      setFormErrors,
      toast,
      selectedEventId: selectedEventId || undefined
    });

    if (success) {
      // Reset form state
      setFormData(initialFormState);
      setFormErrors({});

      // Refetch the parties data to ensure it's updated
      router.back();
      await refetch();
      // Navigate back
    }
  };

  return (
    <GestureHandlerRootView>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <SafeAreaView style={{ flex: 1 }}>
          <BottomSheetModalProvider>
            <KeyboardAwareScrollView keyboardShouldPersistTaps='handled' contentContainerStyle={{ flexGrow: 1, marginBottom: Platform.OS === 'ios' ? 0 : '20%' }}>
              <View style={{ backgroundColor: theme.colors.background, paddingHorizontal: '8%', paddingTop: '4%', paddingBottom: '12%' }}>
                <AddPartyForm
                  partyId={partyId ? partyId : undefined}
                  formData={formData}
                  setFormData={setFormData}
                  formErrors={formErrors}
                  setFormErrors={setFormErrors}
                  partyTypes={partyTypes?.getMdPartyTypes?.result?.mdPartyTypes || []}
                  availableServices={availableServices}
                  handlePartyTypeChange={handlePartyTypeChange}
                  toggleService={toggleService}
                  setShowDatePicker={setShowDatePicker}
                  setShowTimePicker={setShowTimePicker}
                  setShowAreaSearch={setShowAreaSearch}
                  handlePartySubmission={handleSubmit}
                  cohostSheetRef={cohostSheetRef}
                />
              </View>
            </KeyboardAwareScrollView>
            <Portal>
              <DatePickerModal
                visible={showDatePicker}
                onDismiss={() => setShowDatePicker(false)}
                value={formData.date}
                onChange={handleDateChange}
                title={AddPartyNames.SELECT_DATE}
                minimumDate={getCurrentDateTime()}
              />
              <TimePickerModal
                visible={showTimePicker}
                onDismiss={() => setShowTimePicker(false)}
                value={formData.time}
                onChange={handleTimeChange}
                title={AddPartyNames.SELECT_TIME}
                minimumDate={getCurrentDateTime()}
                selectedDate={formData.date}
              />
              <AreaSearchModal
                visible={showAreaSearch}
                onDismiss={() => setShowAreaSearch(false)}
                items={locationItems}
                onSelect={(item) => {
                  setFormData(prev => ({ ...prev, area: item }));
                  setFormErrors(prev => ({ ...prev, area: '' }));
                }}
                searchPlaceholder={AddPartyNames.SEARCH_LOCATION_PLACEHOLDER}
                noOptionsText={AddPartyNames.NO_LOCATION_AVAILABLE}
              />

              {(addPartyLoading || updatePartyLoading) && (
                <View
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    justifyContent: 'center',
                    alignItems: 'center',
                    zIndex: 9999,
                  }}
                >
                  <ActivityIndicator size="large" color={theme.colors.primary} />
                </View>
              )}
            </Portal>
            <BottomSheetModal
              ref={cohostSheetRef}
              index={1}
              snapPoints={snapPoints}
              enablePanDownToClose
              backdropComponent={(props) => (
                <BottomSheetBackdrop
                  {...props}
                  appearsOnIndex={1}
                  disappearsOnIndex={0}
                />
              )}
            >
              <BottomSheetScrollView keyboardShouldPersistTaps='handled'>
                <BottomSheetView
                  style={{
                    flex: 1,
                    marginBottom: "10%",
                    paddingBottom: "10%",
                    marginHorizontal: "4%",
                  }}
                >
                  <AddCohostsForm
                    existingCohosts={formData?.cohosts || []}
                    onCohostAdd={(newCohost) => {
                      setFormData(prev => ({
                        ...prev,
                        cohosts: [...prev.cohosts, newCohost]
                      }));
                      cohostSheetRef.current?.dismiss();
                    }}
                  />
                </BottomSheetView>
              </BottomSheetScrollView>
            </BottomSheetModal>
          </BottomSheetModalProvider>
        </SafeAreaView>
      </KeyboardAvoidingView>
    </GestureHandlerRootView>
  );
}

export default AddParty;