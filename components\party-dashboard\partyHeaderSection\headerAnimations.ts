import { Platform } from 'react-native';
import { interpolate, useAnimatedStyle, interpolateColor } from 'react-native-reanimated';
import { Colors } from '@/constants/Colors';
import Animated from 'react-native-reanimated';

export const HEADER_MAX_HEIGHT = 250;
export const HEADER_MIN_HEIGHT = 60;
export const TAB_HEIGHT = 50; // Adjust this value based on your EventPreferencesScrollView height
export const HEADER_SCROLL_DISTANCE = HEADER_MAX_HEIGHT - HEADER_MIN_HEIGHT;

export const useHeaderAnimations = (scrollY: Animated.SharedValue<number>) => {
  const headerAnimatedStyle = useAnimatedStyle(() => ({
    height: interpolate(
      scrollY.value,
      [0, HEADER_SCROLL_DISTANCE],
      [HEADER_MAX_HEIGHT, HEADER_MIN_HEIGHT],
      'clamp'
    ),
    backgroundColor: interpolateColor(
      scrollY.value,
      [0, HEADER_SCROLL_DISTANCE],
      ['rgba(0, 0, 0, 0.6)', Colors.light.background]
    ),
  }));

  const imageAnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(
      scrollY.value,
      [0, HEADER_SCROLL_DISTANCE / 2],
      [1, 0],
      'clamp'
    ),
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(
      scrollY.value,
      [0, HEADER_SCROLL_DISTANCE / 2],
      [1, 0],
      'clamp'
    ),
    transform: [{
      translateY: interpolate(
        scrollY.value,
        [0, HEADER_SCROLL_DISTANCE],
        [Platform.OS === 'ios' ? 90 : 85, 0],
        'clamp'
      )
    }],
  }));

  const scrollViewAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{
      translateY: interpolate(
        scrollY.value,
        [0, HEADER_SCROLL_DISTANCE],
        [HEADER_MAX_HEIGHT - 50, HEADER_MIN_HEIGHT],
        'clamp'
      )
    }]
  }));

  return {
    headerAnimatedStyle,
    imageAnimatedStyle,
    contentAnimatedStyle,
    scrollViewAnimatedStyle,
  };
};

export const useTopRowAnimations = (scrollY: Animated.SharedValue<number>) => {
  const topRowStyle = useAnimatedStyle(() => ({
    opacity: interpolate(
      scrollY.value,
      [0, HEADER_SCROLL_DISTANCE],
      [0.8, 1],
      'clamp'
    ),
    backgroundColor: interpolateColor(
      scrollY.value,
      [0, HEADER_SCROLL_DISTANCE],
      ['transparent', Colors.light.background]
    ),
    paddingTop: interpolate(
      scrollY.value,
      [0, HEADER_SCROLL_DISTANCE],
      [Platform.OS === 'ios' ? 50 : 45, Platform.OS === 'ios' ? 40 : 35],
      'clamp'
    ),
  }));

  const titleStyle = useAnimatedStyle(() => ({
    fontSize: interpolate(
      scrollY.value,
      [0, HEADER_SCROLL_DISTANCE],
      [20, 18],
      'clamp'
    ),
    color: interpolateColor(
      scrollY.value,
      [0, HEADER_SCROLL_DISTANCE],
      [Colors.dark.text, Colors.light.text]
    ),
    opacity: interpolate(
      scrollY.value,
      [0, HEADER_SCROLL_DISTANCE / 2, HEADER_SCROLL_DISTANCE],
      [1, 0, 1],
      'clamp'
    ),
  }));

  const iconStyle = useAnimatedStyle(() => ({
    color: interpolateColor(
      scrollY.value,
      [0, HEADER_SCROLL_DISTANCE],
      [Colors.dark.text, Colors.light.text]
    ),
    opacity: interpolate(
      scrollY.value,
      [0, HEADER_SCROLL_DISTANCE / 2, HEADER_SCROLL_DISTANCE],
      [1, 0, 1],
      'clamp'
    ),
  }));

  return {
    topRowStyle,
    titleStyle,
    iconStyle,
  };
};