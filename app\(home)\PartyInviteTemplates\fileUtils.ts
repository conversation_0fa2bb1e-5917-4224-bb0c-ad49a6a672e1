import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';

/**
 * Resolves a file URI to ensure it's properly formatted for uploads
 * Handles different URI formats across platforms
 */
export async function resolveFileUri(uri: string): Promise<string> {
  // If it's already a proper file URI or remote URL, return as is
  if (uri.startsWith('file:///') || uri.startsWith('http')) {
    return uri;
  }

  // For Android content:// URIs, we need to copy the file to app's cache directory
  if (Platform.OS === 'android' && uri.startsWith('content://')) {
    try {
      console.log('Resolving Android content URI:', uri);
      
      // Generate a temporary file path in the cache directory
      const fileExtension = uri.split('.').pop() || 'jpg';
      const fileName = `temp_${Date.now()}.${fileExtension}`;
      const destinationUri = `${FileSystem.cacheDirectory}${fileName}`;
      
      console.log('Copying file to:', destinationUri);
      
      // Copy the file to our cache directory
      await FileSystem.copyAsync({
        from: uri,
        to: destinationUri
      });
      
      console.log('File copied successfully');
      return destinationUri;
    } catch (error) {
      console.error('Error resolving Android content URI:', error);
      throw new Error(`Failed to resolve file URI: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // For other Android URIs that don't have the file:// prefix
  if (Platform.OS === 'android' && !uri.startsWith('file:')) {
    return `file://${uri}`;
  }

  // Return the original URI for other cases
  return uri;
}

/**
 * Gets file info from a URI
 */
export async function getFileInfo(uri: string): Promise<{ size: number; exists: boolean }> {
  try {
    // For remote URLs, we can't get file info
    if (uri.startsWith('http')) {
      return { size: 0, exists: true };
    }
    
    // Resolve the URI first
    const resolvedUri = await resolveFileUri(uri);
    
    // Get file info
    const fileInfo = await FileSystem.getInfoAsync(resolvedUri);
    return {
      size: fileInfo.size || 0,
      exists: fileInfo.exists
    };
  } catch (error) {
    console.error('Error getting file info:', error);
    return { size: 0, exists: false };
  }
}

/**
 * Creates a file object suitable for upload
 */
export async function createUploadFileObject(uri: string, type: string | null, name: string): Promise<{
  uri: string;
  type: string;
  name: string;
  size: number;
}> {
  // Resolve the URI to ensure it's properly formatted
  const resolvedUri = await resolveFileUri(uri);
  
  // Get file info
  const fileInfo = await getFileInfo(resolvedUri);
  
  return {
    uri: resolvedUri,
    type: type || 'image/jpeg',
    name,
    size: fileInfo.size
  };
}
