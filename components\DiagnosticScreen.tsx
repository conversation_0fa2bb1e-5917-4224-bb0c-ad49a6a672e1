import { View, Text, StyleSheet } from 'react-native';
import { Modal, IconButton } from 'react-native-paper';
import { useState, useEffect } from 'react';
import { useTheme } from 'react-native-paper';
import { APP_CONFIG, getVersionDetails } from '@/config/appConfig';
import { APP_SETTINGS_VERSION } from '@/constants/bottomNavTabs';
import * as Clipboard from 'expo-clipboard';
import { useAuth } from '@clerk/clerk-expo';

interface DiagnosticScreenProps { 
  visible: boolean;
  onDismiss: () => void;
}

export function DiagnosticScreen({ visible, onDismiss }: DiagnosticScreenProps) {
  const [diagnosticInfo, setDiagnosticInfo] = useState({
    version: 'Loading...',
    environment: 'Loading...',
    source: 'Loading...',
    lastUpdated: 'Loading...',
    fallbackVersion: APP_SETTINGS_VERSION.VERSION,
    token: 'Loading...',
  });
  const theme = useTheme();
  const { getToken } = useAuth();

  useEffect(() => {
    async function loadVersionInfo() {
      if (visible) {
        const versionInfo = await getVersionDetails();
        const token = await getToken();
        setDiagnosticInfo({
          version: versionInfo.version,
          environment: APP_CONFIG.environment,
          source: versionInfo.source,
          lastUpdated: new Date(versionInfo.lastUpdated).toLocaleString(),
          fallbackVersion: APP_SETTINGS_VERSION.VERSION,
          token: token || 'No token available',
        });
      }
    }

    loadVersionInfo();
  }, [visible]);

  const getSourceColor = (source: string) => {
    switch (source) {
      case 'env':
        return theme.colors.primary;
      case 'storage':
        return theme.colors.secondary;
      default:
        return theme.colors.error;
    }
  };

  const copyToClipboard = async (text: string) => {
    await Clipboard.setStringAsync(text);
  };

  const InfoRow = ({ label, value, canCopy = false }: { label: string; value: string; canCopy?: boolean }) => (
    <View style={styles.infoRow}>
      <Text style={[styles.label, { color: theme.colors.onSurface }]}>{label}:</Text>
      <View style={styles.valueContainer}>
        <Text 
          style={[
            styles.value, 
            { color: label === 'Source' ? getSourceColor(value) : theme.colors.onSurface },
            canCopy && styles.truncatedText
          ]}
          numberOfLines={1}
        >
          {value}
        </Text>
        {canCopy && (
          <IconButton
            icon="content-copy"
            size={20}
            onPress={() => copyToClipboard(value)}
            style={styles.copyButton}
          />
        )}
      </View>
    </View>
  );

  return (
    <Modal visible={visible} onDismiss={onDismiss} contentContainerStyle={styles.modal}>
      <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.title, { color: theme.colors.onSurface }]}>
          Diagnostic Information
        </Text>
        
        <InfoRow label="Current Version" value={diagnosticInfo.version} />
        <InfoRow label="Source" value={diagnosticInfo.source} />
        <InfoRow label="Last Updated" value={diagnosticInfo.lastUpdated} />
        <InfoRow label="Fallback Version" value={diagnosticInfo.fallbackVersion} />
        <InfoRow label="Environment" value={diagnosticInfo.environment} />
        <InfoRow label="Auth Token" value={diagnosticInfo.token} canCopy={true} />
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modal: {
    padding: 20,
    margin: 20,
    zIndex: 1000,
  },
  container: {
    padding: 16,
    borderRadius: 8,
    elevation: 5,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    flex: 0.4,
  },
  valueContainer: {
    flex: 0.6,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  value: {
    fontSize: 16,
    flex: 1,
  },
  truncatedText: {
    marginRight: 8,
  },
  copyButton: {
    margin: 0,
    padding: 0,
  }
});