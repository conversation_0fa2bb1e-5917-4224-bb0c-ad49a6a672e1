import React from 'react';
import { View } from 'react-native';
import { Text, TextInput } from 'react-native-paper';
import { DatePickerModal } from '../UI/ReusableComponents/DatePickerModal';
import { Portal } from 'react-native-paper';

interface DateScreenProps {
  date: Date | null;
  onDateChange: (date: Date) => void;
}

export function DateScreen({ date, onDateChange }: DateScreenProps) {
  const [showDatePicker, setShowDatePicker] = React.useState(false);
  const minimumDate = new Date();

  // Set current date as default when component mounts
  React.useEffect(() => {
    if (!date) {
      onDateChange(new Date());
    }
  }, []);

  const handleOpenDatePicker = () => {
    setShowDatePicker(true);
  };

  return (
    <View style={{ padding: 16 }}>
      <Text variant="headlineMedium" style={{ marginBottom: 24 }}>
        When is your event?
      </Text>

      <TextInput
        label="Date"
        value={date ? date.toLocaleDateString() : ''}
        editable={false}
        right={<TextInput.Icon icon="calendar" onPress={handleOpenDatePicker} />}
        onPressIn={handleOpenDatePicker}
      />

      <Portal>
        <DatePickerModal
          visible={showDatePicker}
          onDismiss={() => setShowDatePicker(false)}
          value={date || new Date()}
          onChange={onDateChange}
          title="Select Event Date"
          minimumDate={minimumDate}
        />
      </Portal>
    </View>
  );
}