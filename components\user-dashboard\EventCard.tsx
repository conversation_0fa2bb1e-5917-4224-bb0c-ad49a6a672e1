import React, { useRef, useState } from 'react';
import { View, TouchableOpacity, LayoutRectangle, Pressable, Image } from 'react-native';
import { ThemedText } from '@/components/UI/ThemedText';
import { Ionicons } from '@expo/vector-icons';
import { EventOptionsModal } from './EventOptionsModal';
import { Colors } from '@/constants/Colors';
import { useNavigationStore } from '@/store/navigationStore';
import { NAV_BAR_VIEW_TYPE } from '@/constants/bottomNavTabs';
import { LinearGradient } from 'expo-linear-gradient';
import { styles } from './EventCard.styles';
import { EventStatusType } from '@/constants/eventTabTypes';
import { Text } from 'react-native-paper';

interface EventInfo {
    id: string;
    title: string;
    eventPartyDate?: string;
    status: string;
    image: string;
    isFavorite?: boolean;
    nextPartyName?: string;
    showNextParty?: boolean;
}

interface EventCardCloneProps {
    eventInfo: EventInfo;
    onEdit?: () => void;
    onDelete?: () => void;
    onPress?: () => void;
}

export const EventCard: React.FC<EventCardCloneProps> = ({
    eventInfo,
    onEdit,
    onDelete,
    onPress,
}) => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [iconLayout, setIconLayout] = useState<LayoutRectangle | null>(null);
    const iconRef = useRef<any>(null);
    const { setViewType, setSelectedEventId } = useNavigationStore();

    const handleIconPress = () => {
        iconRef.current?.measure((x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
            setIconLayout({ x: pageX, y: pageY, width, height });
            setIsModalVisible(true);
        });
    };

    const handleCardPress = () => {
        if (eventInfo.status.toLowerCase().includes('host')) {
            setViewType(NAV_BAR_VIEW_TYPE.HOST);
            setSelectedEventId(eventInfo.id);
        }
        onPress?.();
    };
    
    const formattedDate = eventInfo.eventPartyDate || '';

    return (
        <>
            <Pressable
                onPress={handleCardPress}
                style={({ pressed }) => [
                    styles.cardContainer,
                    pressed && styles.pressed
                ]}
            >
                <Image
                    source={{ uri: eventInfo.image }}
                    style={styles.backgroundImage}
                    resizeMode="cover"
                />

                <TouchableOpacity
                    onPress={handleIconPress}
                    ref={iconRef}
                    style={styles.optionsButton}
                >
                    <LinearGradient
                        colors={['transparent', Colors.custom.linearGradient]}
                        start={{ x: 1, y: 1 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.optionsIconContainer}
                    >
                        <Ionicons name="ellipsis-vertical" size={20} color="white" />
                    </LinearGradient>
                </TouchableOpacity>

                <View style={styles.gradientContainer}>
                    <LinearGradient
                        colors={['transparent', 'rgba(0,0,0,0.8)']}
                        style={styles.gradient}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 0.7 }}
                    >
                        <View style={styles.contentContainer}>
                            <ThemedText style={styles.title}>{eventInfo.title}</ThemedText>

                            {eventInfo.eventPartyDate && (
                                <View style={styles.upNextContainer}>
                                    {eventInfo.showNextParty ? <Text style={styles.upNextText}>
                                         Up next:</Text> :null}
                                         <View style={styles.upNextContentContainer}>
                                            <Text 
                                                style={styles.combinedEventInfoText}
                                                numberOfLines={2}
                                                ellipsizeMode="tail"
                                            >
                                                {`${eventInfo.showNextParty && eventInfo.nextPartyName ? eventInfo.nextPartyName + ' • ' : ''}${formattedDate} • OM Convention, Narsinghi, Hyderabad`}
                                            </Text>
                                         </View>
                                </View>
                            )}
                        </View>
                    </LinearGradient>
                </View>

                <View style={[
                    styles.statusContainer,
                    eventInfo.status.toLowerCase().includes(EventStatusType.HOST) 
                        ? styles.hostStatus 
                        : styles.guestStatus
                ]}>
                    <ThemedText style={styles.statusText}>
                        {eventInfo.status}
                    </ThemedText>
                </View>
            </Pressable>

            <EventOptionsModal
                isVisible={isModalVisible}
                onClose={() => setIsModalVisible(false)}
                onEdit={onEdit}
                onDelete={onDelete}
                iconLayout={iconLayout}
            />
        </>
    );
};
