import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, GestureResponderEvent } from 'react-native';
import { ChevronDown } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';
import { ReviewCardClone } from './ReviewCardClone';
import { VendorDetails} from '@/constants/displayNames';
import { ReviewListNames } from '@/constants/displayNames';
interface Review {
    rating: number;
    comment: string;
    userId: string;
    userName: string;
    userImage: string;
    createdAt: string;
  }
  

type SortOption = typeof ReviewListNames[keyof typeof ReviewListNames];

export function ReviewsList({ reviews }: { reviews: Review[] }) {
  const [selectedFilter, setSelectedFilter] = useState<SortOption>(ReviewListNames.MOST_RECENT);
  const [showFilters, setShowFilters] = useState(false);

  const sortedReviews = useMemo(() => {
    const sorted = [...reviews];
    switch (selectedFilter) {
      case ReviewListNames.MOST_RECENT:
        return sorted.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      case ReviewListNames.HIGHEST_RATED:
        return sorted.sort((a, b) => b.rating - a.rating);
      case ReviewListNames.LOWEST_RATED:
        return sorted.sort((a, b) => a.rating - b.rating);
      default:
        return sorted;
    }
  }, [reviews, selectedFilter]);

  function handleOverlayPress(event: GestureResponderEvent) {
    // Prevent closing if clicking on the filter options themselves
    if (event.target === event.currentTarget) {
      setShowFilters(false);
    }
  }

  return (
    <View style={styles.container}>
      {showFilters && (
        <TouchableOpacity 
          style={styles.filterOptionsOverlay}
          activeOpacity={1}
          onPress={handleOverlayPress}
        >
          <View style={styles.filterOptions}>
            {Object.values(ReviewListNames).map((option) => (
              <TouchableOpacity
                key={option}
                style={styles.filterOption}
                onPress={() => {
                  setSelectedFilter(option as SortOption);
                  setShowFilters(false);
                }}
              >
                <Text style={styles.filterOptionText}>{option}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      )}
      
      <View style={styles.header}>
        <Text style={styles.totalReviews}>{reviews.length} {VendorDetails.REVIEWS}</Text>
        <View style={styles.filterContainer}>
          <TouchableOpacity
            style={styles.filterButton}
            onPress={() => setShowFilters(!showFilters)}
          >
            <Text style={styles.filterText}>{selectedFilter}</Text>
            <ChevronDown size={20} color={Colors.light.text} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.reviewsList}>
        {sortedReviews.map((review) => (
          <ReviewCardClone key={review.userId} review={review} />
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  totalReviews: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  filterContainer: {
    position: 'relative',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderWidth: 1,
    borderColor: Colors.light.text,
    borderRadius: 8,
    gap: 8,
  },
  filterText: {
    color: Colors.light.text,
  },
  filterOptionsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    elevation: 1000,
  },
  filterOptions: {
    position: 'absolute',
    top: 60,
    right: 16,
    backgroundColor: 'white',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1000,
  },
  filterOption: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  filterOptionText: {
    color: Colors.light.text,
  },
  reviewsList: {
    gap: 16,
  },
});