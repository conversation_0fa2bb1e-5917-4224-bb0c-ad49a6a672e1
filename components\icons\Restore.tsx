import * as React from "react";
import Svg, {
  G,
  <PERSON>,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Rect,
} from "react-native-svg";
import { CommonProps } from ".";

const Restore = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Restore-1_svg__a)">
            <Path 
                d="M4.26927 1L2.34619 2.92308L4.26927 4.84616" 
                stroke="url(#Restore-1_svg__b)" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
            <Path 
                d="M1.96155 6.9615C1.96155 7.76023 2.1984 8.54104 2.64215 9.20511C3.0859 9.86927 3.71663 10.3869 4.45456 10.6926C5.19249 10.9982 6.00449 11.0782 6.78787 10.9223C7.57126 10.7666 8.29085 10.3819 8.85563 9.81711C9.4204 9.25235 9.80509 8.53273 9.96086 7.74936C10.1167 6.96598 10.0367 6.15397 9.73109 5.41604C9.4254 4.67811 8.90778 4.04739 8.24366 3.60363C7.57954 3.15989 6.79874 2.92303 6.00001 2.92303H2.34616" 
                stroke="url(#Restore-1_svg__c)" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
          </G>
          <Defs>
            <LinearGradient id="Restore-1_svg__b" x1="3.30773" y1="0.535809" x2="3.30773" y2="5.73033" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="Restore-1_svg__c" x1="6.00001" y1="1.94823" x2="6.00001" y2="12.8567" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <ClipPath id="Restore-1_svg__a">
              <Rect width="12" height="12" fill="white"/>
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G clipPath="url(#Restore_svg__a)">
            <Path 
                d="M4.07145 0.428589L1.92859 2.57145L4.07145 4.7143" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
            <Path 
                d="M1.5 7.07141C1.5 7.96143 1.76392 8.83147 2.25838 9.57144C2.75285 10.3115 3.45566 10.8883 4.27792 11.2289C5.10019 11.5694 6.00499 11.6586 6.8779 11.4849C7.75082 11.3114 8.55265 10.8827 9.18197 10.2534C9.81129 9.62407 10.2399 8.82221 10.4135 7.94931C10.5872 7.0764 10.498 6.1716 10.1575 5.34933C9.81686 4.52707 9.24009 3.82426 8.50006 3.32979C7.76005 2.83533 6.89001 2.57141 6 2.57141H1.92857" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
          </G>
          <Defs>
            <ClipPath id="Restore_svg__a">
              <Rect width="12" height="12" fill="white"/>
            </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  );
};
export default Restore;
