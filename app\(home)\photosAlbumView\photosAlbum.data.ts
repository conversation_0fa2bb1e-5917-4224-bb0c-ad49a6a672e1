import { gql } from "@apollo/client";

export const GET_ALBUM_MEDIA_ALBUM_ID = gql`
query Query($getMediaFolderByIdId: ID!) {
  getMediaFolderById(id: $getMediaFolderByIdId) {
    ... on MediaFolderResponse {
      result {
        mediaFolder {
          media {
            result {
              media {
                id
                title
                url
              }
            }
          }
        }
      }
    }
  }
}
`;

export const CREATE_MEDIA=gql`
mutation CreateMedia($input: MediaInput!) {
  createMedia(input: $input) {
    ... on MediaResponse {
      result {
        media {
          id
        }
      }
    }
  }
}`;

export const ADD_MEDIA_TO_MEDIA_FOLDER=gql`
mutation AddMediaToMediaFolder($mediaId: ID!, $mediaFolderId: ID!) {
  addMediaToMediaFolder(mediaId: $mediaId, mediaFolderId: $mediaFolderId) {
    ... on MediaFolderResponse {
      result {
        mediaFolder {
          id
          media {
            result {
              media {
                id
                url
                title
              }
            }
          }
        }
      }
    }
  }
}
`;
export const GET_MEDIA_FOLDER_BY_ID = gql`
query Query($getMediaFolderByIdId: ID!, $filter: MediaFilterForMediaFolderInput) {
  getMediaFolderById(id: $getMediaFolderByIdId) {
    ... on MediaFolderResponse {
      result {
        mediaFolder {
          media(filter: $filter) {
            result {
              media {
                owner {
                  id
                  firstName
                  lastName
                  email
                }
                url
                id
                title
              }
            }
          }
          name
        }
      }
    }
  }
}
`;
export const DELETE_MEDIA_BY_ID = gql`
mutation DeleteMedia($deleteMediaId: ID!) {
  deleteMedia(id: $deleteMediaId) {
    ... on MediaResponse {
      result {
        media {
          id

          }
      }
    }
  }
}
`;
export const ADD_MEDIA_TO_FOVORITES = gql`
mutation Mutation($favoriteMediaId: ID!) {
  favoriteMedia(id: $favoriteMediaId) {
    ... on MediaResponse {
      result {
        media {
          id
          title
        }
      }
    }
  }
}
`;
export const GET_FAVORITE_MEDIA = gql`
query GetFavoritesMediaFolder {
  getFavoritesMediaFolder {
    ... on MediaFolderResponse {
      result {
        mediaFolder {
          id
          media {
            result {
              media {
                id
                title
                url
              }
            }
          }
        }
      }
    }
  }
}
`;

export const GET_ARCHIVES_MEDIA = gql`
  query GetArchivesMedia {
    getArchivesMediaFolder {
      ... on MediaFolderResponse {
        result {
          mediaFolder {
            id
            media {
              result {
                media {
                  id
                  title
                  url
                }
              }
            }
          }
        }
      }
    }
  }
`;

export const DELETE_MEDIA_FROM_MEDIA_FOLDER = gql`
  mutation RemoveMediaFromMediaFolder($mediaId: ID!, $mediaFolderId: ID!) {
  removeMediaFromMediaFolder(mediaId: $mediaId, mediaFolderId: $mediaFolderId) {
    ... on MediaFolderResponse {
      status
      message
      result {
        mediaFolder {
          id
          name
          albumCover
          category
        }
      }
    }
    ... on MediaFolderErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}
`

export const REMOVE_MEDIA_FROM_MEDIA_FOLDER = gql`
  mutation RemoveMediaFromMediaFolder($mediaId: ID!, $mediaFolderId: ID!) {
  removeMediaFromMediaFolder(mediaId: $mediaId, mediaFolderId: $mediaFolderId) {
    ... on MediaFolderResponse {
      status
      message
      result {
        mediaFolder {
          id
          media {
            result {
              media {
                id
                title
                url
              }
            }
          }
        }
      }
    }
  }
}
`;

export const REMOVE_MEDIA_FROM_FAVORITES = gql`
  mutation UnfavoriteMedia($unfavoriteMediaId: ID!) {
    unfavoriteMedia(id: $unfavoriteMediaId) {
      ... on MediaResponse {
        result {
          media {
            id
          }
        }
      }
    }
  }
`;

export const RESTORE_MEDIA = gql`
 mutation RestoreMediaFromArchives($mediaId: ID!) {
    restoreMediaFromArchives(mediaId: $mediaId) {
      ... on MediaFolderResponse {
        result {
          mediaFolder {
            id
          }
        }
      }
      ... on MediaFolderErrorResponse {
        errors {
          message
          field
        }
      }
    }
  }
`;

export const PERMANENTLY_DELETE_MEDIA = gql`
  mutation DeleteMedia($deleteMediaId: ID!) {
    deleteMedia(id: $deleteMediaId) {
      ... on MediaResponse {
        result {
          media {
            id
          }
        }
      }
      ... on MediaErrorResponse {
        errors {
          message
          field
        }
      }
    }
  }
`;
