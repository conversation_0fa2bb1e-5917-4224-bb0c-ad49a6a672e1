import * as React from "react";
import Svg, { <PERSON>, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const DueDate = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Due_date-1_svg__a)">
      <Path
        fill="url(#Due_date-1_svg__b)"
        d="M9.933 1.123c.282.028.547.153.75.356l.082.09c.18.219.279.495.279.782v4.104a3.243 3.243 0 0 0-4.94 4.163H1.666c-.286 0-.563-.1-.782-.28l-.09-.081a1.23 1.23 0 0 1-.361-.872V2.35c0-.328.13-.641.36-.872l.09-.083c.22-.18.497-.279.783-.279h8.145z"
      />
      <Path
        stroke="url(#Due_date-1_svg__c)"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={0.987}
        d="M3.147.5v2.221"
      />
      <Path
        stroke="url(#Due_date-1_svg__d)"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={0.987}
        d="M8.33.5v2.221"
      />
      <Path
        stroke="url(#Due_date-1_svg__e)"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={0.987}
        d="M3.147 1.61h3.702"
      />
      <Path
        fill="url(#Due_date-1_svg__f)"
        d="M8.753 6a2.75 2.75 0 1 1 0 5.5 2.75 2.75 0 0 1 0-5.5m.7 4.324a.494.494 0 0 0 .749-.643zm-.7-3.125a.493.493 0 0 0-.493.493V8.75c0 .118.041.232.118.321l1.075 1.253.374-.322.375-.321-.956-1.115v-.874a.493.493 0 0 0-.493-.493"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Due_date-1_svg__b"
        x1={5.738}
        x2={5.738}
        y1={-0.029}
        y2={12.802}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Due_date-1_svg__c"
        x1={3.647}
        x2={3.647}
        y1={0.232}
        y2={3.232}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Due_date-1_svg__d"
        x1={8.83}
        x2={8.83}
        y1={0.232}
        y2={3.232}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Due_date-1_svg__e"
        x1={4.998}
        x2={4.998}
        y1={1.49}
        y2={2.84}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Due_date-1_svg__f"
        x1={8.753}
        x2={8.753}
        y1={5.336}
        y2={12.764}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Due_date-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Due_date_svg__a)"
    >
      <Path d="M1.914 1.85a.7.7 0 0 0-.7.7V9.2a.7.7 0 0 0 .7.7h3.2m3.1-8.05h1.4a.7.7 0 0 1 .7.7V4.7M3.314.8v2.1M8.214.8v2.1M3.314 1.85h3.5M8.614 11.2a2.6 2.6 0 1 0 0-5.2 2.6 2.6 0 0 0 0 5.2" />
      <Path d="M8.614 7.6v1L9.63 9.784" />
    </G>
    <Defs>
      <ClipPath id="Due_date_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default DueDate;
