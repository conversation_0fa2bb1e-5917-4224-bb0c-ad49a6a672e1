import * as React from "react";
import Svg, { <PERSON>, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Direction = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Direction-1_svg__a)">
      <Path
        fill="url(#Direction-1_svg__b)"
        d="M5.633.652a.52.52 0 0 1 .734 0l4.98 4.98a.52.52 0 0 1 0 .735l-4.98 4.98a.52.52 0 0 1-.734 0l-4.98-4.98a.52.52 0 0 1 0-.734zm1.561 3.082a.47.47 0 0 0-.648.65l.06.073.605.606H5.157c-.569 0-1.03.462-1.031 1.03v1.312l.01.095a.47.47 0 0 0 .918 0l.01-.095V6.094c0-.052.042-.094.093-.094h2.054l-.606.605a.469.469 0 0 0 .59.723l.074-.06 1.405-1.406.058-.071a.47.47 0 0 0 0-.52l-.058-.07-1.405-1.406z"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Direction-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.827}
        y2={14.028}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Direction-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G stroke={color} clipPath="url(#Direction_svg__a)">
      <Path d="M5.646.854a.5.5 0 0 1 .708 0l4.792 4.792a.5.5 0 0 1 0 .708l-4.792 4.792a.5.5 0 0 1-.708 0L.854 6.354a.5.5 0 0 1 0-.708z" />
      <Path
        strokeLinecap="round"
        d="M4.5 7.5V6.1a.6.6 0 0 1 .6-.6h3.4m0 0L7 4m1.5 1.5L7 7"
      />
    </G>
    <Defs>
      <ClipPath id="Direction_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Direction;
