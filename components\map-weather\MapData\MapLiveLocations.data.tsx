import gql from 'graphql-tag';

export const GET_LIVE_LOCATIONS = gql`
query GetLiveLocationsByPartyId($partyId: ID!) {
  getLiveLocationsByPartyId(partyId: $partyId) {
    ... on LiveLocationsResponse {
      result {
        liveLocations {
          ETA
          allowLiveTracking
          coordinates {
            latitude
            longitude
          }
          guest {
            user {
              firstName
              profilePicture
              lastName
            }
          }
          id
        }
      }
    }
  }
}`