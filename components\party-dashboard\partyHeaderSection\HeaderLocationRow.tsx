import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Share2 } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';

interface HeaderLocationRowProps {
  location: string;
}

export const HeaderLocationRow: React.FC<HeaderLocationRowProps> = ({
  location
}) => {
  return (
    <View style={styles.locationRow}>
      <Ionicons name="location-outline" size={20} color={Colors.dark.text} />
      <Text style={styles.locationText} numberOfLines={1}>{location}</Text>
      <TouchableOpacity style={styles.shareButton}>
        <Share2 size={20} color={Colors.dark.text} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 16,
  },
  locationText: {
    flex: 1,
    color: Colors.dark.text,
    fontSize: 14,
  },
  shareButton: {
    padding: 8,
  },
});