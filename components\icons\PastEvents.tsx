import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const PastEvents = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Past_events-1_svg__a)">
      <Path
        fill="url(#Past_events-1_svg__b)"
        stroke="transparent"
        d="M8.75.5c.434 0 .786.352.786.786v.785h.785c.651 0 1.179.528 1.179 1.179v7.071c0 .651-.528 1.179-1.179 1.179H1.68C1.028 11.5.5 10.972.5 10.321V3.25c0-.65.528-1.179 1.179-1.179h.785v-.785a.786.786 0 0 1 1.572 0v.785h3.929v-.785c0-.434.351-.786.785-.786M6.127 4.916a.42.42 0 0 0-.53-.054l-.066.054-1.401 1.4a.42.42 0 0 0-.052.533l.052.064 1.401 1.4a.421.421 0 0 0 .596-.595L5.025 6.614l1.102-1.102.054-.067a.42.42 0 0 0-.054-.529m2.528 0a.42.42 0 0 0-.529-.054l-.066.054-1.402 1.4a.42.42 0 0 0-.052.533l.052.064 1.402 1.4a.421.421 0 0 0 .595-.595L7.554 6.614l1.101-1.102.054-.067a.42.42 0 0 0-.054-.529"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Past_events-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.828}
        y2={14.029}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Past_events-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Past_events_svg__a)"
    >
      <Path d="M1.77 2.154a.77.77 0 0 0-.77.77v7.307a.77.77 0 0 0 .77.769h8.46a.77.77 0 0 0 .77-.77V2.924a.77.77 0 0 0-.77-.77H8.693M3.308 1v2.308M8.692 1v2.308M3.308 2.154h3.846" strokeWidth={strokeWidth} fill="none" />
      <Path d="M5.954 8.434 4.462 6.942 5.954 5.45M7.986 8.215 6.494 6.723 7.986 5.23" strokeWidth={strokeWidth} fill="none" />
    </G>
    <Defs>
      <ClipPath id="Past_events_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default PastEvents;
