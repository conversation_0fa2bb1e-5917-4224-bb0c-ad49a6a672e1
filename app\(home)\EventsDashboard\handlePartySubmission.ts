import { PartyFormData, FormErrors, PartyErrorResponse, Cohost, PartyResponse, LocationItem } from './AddParty.models';
import { combineDateAndTime } from '@/app/(home)/utils/reusableFunctions';
import { ToastInstance } from '@/components/Toast/useToast';
import { useEventRefreshStore } from '@/store/eventRefreshStore';
import { useEventStore } from '@/store/eventStore';

interface HandlePartySubmissionParams {
  formData: PartyFormData;
  mutation: (options: { variables: any }) => Promise<any>;
  partyId?: string;
  setFormErrors: (errors: FormErrors) => void;
  toast: ToastInstance;
  selectedEventId?: string;
}

export async function handlePartySubmission({
  formData,
  mutation,
  partyId,
  setFormErrors,
  toast,
  selectedEventId,
}: HandlePartySubmissionParams) {
  try {
    const mutationInput = {
      name: formData.partyName,
      partyType: formData.partyType,
      time: combineDateAndTime(formData.date, formData.time).toISOString(),
      serviceLocation: formData.area?.id,
      eventId: partyId ? selectedEventId : formData.eventId,
      expectedGuestCount: parseInt(formData.guests),
      totalBudget: parseFloat(formData.budget),
      vendorTypes: formData.services,
      coHosts: formData.cohosts.map((cohost: Cohost) => ({
        firstName: cohost?.firstName,
        lastName: cohost?.lastName ? cohost?.lastName : '',
        phone: cohost?.phoneNumber,
        email: cohost?.email
      }))
    };

    const variables = partyId 
      ? { input: mutationInput, updatePartyId: partyId }
      : { input: mutationInput };

    // Add detailed logging
    const { data } = await mutation({ variables });

    const response = data?.createParty || data?.updateParty;

    if (response?.__typename === 'PartyResponse') {
      toast.success(partyId ? 'Party updated successfully' : 'Party added successfully');
      useEventStore.getState().setShouldRefreshParties(true);
      useEventRefreshStore.getState().setNeedsRefresh(true);  
      return true;
    } 

    if (response?.__typename === 'PartyErrorResponse') {
      const errorResponse = response as PartyErrorResponse;
      console.log('errorResponse', JSON.stringify(errorResponse, null, 2))
      if (errorResponse.errors?.length > 0) {
        const newFormErrors: FormErrors = {};
        
        // Show the specific error message from the first error
        const firstError = errorResponse.errors[0];
        toast.error(firstError.message);
        
        // Still update form errors for field highlighting
        errorResponse.errors.forEach(error => {
          const fieldMapping: Record<string, keyof FormErrors> = {
            createParty: 'services',
          };
          
          const formField = fieldMapping[error.field] || error.field as keyof FormErrors;
          newFormErrors[formField] = error.message;
        });
        setFormErrors(newFormErrors);
      } else if (errorResponse.message) {
        // Fallback to general error message if no specific errors
        toast.error(errorResponse.message);
      }
      
      return false;
    }

    return false;
  } catch (error) {
    console.error(partyId ? 'Update party error:' : 'Add party error:', error);
    toast.error(error instanceof Error ? error.message : 'An unexpected error occurred');
    return false;
  }
}

export function transformPartyData(partyData: PartyResponse): PartyFormData {
  console.log('partyData', JSON.stringify({
    getPartyById: {
      result: {
        party: {
          name: partyData.getPartyById.result.party.name,
          partyType: {
            id: partyData.getPartyById.result.party.partyType.id,
            name: partyData.getPartyById.result.party.partyType.name
          },
          time: partyData.getPartyById.result.party.time,
          vendorTypes: partyData.getPartyById.result.party.vendorTypes,
          serviceLocation: {
            id: partyData.getPartyById.result.party.serviceLocation.id,
            name: partyData.getPartyById.result.party.serviceLocation.city,
          },
          expectedGuestCount: partyData.getPartyById.result.party.expectedGuestCount,
          totalBudget: partyData.getPartyById.result.party.totalBudget,
          coHosts: partyData.getPartyById.result.party.coHosts,
          event: {
            id: partyData.getPartyById.result.party.event.id
          }
        }
      }
    }
  }, null, 2));
  
  const party = partyData.getPartyById.result.party;
  const dateTime = new Date(party.time);

  return {
    partyName: party.name,
    partyType: party.partyType.id,
    date: dateTime,
    time: dateTime,
    area: {
      id: party.serviceLocation.id,
      title: party.serviceLocation.city,
    } as LocationItem,
    eventId: party.event.id,
    guests: party.expectedGuestCount.toString(),
    budget: party.totalBudget.toString(),
    services: party.vendorTypes.map(vendor => vendor.id),
    cohosts: party.coHosts.map(cohost => ({
      id: cohost.userId.id,
      firstName: cohost.userId.firstName,
      lastName: cohost.userId?.lastName ? cohost?.userId?.lastName : '',
      phoneNumber: cohost.userId.phone,
      email: cohost.userId.email,
    })),
  };
}
