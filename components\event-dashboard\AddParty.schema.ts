import { z } from "zod";
import { AddPartyNames } from "@/constants/displayNames";

export const formSchema = z.object({
    partyType: z.string().min(1, AddPartyNames.PARTY_TYPE_ERROR_MESSAGE),
    partyName: z.string().min(1, AddPartyNames.PARTY_NAME_ERROR_MESSAGE).max(300, 'Party Name must be at most 300 characters'),
    date: z.date(),
    time: z.date(),
    area: z.object({
      id: z.string(),
      title: z.string()
    }).nullable().refine(val => val !== null, AddPartyNames.AREA_ERROR_MESSAGE),
    guests: z.string().min(1, AddPartyNames.GUESTS_ERROR_MESSAGE),
    budget: z.string().min(1, AddPartyNames.BUDGET_ERROR_MESSAGE),
    services: z.array(z.string()).min(1, AddPartyNames.SERVICES_ERROR_MESSAGE)
  });

  