import { VendorType } from "@/app/(home)/EventsDashboard/AddParty.models";

export interface PartyType {
  id: string;
  name: string;
  vendorTypes: VendorType[];
}
export interface EventType {
  name: string;
  id: string;
  partyTypes: PartyType[];
}

export interface EventTypesResult {
  mdEventTypes: EventType[];
}

export interface EventTypesResponse {
  result: EventTypesResult;
}

export interface GetEventTypesData {
  getMdEventTypes: EventTypesResponse;
}

// Optional: If you need the pagination input type
export interface PaginationInput {
  page?: number;
  limit?: number;
}


export interface MdEventType {
  partyTypes: PartyType[];
}

export interface GetMdEventTypeByIdResult {
  mdEventType: MdEventType;
}

export interface GraphQLEventTypesResponse {
    getMdEventTypeById: {
      result: GetMdEventTypeByIdResult;
    }
}
interface Location {
  id: string;
  city: string;
}

interface MdServiceLocations {
  mdServiceLocations: Location[];
}

export interface GraphQLLocationsResponse {
  getMdServiceLocations: {
    result: MdServiceLocations;
  }
}

export interface LocationItem {
  id: string;
  title: string;
}