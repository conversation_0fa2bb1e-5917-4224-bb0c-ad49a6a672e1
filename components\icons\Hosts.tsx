import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const Hosts = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      fill="url(#Hosts-1_svg__a)"
      stroke="url(#Hosts-1_svg__b)"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={0.897}
      d="M11 2.901 8.692 5.21 6 1.363 3.308 5.209 1 2.9v4.615A1.154 1.154 0 0 0 2.154 8.67h7.692A1.154 1.154 0 0 0 11 7.516z"
    />
    <Path
      stroke="url(#Hosts-1_svg__c)"
      strokeLinecap="round"
      strokeWidth={0.897}
      d="M1.77 10.209h8.46"
    />
    <Defs>
      <LinearGradient
        id="Hosts-1_svg__a"
        x1={6}
        x2={6}
        y1={0.481}
        y2={10.35}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Hosts-1_svg__b"
        x1={6}
        x2={6}
        y1={0.481}
        y2={10.35}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Hosts-1_svg__c"
        x1={6}
        x2={6}
        y1={10.088}
        y2={11.439}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <Path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M11 2.901 8.692 5.21 6 1.363 3.308 5.209 1 2.9v4.615A1.154 1.154 0 0 0 2.154 8.67h7.692A1.154 1.154 0 0 0 11 7.516z"
    />
    <Path stroke={color} strokeLinecap="round" d="M1.77 10.209h8.46" />
        </>
      )}
    </Svg>
  );
};
export default Hosts;
