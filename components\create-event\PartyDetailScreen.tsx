import React, { useState, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>, useEffect } from "react";
import { ADD_PARTY, GET_LOCATIONS } from "@/app/(home)/EventsDashboard/AddParty.data";
import { GET_EVENT_TYPES_BY_ID } from "@/components/user-events/user-events.data";
import { useMutation, useQuery } from "@apollo/client";
import { GraphQLEventTypesResponse } from "@/components/user-events/user-events.model";
import AddPartyForm from "@/components/event-dashboard/AddPartyFormClone";
import { transformPartyTypesResponse } from "./partyDetailScreen.helper";
import { PartyFormData, LocationItem, FormErrors, PartyType, Cohost } from "@/app/(home)/EventsDashboard/AddParty.models";
import { handlePartySubmission } from "@/app/(home)/EventsDashboard/handlePartySubmission";
import { z } from "zod";
import { AddPartyNames } from "@/constants/displayNames";
import { formSchema } from "../event-dashboard/AddParty.schema";
import { ActivityIndicator, Platform, StyleSheet } from "react-native";
import { Portal, Surface, useTheme, MD3Theme } from "react-native-paper";
import { View } from "react-native";
import { getCurrentDateTime } from "@/app/(home)/utils/reusableFunctions";
import { DatePickerModal } from "../UI/ReusableComponents/DatePickerModal";
import { useToast } from "../Toast";
import { TimePickerModal } from "../UI/ReusableComponents/TimePickerModal";
import { SearchModal } from "../UI/ReusableComponents/SerachModal";
import { router } from "expo-router";
import { Text } from "react-native-paper";
import { PartyDetailScreenType } from "@/constants/eventTabTypes";
import { BottomSheetModal } from "@gorhom/bottom-sheet";

interface PartyDetailScreenProps {
  eventTypeId?: string;
  eventName?: string;
  onPartyCreated?: () => void;
  onPartyDetailsChange?: (details: {
    date: Date;
    locationId: string;
    cohosts: Cohost[];
  }) => void;
  cohostSheetRef: React.RefObject<BottomSheetModal>;
}

interface PartyDetailScreenRef {
  handleSubmit: (eventId: string) => Promise<boolean>;
  getPartyDate: () => Date;
  getLocationId: () => string;
  getCohosts: () => Cohost[];
  isValid: () => boolean;
}

const PartyDetailScreen = forwardRef<PartyDetailScreenRef, PartyDetailScreenProps>(
  function PartyDetailScreen({ eventTypeId, eventName, onPartyCreated, onPartyDetailsChange, cohostSheetRef  }, ref) {
    const { data: locations } = useQuery(GET_LOCATIONS);
    const [formErrors, setFormErrors] = useState<FormErrors>({});
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [showTimePicker, setShowTimePicker] = useState(false);
    const [showAreaSearch, setShowAreaSearch] = useState(false);
    const [addParty, { loading: addPartyLoading }] = useMutation(ADD_PARTY);
    const { data: partyTypes } = useQuery<GraphQLEventTypesResponse>(
      GET_EVENT_TYPES_BY_ID,
      {
        variables: {
          getMdEventTypeByIdId: eventTypeId,
        },
      }
    );
    const toast = useToast();
    const theme = useTheme<MD3Theme>();
    const AreaSearchModal = SearchModal<LocationItem>();
    const partyTypesData = transformPartyTypesResponse(partyTypes);
    const [formData, setFormData] = useState<PartyFormData>(() => {
      const initialData: PartyFormData = {
        partyType: partyTypesData?.[0]?.id || '',
        partyName: eventName || '',
        date: new Date(),
        time: new Date(),
        area: null,
        eventId: eventTypeId as string,
        guests: '',
        budget: '',
        services: [],
        cohosts: [],
      };
      return initialData;
    });
    const initialFormState: PartyFormData = {
      partyType: '',
      partyName: '',
      date: new Date(),
      time: new Date(),
      area: null,
      eventId: eventTypeId as string,
      guests: '',
      budget: '',
      services: [],
      cohosts: [],
    };
    const validateForm = () => {
      try {
        formSchema.parse(formData);
        setFormErrors({});
        return true;
      } catch (error) {
        if (error instanceof z.ZodError) {
          const errors: Record<string, string> = {};
          error.errors.forEach((err) => {
            if (err.path) {
              errors[err.path[0]] = err.message;
            }
          });
          setFormErrors(errors);
        }
        return false;
      }
    };
    const toggleService = (serviceId: string) => {
      setFormData(prev => {
        const newServices = prev.services.includes(serviceId)
          ? prev.services.filter(id => id !== serviceId)
          : [...prev.services, serviceId];
        if (newServices.length > 0) {
          setFormErrors(prev => ({ ...prev, services: '' }));
        }
        return {
          ...prev,
          services: newServices
        };
      });
    };
    const locationItems: LocationItem[] = React.useMemo(() => locations?.getMdServiceLocations?.result?.mdServiceLocations?.map(
          (location: any) => ({
            id: location?.id,
            title: location?.city,
          })
      ) || [],
      [locations]
    );
    const handlePartyTypeChange = (type: PartyType) => {
      setFormData(prev => ({
        ...prev,
        partyType: type.id,
        services: []
      }));
      setFormErrors(prev => ({ ...prev, partyType: '' }));
    };

    useEffect(() => {
      if (partyTypesData?.length > 0 && (!formData.partyType || formData.partyType === '')) {
        setFormData(prev => ({
          ...prev,
          partyType: partyTypesData[0].id
        }));
      }
    }, [partyTypesData, formData.partyType]);

    const handleDateChange = (date: Date) => {
      setFormData(prev => ({ ...prev, date }));
      if (Platform.OS === 'ios') {
        setShowDatePicker(false);
      }
    };

    const handleTimeChange = (time: Date) => {
      setFormData(prev => ({ ...prev, time }));
      if (Platform.OS === 'ios') {
        setShowTimePicker(false);
      }
    };

    const availableServices = React.useMemo(() => {
      const allVendors = partyTypesData.flatMap(partyType => partyType.vendorTypes);
      const uniqueVendors = Array.from(
        new Map(allVendors.map(vendor => [vendor.id, vendor])).values()
      );
      return uniqueVendors;
    }, [partyTypesData]);
    const handleSubmit = async (eventId?: string) => {
      if (!validateForm()) return;
      
      const success = await handlePartySubmission({
        formData: {
          ...formData,
          partyType: formData.partyType || partyTypesData?.[0]?.id,
          eventId: eventId || formData.eventId
        },
        mutation: addParty,
        partyId: undefined,
        setFormErrors,
        toast,
        selectedEventId: eventId
      });

      if (success) {
        setFormData(initialFormState);
        setFormErrors({});
        router.back();
      }
    };
    useImperativeHandle(ref, () => ({
      handleSubmit: async (eventId: string) => {
        if (!validateForm()) return false;
        
        const success = await handlePartySubmission({
          formData: {
            ...formData,
            partyType: formData.partyType || partyTypesData?.[0]?.id,
            eventId // Use the passed eventId from event creation
          },
          mutation: addParty,
          partyId: undefined,
          setFormErrors,
          toast,
          selectedEventId: eventId
        });

        if (success) {
          setFormData(initialFormState);
          setFormErrors({});
          onPartyCreated?.();
          return true;
        }
        return false;
      },
      getPartyDate: () => formData.date,
      getLocationId: () => formData.area?.id || '',
      getCohosts: () => formData.cohosts,
      isValid: () => validateForm()
    }));

    // Add effect to notify parent of changes
    useEffect(() => {
      onPartyDetailsChange?.({
        date: formData.date,
        locationId: formData.area?.id || '',
        cohosts: formData.cohosts
      });
    }, [formData.date, formData.area, formData.cohosts]);

    return (
      <Surface style={styles(theme).container}>
        <View style={styles(theme).header}>
          <Text variant="titleLarge">Add Your Party Details</Text>
        </View>
        <AddPartyForm
          formData={formData}
          setFormData={setFormData}
          partyTypes={partyTypesData}
          key={eventTypeId}
          availableServices={availableServices}
          formErrors={formErrors}
          setFormErrors={setFormErrors}
          setShowDatePicker={setShowDatePicker}
          setShowTimePicker={setShowTimePicker}
          setShowAreaSearch={setShowAreaSearch}
          handlePartyTypeChange={handlePartyTypeChange}
          toggleService={toggleService}
          handlePartySubmission={handleSubmit}
          showSubmitButton={false}
          showPartyNameAndType={false}
          cohostSheetRef={cohostSheetRef}
        />
        
        <Portal>
          <DatePickerModal
            visible={showDatePicker}
            onDismiss={() => setShowDatePicker(false)}
            value={formData.date}
            onChange={handleDateChange}
            title={AddPartyNames.SELECT_DATE}
            minimumDate={getCurrentDateTime()}
          />
          <TimePickerModal
            visible={showTimePicker}
            onDismiss={() => setShowTimePicker(false)}
            value={formData.time}
            onChange={handleTimeChange}
            title={AddPartyNames.SELECT_TIME}
            minimumDate={getCurrentDateTime()}
            selectedDate={formData.date}
          />
          <AreaSearchModal
            visible={showAreaSearch}
            onDismiss={() => setShowAreaSearch(false)}
            items={locationItems}
            onSelect={(item) => {
              setFormData(prev => ({ ...prev, area: item }));
              setFormErrors(prev => ({ ...prev, area: '' }));
            }}
            searchPlaceholder={AddPartyNames.SEARCH_LOCATION_PLACEHOLDER}
            noOptionsText={AddPartyNames.NO_LOCATION_AVAILABLE}
          />

          {addPartyLoading && (
            <View style={styles(theme).loadingOverlay}>
              <ActivityIndicator 
                size="large" 
                color={theme.colors.primary} 
              />
            </View>
          )}
        </Portal>
      </Surface>
    );
  }
);

PartyDetailScreen.displayName = PartyDetailScreenType.PARTY_DETAIL_SCREEN;

export default PartyDetailScreen;

const styles = (theme: MD3Theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: 16,
  },
  form: {
    flex: 1,
    gap: 16,
  },
  header: {
    marginBottom: 16,
    alignItems: 'center',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  modal: {
    margin: 16,
    borderRadius: 8,
    backgroundColor: theme.colors.surface,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
    }),
  },
});

