import { useLocalSearchParams } from "expo-router";
import { ThemedView } from "@/components/UI/ThemedView";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Text,
} from "react-native";
import { VendorHeader } from "@/components/party-dashboard/VendorDetails/VendorHeader";
import vendor_details from "@/data/vendor_details.json";
import { MainFeatures } from "@/components/party-dashboard/VendorDetails/MainFeatures";
import { VendorDescription } from "@/components/party-dashboard/VendorDetails/Description";
import { PriceRange } from "@/components/party-dashboard/VendorDetails/PriceRange";
import { Features } from "@/components/party-dashboard/VendorDetails/features";
import { Specialities } from "@/components/party-dashboard/VendorDetails/Specialitites";
import { Reviews } from "@/components/party-dashboard/VendorDetails/Reviews";
import { Colors } from "@/constants/Colors";
import MediaGallery from "@/components/party-dashboard/VendorDetails/MediaGallery";
import { useScrollContext } from "@/commons/ScrollContext";
import Animated, { useAnimatedScrollHandler } from "react-native-reanimated";
import { VendorLocation } from "@/components/party-dashboard/VendorDetails/VendorLocation";
import { useRef, useMemo } from "react";
import {
  BottomSheetModalProvider,
  BottomSheetBackdrop,
  BottomSheetView,
  BottomSheetModal,
  BottomSheetScrollView,
} from "@gorhom/bottom-sheet";
import { ReviewModalContent } from "@/components/party-dashboard/VendorDetails/ReviewModalContent";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { DarkStatusBar } from "@/components/shared/GlobalStatusBar";

export default function VendorDetailsScreen() {
  const params = useLocalSearchParams();
  console.log(params?.vendorId);
  const { scrollY } = useScrollContext();
  const reviewSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ["90%", "90%"], []);

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });
  const handleShowAllReviews = () => {
    reviewSheetRef.current?.present();
  };

  return (
    <GestureHandlerRootView>
      <BottomSheetModalProvider>
        <View style={styles.safeArea}>
          <DarkStatusBar />
          <Animated.ScrollView
            onScroll={scrollHandler}
            scrollEventThrottle={16}
            style={styles.scrollView}
            contentContainerStyle={styles.scrollViewContent}
          >
            <ThemedView style={styles.container}>
              <MediaGallery images={vendor_details?.media || []} />
              <View style={{ paddingHorizontal: "8%" }}>
                <VendorHeader vendorName={vendor_details?.name} />
                <MainFeatures
                  features={vendor_details?.main_feature}
                  ratingAggregate={vendor_details?.rating_aggregate}
                />
                <VendorDescription
                  vendorDescription={vendor_details?.vendor_description}
                />
                <PriceRange priceRange={vendor_details?.price_range} />
                <Features
                  features={
                    vendor_details?.features ? vendor_details?.features : []
                  }
                />
                <Specialities specialities={vendor_details?.specialities} />
                <VendorLocation
                  address={vendor_details?.address}
                  coordinates={vendor_details?.location_coordinates}
                />
                <Reviews
                  reviews={
                    vendor_details?.reviews ? vendor_details?.reviews : []
                  }
                  ratingAggregate={
                    vendor_details?.rating_aggregate
                      ? vendor_details?.rating_aggregate
                      : { rating_avg: 0, review_count: 0 }
                  }
                  onShowAllReviews={handleShowAllReviews}
                />
              </View>
            </ThemedView>
          </Animated.ScrollView>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.button}
              onPress={() => console.log("Request Quote")}
            >
              <Text style={styles.buttonText}>Request Quote</Text>
            </TouchableOpacity>
          </View>
          <BottomSheetModal
            ref={reviewSheetRef}
            index={1}
            snapPoints={snapPoints}
            enablePanDownToClose
            backdropComponent={(props) => (
              <BottomSheetBackdrop
                {...props}
                appearsOnIndex={1}
                disappearsOnIndex={0}
              />
            )}
          >
            <BottomSheetScrollView>
              <BottomSheetView
                style={{
                  flex: 1,
                  marginBottom: "10%",
                  paddingBottom: "10%",
                  marginHorizontal: "4%",
                }}
              >
                <ReviewModalContent
                  reviews={
                    vendor_details?.reviews ? vendor_details?.reviews : []
                  }
                  ratingAggregate={
                    vendor_details?.rating_aggregate
                      ? vendor_details?.rating_aggregate
                      : { rating_avg: 0 }
                  }
                />
              </BottomSheetView>
            </BottomSheetScrollView>
          </BottomSheetModal>
        </View>
      </BottomSheetModalProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "white", // Adjust this to match your theme
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    paddingBottom: 80, // Add extra padding to account for the fixed button
  },
  buttonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: "8%",
    paddingVertical: 16,
    backgroundColor: "white",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -3,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 20, 
    // Add border top for additional definition
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  },
  button: {
    backgroundColor: Colors.light.buttonBackground,
    borderRadius: 10,
    paddingVertical: 12,
    alignItems: "center",
    width: "60%",
    alignSelf: "center",
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
    paddingVertical: 4,
  },
});
