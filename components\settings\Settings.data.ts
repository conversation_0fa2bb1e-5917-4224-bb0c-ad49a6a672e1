import { gql } from "@apollo/client";

export const DELETE_USER = gql `
mutation DeleteUser {
  deleteUser {
    ... on UserResponse {
      result {
        user {
          id
          firstName
          lastName
        }
      }
    }
    ... on UserErrorResponse {
      errors {
        field
        message
      }
    }
  }
}
`

export const UPDATE_USER = gql `
mutation UpdateUser($input: UserUpdateInput!) {
  updateUser(input: $input) {
    ... on UserResponse {
      result {
        user {
          externalId
          email
          emailVerified
          firstName
          id
          lastName
          phone
          profilePicture
        }
      }
    }
  }
}
`