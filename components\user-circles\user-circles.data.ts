import { gql } from "@apollo/client";

export const CREATE_CIRCLE = gql`
mutation CreateEventGroup($input: EventGroupInput!) {
  createEventGroup(input: $input) {
    ... on EventGroupResponse {
      message
      result {
        eventGroup {
          description
          id
          imageUrl
          name
          type
        }
      }
      status
    }
    ... on EventGroupErrorResponse {
      errors {
        field
        message
      }
      message
      status
    }
  }
}`;

export const SEND_CIRCLE_INVITATION = gql`
mutation SendEventGroupInvitation($input: SendEventGroupInvitationInput!) {
  sendEventGroupInvitation(input: $input) {
    ... on EventGroupInvitationResponse {
      status
      message
      result {
        eventGroupInvitation {
          id
          user {
            firstName
            id
            phone
          }
          eventGroup {
            id
            membersCount
          }
          status
        }
      }
    }
    ... on EventGroupInvitationErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}
`;