import { Dimensions, Platform, StyleSheet } from 'react-native';

const screenHeight = Dimensions.get('window').height;

export const taskNameFieldStyles = StyleSheet.create({
  inputStyle: {
    fontSize: 16,
    lineHeight: 24,
    flexWrap: 'wrap',
    flexShrink: 1,
  },

  containerStyle: {
    minHeight: 50,
    paddingTop: 8,
    justifyContent: 'center',
  },

  TaskNameFieldStyle: {
    top: screenHeight * 0.0088,
    paddingRight: 20,
    fontWeight: '600',
    fontSize: 20,
    lineHeight: 24,
    color: '#000000',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    textAlign: 'left',
    fontFamily: 'Plus Jakarta Sans',
  },

  placeholderStyle: {
    color: '#9F9C9C',
  },

  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },

  modalContent: {
    flex: 1,
    paddingHorizontal: 0,
    paddingTop: 0,
  },

  modalInput: {
    backgroundColor: 'white',
    fontSize: 15,
    lineHeight: 18,
    paddingHorizontal: 0,
    minHeight: 120,
    height: 'auto',
    top: 90,
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: 500,
    textAlign: 'left',
    paddingLeft: 26,
    paddingRight: 26,
    paddingTop: 16,
    paddingBottom: 16,
    textAlignVertical: 'top',
  },

  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 20,
    left: 0,
    right: 0,
    boxShadow: '0px 10px 10px 0px rgba(0, 0, 0, 0.15)',
    borderBottomWidth: 0,
  },

  button: {
    minWidth: 80,
  },

  buttonText: {
    color: '#0A0A0B',
    fontSize: 12,
    fontWeight: '400',
    fontFamily: 'Plus Jakarta Sans',
  },
});