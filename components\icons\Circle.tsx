import * as React from "react";
import Svg, {
  G,
  Path,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Rect,
} from "react-native-svg";
import { CommonProps } from ".";

const Circle = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Circle-1_svg__a)">
            <Path d="M6 0.5C9.03761 0.5 11.5 2.96243 11.5 6C11.5 9.03761 9.03761 11.5 6 11.5C2.96243 11.5 0.5 9.03761 0.5 6C0.5 2.96243 2.96243 0.5 6 0.5Z" fill="url(#Circle-1_svg__b)"/>
          </G>
          <Defs>
            <LinearGradient id="Circle-1_svg__b" x1="6" y1="-0.827586" x2="6" y2="14.0287" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <ClipPath id="Circle-1_svg__a">
              <Rect width="12" height="12" fill="white"/>
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G clipPath="url(#Circle_svg__a)">
            <Path d="M6 11C8.76146 11 11 8.76146 11 6C11 3.23858 8.76146 1 6 1C3.23858 1 1 3.23858 1 6C1 8.76146 3.23858 11 6 11Z" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
          </G>
          <Defs>
            <ClipPath id="Circle_svg__a">
              <Rect width="12" height="12" fill="white"/>
            </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  );
};
export default Circle;
