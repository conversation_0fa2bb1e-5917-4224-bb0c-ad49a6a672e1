import React, { useState } from 'react';
import { View, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import { Text} from 'react-native-paper';
import { router } from 'expo-router';
import { ImageCarousel } from '@/components/ImageCarousel';
import { SafeAreaView } from 'react-native-safe-area-context';
  import { Colors, Spacing, Typography ,Borders} from '@/constants/DesignSystem';

const { width } = Dimensions.get('window');

const onboardingImages = [
  {
    id: '1',
    image_src: require('@/assets/images/onBoarding/1_3x.png')
  },
  {
    id: '2',
    image_src: require('@/assets/images/onBoarding/2_3x.png'),
  },
  {
    id: '3',
    image_src: require('@/assets/images/onBoarding/3_3x.png'),
  },
  {
    id: '4',
    image_src: require('@/assets/images/onBoarding/4_3x.png'),
  },
];

export default function OnBoarding() {
    console.log('onBoarding');
  const [currentIndex, setCurrentIndex] = useState(0);



  const handleNext = () => {
    if(currentIndex < onboardingImages.length - 1) {
      setCurrentIndex(currentIndex + 1);
    } else {
      router.replace('/(home)/(tabs)');
    }
  };

  const handleSkip = () => {
    if(currentIndex < onboardingImages.length - 1) {
      router.replace('/(home)/(tabs)');
    } 
  };

  return (
    <SafeAreaView style={styles.container}>
      
    

      <View style={styles.carouselContainer}>
        <ImageCarousel
          images={onboardingImages}
          showDotPagination={true}
          showNumberPagination={false}
          isOnBoarding={true}
          currentIndex={currentIndex}
          onIndexChange={setCurrentIndex}
        />
          <View style={styles.headerContainer}>
     
      <View style={styles.skipContainer}>
        <TouchableOpacity onPress={handleSkip}>
          <Text style={[styles.skipText ]}>{currentIndex === onboardingImages.length - 1 ? '' : 'SKIP'}</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.nextContainer}>
        <TouchableOpacity onPress={handleNext}>
          <Text style={styles.nextText }>{currentIndex === onboardingImages.length - 1 ? 'START' : 'NEXT'}</Text>
        </TouchableOpacity>
      </View>
      </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    position: 'absolute',
    left: Spacing.lg,
    right: Spacing.lg,
    zIndex: 10000,
    bottom: Spacing.md,

  },
  skipContainer: {
  alignItems: 'center',
  justifyContent: 'center',

  },
  skipText: {
    fontSize: Typography.fontSize.md,
    color: Colors.text.tertiary,
    padding: Spacing.md,
  },
  nextContainer: {
    borderWidth: 1,
    borderColor: Colors.error,
   backgroundColor: Colors.background.secondary,
   borderRadius: Borders.radius.md,
   height: 40,
   width: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.error,
    padding: Spacing.md,
  },
  carouselContainer: {
    flex: 1,
    width: width,
  },
  bottomContainer: {
    padding: Spacing.lg,
    paddingBottom: Spacing.xxl,
  },
  button: {
    borderRadius: Borders.radius.md,
    paddingVertical: Spacing.sm,
  },
  buttonLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.semibold,
  },
});
