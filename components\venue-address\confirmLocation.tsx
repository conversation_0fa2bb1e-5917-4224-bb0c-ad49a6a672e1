import { View, Text, StyleSheet, Dimensions, TouchableOpacity } from "react-native";
import { StackScreenProps } from '@react-navigation/stack';
import { AddPartyRootStackList } from '@/app/(home)/EventsDashboard/AddPartyRootStackList';
import MapView, { Marker } from 'react-native-maps';
import { useEffect, useState } from 'react';
import { Button } from 'react-native-paper';
import { Colors, Spacing, Typography, Borders } from "@/constants/DesignSystem";
import { getPlaceDetails } from "@/services/googleMapsService";
import { useVenueStore } from '@/store/venueStore';

export interface LocationDetails {
    latitude: number;
    longitude: number;
    name: string,
    formatted_address: string;
}

type ConfirmLocationRouteProp = StackScreenProps<AddPartyRootStackList, 'ConfirmLocation'>;

export default function ConfirmLocation({ route, navigation }: ConfirmLocationRouteProp) {
    const { placeId, name, partyId, eventGroupId, isEditing, onPressUpdate } = route.params;
    const [locationDetails, setLocationDetails] = useState<LocationDetails | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const setVenueDetails = useVenueStore((state) => state.setVenueDetails);
    const clearVenueDetails = useVenueStore((state) => state.clearVenueDetails);

    useEffect(() => {
        const fetchLocationDetails = async () => {
            if (!placeId) {
                clearVenueDetails();
                return;
            }

            try {
                const locationDetails = await getPlaceDetails(placeId)

                setLocationDetails({
                    latitude: locationDetails.result.geometry.location.lat,
                    longitude: locationDetails.result.geometry.location.lng,
                    name: name,
                    formatted_address: locationDetails.result.formatted_address
                });
            } catch (error) {
                console.error('Error fetching location details:', error);
                clearVenueDetails();
            } finally {
                setIsLoading(false);
            }
        };

        fetchLocationDetails();
    }, [placeId, name, clearVenueDetails]);

    if (!placeId) {
        return (
            <View style={styles.container}>
                <Text>Error: Location data not found.</Text>
            </View>
        );
    }

    if (isLoading) {
        return (
            <View style={styles.container}>
                <Text>Loading location details...</Text>
            </View>
        );
    }

    if (!locationDetails) {
        return (
            <View style={styles.container}>
                <Text>Error: Could not fetch location details.</Text>
            </View>
        );
    }

    const handleAddMoreDetails = () => {
        if (!locationDetails) return;
        
        setVenueDetails({
            isNew: true,
            placeId,
            name: locationDetails.name
        });
        
        navigation.popTo('AddressDetails', {address: locationDetails.formatted_address, title: locationDetails.name, partyId: partyId, eventGroupId: eventGroupId, isEditing: isEditing, onPressUpdate: onPressUpdate});
    }

    return (
        <View style={styles.container}>
            <View style={styles.mapContainer}>
                <MapView
                    style={styles.map}
                    initialRegion={{
                        latitude: locationDetails.latitude,
                        longitude: locationDetails.longitude,
                        latitudeDelta: 0.005,
                        longitudeDelta: 0.005,
                    }}
                >
                    <Marker
                        coordinate={{
                            latitude: locationDetails.latitude,
                            longitude: locationDetails.longitude,
                        }}
                    />
                </MapView>
            </View>
            
            <View style={styles.detailsContainer}>
                <View>
                    <Text style={styles.title}>{locationDetails.name}</Text>
                    <View style={styles.addressContainer}>
                        <Text style={styles.address}>{locationDetails.formatted_address}</Text>
                        <TouchableOpacity onPress={() => {
                            clearVenueDetails();
                            navigation.goBack();
                        }}>
                            <Text style={styles.changeText}>Change</Text>
                        </TouchableOpacity>
                    </View>
                </View>
                <Button 
                    mode="contained" 
                    onPress={() => {
                        handleAddMoreDetails();
                    }}
                    style={styles.button}
                    labelStyle={styles.buttonLabel}
                >
                    Add More Details
                </Button>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background.primary,
    },
    mapContainer: {
        height: Dimensions.get('window').height * 0.6,
        width: '100%',
    },
    map: {
        ...StyleSheet.absoluteFillObject,
    },
    detailsContainer: {
        flex: 1,
        padding: Spacing.lg,
        justifyContent: 'space-evenly',
        alignItems: 'stretch',
    },
    title: {
        fontSize: Typography.fontSize.lg,
        fontWeight: Typography.fontWeight.bold,
        marginBottom: Spacing.md,
    },
    addressContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: Spacing.md,
    },
    address: {
        fontSize: Typography.fontSize.md,
        color: Colors.text.secondary,
        flex: 1,
        marginRight: Spacing.sm,
    },
    changeText: {
        color: Colors.text.primary,
        fontSize: Typography.fontSize.md,
        fontWeight: Typography.fontWeight.bold,
    },
    button: {
        marginTop: Spacing.md,
        backgroundColor: Colors.primary,
        borderRadius: Borders.radius.md,
        paddingVertical: Spacing.sm,
    },
    buttonLabel: {
        fontSize: Typography.fontSize.md,
        fontWeight: Typography.fontWeight.bold,
        color: Colors.black,
    },
    markerContainer: {
        alignItems: 'center',
        justifyContent: 'center',
    },
    addressInput: {
        flex: 1,
        borderWidth: Borders.width.thin,
        borderColor: Colors.border.medium,
        borderRadius: Borders.radius.md,
        padding: Spacing.sm,
        fontSize: Typography.fontSize.md,
        minHeight: 40,
    },
});