import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Keyboard,
  Modal,
  SafeAreaView,
  Platform,
} from "react-native";
import {
  Activity,
  Reaction,
  User,
} from "@/app/(home)/PartyDetails/NewPartyDetailsResponse.model";
import { getMessageTime } from "@/app/(home)/utils/reusableFunctions";
import ReactionsRow from "./ReactionView";
import { REMOVE_REACTION } from "./ActivityFeed.data";
import { useMutation } from "@apollo/client";
import { useToast } from "@/components/Toast";
import { CREATE_REACTION, ADD_REACTION } from "./CreateReaction.data";
import { POST_COMMENT } from "@/app/(home)/(tabs)/party-dashboard/partydetails.data";
import EmojiPopover from "./EmojiRobbon";
import { useNavigation } from "expo-router";
import { NavigationProp } from "@react-navigation/native";
import { PartyDetailsRootList } from "../Navigation/PartyDetailsRootList";
import { useUserStore } from "@/app/auth/userStore";
import { WebView } from 'react-native-webview';
import { BackArrow, File, Emoji } from '@/components/icons';
import { Borders, Colors,Icons, Spacing, Typography } from "@/constants/DesignSystem";

interface ActivityContentProps {
  activity: Activity;
  partyId: string;
}

const CommentAndMedia: React.FC<ActivityContentProps> = ({
  activity,
  partyId,
}) => {
  const [containsReactions, setReactions] = useState(
    activity.reactions.length > 0
  );
  const [isEditorVisible, setEditorVisible] = useState(false);
  const [isEmojiPickerVisible, setEmojiPickerVisible] = useState(false);
  const [currentActivity, setCurrentActivity] = useState(activity);
  const [selectedEmoji, setSelectedEmoji] = useState<string | null>(null);
  const [documentUrl, setDocumentUrl] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [postComment] = useMutation(POST_COMMENT);
  const [createReaction] = useMutation(CREATE_REACTION);
  const [addReaction] = useMutation(ADD_REACTION);
  const [removeReaction] = useMutation(REMOVE_REACTION);
  const userData = useUserStore((state) => state.userData);
  const navigation = useNavigation<NavigationProp<PartyDetailsRootList>>();
  const toast = useToast();
  const smileIconRef = useRef<View>(null);

  useEffect(() => {
    const userReaction = currentActivity.reactions.find(
      (reaction) => reaction.user.id === userData?.id
    );
    setSelectedEmoji(userReaction?.reactionUnicode || null);
  }, [currentActivity.reactions, userData?.id]);

  useEffect(() => {
    setCurrentActivity(activity);
  }, [activity]);

  const showEditor = () => {
    console.log("Showing reply editor", activity);
    navigation.navigate("KeyboardEditor", {
      props: {
        isVisible: true,
        parentId: activity.id,
        toggleLocation: false,
        onSubmit: handleCommentSubmission,
        onClose: () => setEditorVisible(false),
        editedComment: undefined,
        handleEditedComment: async () => { },
        onLocation: handleLocationToggle,
      },
    });
  };

  const handleLocationToggle = async (isEnabled: boolean) => { };

  const handleCommentSubmission = (
    commentText: string,
    parentId: string | undefined,
    mediaId: string | null
  ) => {
    console.log("Just checking if this is called somewhere");
    setEditorVisible(false);
    postComment({
      variables: {
        input: {
          partyId: partyId,
          text: commentText,
          type: "USER",
          parentMessageId: activity.id ?? undefined,
          media: mediaId,
        },
      },
    })
      .then(() => {
        Keyboard.dismiss();
      })
      .catch((error) => {
        const graphqlError = error?.graphQLErrors?.[0]?.message;
        const networkError = error?.networkError?.result?.errors?.[0]?.message;
        const errorMessage =
          graphqlError ||
          networkError ||
          "Failed to update party. Please try again.";
        Keyboard.dismiss();
        toast.error(errorMessage);
        console.error("Error adding comment", JSON.stringify(error));
      });
  };

  const handleEmojiSelected = async (emoji: string) => {
    console.log("Selected emoji:", emoji);
    setEmojiPickerVisible(false);

    try {
      const reaction = await createReaction({
        variables: {
          input: {
            reactionUnicode: emoji,
          },
        },
      });
      const reactionId = reaction.data.createReaction.result.reaction.id;
      console.log("Reaction created:", reactionId);

      const newReaction: Reaction = {
        reactionUnicode: emoji,
        updatedAt: new Date(),
        user: {
          id: userData?.id ?? "",
          firstName: userData?.firstName ?? "",
          profilePicture: "",
          email: "",
          phone: "",
          lastName: "",
          role: [""],
        },
        id: reactionId,
      };

      setCurrentActivity((prevActivity) => {
        const existingReactionIndex = prevActivity.reactions.findIndex(
          (reaction) => reaction.user.id === userData?.id
        );

        const existingReaction = prevActivity.reactions[existingReactionIndex];
        if (existingReaction && existingReaction.reactionUnicode === emoji) {
          console.log(
            "User has already reacted with the same emoji. Skipping..."
          );
          return prevActivity;
        }

        if (existingReaction && existingReactionIndex !== -1) {
          const updatedReactions = [...prevActivity.reactions];
          updatedReactions[existingReactionIndex] = newReaction;
          return {
            ...prevActivity,
            reactions: updatedReactions,
          };
        } else {
          return {
            ...prevActivity,
            reactions: [...prevActivity.reactions, newReaction],
          };
        }
      });

      setReactions(true);

      await addReaction({
        variables: {
          messageId: currentActivity.id,
          reactionId: reactionId,
        },
      });
      console.log('Added a new reaction');
      console.log("Added a new reaction");
    } catch (error) {
      console.error("Error creating reaction:", error);
      alert("Failed to add a reaction");
    }
  };

  const remove = async (emoji: string) => {
    setEmojiPickerVisible(false);
    const reaction = currentActivity.reactions.find(
      (reaction) =>
        reaction.reactionUnicode === emoji && reaction.user.id === userData?.id
    );

    if (!reaction) {
      console.log("No matching reaction found for the emoji.");
      return;
    }

    try {
      const removeReactionResult = await removeReaction({
        variables: {
          messageId: currentActivity.id,
          reactionId: reaction.id,
        },
      });
      console.log("Removed reaction successfully!");
      setCurrentActivity((prevActivity) => ({
        ...prevActivity,
        reactions: prevActivity.reactions.filter((r) => r.id !== reaction.id),
      }));

      setReactions(currentActivity.reactions.length > 1);
    } catch {
      console.log("Unable to remove reaction for the message");
    }
  };

  useEffect(() => {
    setReactions(currentActivity.reactions.length > 0);
  }, [currentActivity]);

  const openDocument = (url: string) => {
    setDocumentUrl(url);
  };

  const closeDocument = () => {
    setDocumentUrl(null);
  };

  const getPdfViewerUrl = (url: string) =>
    Platform.OS === "android"
      ? `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(`${url}?t=${Date.now()}`)}`
      : url;
  

  const openImage = (url: string) => {
    setImageUrl(url);
  };

  const renderMedia = () => {
    if (!currentActivity.media || currentActivity.media.length === 0)
      return null;

    const mediaItem = currentActivity.media[0];
    const isImage = mediaItem.url.match(/\.(jpeg|jpg|png|gif)$/i);
    const isPdf = mediaItem.url.match(/\.pdf$/i);
    const fileName = mediaItem.url.split("/").pop() || "Document";

    return (
      <View style={styles.mediaContainer}>
        {isImage ? (
          <TouchableOpacity onPress={() => openImage(mediaItem.url)}>
            <Image source={{ uri: mediaItem.url }} style={styles.mediaImage} />
          </TouchableOpacity>
        ) : isPdf ? (
          <TouchableOpacity
            style={styles.documentPreview}
            onPress={() => openDocument(mediaItem.url)
              
            }

          >
            <File size={Icons.size.lg} color={Colors.primary} />
            <Text style={styles.documentName}>{fileName}</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={styles.documentPreview}
            onPress={() => openDocument(mediaItem.url)}
          >
            <File size={Icons.size.lg} color={Colors.primary} />
            <Text style={styles.documentName}>{fileName}</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  useEffect(() => {
    setReactions(currentActivity.reactions.length > 0);
  }, [currentActivity]);

  const displayName = currentActivity.type === "SYSTEM"
    ? "Antsy"
    : `${currentActivity.createdBy?.firstName ?? ""} ${currentActivity.createdBy?.lastName ?? ""}`;

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <View style={styles.commentColumn}>
          <View style={styles.commentRow}>
            <Text style={styles.headline}>{displayName}</Text>
            <Text style={styles.subHeadline}>
              {getMessageTime(currentActivity.createdAt)}
            </Text>
          </View>
          {currentActivity.text && <Text style={styles.text}>{currentActivity.text}</Text>}
          {renderMedia()}

          {/* Reactions and Comments */}
          <View style={[styles.row, { marginTop: 15 }]}>
            {containsReactions && (
              <ReactionsRow
                reactions={currentActivity.reactions}
                remove={remove}
              />
            )}
            <TouchableOpacity
              ref={smileIconRef}
              onPress={() => setEmojiPickerVisible(true)}
            >
              <Emoji
                size={Icons.size.lg}
                color={Colors.primary} />
            </TouchableOpacity>
            <View style={{ width: 10 }} />

            {currentActivity.type !== "SYSTEM" &&
              (currentActivity.parentMessageId === undefined ||
                currentActivity.parentMessageId === null) && (
                <TouchableOpacity onPress={() => showEditor()}>
                  <Text style={styles.commentFont}>Reply</Text>
                </TouchableOpacity>
              )}
          </View>

          {isEmojiPickerVisible && (
            <EmojiPopover
              isVisible={isEmojiPickerVisible}
              onEmojiSelected={handleEmojiSelected}
              remove={remove}
              onClose={() => setEmojiPickerVisible(false)}
              fromView={smileIconRef}
              selectedEmoji={selectedEmoji}
            />
          )}
        </View>
      </View>
      <Modal
        visible={!!documentUrl}
        animationType="slide"
        onRequestClose={closeDocument}
      >
        <SafeAreaView style={styles.webViewContainer}>
          <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={closeDocument}>
              <BackArrow size={Icons.size.md} color={Colors.black} />
            </TouchableOpacity>
          </View>
          {documentUrl && (
            <WebView
              source={{ uri: getPdfViewerUrl(documentUrl) }}
              onError={() => {
                setDocumentUrl(null);
                toast.error("Failed to load document. Try again.");
              }}
              style={styles.webView}
              startInLoadingState
            />
          )}
        </SafeAreaView>
      </Modal>
      <Modal
        visible={!!imageUrl}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setImageUrl(null)}
      >
        <SafeAreaView style={styles.imageModalContainer}>
          <View style={styles.imageContainer}>
          <View >
            <TouchableOpacity 
              style={styles.closeButton} 
              onPress={() => setImageUrl(null)}
            >
              <BackArrow size={Icons.size.lg} color={Colors.black} />
            </TouchableOpacity>
          </View>
          <Image
            source={{ uri: imageUrl || '' }}
            style={styles.fullSizeImage}
              resizeMode="contain"
            />
          </View>
        </SafeAreaView>
      </Modal>
    </View>
  );

};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: Spacing.md,
    backgroundColor: Colors.white,
    marginRight: Spacing.md,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap",
  },
  commentRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  commentColumn: {
    flexDirection: "column",
    alignItems: "flex-start",
  },
  text: {
    marginLeft: Spacing.xs,
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.md,
    color: Colors.black,
  },
  icon: {
    top: 0,
    right: 0,
    marginLeft: Spacing.sm,
  },
  commentFont: {
    color: Colors.mediumGray,
    marginLeft: Spacing.xs,
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.bold,
  },
  headline: {
    marginLeft: Spacing.xs,
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.black,
  },
  subHeadline: {
    marginLeft: Spacing.xs,
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.primary,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.mediumGray,
  },
  mediaContainer: {
    marginTop: Spacing.sm,

  },
  mediaImage: {
    width: 100,
    height: 100,
    resizeMode: "cover",
    borderRadius: Borders.radius.lg,
  },
  documentPreview: {
    flexDirection: "row",
    alignItems: "center",
    padding: Spacing.sm,
  },
  documentName: {
    marginLeft: Spacing.sm,
    fontSize: Typography.fontSize.md,
    color: Colors.black,
  },
  modal: {
    justifyContent: "flex-end",
    margin: 0,
  },
  bottomSheet: {
    height: "50%",
    backgroundColor: Colors.white,
    borderTopLeftRadius: Borders.radius.lg,
    borderTopRightRadius: Borders.radius.lg,
    padding: Spacing.sm,
  },
  webViewContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    backgroundColor: Colors.white,
    padding: Spacing.sm,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    padding: Spacing.sm,
  },
  webView: {
    flex: 1,
  },
  imageModalContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  imageContainer: {
    flex: 1,
    backgroundColor: Colors.white,
    position: 'relative',
    justifyContent: 'center',
    
  },

  closeButton: {
    padding: Spacing.sm,
    borderRadius: Borders.radius.lg,
  },
  fullSizeImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
});

export default CommentAndMedia;
