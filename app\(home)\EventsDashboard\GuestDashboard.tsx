import { router } from 'expo-router';
import React, { useState } from 'react';
import { StyleSheet, View, Text, TextInput, TouchableOpacity, Image, Alert, ScrollView } from 'react-native';

interface EventData {
  imageUrl: string;
  heading: string;
  date: string;
  location: string;
  host: string;
}

const eventData: EventData = {
  imageUrl: 'https://picsum.photos/200/300',
  heading: 'Get Together Party!',
  date: '🗓️ 14th February 2025',
  location: '📍 Downtown Party Hall',
  host: '<PERSON>',
};

const GuestDashboard: React.FC = () => {
  const [response, setResponse] = useState<'yes' | 'maybe' | 'no' | null>(null);
  const [name, setName] = useState<string>('');
  const [guests, setGuests] = useState<number>(0);
  const [comment, setComment] = useState<string>('');

  const handleFormSubmit = () => {
    Alert.alert(
      'RSVP Received', 
      `Thank you, ${name}! Your RSVP with ${guests} guest(s) and the comment "${comment}" has been recorded.`,
      [
        { text: "Close", onPress: () => router.back() }
      ]
    );
};

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.card}>
        <Image source={{ uri: eventData.imageUrl }} style={styles.image} />
        <Text style={styles.heading}>{eventData.heading}</Text>
        <Text style={styles.details}>{eventData.date}</Text>
        <Text style={styles.details}>{eventData.location}</Text>
        <Text style={styles.details}>👤 Hosted by: {eventData.host}</Text>
        
        {!response && (
          <View style={styles.buttonContainer}>
            {['yes', 'maybe', 'no'].map((option) => (
              <TouchableOpacity
                key={option}
                style={[styles.button, { backgroundColor: option === 'yes' ? '#4CAF50' : option === 'maybe' ? '#FFC107' : '#F44336' }]}
                onPress={() => setResponse(option as 'yes' | 'maybe' | 'no')}
              >
                <Text style={styles.buttonText}>{option.charAt(0).toUpperCase() + option.slice(1)}</Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
        
        {response && (
          <View style={[styles.formContainer, { display: response === 'no' ? 'none' : 'flex' }]}>
            <TextInput
              style={styles.input}
              onChangeText={setName}
              value={name}
              placeholder="Your Name"
              keyboardType="default"
            />
            <View style={styles.counterContainer}>
              <TouchableOpacity style={styles.counterButton} onPress={() => setGuests(Math.max(0, guests - 1))}>
                <Text style={styles.counterButtonText}>-</Text>
              </TouchableOpacity>
              <Text style={styles.counterLabel}>{guests}</Text>
              <TouchableOpacity style={styles.counterButton} onPress={() => setGuests(guests + 1)}>
                <Text style={styles.counterButtonText}>+</Text>
              </TouchableOpacity>
            </View>
            <TextInput
              style={styles.input}
              onChangeText={setComment}
              value={comment}
              placeholder="Comment (Optional)"
              keyboardType="default"
            />
            <TouchableOpacity style={styles.submitButton} onPress={handleFormSubmit}>
              <Text style={styles.submitButtonText}>Submit RSVP</Text>
            </TouchableOpacity>
          </View>
        )}
        
        {response === 'no' && (
          <Text style={styles.sorryText}>We're sorry to miss you. Thank you for letting us know.</Text>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  card: {
    width: '100%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  image: {
    width: 200,
    height: 300,
    borderRadius: 10,
    alignSelf: 'center',
    marginBottom: 20,
  },
  heading: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  details: {
    fontSize: 16,
    marginBottom: 5,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 20,
  },
  button: {
    padding: 10,
    borderRadius: 20,
    marginHorizontal: 10,
  },
  buttonText: {
    color: 'white',
  },
  formContainer: {
    marginTop: 20,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  counterContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  counterButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ddd',
    borderRadius: 20,
  },
  counterButtonText: {
    fontSize: 18,
    color: '#333',
  },
  counterLabel: {
    marginHorizontal: 20,
    fontSize: 18,
    fontWeight: 'bold',
  },
  submitButton: {
    backgroundColor: '#007BFF',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
  },
  sorryText: {
    marginTop: 20,
    fontSize: 16,
    textAlign: 'center',
  },
});

export default GuestDashboard;
