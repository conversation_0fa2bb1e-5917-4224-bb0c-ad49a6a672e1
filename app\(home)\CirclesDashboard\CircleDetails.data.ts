import { gql } from "graphql-tag";

export const GET_CIRCLE_BY_ID = gql`
  query GetEventGroupById($getEventGroupByIdId: ID!) {
    getEventGroupById(id: $getEventGroupByIdId) {
      ... on EventGroupErrorResponse {
        status
        message
        errors {
          field
          message
        }
      }
      ... on EventGroupResponse {
        status
        message
        result {
          eventGroup {
            createdAt
            createdBy {
              firstName
              lastName
              id
              role
            }
            description
            id
            imageUrl
            members {
              firstName
              lastName
              role
              profilePicture
              id
              phone
            }
            organizers {
              email
              firstName
              lastName
              phone
              role
              profilePicture
              id
            }
            name
            membersCount
            type
            userRole
          }
        }
      }
    }
  }
`;

export const DELETE_CIRCLE_BY_ID = gql`
  mutation DeleteEventGroup($deleteEventGroupId: ID!) {
    deleteEventGroup(id: $deleteEventGroupId) {
      ... on EventGroupResponse {
        message
        status
      }
      ... on EventGroupErrorResponse {
        errors {
          message
        }
        message
        status
      }
    }
  }
`;

export const UPDATE_CIRCLE_BY_ID = gql`
 mutation UpdateEventGroup($updateEventGroupId: ID!, $input: EventGroupUpdateInput!) {
  updateEventGroup(id: $updateEventGroupId, input: $input) {
    ... on EventGroupResponse {
      message
      result {
        eventGroup {
          createdAt
          createdBy {
            email
            firstName
            id
            lastName
            phone
            role
          }
          description
          id
          imageUrl
          members {
            email
            firstName
            id
            lastName
            phone
            profilePicture
            role
          }
          membersCount
          name
          organizers {
            email
            firstName
            id
            lastName
            phone
            role
          }
          userRole
        }
      }
      status
    }
    ... on EventGroupErrorResponse {
      errors {
        message
        field
      }
      message
      status
    }
  }
}
`;

export const GET_CIRCLE_INVITATIONS_COUNT = gql`
query GetCircleInvitationCounts($eventGroupId: ID) {
  accepted: getEventGroupInvitations (filter:  {
     status: ACCEPTED,
     eventGroupId: $eventGroupId
  }) {
    ... on EventGroupInvitationsResponse {
      pagination {
        totalItems
        totalPages
        pageSize
        currentPage
      }
      message
      status
    }
  }
  pending: getEventGroupInvitations (filter:  {
     status: PENDING,
     eventGroupId: $eventGroupId
  }) {
    ... on EventGroupInvitationsResponse {
      pagination {
        totalItems
        totalPages
        pageSize
        currentPage
      }
      message
      status
    }
  }
  rejected: getEventGroupInvitations (filter:  {
     status: REJECTED,
     eventGroupId: $eventGroupId
  }) {
    ... on EventGroupInvitationsResponse {
      pagination {
        totalItems
        totalPages
        pageSize
        currentPage
      }
      message
      status
    }
  }
}
`;

export const GET_CIRCLE_INVITATIONS = gql`
query EventGroupInvitations($pagination: PaginationInput, $filter: EventGroupInvitationFilterInput, $getEventGroupByIdId: ID!) {
  getEventGroupInvitations(pagination: $pagination, filter: $filter) {
    ... on EventGroupInvitationsResponse {
      result {
        eventGroupInvitations {
          createdAt
          id
          user {
            id
            firstName
            lastName
            phone
            profilePicture
          }
          status
        }
      }
      pagination {
        totalItems
        totalPages
        pageSize
        currentPage
      }
      message
      status
    }
    ... on EventGroupInvitationErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
  getEventGroupById(id: $getEventGroupByIdId) {
    ... on EventGroupResponse {
      status
      message
      result {
        eventGroup {
          id
          name
          membersCount
          type
          userRole
          description
          imageUrl
          createdAt
          members {
            id
            firstName
            lastName
            phone
            profilePicture
          }
          organizers {
            id
          }
          createdBy {
            id
          }
        }
      }
    }
    ... on EventGroupErrorResponse {
      errors {
        field
        message
      }
      status
      message
    }
  }
}
`;


export const DELETE_EVENT_GROUP_INVITATION = gql`
  mutation DeleteEventGroupInvitation($deleteEventGroupInvitationId: ID!) {
    deleteEventGroupInvitation(id: $deleteEventGroupInvitationId) {
      ... on EventGroupInvitationResponse {
        status
        message
        result {
          eventGroupInvitation {
            id
            user {
              firstName
              id
            }
          }
        }
      }
      ... on EventGroupInvitationErrorResponse {
        status
        message
        errors {
          field
          message
        }
      }
    }
  }
`; 