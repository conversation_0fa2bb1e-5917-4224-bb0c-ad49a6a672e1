import React from 'react';
import { View, StyleSheet, FlatList, ActivityIndicator, Text, Linking } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { Stack } from 'expo-router';
import { useQuery } from '@apollo/client';
import { 
  MdPromotionByTagId, 
  GetMdPromotionsByTagIdData, 
  GetMdPromotionsVars 
} from '@/app/(home)/UserDashboard/promotions';
import { 
  GET_PROMOTIONS_BY_TAG,
} from '@/app/(home)/UserDashboard/Promotions.data';
import { ProductCard } from '@/components/home/<USER>';

async function openPromotionLink(ctaLink?: string) {
  if (!ctaLink) return;
  
  try {
    const canOpen = await Linking.canOpenURL(ctaLink);
    if (canOpen) {
      await Linking.openURL(ctaLink);
    } else {
      console.log('Cannot open URL:', ctaLink);
    }
  } catch (error) {
    console.error('Error opening URL:', error);
  }
}

const SpecialOffersHeader: React.FC<{ item: MdPromotionByTagId }> = ({ item }) => (
  <View style={styles.fullWidthItem}>
    <ProductCard
      product={item}
      onPress={(product) => {
        if (product.ctaLink) {
          openPromotionLink(product.ctaLink);
        }
      }}
      style={styles.specialOfferFullItem}
      variant="offer"
      partnerName={item.partner?.name}
      isFullWidth={true}
    />
  </View>
);

export default function ProductListView() {
  const { category, tagSlug } = useLocalSearchParams<{ 
    category: string;
    tagSlug: string;
  }>();

  const { data, loading, error } = useQuery<GetMdPromotionsByTagIdData, GetMdPromotionsVars>(
    GET_PROMOTIONS_BY_TAG,
    {
      variables: {
        pagination: { limit: 100, skip: 0 },
        tagSlug
      },
      skip: !tagSlug,
      fetchPolicy: 'network-only'
    }
  );

  const getTitle = () => {
    switch (category) {
      case 'deal-of-the-day':
        return 'Deal of the Day';
      case 'trending-products':
        return 'Trending Products';
      case 'special-offers':
        return 'Special Offers';
      default:
        return 'Products';
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text>Error loading products. Please try again later.</Text>
      </View>
    );
  }

  const products = data?.getMdPromotions.result.mdPromotions || [];

  const formattedProducts = products.map(product => ({
    ...product,
    tags: [{
      id: tagSlug,
      name: getTitle(),
      slug: category
    }]
  }));

  console.log('Formatted products:', formattedProducts.length);

  const renderItem = ({ item }: { item: MdPromotionByTagId }) => (
    <View style={styles.gridItem}>
      <ProductCard
        product={item}
        onPress={(product) => {
          if (product.ctaLink) {
            openPromotionLink(product.ctaLink);
          }
        }}
        style={category === 'special-offers' ? styles.specialOfferGridItem : undefined}
        variant={category === 'special-offers' ? 'offer' : undefined}
        partnerName={category === 'special-offers' ? item.partner?.name : undefined}
      />
    </View>
  );

  return (
    <>
      <Stack.Screen 
        options={{
          title: getTitle(),
          headerBackTitle: 'Back',
        }} 
      />
      <View style={styles.container}>
        <FlatList
          data={category === 'special-offers' ? formattedProducts.slice(1) : formattedProducts}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          numColumns={2}
          contentContainerStyle={styles.listContent}
          ListHeaderComponent={
            category === 'special-offers' && formattedProducts.length > 0
              ? () => <SpecialOffersHeader item={formattedProducts[0]} />
              : undefined
          }
          columnWrapperStyle={styles.columnWrapper}
          ListEmptyComponent={() => (
            <View style={styles.centered}>
              <Text>No products found</Text>
            </View>
          )}
        />
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  listContent: {
    padding: 20,
    gap: 16,
  },
  columnWrapper: {
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  specialOfferItem: {
    marginBottom: 12,
    width: '100%',
    height: 160,
  },
  specialOfferList: {
    paddingHorizontal: 0,
  },
  specialOfferFullItem: {
    width: '100%',
    height: 240,
  },
  specialOfferGridItem: {
    height: 240,
  },
  fullWidthContainer: {
    width: '100%',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  separator: {
    height: 16,
  },
  gridItemContainer: {
    flex: 1,

    flexDirection: 'row',
    paddingHorizontal: 16,
  },
  fullWidthItem: {
    width: '100%',
    marginBottom: 16,
    paddingHorizontal: 8,
  },
  gridItem: {
    width: '48%',
    marginBottom: 16,
    height: 240,
  },
}); 