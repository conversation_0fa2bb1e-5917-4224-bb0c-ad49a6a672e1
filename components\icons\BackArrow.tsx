import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const BackArrow = ({
  size,
  color,
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
            stroke="url(#Back_arrow-1_svg__a)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={strokeWidth}
            d="m5.272 1.999-3.888 4 3.89 4"
          />
          <Path
            stroke="url(#Back_arrow-1_svg__b)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={strokeWidth}
            d="M10.718 5.998 1.384 6"
          />
          <Defs>
            <LinearGradient
              id="Back_arrow-1_svg__a"
              x1={0.915}
              x2={6.167}
              y1={6}
              y2={5.999}
              gradientUnits="userSpaceOnUse"
            >
              <Stop stopColor={gradientStartColor} />
              <Stop offset={1} stopColor={gradientEndColor} />
            </LinearGradient>
            <LinearGradient
              id="Back_arrow-1_svg__b"
              x1={0.258}
              x2={12.863}
              y1={6.5}
              y2={6.497}
              gradientUnits="userSpaceOnUse"
            >
              <Stop stopColor={gradientStartColor} />
              <Stop offset={1} stopColor={gradientEndColor} />
            </LinearGradient>
          </Defs>
        </>
      ) : (
        <Path
          stroke={color}
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={strokeWidth}
          d="m5.224 1.749-4.131 4.25 4.133 4.25M11.01 5.998 1.092 6"
        />
      )}
    </Svg>
  )
};

export default BackArrow;
