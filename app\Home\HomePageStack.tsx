import { createNativeStackNavigator } from '@react-navigation/native-stack';
import HomePage from '../(home)/(tabs)/homePage';
import PartyDetailsStack from '@/components/create-event/Navigation/PartyDetailsStack';
import { HomeRootStackList } from './HomeNavigation';
import { Platform } from 'react-native';
import { ArrowLeft } from 'lucide-react-native';

import { PaperProvider } from 'react-native-paper';
import AddPartyPageStack from '../(home)/EventsDashboard/AddPartyStack';
import AddCirclePageStack from '../(home)/CirclesDashboard/AddCircleStack';
import SearchInvitations from '../(home)/SearchInvitations';
import InAppNotification from '../(home)/inAppNotifications';

const HomeStack = createNativeStackNavigator<HomeRootStackList>();

const HomePageStack = () => {
  return (
    <PaperProvider>
      <HomeStack.Navigator
        screenOptions={{
          headerShown: false,
          presentation: Platform.OS === 'ios' ? 'fullScreenModal' : 'modal',
          animation: 'slide_from_bottom',
        }}
      >
        <HomeStack.Screen
          name="HomePage"
          component={HomePage}
          options={{
            headerShown: false,
          }}
        />
        <HomeStack.Screen
          name="PartyDetails"
          component={PartyDetailsStack}
        />
        <HomeStack.Screen
          name="AddPartyScreen"
          component={AddPartyPageStack}
        />
        <HomeStack.Screen
          name="AddCircleScreen"
          component={AddCirclePageStack}
        />
        <HomeStack.Screen
          name="SearchInvitations"
          component={SearchInvitations}
          options={{
            headerShown: true,
            headerTitle: 'Search Parties',
            headerTitleStyle: {
              fontSize: 24,
            },
            headerBackTitle: '',
            headerBackButtonDisplayMode: 'minimal',
            headerShadowVisible: true,
            headerTitleAlign: 'left',
            presentation: 'card'
          }}
        />
        <HomeStack.Screen
          name="InAppNotification"
          component={InAppNotification}
          options={({ navigation }) => ({
            headerShown: true,
            headerTitle: 'Notifications',
            headerTitleStyle: {
              fontSize: 24,
            },
            animation:"none",
            headerLeft: () => (
              Platform.OS === 'ios' ?
                <ArrowLeft color={'black'}
                  onPress={() => navigation.goBack()}
                /> : undefined
            ),
          })}
        />
      </HomeStack.Navigator>
    </PaperProvider>
  );
};

export default HomePageStack;

