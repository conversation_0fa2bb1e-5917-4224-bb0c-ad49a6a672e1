import { StyleSheet } from 'react-native';
import { sharedStyles } from '../Shared.styles';

export const styles = StyleSheet.create({
  inputWrapper: {
    flex: 1,
    backgroundColor: '#fff',
    height: 45,
  },
  inputStyle: {
    backgroundColor: '#fff',
    fontSize: 16,
  },
  assignedToInput: {
    backgroundColor: '#fff',
    textAlign: 'left',
    paddingHorizontal: 0,
    justifyContent: 'flex-start'
  },

  labelText: {
    ...sharedStyles.label,
  },
  
  valueText: {
    ...sharedStyles.value,
  },

  selectText: {
    position: 'absolute',
    right: 16,
    top: '50%',
    transform: [{ translateY: -8 }],
    color: '#655F5F',
    fontSize: 14,
    marginTop: 28,
  },
  assigneeSelectText: {
    left: 30,
    marginTop: 28,
  },
  saveValueText: {
    ...sharedStyles.placeholder,
  },

  bottomSheetContainer: {
    flex: 1,
    padding: 0,
  },
  searchInput: {
    height: 50,
    fontSize: 16,
    backgroundColor: '#fff',
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 2,
    borderBottomWidth: 1,
    borderTopWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.15)',
    overflow: 'visible',
  },
  infoText: {
    textAlign: 'center',
    color: '#6366F1',
    marginBottom: 16,
  },
  titleContainer: {
     flex: 1,
    marginRight: 16,
    paddingRight: 8,
  },
  listItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    minHeight: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    flexGrow: 1,
  },

  listItemTitle: {
    fontSize: 16,
    color: '#344054',
    flexWrap: 'wrap',
    flexShrink: 1,
  },

  loadingText: {
    textAlign: 'center',
    color: '#6B7280',
    marginTop: 16,
  },
  
  noDataText: {
    textAlign: 'center',
    color: '#6B7280',
    marginTop: 16,
  },

  scrollContent: {

  },

  header: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    backgroundColor: '#ffffff',
    marginBottom: 0, 
  },

  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#000000',
    textAlign: 'center',
  },

  bottomSheet: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
});