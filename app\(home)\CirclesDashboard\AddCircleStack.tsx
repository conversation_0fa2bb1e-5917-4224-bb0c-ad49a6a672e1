import { createNativeStackNavigator } from "@react-navigation/native-stack";
import AddCircle from "./AddCircle";
import EditMembers from "@/components/create-circle/EditMembers";
import { AddCircleRootStackList } from "./AddCircleRootStackList";
import { PaperProvider } from "react-native-paper";
import CircleDetails from "@/components/create-circle/CircleDetails";
import { Typography, Colors } from "@/constants/DesignSystem";

const AddCircleStack = createNativeStackNavigator<AddCircleRootStackList>();

const AddCirclePageStack = () => {

    return (
        <PaperProvider>
            <AddCircleStack.Navigator
                screenOptions={{
                    headerTitleStyle: {
                        fontFamily: Typography.fontFamily.primary,
                        fontWeight: Typography.fontWeight.semibold,
                        fontSize: Typography.fontSize.lg,
                        color: Colors.text.tertiary,
                    },
                    headerBackTitle: 'Back',
                    headerBackTitleStyle: {
                        fontFamily: Typography.fontFamily.primary,
                        fontSize: Typography.fontSize.md,
                    },
                }}>
                <AddCircleStack.Screen
                    name="AddCircle"
                    component={AddCircle}
                    options={{ headerShown: false }}
                />
                <AddCircleStack.Screen
                    name="EditMembers"
                    component={EditMembers}
                    options={{ headerShown: false }}
                />
                <AddCircleStack.Screen
                    name="CircleDetails"
                    component={CircleDetails}
                    options={{ headerShown: false }}
                />

            </AddCircleStack.Navigator>
        </PaperProvider>
    );
};

export default AddCirclePageStack;