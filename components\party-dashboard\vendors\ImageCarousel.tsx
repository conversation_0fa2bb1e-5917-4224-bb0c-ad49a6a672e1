import React, { useState } from 'react';
import {
  View,
  ScrollView,
  Image as RNImage,
  StyleSheet,
  NativeSyntheticEvent,
  NativeScrollEvent,
  ActivityIndicator,
  Text,
} from 'react-native';
import AnimatedDotsCarousel from 'react-native-animated-dots-carousel';
import { Colors } from '@/constants/Colors';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';

interface Image {
  id: string;
  image_src: string;
}

interface ImageCarouselProps {
  images: Image[];
  showDotPagination?: boolean;
  showNumberPagination?: boolean;
}

interface ImageItemProps {
  image_src: string;
  containerWidth: number;
}

function ImageItem({ image_src, containerWidth }: ImageItemProps) {
  const [isLoading, setIsLoading] = useState(true);

  return (
    <View style={[styles.imageContainer, { width: containerWidth }]}>
      <RNImage
        source={{ uri: image_src }}
        style={styles.image}
        resizeMode="cover"
        onLoadStart={() => setIsLoading(true)}
        onLoadEnd={() => setIsLoading(false)}
        onError={(error) => console.error('Image loading error:', error.nativeEvent.error)}
      />
      {isLoading && (
        <View style={styles.loaderContainer}>
          <FastPartyActivityIndicator />
        </View>
      )}
    </View>
  );
}

export function ImageCarousel({
  images,
  showDotPagination = true,
  showNumberPagination = false
}: ImageCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(offsetX / containerWidth);
    setCurrentIndex(index);
  };

  return (
    <View
      style={styles.container}
      onLayout={(event) => setContainerWidth(event.nativeEvent.layout.width)}
    >
      <ScrollView
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        {images.map((image) => (
          <ImageItem
            key={image.id}
            image_src={image.image_src}
            containerWidth={containerWidth}
          />
        ))}
      </ScrollView>

      {showNumberPagination && (
        <View style={styles.numberPaginationContainer}>
          <View style={styles.numberPaginationBox}>
            <Text style={styles.numberPaginationText}>
              {`${currentIndex + 1}/${images.length}`}
            </Text>
          </View>
        </View>
      )}

      {showDotPagination && (
        <View style={styles.dotsContainer}>
          <AnimatedDotsCarousel
            length={images.length}
            currentIndex={currentIndex}
            maxIndicators={4}
            interpolateOpacityAndColor
            activeIndicatorConfig={{
              color: Colors.light.background,
              margin: 3,
              opacity: 1,
              size: 8,
            }}
            inactiveIndicatorConfig={{
              color: '#C4C4C4',
              margin: 3,
              opacity: 0.5,
              size: 8,
            }}
            decreasingDots={[
              {
                config: { color: '#C4C4C4', margin: 3, opacity: 0.5, size: 6 },
                quantity: 1,
              },
              {
                config: { color: '#C4C4C4', margin: 3, opacity: 0.5, size: 4 },
                quantity: 1,
              },
            ]}
            scrollableDotsConfig={{
              setIndex: setCurrentIndex,
              onNewIndex: (newIndex) => {
                setCurrentIndex(newIndex);
              },
              containerBackgroundColor: 'rgba(0, 0, 0, 0.1)',
              container: {
                paddingHorizontal: 15,
                paddingVertical: 8,
                borderRadius: 20,
              },
            }}
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 250,
    width: '100%',
    backgroundColor: '#f0f0f0',
  },
  scrollView: {
    flexDirection: 'row',
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  imageContainer: {
    height: '100%',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  loaderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  dotsContainer: {
    position: 'absolute',
    bottom: 20,
    width: '100%',
    alignItems: 'center',
  },
  numberPaginationContainer: {
    position: 'absolute',
    bottom: 20,
    width: '100%',
    alignItems: 'flex-end',
    paddingRight: '4%',
  },
  numberPaginationBox: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  numberPaginationText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
});