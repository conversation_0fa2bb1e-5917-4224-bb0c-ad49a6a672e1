import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Image, FlatList, TouchableOpacity, ScrollView, Pressable, TextInput } from 'react-native';
import { Chip, ActivityIndicator } from 'react-native-paper';
import { BottomSheetModal, BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { Colors, Typography, Spacing, Icons, Borders } from '@/constants/DesignSystem';
import { Search, Orbit, Delete, MoreVertical } from '@/components/icons';
import { UPDATE_CIRCLE_BY_ID, DELETE_EVENT_GROUP_INVITATION } from '@/app/(home)/CirclesDashboard/CircleDetails.data';
import { useMutation } from '@apollo/client';
import { SEND_CIRCLE_INVITATION } from '@/components/user-circles/user-circles.data';
import { useToast } from '@/components/Toast';

export interface CircleMember {
    id: string;
    email?: string;
    firstName: string;
    lastName: string;
    phone?: string;
    phoneNumbers?: any[];
    profilePicture?: string;
    role?: 'ORGANIZER' | 'CO-ORGANIZER' | 'MEMBER';
    status?: 'ACCEPTED' | 'PENDING' | 'REJECTED';
    invitationId?: string;
}

interface Organizer {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    phone?: string;
}

export interface CircleState {
    name: string;
    id: string;
    description: string;
    members: CircleMember[];
    createdBy: {
        id?: string;
        firstName?: string;
        lastName?: string | null;
        phone?: string;
        profilePicture?: string | null;
    };
    createdAt: string;
    membersCount: number;
    imageUrl: string;
    userRole: string;
    organizers: Organizer[];
}

type FilterStatus = 'ACCEPTED' | 'PENDING' | 'REJECTED';

const getInitials = (name: string) => {
    if (!name) return '';
    const nameParts = name.trim().split(' ');
    if (nameParts.length > 1) {
        return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
    }
    return `${nameParts[0][0]}`.toUpperCase();
};

const memberFilters = [
    { label: 'Accepted', type: 'ACCEPTED' },
    { label: 'Pending', type: 'PENDING' },
    { label: 'Denied', type: 'REJECTED' },
];

interface MembersTabProps {
    circle: CircleState;
    refetch: () => void;
    membersToDisplay: CircleMember[];
    filterCounts: Record<FilterStatus, number>;
    isFilterLoading: boolean;
    selectedFilter: FilterStatus;
    setSelectedFilter: (status: FilterStatus) => void;
    searchQuery: string;
    setSearchQuery: (query: string) => void;
    isSearchVisible: boolean;
    setIsSearchVisible: (visible: boolean) => void;
    refetchFilteredMembers: () => void;
    refetchCounts: () => void;
}

const MembersTab = ({
    circle,
    refetch,
    membersToDisplay,
    filterCounts,
    isFilterLoading,
    selectedFilter,
    setSelectedFilter,
    searchQuery,
    setSearchQuery,
    isSearchVisible,
    setIsSearchVisible,
    refetchFilteredMembers,
    refetchCounts,
}: MembersTabProps) => {
    const bottomSheetRef = useRef<BottomSheetModal>(null);
    const [selectedMember, setSelectedMember] = useState<CircleMember | null>(null);
    const [updateCircle] = useMutation(UPDATE_CIRCLE_BY_ID);
    const [deleteEventGroupInvitation] = useMutation(DELETE_EVENT_GROUP_INVITATION);
    const [sendCircleInvitation] = useMutation(SEND_CIRCLE_INVITATION);
    const toast = useToast();

    useEffect(() => {
        if (selectedMember) {
            bottomSheetRef.current?.present();
        }
    }, [selectedMember]);

    const handleUpdateMemberRole = async () => {
        if (!selectedMember) return;
        bottomSheetRef.current?.dismiss();

        const isMakingCoOrganizer = selectedMember.role === 'MEMBER';

        const currentOrganizers = circle.organizers;

        let newOrganizersList;
        if (isMakingCoOrganizer) {
            newOrganizersList = [...currentOrganizers, selectedMember];
        } else {
            newOrganizersList = currentOrganizers.filter(org => org.id !== selectedMember.id);
        }

        const updatedOrganizers = newOrganizersList.map(
            ({ firstName, lastName, email, phone }) => ({
                firstName: firstName || '',
                lastName: lastName || '',
                email: email ?? null,
                phone: phone ?? null,
            })
        );

        try {
            const result = await updateCircle({
                variables: {
                    updateEventGroupId: circle.id,
                    input: {
                        organizers: updatedOrganizers,
                    },
                },
                onCompleted: () => {
                    toast.success('Role updated successfully');
                    refetch();
                    refetchFilteredMembers();
                    refetchCounts();
                },
                onError: (error) => {
                    console.error('Failed to update member role:', error);
                    toast.error('Failed to update member role');
                },
            });

            if (result.data?.updateEventGroup.errors) {
                toast.error(result.data.updateEventGroup.errors[0].message);
            }

        } catch (error) {
            console.error("Failed to update member role:", error);
            toast.error('Failed to update member role');
        }
    };

    const handleResendInvite = async () => {
        if (!selectedMember) return;

        try {
            await sendCircleInvitation({
                variables: {
                    input: {
                        eventGroupId: circle.id,
                        members: [{
                            email: selectedMember.email ?? null,
                            firstName: selectedMember.firstName ?? null,
                            lastName: selectedMember.lastName ?? null,
                            phone: selectedMember.phone ?? null,
                        }]
                    }
                },
                onCompleted: (data) => {
                    if (data.sendEventGroupInvitation.status === 'SUCCESS') {
                        toast.success('Invitation resent successfully');
                    } else {
                        toast.error(data.sendEventGroupInvitation.message || 'Failed to resend invitation');
                    }
                    bottomSheetRef.current?.dismiss();
                },
                onError: () => {
                    toast.error('Failed to resend invitation');
                    bottomSheetRef.current?.dismiss();
                }
            });
        } catch (error) {
            console.error("Failed to resend invitation:", error);
            toast.error('An unexpected error occurred.');
        }
    };

    const handleRemoveMember = async () => {
        if (!selectedMember) return;
        bottomSheetRef.current?.dismiss();

        const invitationId = selectedMember.invitationId;

        if (!invitationId) {
            toast.error('Cannot remove member: Invitation ID not found.');
            return;
        }

        try {
            await deleteEventGroupInvitation({
                variables: {
                    deleteEventGroupInvitationId: invitationId,
                },
                onCompleted: () => {
                    refetchFilteredMembers();
                    refetchCounts();
                },
                onError: (error) => {
                    console.error("Failed to remove member:", error);
                    toast.error('Failed to remove member');
                }
            });
        } catch (error) {
            console.error("Failed to remove member:", error);
        }
    };

    const handlePressMember = (item: CircleMember) => {
        setSelectedMember(item);
    };

    const renderItem = ({ item }: { item: CircleMember }) => (
        <View style={styles.memberRow}>
            <View style={styles.profilePicContainer}>
                {item.profilePicture ? (<Image
                    source={{ uri: item.profilePicture }}
                    style={styles.profilePic}
                />) : (
                    <Text style={styles.monogram}>
                        {getInitials(item.firstName + (item.lastName ? ` ${item.lastName}`: ''))}
                    </Text>
                )}
            </View>
            <View style={styles.infoContainer}>
                <Text style={styles.name}>
                    {item.firstName + (item.lastName ? ` ${item.lastName}` : '')}
                </Text>
                <Text style={styles.role}>{(item.role === 'ORGANIZER' || item.role === 'CO-ORGANIZER') && item.role.charAt(0).toUpperCase() + item.role.slice(1).toLowerCase()}</Text>
            </View>
            {item.role !== 'ORGANIZER' && (
                <TouchableOpacity style={styles.iconContainer} hitSlop={10} onPress={() => handlePressMember(item)}>
                    <MoreVertical size={Icons.size.md} color={Colors.black} />
                </TouchableOpacity>
            )}
        </View>
    );

    return (
        <View style={{ flex: 1, paddingTop: Spacing.lg }}>
            <View style={styles.filtersRow}>
                <Chip
                    style={[
                        styles.searchChip,
                        {
                            borderColor: isSearchVisible ? Colors.primary : Colors.buttonBorder.medium,
                            backgroundColor: isSearchVisible ? Colors.background.secondary : Colors.background.transparent,
                        }
                    ]}
                    onPress={() => {
                        setIsSearchVisible(!isSearchVisible);
                        if (isSearchVisible) {
                            setSearchQuery('');
                        }
                    }}
                >
                    <Search size={Typography.fontSize.md} color={isSearchVisible ? Colors.text.primary : Colors.text.secondary} />
                </Chip>
                {isSearchVisible ? (
                    <TextInput
                        style={styles.searchInput}
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        placeholder="Search members..."
                        placeholderTextColor={Colors.text.secondary}
                        autoFocus
                    />
                ) : (
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        style={{ flex: 1 }}
                    >
                        {memberFilters.map((filter, index) => {
                            const isSelected = selectedFilter === filter.type;
                            return (
                                <Chip
                                    key={index}
                                    mode="outlined"
                                    selected={isSelected}
                                    showSelectedCheck={false}
                                    style={[
                                        styles.chip,
                                        {
                                            borderColor: isSelected ? Colors.primary : Colors.buttonBorder.medium,
                                            backgroundColor: isSelected ? Colors.background.secondary : Colors.background.transparent,
                                        },
                                    ]}
                                    textStyle={[styles.chipLabel]}
                                    onPress={() => setSelectedFilter(filter.type as FilterStatus)}
                                >
                                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', gap: 4 }}>
                                        <Text style={{ color: isSelected ? Colors.text.primary : Colors.text.secondary, fontWeight: isSelected ? Typography.fontWeight.semibold : Typography.fontWeight.regular }}>
                                            {filter.label}
                                        </Text>
                                        <Text style={{ color: isSelected ? Colors.text.primary : Colors.text.secondary, fontWeight: isSelected ? Typography.fontWeight.semibold : Typography.fontWeight.regular }}>
                                            {filterCounts[filter.type as keyof typeof filterCounts]}
                                        </Text>
                                    </View>
                                </Chip>
                            );
                        })}
                    </ScrollView>
                )}
            </View>
            {isFilterLoading ? (
                <ActivityIndicator style={{ marginTop: 20 }} />
            ) : membersToDisplay.length > 0 ? (
                <FlatList
                    data={membersToDisplay}
                    keyExtractor={(item, index) => item.id + index.toString()}
                    renderItem={renderItem}
                    contentContainerStyle={{ padding: Spacing.lg, flexGrow: 1 }}
                    showsVerticalScrollIndicator={false}
                />
            ) : (
                <View style={{ padding: Spacing.lg, flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                    <Text style={{
                        textAlign: 'center',
                        color: Colors.text.tertiary,
                        fontSize: Typography.fontSize.md,
                        fontFamily: Typography.fontFamily.primary
                    }}>
                        {'No members found for this filter.'}
                    </Text>
                </View>
            )}
            {selectedMember && (
                 <BottomSheetModal
                    ref={bottomSheetRef}
                    index={0}
                    snapPoints={['20%']}
                    enablePanDownToClose
                    onDismiss={() => setSelectedMember(null)}
                    backdropComponent={(props) => (
                        <BottomSheetBackdrop {...props} appearsOnIndex={0} disappearsOnIndex={-1} />
                    )}
                >
                    <BottomSheetView style={{ backgroundColor: Colors.background.primary, flex: 1 }}>
                        <View style={styles.bottomSheetContainer}>
                            {selectedMember.status === 'ACCEPTED' ? (
                                <>
                                    <Pressable style={styles.row} onPress={handleUpdateMemberRole}>
                                        <Orbit color={Colors.text.secondary} size={Icons.size.xl} />
                                        <Text style={styles.editCircleText}>{selectedMember.role === 'MEMBER' ? 'Make Co-organizer' : 'Make Member'}</Text>
                                    </Pressable>
                                    <Pressable style={styles.row} onPress={handleRemoveMember}>
                                        <Delete color={Colors.error} size={Icons.size.xl} />
                                        <Text style={styles.deleteCircleText}>{selectedMember.role === 'MEMBER' ? 'Remove Member' : 'Remove Co-organizer'}</Text>
                                    </Pressable>
                                </>
                            ) : (
                                <>
                                    <Pressable style={styles.row} onPress={handleResendInvite}>
                                        <Orbit color={Colors.text.secondary} size={Icons.size.xl} />
                                        <Text style={styles.editCircleText}>Resend Invite</Text>
                                    </Pressable>
                                    <Pressable style={styles.row} onPress={handleRemoveMember}>
                                        <Delete color={Colors.error} size={Icons.size.xl} />
                                        <Text style={styles.deleteCircleText}>Delete Invitation</Text>
                                    </Pressable>
                                </>
                            )}
                        </View>
                    </BottomSheetView>
                </BottomSheetModal>
            )}
        </View>
    );
};

export default MembersTab;

const styles = StyleSheet.create({
    filtersRow: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: Spacing.lg,
        marginVertical: Spacing.sm,
        marginBottom: Spacing.md - 2,
        height: 32,
    },
    searchInput: {
        flex: 1,
        backgroundColor: Colors.background.tertiary,
        borderRadius: Borders.radius.md,
        paddingHorizontal: Spacing.md,
        fontSize: Typography.fontSize.md,
        fontFamily: Typography.fontFamily.primary,
        color: Colors.text.tertiary,
        borderWidth: 1,
        borderColor: Colors.border.light,
        marginLeft: Spacing.sm,
        height: 40,
    },
    contentContainer: {
        paddingHorizontal: Spacing.lg
    },
    chip: {
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Colors.white,
        borderWidth: Borders.width.thin,
        borderColor: Colors.buttonBorder.medium,
        borderRadius: Borders.radius.circle,
        marginRight: Spacing.sm,
        height: 32,
    },
    chipLabel: {
        color: Colors.text.secondary,
        textAlign: 'center',
        fontFamily: Typography.fontFamily.primary,
        fontSize: Typography.fontSize.sm
    },
    searchChip: {
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Colors.white,
        borderWidth: Borders.width.thin,
        borderColor: Colors.buttonBorder.medium,
        borderRadius: Borders.radius.circle,
        marginRight: Spacing.sm,
        height: 32,
    },
    memberRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: Spacing.lg
    },
    profilePicContainer: {
        width: 54,
        height: 54,
        borderRadius: 27,
        overflow: 'hidden',
        marginRight: Spacing.md,
        backgroundColor: Colors.background.tertiary,
        justifyContent: 'center',
        alignItems: 'center'
    },
    profilePic: {
        width: 54,
        height: 54,
    },
    infoContainer: {
        flex: 1,
        justifyContent: 'center',
    },
    name: {
        fontWeight: Typography.fontWeight.medium,
        fontSize: Typography.fontSize.md,
    },
    role: {
        fontWeight: Typography.fontWeight.medium,
        color: Colors.border.light,
        fontSize: Typography.fontSize.md,
    },
    iconContainer: {
        paddingHorizontal: Spacing.sm,
    },
    monogram: {
        color: Colors.text.secondary,
        fontWeight: Typography.fontWeight.bold,
        fontSize: Typography.fontSize.md,
        fontFamily: Typography.fontFamily.primary,
    },
    bottomSheetContainer: {
        padding: Spacing.md,
        gap: Spacing.lg
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        gap: Spacing.md,
        paddingHorizontal: Spacing.md
    },
    editCircleText: {
        fontFamily: Typography.fontFamily.primary,
        fontSize: Typography.fontSize.lg,
        color: Colors.text.tertiary,
        fontWeight: Typography.fontWeight.semibold,
    },
    deleteCircleText: {
        fontFamily: Typography.fontFamily.primary,
        fontSize: Typography.fontSize.lg,
        color: Colors.error,
        fontWeight: Typography.fontWeight.semibold,
    },
}); 