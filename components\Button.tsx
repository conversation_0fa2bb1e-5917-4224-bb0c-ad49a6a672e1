import React, { ReactNode } from 'react';
import { StyleSheet, TouchableOpacity, ViewStyle, TextStyle, View, useWindowDimensions } from 'react-native';
import { Text } from 'react-native-paper';
import { Colors } from '@/constants/Colors';

interface ButtonProps {
  title: string;
  onPress: () => void;
  mode?: 'contained' | 'outlined';
  containerStyle?: ViewStyle;
  buttonStyle?: ViewStyle;
  textStyle?: TextStyle;
  rightIcon?: ReactNode;
  leftIcon?: ReactNode;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large' | 'full';
}

export function Button({ 
  title, 
  onPress, 
  mode = 'contained',
  containerStyle,
  buttonStyle,
  textStyle,
  rightIcon,
  leftIcon,
  disabled = false,
  size = 'medium'
}: ButtonProps) {
  const { width: screenWidth } = useWindowDimensions();

  const getTextStyles = () => {
    const textStyles = [
      styles.text,
      mode === 'outlined' ? styles.outlinedText : null,
      leftIcon ? styles.textWithLeftIcon : null,
      rightIcon ? styles.textWithRightIcon : null,
      !leftIcon && !rightIcon ? styles.centeredText : null,
      getResponsiveTextSize(screenWidth),
      textStyle
    ].filter(Boolean);

    return textStyles;
  };

  const getButtonStyles = () => {
    const buttonStyles = [
      styles.button,
      mode === 'outlined' && styles.outlinedButton,
      !leftIcon && !rightIcon && styles.centeredButton,
      getResponsiveButtonSize(size, screenWidth),
      buttonStyle
    ].filter(Boolean);

    return buttonStyles;
  };

  const getResponsiveButtonSize = (size: string, screenWidth: number): ViewStyle => {
    const baseStyles: ViewStyle = {
      paddingVertical: getResponsivePadding(screenWidth),
      paddingHorizontal: getResponsivePadding(screenWidth) * 2,
    };

    switch (size) {
      case 'small':
        return {
          ...baseStyles,
          width: screenWidth * 0.3,
        };
      case 'medium':
        return {
          ...baseStyles,
          width: screenWidth * 0.45,
        };
      case 'large':
        return {
          ...baseStyles,
          width: screenWidth * 0.7,
        };
      case 'full':
        return {
          ...baseStyles,
          width: screenWidth * 0.9,
        };
      default:
        return baseStyles;
    }
  };

  const getResponsiveTextSize = (screenWidth: number): TextStyle => {
    return {
      fontSize: Math.max(14, Math.min(16, screenWidth * 0.04)),
    };
  };

  const getResponsivePadding = (screenWidth: number): number => {
    return Math.max(8, Math.min(12, screenWidth * 0.03));
  };

  return (
    <TouchableOpacity 
      style={[
        styles.container,
        containerStyle
      ]} 
      onPress={onPress}
      disabled={disabled}
    >
      <View style={getButtonStyles()}>
        {leftIcon && <View style={styles.iconContainer}>{leftIcon}</View>}
        <Text style={getTextStyles()}>
          {title}
        </Text>
        {rightIcon && <View style={styles.iconContainer}>{rightIcon}</View>}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.buttonBackground,
    borderRadius: 12,
  },
  centeredButton: {
    justifyContent: 'center',
  },
  outlinedButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.light.buttonBackground,
  },
  text: {
    color: Colors.light.background,
    fontWeight: '600',
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  centeredText: {
    flex: 1,
  },
  outlinedText: {
    color: Colors.light.text,
  },
  textWithLeftIcon: {
    marginLeft: 8,
  },
  textWithRightIcon: {
    marginRight: 8,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});