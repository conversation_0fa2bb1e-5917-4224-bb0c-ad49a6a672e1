import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, Image, SafeAreaView, TouchableOpacity, Platform, ScrollView, ScrollView as RNScrollView, Alert, Linking } from 'react-native';
import { RouteProp, useRoute, useNavigation, useFocusEffect } from '@react-navigation/native';
import { PartyDetailsRootList } from './Navigation/PartyDetailsRootList';
import { StackNavigationProp } from '@react-navigation/stack';
import { Colors, Typography, Spacing, Borders } from '@/constants/DesignSystem';
import { Cross, Location, ColorPallete } from '@/components/icons';
import { Shadows } from '@/constants/DesignSystem';
import { FlyerQRCode } from './flyer/FlyerQRCode';
import Constants from 'expo-constants';
import ViewShot from 'react-native-view-shot';
import * as MediaLibrary from 'expo-media-library';
import { Snackbar } from 'react-native-paper';

const availableColors = [
    Colors.black,
    Colors.gradient.orange, Colors.success, Colors.gradient.blue, Colors.gradient.purple, Colors.gradient.darkPurple, Colors.gradient.magenta,
    Colors.gradient.red, Colors.gradient.yellow, Colors.warning,
];

const DEFAULT_IMAGE = require('../../assets/images/flyer/newpartyimage.png');
const LOGO_IMAGE = require('../../assets/images/flyer/flyer-logo.png');

type FlyerScreenRouteProp = RouteProp<PartyDetailsRootList, 'FlyerScreen'>;
type FlyerScreenNavigationProp = StackNavigationProp<PartyDetailsRootList, 'FlyerScreen'>;

const FlyerScreen: React.FC = () => {
  const route = useRoute<FlyerScreenRouteProp>();
  const navigation = useNavigation<FlyerScreenNavigationProp>();
  const { image, date, venue, name, address } = route.params;
  const viewShotRef = useRef<ViewShot>(null);
  
  const [flyerColor, setFlyerColor] = useState(Colors.black);
  const [showPalette, setShowPalette] = useState(false);
  const [isCapturing, setIsCapturing] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  useFocusEffect(
    React.useCallback(() => {
      const parent = navigation.getParent();
      parent?.setOptions({
        tabBarStyle: { display: 'none' },
      });

      return () => {
        parent?.setOptions({
          tabBarStyle: { display: 'flex' },
        });
      };
    }, [navigation])
  );

  const partyDate = date ? new Date(date) : null;
  const flyerImageSource = image ? { uri: image } : DEFAULT_IMAGE;

  const generateRsvpUrl = () => {
    const baseUrl = Constants.expoConfig?.extra?.apiUrl || 'https://main.d1aqtkmxs38r8v.amplifyapp.com';
    const partyId = route.params.partyId;
    return `${baseUrl}/rsvp/${partyId}`;
  };

  const rsvpUrl = generateRsvpUrl();

  const handleCapture = async () => {
    if (!viewShotRef.current?.capture) return;
    try {
      setIsCapturing(true);
      
      const { status: currentStatus } = await MediaLibrary.getPermissionsAsync();
      
      if (currentStatus === 'denied') {
        Alert.alert(
          'Permission Required',
          'Please enable photo library access in your device settings to save the flyer.',
          [
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: () => setIsCapturing(false)
            },
            {
              text: 'Open Settings',
              onPress: async () => {
                setIsCapturing(false);
                await Linking.openSettings();
              }
            }
          ]
        );
        return;
      }

      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== 'granted') {
        setSnackbarMessage('Permission denied. Please allow access to save the flyer.');
        setSnackbarVisible(true);
        setIsCapturing(false);
        return;
      }

      const uri = await viewShotRef.current.capture();
      await MediaLibrary.saveToLibraryAsync(uri);
      
      setSnackbarMessage('Flyer saved to your photos');
      setSnackbarVisible(true);
      
    } catch (error) {
      const message = typeof error === 'object' && error && 'message' in error ? (error as any).message : '';
      if (message && /permission|grant/i.test(message)) {
        Alert.alert(
          'Permission Required',
          'Please enable photo library access in your device settings to save the flyer.',
          [
            {
              text: 'Cancel',
              style: 'cancel'
            },
            {
              text: 'Open Settings',
              onPress: async () => {
                await Linking.openSettings();
              }
            }
          ]
        );
      } else {
        setSnackbarMessage('Failed to save flyer');
        setSnackbarVisible(true);
        console.error('Error capturing flyer:', error);
      }
    } finally {
      setIsCapturing(false);
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: Colors.black }}>
        <ScrollView style={{backgroundColor: Colors.black}} contentContainerStyle={styles.scrollContainer}>
            <ViewShot
            ref={viewShotRef}
            options={{ format: 'png', quality: 1 }}
            style={styles.flyerContainer}
            >
                <Text style={[styles.headerText, { color: flyerColor }]}>YOU ARE INVITED</Text>

                <View style={styles.mainContent}>
                    <Image source={flyerImageSource} style={styles.flyerImage} resizeMode="cover" />

                    <View style={styles.rightColumn}>
                        <View style={[styles.coloredBox, { backgroundColor: flyerColor }]}>
                            <View style={styles.qrContainer}>
                                <View style={styles.qrInnerBox}>
                                    <FlyerQRCode value={rsvpUrl} size={130} />
                                </View>
                            </View>
                            <Text style={styles.scanToRsvpText}>Scan to RSVP</Text>
                        </View>
                        {partyDate && (
                            <View style={[styles.coloredBox, { backgroundColor: flyerColor }]}>
                                <Text style={styles.dateMonth}>{partyDate.toLocaleString('default', { month: 'long' }).toUpperCase()}</Text>
                                <Text style={styles.dateDay}>{partyDate.getDate()}</Text>
                                <Text style={styles.dateWeekday}>{partyDate.toLocaleString('default', { weekday: 'long' }).toUpperCase()}</Text>
                                <View style={styles.dateSeparator} />
                                <Text style={styles.dateStarts}>STARTS FROM {partyDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</Text>
                            </View>
                        )}
                    </View>
                </View>

                <Text style={[styles.partyName, { color: flyerColor }]}>{(name || "Get Together").toUpperCase()}</Text>
                <View style={styles.venueContainer}>
                    <View style={styles.mapPinContainer}>
                        <Location size={24} color={flyerColor} variant="filled" />
                    </View>
                    <View style={styles.venueTextContainer}>
                        <Text style={[styles.venueName, { color: flyerColor }]}>{venue || "Venue TBD"}</Text>
                        <Text style={styles.venueAddress}>{address || "Stay tuned! The party spot is yet to be revealed."}</Text>
                    </View>
                </View>

                <View style={styles.logoContainer}>
                    <Image source={LOGO_IMAGE} style={styles.logoImg} resizeMode="contain" />
                </View>
            </ViewShot>
        </ScrollView>

      <View style={styles.bottomActions}>
        <View style={styles.bottomBar}>
            {showPalette ? (
              <View style={styles.palettePill}>
                <RNScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.paletteScrollContent}>
                  <TouchableOpacity
                    style={[styles.colorCircle, styles.closeCircle]}
                    onPress={() => setShowPalette(false)}
                    accessibilityLabel="Close color palette"
                  >
                    <Cross size={18} color={Colors.text.secondary} />
                  </TouchableOpacity>
                  {availableColors.map((color) => (
                    <TouchableOpacity
                      key={color}
                      style={[styles.colorCircle, { backgroundColor: color }]}
                      onPress={() => {
                        setFlyerColor(color);
                        setShowPalette(false);
                      }}
                      accessibilityLabel={`Select color ${color}`}
                    />
                  ))}
                </RNScrollView>
              </View>
            ) : (
                <TouchableOpacity style={styles.editButton} onPress={() => setShowPalette(true)}>
                  <View style={{ marginRight: 8 }}>
                    <ColorPallete size={22} color={Colors.text.secondary} />
                  </View>
                  <Text style={styles.editButtonText}>Edit Colors</Text>
                </TouchableOpacity>
            )}
        </View>
        <TouchableOpacity 
          style={[styles.downloadButton, isCapturing && styles.downloadButtonDisabled]} 
          onPress={handleCapture}
          disabled={isCapturing}
        >
          <Text style={[styles.downloadButtonText, isCapturing && styles.downloadButtonTextDisabled]}>
            {isCapturing ? 'Saving...' : 'Download Flyer'}
          </Text>
        </TouchableOpacity>
      </View>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    paddingTop: Spacing.xxl,
    paddingBottom: Spacing.lg,
    paddingHorizontal: Spacing.md,
  },
  flyerContainer: {
    borderRadius: Borders.radius.lg,
    padding: Spacing.md,
    ...Shadows.lg,
  },
  headerText: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    letterSpacing: 1,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  mainContent: {
    flexDirection: 'row',
    gap: Spacing.sm,
    marginBottom: Spacing.md,
  },
  flyerImage: {
    flex: 7,
    height: '100%',
    borderRadius: Borders.radius.md,
    backgroundColor: Colors.background.secondary,
  },
  rightColumn: {
    flex: 5,
    gap: Spacing.sm,
    justifyContent: 'space-between',
  },
  coloredBox: {
    borderRadius: Borders.radius.lg,
    padding: Spacing.sm,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  qrContainer: {
    width: '100%',
    aspectRatio: 1,
    backgroundColor: Colors.white,
    marginBottom: Spacing.xs,
    padding: Spacing.xs,
    borderRadius: Borders.radius.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  qrInnerBox: {
    width: '100%',
    height: '100%',
    borderWidth: 2,
    borderColor: Colors.border.medium,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  scanToRsvpText: {
    color: Colors.white,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
    textAlign: 'center',
  },
  dateMonth: {
    color: Colors.white,
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    letterSpacing: 2,
  },
  dateDay: {
    color: Colors.white,
    fontSize: 60,
    fontWeight: 'bold',
    lineHeight: 60,
  },
  dateWeekday: {
    color: Colors.white,
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.bold,
    letterSpacing: 2,
  },
  dateSeparator: {
    width: '75%',
    height: 1,
    backgroundColor: Colors.white,
    marginVertical: Spacing.sm,
  },
  dateStarts: {
    color: Colors.white,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
    letterSpacing: 1,
  },
  partyName: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  venueContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Spacing.md,
  },
  mapPinContainer: {
    width: 24,
    height: 24,
    marginRight: Spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  venueTextContainer: {
    flex: 1,
  },
  venueName: {
    fontWeight: 'bold',
    fontSize: Typography.fontSize.sm,
  },
  venueAddress: {
    color: Colors.text.secondary,
    fontSize: Typography.fontSize.sm,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: Spacing.md,
  },
  logoImg: {
    height: 40,
    width: 150,
  },
  bottomActions: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: Spacing.lg,
    paddingBottom: Platform.OS === 'ios' ? Spacing.xl : Spacing.lg,
    backgroundColor: Colors.black,
  },
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Spacing.md,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderRadius: 999,
    paddingHorizontal: 18,
    paddingVertical: 8,
    borderWidth: 2,
    borderColor: Colors.border.light,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
  editButtonText: {
    color: Colors.text.secondary,
    fontWeight: Typography.fontWeight.bold,
    fontSize: Typography.fontSize.md,
  },
  palettePill: {
    backgroundColor: Colors.white,
    borderRadius: 999,
    paddingVertical: 6,
    paddingHorizontal: 6,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
    marginBottom: 8,
    maxWidth: '100%',
    overflow: 'hidden',
    alignSelf: 'center',
  },
  paletteScrollContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  colorCircle: {
    width: 28,
    height: 28,
    borderRadius: 14,
    marginHorizontal: 3,
    borderWidth: 1.5,
    borderColor: Colors.border.light,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeCircle: {
    backgroundColor: Colors.white,
    borderColor: Colors.border.light,
    marginRight: 6,
  },
  downloadButton: {
    borderWidth: 1,
    borderColor: Colors.border.light,
    borderRadius: Borders.radius.md,
    padding: Spacing.md,
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  downloadButtonDisabled: {
    opacity: 0.7,
  },
  downloadButtonText: {
    color: Colors.white,
    fontWeight: Typography.fontWeight.bold,
    fontSize: Typography.fontSize.md,
    fontFamily: Typography.fontFamily.primary,
  },
  downloadButtonTextDisabled: {
    color: Colors.text.secondary,
  },
  snackbar: {
    backgroundColor: Colors.primary,
    position: 'absolute',
    bottom: 10,
    left: 0,
    right: 0,
  },
});

export default FlyerScreen; 