import { Assignee } from "@/components/Task/SharedInterfaces";
import { parseISO } from 'date-fns';

export function formatDateString(dateString: string, formatString: string): string {
  try {
    const date = parseISO(dateString);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date');
    }
    return format(date, formatString);
  } catch (error) {
    console.error('Error formatting date string:', error);
    return '--';
  }
}

export function getFormattedDate(utcDateString: string): string {
  try {
    const date = new Date(utcDateString);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date');
    }

    const day = date.getDate().toString().padStart(2, '0');
    const month = date.toLocaleString('default', { month: 'short' });
    const year = date.getFullYear();
    return `${month} ${day}, ${year}`;
  } catch (error) {
    console.error('Error formatting date:', error);
    return '--';
  }
}

export const getCurrentDateTime = () => {
  const now = new Date();
  now.setSeconds(0, 0);
  return now;
};

export function getDayFromDate(utcDateString: string): string {
  try {
    const date = new Date(utcDateString);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date');
    }
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thur', 'Fri', 'Sat'];
    return days[date.getUTCDay()];
  } catch (error) {
    console.error('Error getting day from date:', error);
    return '--';
  }
}

function getDaySuffix(day: number): string {
  if (!Number.isInteger(day) || day < 1 || day > 31) {
    return '';
  }

  if (day >= 11 && day <= 13) {
    return 'th';
  }

  switch (day % 10) {
    case 1: return 'st';
    case 2: return 'nd';
    case 3: return 'rd';
    default: return 'th';
  }
}

export const regexValidators = {
  numbersOnly: (value: string): string => { // Removes all non-digit characters from a string
    return value.replace(/\D/g, '');
  },
  isNumbersOnly: (value: string): boolean => { // Validates if string contains only numbers
    return /^\d+$/.test(value);
  },
  sanitizeNumberInput: (value: string, maxLength: number): string => { // Removes all non-digit characters and limits length
    return value.replace(/\D/g, '').slice(0, maxLength);
  },
  isValidEmail: (email: string): boolean => { // Validates email format
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  },
  isValidPhone: (phone: string): boolean => { // Validates phone number format (basic)
    return /^[\d\s+()-]{10,}$/.test(phone);
  },
  isValidZipCode: (zip: string): boolean => { // Validates zip code format
    return /^[0-9]{5}(?:-[0-9]{4})?$/.test(zip);
  }
} as const;

export const numberConstraints = {
  sanitizeNumberWithinRange: (value: string, min: number, max: number): string => {
    const numericValue = value.replace(/\D/g, '');// Remove non-numeric characters
    const numberValue = Number(numericValue); // Convert to number for comparison
    if (!numericValue || numberValue < min) return ''; // If empty string or less than min, return empty string
    if (numberValue > max) {
      return numericValue.slice(0, -1); // Remove the last digit if it exceeds max
    }
    return numericValue;
  }
} as const;

export const textConstraints = {
  sanitizeTextInput: (value: string, maxLength: number): string => {
    return value.slice(0, maxLength);
  }
};
export function combineDateAndTime(date: Date, time: Date): Date {
  return new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    time.getHours(),
    time.getMinutes(),
    time.getSeconds()
  );
}

export function getFormattedTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date');
    }
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  } catch (error) {
    console.error('Error formatting time:', error);
    return '--';
  }
}

export function getOrdinalSuffix(day: number): string {
  if (day > 3 && day < 21) return 'th';
  switch (day % 10) {
    case 1: return 'st';
    case 2: return 'nd';
    case 3: return 'rd';
    default: return 'th';
  }
}

export function formatEventDate(startDate: string | null, endDate: string | null): string {
  const start = new Date(startDate!);
  const end = new Date(endDate!);

  const startDay = start.getDate();
  const startMonth = start.toLocaleString('default', { month: 'short' });
  const startYear = start.getFullYear();
  const endDay = end.getDate();
  const endMonth = end.toLocaleString('default', { month: 'short' });
  const endYear = end.getFullYear();

  if (startYear !== endYear) {
    return `${startDay}${getOrdinalSuffix(startDay)} ${startMonth} ${startYear} - ${endDay}${getOrdinalSuffix(endDay)} ${endMonth} ${endYear}`;
  }

  return `${startDay}${getOrdinalSuffix(startDay)} ${startMonth} - ${endDay}${getOrdinalSuffix(endDay)} ${endMonth}`;
}

export function getEventStatus(status: string[]): string {
  if (status.includes('MAIN_HOST') || status.includes('CO_HOST')) {
    return 'Host';
  }
  return 'Guest';
}

export function isHost(status: string): boolean {
  return status === 'MAIN_HOST' || status === 'CO_HOST';
}
export function formatPartyDate(isoString: string | undefined | null): string {
  if (!isoString) {
    return '--';
  }

  try {
    const date = new Date(isoString);
    if (isNaN(date.getTime())) {
      throw new Error(`Invalid date from string: ${isoString}`);
    }

    const day = date.getDate();
    const month = date.toLocaleString('default', { month: 'short' });
    const year = date.getFullYear();
    const weekday = date.toLocaleString('default', { weekday: 'short' });

    return `${day} ${month}, ${year} ${weekday}`;
  } catch (error) {
    console.error('Error formatting party date:', error);
    return '--';
  }
}

export function formatPartyTime(isoString: string | undefined | null): string {
  if (!isoString) {
    return '--';
  }

  try {
    const date = new Date(isoString);
    if (isNaN(date.getTime())) {
      throw new Error(`Invalid date from string: ${isoString}`);
    }

    return date.toLocaleString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    }).toLowerCase();
  } catch (error) {
    console.error('Error formatting party time:', error);
    return '--';
  }
}

export const getUserMonogram = (firstName: string, lastName: string) => {
  return `${firstName?.charAt(0)}${lastName?.charAt(0)}`.toUpperCase()
}

export function formatDateAndTime(date: Date): string {
  try {
    const day = date.getDate();
    const month = date.toLocaleString('default', { month: 'short' });
    const year = date.getFullYear();
    const timeFormatter = new Intl.DateTimeFormat(undefined, {
      hour: 'numeric',
      hour12: true,
    });

    const time = timeFormatter.format(date).replace(':00', '').toLowerCase();

    return `${month} ${day}, ${year} ${time}`;
  } catch (error: any) {
    return `${error.message}`;
  }
}

function getSuperscriptOrdinalSuffix(day: number): string {
  if (day >= 11 && day <= 13) {
    return '\u1D57\u02B0'; // ᵗʰ without space
  }
  const lastDigit = day % 10;
  switch (lastDigit) {
    case 1:
      return '\u02E2\u1D57'; // ˢᵗ without space
    case 2:
      return '\u207F\u1D48'; // ⁿᵈ without space
    case 3:
      return '\u02B3\u1D48'; // ʳᵈ without space
    default:
      return '\u1D57\u02B0'; // ᵗʰ without space
  }
}


export function getAssigneeName(assignee: Assignee): string {
  const firstName = assignee.firstName || '';
  const lastName = assignee.lastName || '';
  return `${firstName} ${lastName}`;
}

export function getLongDayFromDate(utcDateString: string): string {
  try {
    const date = new Date(utcDateString);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date');
    }

    return date.toLocaleString('default', { weekday: 'long' });
  } catch (error) {
    console.error('Error getting day from date:', error);
    return '--';
  }
}

export function getSmartDateTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    const now = new Date();

    const hours = date.getHours() % 12 || 12;
    const minutes = date.getMinutes();
    const ampm = date.getHours() >= 12 ? "pm" : "am";

    const time =
      minutes === 0
        ? `${hours} ${ampm}`
        : `${hours}:${minutes.toString().padStart(2, "0")} ${ampm}`;
    
    const day = date.toLocaleString("en-US", { weekday: "short" });
    const dayOfMonth = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    const shortYear = year.toString().slice(-2);
    
    const currentYear = now.getFullYear();

    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay());
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    if (date >= startOfWeek && date <= endOfWeek) {
      return `${day}, ${time}`;
    }
    
    if (year !== currentYear) {
      return `${day} ${dayOfMonth}/${month}/${shortYear}, ${time}`;
    }
    return `${day} ${dayOfMonth}/${month}, ${time}`;

  } catch {
    return "--";
  }
}

import { format } from 'date-fns';

export function getMessageTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date');
    }
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    if (diffInMinutes <= 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
    } else if (diffInMinutes < 24 * 60) {
      const diffInHours = Math.floor(diffInMinutes / 60);
      return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
    }

    return format(date, 'MMM dd, yyyy hh:mm a');
  } catch (error) {
    console.error('Error formatting date:', error);
    return '--';
  }
}

