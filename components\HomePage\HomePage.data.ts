import gql from "graphql-tag";

export const GET_EVENTS = gql`
  query EventsResponse($filter: EventFilterInput, $pagination: PaginationInput) {
    getUserEvents(filter: $filter, pagination: $pagination) {
      ... on EventsResponse {
        pagination {
          totalItems
        }
        result {
          events {
            id
            name
            startDate
            parties {
              id
              name
              time
              muted
              invitation {
                media {
                  url
                }
              }
              serviceLocation {
                city
                state
              }
              rsvps {
                guest {
                  user {
                    id
                  }
                }
                status
                id
              }
              guests {
                user {
                  id
                }
              }
            }
            mainHost {
              userId {
                firstName
                lastName
              }
            }
          }
        }
      }
    }
  }
`;
export const GET_EVENT_COUNTS = gql`
  query EventCounts {
    upcomingEvents: getUserEvents(filter: { presentAndUpcoming: true }) {
      ... on EventsResponse {
        status
        message
        pagination {
          totalItems
        }
      }
    }
    pastEvents: getUserEvents(filter: { presentAndUpcoming: false }) {
      ... on EventsResponse {
        status
        message
        pagination {
          totalItems
        }
      }
    }
    hostEvents: getUserEvents(filter: { userType: HOST }) {
      ... on EventsResponse {
        status
        message
        pagination {
          totalItems
        }
      }
    }
    guestEvents: getUserEvents(filter: { userType: GUEST }) {
      ... on EventsResponse {
        status
        message
        pagination {
          totalItems
        }
      }
    }
  }
`; 

export const GET_INVITATIONS = gql`
  query GetUserEvents($filter: EventFilterInput, $pagination: PaginationInput) {
    getUserEvents(filter: $filter, pagination: $pagination) {
      ... on EventsResponse {
        message
        pagination {
          currentPage
          pageSize
          totalItems
          totalPages
        }
        status
        result {
          events {
            parties {
              id
              coHosts {
                id
                userId {
                  id
                  firstName
                  lastName
                }
              }
              name
              time
              invitation {
                _id
                message
                media {
                  url
                  description
                  id
                  owner {
                    id
                    firstName
                    lastName
                    role
                  }
                  title
                  uploadedAt
                }
              }
              serviceLocation {
                id
                city
                areas
                name
                state
                updatedAt
              }
              guests {
                id
                user {
                  id
                }
              }
              rsvps {
                id
                guest {
                  id
                  user {
                    id
                  }
                }
              }
            }
            id
            eventType {
              id
              name
            }
            mainHost {
              id
              userId {
                id
                firstName
                lastName
                role
              }
            }
            coHosts {
              id
              userId {
                id
                firstName
                lastName
              }
            }
            endDate
            startDate
            guests {
              id
              user {
                id
                firstName
                lastName
                role
              }
              party {
                id
                name
              }
            }
            name
            status
          }
        }
      }
    }
  }
`;

export const GET_INTERESTS = gql`
query GetInterests {
  getMdInterests {
    ... on MdInterestsResponse {
      status
      message
      result {
        mdInterests {
          id
          title
        }
      }
      pagination {
        totalItems
        totalPages
        pageSize
        currentPage
      }
    }
    ... on MdInterestErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}`;

export const GET_CIRCLES = gql`
 query GetEventGroups($pagination: PaginationInput) {
  getEventGroups(pagination: $pagination) {
    ... on EventGroupsResponse {
      status
      message
      result {
        eventGroups {
          id
          createdBy {
            id
            firstName
            lastName
          }
          name
          type
          imageUrl
        }
      }
      pagination {
        totalItems
        totalPages
        pageSize
        currentPage
      }
    }
    ... on EventGroupErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}
`;

export const DELETE_EVENT = gql`
  mutation DeleteEvent($deleteEventId: ID!) {
  deleteEvent(id: $deleteEventId) {
    ... on EventResponse {
      status
      message
      result {
        event {
          id
          name
        }
      }
    }
    ... on EventErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}
`;

export const RSVP_EVENT = gql`
  mutation UpdateInvitationRSVP($updateInvitationRsvpId: ID!, $input: InvitationRSVPInput!) {
  updateInvitationRSVP(id: $updateInvitationRsvpId, input: $input) {
    ... on InvitationRSVPResponse {
      status
      message
      result {
        invitationRSVP {
          id
          message
          status
        }
      }
    }
    ... on InvitationRSVPErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}
`;

