import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '@/constants/Colors';
import { VendorDetails } from '@/constants/displayNames';

interface VendorDescriptionProps {
    vendorDescription: string;
}

export function VendorDescription({ vendorDescription }: VendorDescriptionProps) {
    return (
        <View style={styles.content}>
            <Text style={styles.title}>{VendorDetails.ABOUT_US}</Text>
            <Text style={styles.description}>{vendorDescription}</Text>
        </View>
    );
}

const styles = StyleSheet.create({
    content: {
        marginTop: '4%',
        borderTopWidth: 1,
        borderColor: Colors.light.text,
        paddingTop: 4,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
        marginTop: 16,
    },
    description: {
        fontSize: 14,
        lineHeight: 24,
    },
});