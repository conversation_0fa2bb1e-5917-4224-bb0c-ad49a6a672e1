import React from 'react';
import { View, Modal, Platform } from 'react-native';
import { TextInput, Button } from 'react-native-paper';
import { textModalStyles } from './TextModal.styles';

interface TextModalProps {
  visible: boolean;
  value: string;
  onChangeText: (text: string) => void;
  onCancel: () => void;
  onSave: () => void;
  inputTheme: any;
  placeholder?: string;
  maxLength?: number;
  numberOfLines?: number;
  characterCount?: string;
  isDescription?: boolean;
}

export function TextModal({
  visible,
  value,
  onChangeText,
  onCancel,
  onSave,
  inputTheme,
  placeholder = "Please Enter Something...",
  maxLength = 150,
  numberOfLines = 4,
  characterCount,
  isDescription = false,
}: TextModalProps) {
  const isEmptyText = !value.trim();

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onCancel}
    >
      <View style={textModalStyles.modalContainer}>
        <View style={textModalStyles.modalContent}>
          <View style={textModalStyles.buttonContainer}>
            <Button 
              mode="text" 
              onTouchStart={onCancel}
              style={{
                ...textModalStyles.button,
                zIndex: 1000,
              }}
              labelStyle={textModalStyles.buttonText}
            >
              Cancel
            </Button>
            <Button 
              mode="text" 
              disabled={isEmptyText && !isDescription}
              onTouchStart={onSave}
              style={textModalStyles.button}
              labelStyle={[
                textModalStyles.buttonText,
                (isEmptyText && !isDescription) && { color: '#E4E4E4' }
              ]}
            >
              Save
            </Button>
          </View>
          <TextInput
            mode="flat"
            placeholder={placeholder}
            placeholderTextColor="#0A0A0B"
            value={value}
            onChangeText={onChangeText}
            multiline={true}
            numberOfLines={numberOfLines}
            maxLength={maxLength}
            autoFocus
            style={[
              textModalStyles.modalInput,
              { 
                borderBottomWidth: 0,
                paddingBottom: 8,
                color: 'black',
                ...(Platform.OS === 'ios' && {
                  selectionColor: '#0A0A0B',
                  tintColor: '#0A0A0B',
                }),
              },
            ]}
            theme={{
              ...inputTheme,
              colors: {
                ...inputTheme.colors,
                primary: 'transparent',
                background: 'white',
              },
            }}
            underlineColor="transparent"
            textAlignVertical="top"
            cursorColor={Platform.OS === 'ios' ? '#0A0A0B' : '#0A0A0B'}
            selectionColor="#0A0A0B"
          />
        </View>
      </View>
    </Modal>
  );
} 