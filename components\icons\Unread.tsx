import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Unread = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Unread-1_svg__a)">
      <Path
        fill="url(#Unread-1_svg__b)"
        fillRule="evenodd"
        d="M5.998.502c.414 0 .826.046 1.227.137a2.944 2.944 0 0 0 4.137 4.135l.002.012a5.499 5.499 0 0 1-7.658 6.21l-2.74.496a.396.396 0 0 1-.44-.528l.785-2.092A5.499 5.499 0 0 1 5.998.502m5.498 1.965a1.963 1.963 0 1 1-3.926 0 1.963 1.963 0 0 1 3.926 0"
        clipRule="evenodd"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Unread-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.826}
        y2={14.027}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Unread-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Unread_svg__a)"
    >
      <Path d="M5.315 1.046a5 5 0 0 0-3.477 7.692L1 11l2.808-.508a4.993 4.993 0 0 0 7.161-3.97" />
      <Path d="M9.077 4.846a1.923 1.923 0 1 0 0-3.847 1.923 1.923 0 0 0 0 3.847" />
    </G>
    <Defs>
      <ClipPath id="Unread_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Unread;
