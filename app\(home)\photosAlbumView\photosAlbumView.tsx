import React from 'react';
import { View, StyleSheet, TouchableOpacity, Text, FlatList, Dimensions, RefreshControl, Platform, Animated, Pressable, Share, SafeAreaView, AppState } from "react-native";
import { Stack, router } from "expo-router";
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { FastPartyActivityIndicator } from "@/components/FastPartyActivityIndicator";
import { Dialog, Portal, Button } from 'react-native-paper';
import { Colors, Typography, Spacing, Borders, Shadows, Icons } from '@/constants/DesignSystem';
import * as ImagePicker from 'expo-image-picker';
import { uploadFiles } from '../Task/Task.utils';
import { useState, useCallback, useEffect } from 'react';
import { useMutation, useQuery } from '@apollo/client';
import { CREATE_MEDIA , ADD_MEDIA_TO_MEDIA_FOLDER , GET_MEDIA_FOLDER_BY_ID, ADD_MEDIA_TO_FOVORITES, GET_FAVORITE_MEDIA, GET_ARCHIVES_MEDIA, DELETE_MEDIA_FROM_MEDIA_FOLDER, REMOVE_MEDIA_FROM_FAVORITES, RESTORE_MEDIA, PERMANENTLY_DELETE_MEDIA } from './photosAlbum.data';
import { useUserStore } from "@/app/auth/userStore";
import { Image } from 'expo-image';
import { useAttachmentPreviewStore } from "@/components/Task/Attachments/attachmentPreview.store";
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { useToast } from '@/components/Toast/useToast';
import * as MediaLibrary from 'expo-media-library';
import { Favourite, Delete, Share as ShareIcon, Download, Cross, Upload, Restore, Check, CircleCheck, MultipleUsers } from '@/components/icons';
import NoPhotos from '@/components/Illustrations/NoPhotos';

const { width } = Dimensions.get('window');
const PHOTO_SIZE = width / 3 - 2;

// Add this constant at the top of the file
const DEFAULT_BLURHASH = 'L5H2EC=PM+yV0g-mq.wG9c010J}I';

// Add new interfaces for image data
interface ImageData {
  uri: string;
  key: string;
  originalName: string;
}

interface UploadResult {
  url: string;
  key: string;
  originalName: string;
}

interface MediaItem {
  id: string;
  url: string;
  title: string;
  isFavorite?: boolean;
  owner?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  userType?: string;
}

// Add interface for media folder data
interface MediaFolderItem {
  id: string;
  url: string;
  title: string | null;
  owner?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

// Update SelectionActionBar component
function SelectionActionBar({ selectedCount, onSave, onShare, onDelete, onFavorite, onUnfavorite, onRestore, isFavoritesAlbum, isDeletedAlbum }: {
  selectedCount: number;
  onSave: () => void;
  onShare: () => void;
  onDelete: () => void;
  onFavorite: () => void;
  onUnfavorite: () => void;
  onRestore: () => void;
  isFavoritesAlbum: boolean;
  isDeletedAlbum: boolean;
}) {
  if (isDeletedAlbum) {
    return (
      <View style={styles.selectionActionBar}>
        <TouchableOpacity style={styles.actionButton} onPress={onRestore}>
          <Restore size={Icons.size.xl} color={Colors.primary} />
          <Text style={styles.actionButtonText}>Restore</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={onDelete}>
          <Delete size={Icons.size.xl} color={Colors.error} />
          <Text style={styles.actionButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.selectionActionBar}>
      {selectedCount === 1 ? (
        <TouchableOpacity style={styles.actionButton} onPress={onShare}>
          <ShareIcon size={Icons.size.xl} color={Colors.primary} />
          <Text style={styles.actionButtonText}>Share</Text>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity style={styles.actionButton} onPress={onSave}>
          <Download size={Icons.size.xl} color={Colors.primary} />
          <Text style={styles.actionButtonText}>Save</Text>
        </TouchableOpacity>
      )}
      {isFavoritesAlbum ? (
        <TouchableOpacity style={styles.actionButton} onPress={onUnfavorite}>
          <Favourite size={Icons.size.xl} variant='filled' gradientStartColor={Colors.primary} gradientEndColor={Colors.gradient.orange} />
          <Text style={styles.actionButtonText}>Unfavorite</Text>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity style={styles.actionButton} onPress={onFavorite}>
          <Favourite size={Icons.size.xl} color={Colors.primary} />
          <Text style={styles.actionButtonText}>Favorite</Text>
        </TouchableOpacity>
      )}
      <TouchableOpacity style={styles.actionButton} onPress={onDelete}>
        <Delete size={Icons.size.xl} color={Colors.error} />
        <Text style={styles.actionButtonText}>Delete</Text>
      </TouchableOpacity>
    </View>
  );
}

// Add this function before the component
const getOptimizedImageUrl = (url: string) => {
  // If the URL already contains size parameters, return as is
  if (url.includes('width=') || url.includes('height=')) {
    return url;
  }
  
  // Add size parameters to the URL
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}width=${PHOTO_SIZE * 2}&height=${PHOTO_SIZE * 2}&quality=80`;
};

export default function PhotosAlbumView() {
  const navigation = useNavigation();
  const route = useRoute<RouteProp<Record<string, { albumId?: string; albumType?: string; deleteSuccess?: string; message?: string }>, string>>();
  const { albumId, albumType, deleteSuccess, message } = route.params || {};
  const [isUploading, setIsUploading] = useState(false);
  const [selectedImageUris, setSelectedImageUris] = useState<string[]>([]);
  const [selectedPhotos, setSelectedPhotos] = useState<Set<string>>(new Set());
  const [refreshing, setRefreshing] = useState(false);
  const [showUploadSuccess, setShowUploadSuccess] = useState(false);
  const [uploadedCount, setUploadedCount] = useState(0);
  const fadeAnim = useState(new Animated.Value(0))[0];
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const { setSelectedFile, setCombinedAttachmentsForTask } = useAttachmentPreviewStore();
  const [isDeleteDialogVisible, setIsDeleteDialogVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [favoritedPhotos, setFavoritedPhotos] = useState<Set<string>>(new Set());
  const [useTitle, setUseTitle] = useState<string>("loading...");
  const [hasInitialTitle, setHasInitialTitle] = useState(false);
  const toast = useToast();
  const [isComponentMounted, setIsComponentMounted] = useState(true);

  const [createMedia] = useMutation(CREATE_MEDIA);
  const [addMediaToMediaFolder] = useMutation(ADD_MEDIA_TO_MEDIA_FOLDER);
  const [favoriteMedia] = useMutation(ADD_MEDIA_TO_FOVORITES);
  const [removeMediaFromFolder] = useMutation(DELETE_MEDIA_FROM_MEDIA_FOLDER);
  const [unfavoriteMedia] = useMutation(REMOVE_MEDIA_FROM_FAVORITES);
  const [restoreMedia] = useMutation(RESTORE_MEDIA);
  const [permanentlyDeleteMedia] = useMutation(PERMANENTLY_DELETE_MEDIA);
  const userData = useUserStore((state) => state.userData);
  const { data: favoritesData, refetch: favoritesRefetch } = useQuery(GET_FAVORITE_MEDIA, { fetchPolicy: 'network-only' });
  const { data: deletedData, refetch: deletedRefetch } = useQuery(GET_ARCHIVES_MEDIA, { fetchPolicy: 'network-only' });

  // Update the getQueryVariables function
  const getQueryVariables = useCallback(() => {
    if (albumType === 'FAVORITES' || albumType === 'DELETED') {
      return {};
    }

    return {
      getMediaFolderByIdId: albumId as string,
    };
  }, [albumId, albumType]);

  const {
    data: getMediaFolderByIdData,
    refetch: getMediaFolderByIdRefetch,
    loading: getMediaFolderByIdLoading,
    error: getMediaFolderByIdError
  } = useQuery(GET_MEDIA_FOLDER_BY_ID, {
    variables: getQueryVariables(),
    skip: albumType === 'FAVORITES' || albumType === 'DELETED',
    fetchPolicy: 'network-only'
  });

  // Modify the data fetching to implement pagination
  const [page, setPage] = useState(1);
  const ITEMS_PER_PAGE = 30;

  const getPhotosData = useCallback(() => {
    const allPhotos = albumType === 'FAVORITES'
      ? favoritesData?.getFavoritesMediaFolder?.result?.mediaFolder?.media?.result?.media || []
      : albumType === 'DELETED'
      ? deletedData?.getArchivesMediaFolder?.result?.mediaFolder?.media?.result?.media || []
      : getMediaFolderByIdData?.getMediaFolderById?.result?.mediaFolder?.media?.result?.media || [];

    // Implement pagination
    return allPhotos.slice(0, page * ITEMS_PER_PAGE);
  }, [albumType, favoritesData, deletedData, getMediaFolderByIdData, page]);

  const handleLoadMore = () => {
    if (isComponentMounted) {
      setPage(prev => prev + 1);
    }
  };

  useEffect(() => {
    setIsComponentMounted(true);
    return () => {
      setIsComponentMounted(false);
      // Cleanup memory when component unmounts
      if (Platform.OS === 'android') {
        Image.clearMemoryCache();
      }
      // Clear any cached data
      setSelectedPhotos(new Set());
      setFavoritedPhotos(new Set());
    };
  }, []);

  // Add memory warning listener
  useEffect(() => {
    const handleMemoryWarning = () => {
      if (Platform.OS === 'android') {
        Image.clearMemoryCache();
      }
      // Force garbage collection by clearing some state
      setSelectedPhotos(new Set());
    };

    if (Platform.OS === 'ios') {
      // iOS memory warning
      const subscription = AppState.addEventListener('memoryWarning', handleMemoryWarning);
      return () => {
        subscription.remove();
      };
    } else {
      // Android memory warning
      const subscription = AppState.addEventListener('change', (nextAppState) => {
        if (nextAppState === 'active') {
          handleMemoryWarning();
        }
      });
      return () => {
        subscription.remove();
      };
    }
  }, []);

  useEffect(() => {
    const newTitle = albumType === 'FAVORITES'
      ? 'Favorites'
      : albumType === 'DELETED'
      ? 'Deleted Photos'
      : getMediaFolderByIdData?.getMediaFolderById?.result?.mediaFolder?.name;

    if (newTitle) {
      setUseTitle(newTitle);
      setHasInitialTitle(true);
    } else if (!hasInitialTitle) {
      setUseTitle("loading...");
    }
  }, [getMediaFolderByIdData?.getMediaFolderById?.result?.mediaFolder?.name, hasInitialTitle, albumType]);

  const handleClose = () => {
    navigation.goBack();
  };

  const showUploadNotification = (count: number) => {
    setUploadedCount(count);
    setShowUploadSuccess(true);
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.delay(1000),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowUploadSuccess(false);
    });
  };

  const handleUpload = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        alert('Permission to access gallery is required!');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        quality: 1,
        selectionLimit: 100,
      });

      if (!result.canceled && result.assets.length > 0) {
        setIsUploading(true);
        setSelectedImageUris(result.assets.map(asset => asset.uri));

        const files = result.assets.map(asset => ({
          name: asset.uri.split('/').pop() || `image_${Date.now()}.jpg`,
          type: 'image/jpeg',
          uri: asset.uri,
          size: asset.fileSize || 0
        }));

        try {
          const uploadResult = await uploadFiles(files, 'media');

          if (uploadResult.successful.length > 0) {
            const newImages: ImageData[] = uploadResult.successful.map((file: UploadResult) => ({
              uri: file.url,
              key: file.key,
              originalName: file.originalName
            }));

            const newMediaIds: string[] = [];
            for (const image of newImages) {
              const response = await createMedia({
                variables: {
                  input: {
                    url: image.uri,
                    title: getMediaFolderByIdData?.getMediaFolderById?.result?.mediaFolder?.name || "loading"
                  }
                }
              });

              if (response.data?.createMedia?.result?.media?.id) {
                newMediaIds.push(response.data.createMedia.result.media.id);
              }
            }

            for (const mediaId of newMediaIds) {
              await addMediaToMediaFolder({
                variables: {
                  mediaFolderId: albumId as string,
                  mediaId: mediaId
                }
              });
            }

            if (newMediaIds.length > 0) {
              showUploadNotification(newMediaIds.length);
            }

            await getMediaFolderByIdRefetch();
          }
        } catch (uploadError) {
          console.error('Upload failed:', uploadError);
          alert('Failed to upload images. Please try again.');
          setSelectedImageUris([]);
        }
      }
    } catch (error) {
      console.error('Image picker error:', error);
      alert('Failed to open image picker');
    } finally {
      setIsUploading(false);
    }
  };

  const handlePhotoSelect = (photoId: string) => {
    setSelectedPhotos(prev => {
      const newSelection = new Set(prev);
      if (newSelection.has(photoId)) {
        newSelection.delete(photoId);
      } else {
        newSelection.add(photoId);
      }
      return newSelection;
    });
  };

  const handleImagePress = useCallback((item: MediaItem) => {
    if (!isSelectionMode) {
      let existingAttachments;
      if (albumType === 'FAVORITES') {
        const mediaFolderAttachments = favoritesData?.getFavoritesMediaFolder?.result?.mediaFolder?.media?.result?.media || [];
        existingAttachments = mediaFolderAttachments.map((media: MediaFolderItem) => ({
          id: media.id,
          name: media.title || 'Image',
          documentUrl: media.url,
          documentType: 'image/jpeg',
          documentSize: 0,
          owner: media.owner
        }));
      } else if (albumType === 'DELETED') {
        const mediaFolderAttachments = deletedData?.getArchivesMediaFolder?.result?.mediaFolder?.media?.result?.media || [];
        existingAttachments = mediaFolderAttachments.map((media: MediaFolderItem) => ({
          id: media.id,
          name: media.title || 'Image',
          documentUrl: media.url,
          documentType: 'image/jpeg',
          documentSize: 0,
          owner: media.owner
        }));
      } else {
        const mediaFolderAttachments = getMediaFolderByIdData?.getMediaFolderById?.result?.mediaFolder?.media?.result?.media || [];
        existingAttachments = mediaFolderAttachments.map((media: MediaFolderItem) => ({
          id: media.id,
          name: media.title || 'Image',
          documentUrl: media.url,
          documentType: 'image/jpeg',
          documentSize: 0,
          owner: media.owner
        }));
      }

      setCombinedAttachmentsForTask(albumId as string, {
        existing: existingAttachments,
        uploading: []
      });

      const attachmentFile = {
        id: item.id,
        name: item.title || 'Image',
        documentUrl: getOptimizedImageUrl(item.url),
        documentType: 'image/jpeg',
        size: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        owner: {
          id: item.owner?.id || '',
          firstName: item.owner?.firstName || '',
          lastName: item.owner?.lastName || '',
          email: item.owner?.email || ''
        }
      };

      setSelectedFile(attachmentFile);

      router.push(`/(home)/Task/attachment-preview?taskId=${albumId}&albumType=${albumType}&mediaId=${item.id}`);
    }
  }, [
    isSelectionMode,
    setSelectedFile,
    setCombinedAttachmentsForTask,
    getMediaFolderByIdData,
    favoritesData,
    deletedData,
    albumId,
    albumType,
  ]);

  const renderPhotoItem = useCallback(({ item }: { item: MediaItem }) => (
    <TouchableOpacity
      style={styles.photoContainer}
      onLongPress={() => handlePhotoSelect(item.id)}
      onPress={() => isSelectionMode ? handlePhotoSelect(item.id) : handleImagePress(item)}
      delayLongPress={200}
    >
      <Image
        source={{ uri: getOptimizedImageUrl(item.url) }}
        style={[
          styles.photo,
          selectedPhotos.has(item.id) && styles.selectedPhoto
        ]}
        contentFit="cover"
        transition={200}
        cachePolicy="memory-disk"
        recyclingKey={item.id}
        placeholder={DEFAULT_BLURHASH}
        placeholderContentFit="cover"
        onLoad={() => {
          if (Platform.OS === 'android') {
            Image.clearMemoryCache();
          }
        }}
      />
      {selectedPhotos.has(item.id) && (
        <View style={styles.selectionOverlay}>
          <View style={styles.checkmark}>
            <CircleCheck size={Icons.size.md} color={Colors.primary} />
          </View>
        </View>
      )}
      {favoritedPhotos.has(item.id) && (albumType !== 'DELETED' && albumType !== 'FAVORITES') && (
        <View style={styles.favoriteIcon}>
          <Favourite size={Icons.size.md} variant='filled' gradientStartColor={Colors.primary} gradientEndColor={Colors.gradient.orange} />
        </View>
      )}
    </TouchableOpacity>
  ), [selectedPhotos, isSelectionMode, handleImagePress, favoritedPhotos, albumType]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      if (albumType === 'FAVORITES') {
        await favoritesRefetch();
      } else if (albumType === 'DELETED') {
        await deletedRefetch();
      } else {
        await getMediaFolderByIdRefetch(getQueryVariables());
      }
    } catch (error) {
      console.error('Refresh failed:', error);
    } finally {
      setRefreshing(false);
    }
  }, [getMediaFolderByIdRefetch, getQueryVariables, favoritesRefetch, deletedRefetch, albumType]);

  useEffect(() => {
    getMediaFolderByIdRefetch(getQueryVariables());
  }, [getMediaFolderByIdRefetch, getQueryVariables]);

  useEffect(() => {
    if (favoritesData?.getFavoritesMediaFolder?.result?.mediaFolder?.media?.result?.media) {
      const favoriteIds = new Set(
        favoritesData.getFavoritesMediaFolder.result.mediaFolder.media.result.media.map(
          (media: { id: string; title: string }) => media.id
        )
      ) as Set<string>;
      setFavoritedPhotos(favoriteIds);
    }
  }, [favoritesData]);

  useEffect(() => {
    if (deletedData?.getArchivesMediaFolder?.result?.mediaFolder?.media?.result?.media) {
      const deletedIds = new Set(
        deletedData.getArchivesMediaFolder.result.mediaFolder.media.result.media.map(
          (media: { id: string; title: string }) => media.id
        )
      ) as Set<string>;
      setFavoritedPhotos(deletedIds);
    }
  }, [deletedData]);

  const handleShareSelected = async () => {
    try {
      setIsSharing(true);
      const selectedPhotoIds = Array.from(selectedPhotos);
      
      let photos: MediaItem[] = [];
      if (albumType === 'FAVORITES') {
        photos = favoritesData?.getFavoritesMediaFolder?.result?.mediaFolder?.media?.result?.media || [];
      } else if (albumType === 'DELETED') {
        photos = deletedData?.getArchivesMediaFolder?.result?.mediaFolder?.media?.result?.media || [];
      } else {
        photos = getMediaFolderByIdData?.getMediaFolderById?.result?.mediaFolder?.media?.result?.media || [];
      }

      const selectedPhotoUrls = photos
        .filter((photo: MediaItem) => selectedPhotoIds.includes(photo.id))
        .map((photo: MediaItem) => photo.url);

      if (selectedPhotoUrls.length === 0) {
        toast.error('No photos selected for sharing.');
        return;
      }

      const tempDir = `${FileSystem.cacheDirectory}temp_shares/`;
      await FileSystem.makeDirectoryAsync(tempDir, { intermediates: true }).catch(() => {});

      const downloadPromises = selectedPhotoUrls.map(async (url: string, index: number) => {
        const filename = `photo_${index + 1}.jpg`;
        const fileUri = `${tempDir}${filename}`;
        const downloadedFile = await FileSystem.downloadAsync(url, fileUri);
        return downloadedFile.uri;
      });

      const localFiles = await Promise.all(downloadPromises);

      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        toast.error("Sharing isn't available on your platform");
        return;
      }

      if (Platform.OS === 'ios') {
        for (const file of localFiles) {
          try {
            await Sharing.shareAsync(file, {
              mimeType: 'image/jpeg',
              dialogTitle: 'Share Photo',
              UTI: 'public.jpeg'
            });
          } catch (error) {
            console.log('Share sheet dismissed');
          }
        }
      } else {
        for (const file of localFiles) {
          try {
            await Sharing.shareAsync(file, {
              mimeType: 'image/jpeg',
              dialogTitle: 'Share Photo',
            });
          } catch (error) {
            console.log('Share sheet dismissed');
          }
        }
      }

      setTimeout(async () => {
        try {
          await FileSystem.deleteAsync(tempDir, { idempotent: true });
        } catch (error) {
          console.error('Failed to cleanup temp files:', error);
        }
      }, 3000);

      setSelectedPhotos(new Set());
    } catch (error) {
      console.error('Failed to share photos:', error);
      toast.error('Failed to share photos. Please try again.');
    } finally {
      setIsSharing(false);
    }
  };

  const handleSaveSelected = async () => {
    try {
      setIsSaving(true);
      const selectedPhotoIds = Array.from(selectedPhotos);
      
      let photos: MediaItem[] = [];
      if (albumType === 'FAVORITES') {
        photos = favoritesData?.getFavoritesMediaFolder?.result?.mediaFolder?.media?.result?.media || [];
      } else if (albumType === 'DELETED') {
        photos = deletedData?.getArchivesMediaFolder?.result?.mediaFolder?.media?.result?.media || [];
      } else {
        photos = getMediaFolderByIdData?.getMediaFolderById?.result?.mediaFolder?.media?.result?.media || [];
      }

      const selectedPhotoUrls = photos
        .filter((photo: MediaItem) => selectedPhotoIds.includes(photo.id))
        .map((photo: MediaItem) => photo.url);

      if (selectedPhotoUrls.length === 0) {
        toast.error('No photos selected for saving.');
        return;
      }

      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        toast.error('Permission to save photos is required!');
        return;
      }

      // Create a unique directory for this batch of downloads
      const timestamp = Date.now();
      const saveDir = `${FileSystem.documentDirectory}SavedPhotos_${timestamp}/`;
      await FileSystem.makeDirectoryAsync(saveDir, { intermediates: true }).catch(() => {});

      const downloadPromises = selectedPhotoUrls.map(async (url: string, index: number) => {
        const filename = `photo_${timestamp}_${index + 1}.jpg`;
        const fileUri = `${saveDir}${filename}`;
        const downloadedFile = await FileSystem.downloadAsync(url, fileUri);
        return downloadedFile.uri;
      });

      const savedFiles = await Promise.all(downloadPromises);

      // Save each file to the media library
      for (const fileUri of savedFiles) {
        try {
          await MediaLibrary.saveToLibraryAsync(fileUri);
        } catch (error) {
          console.error('Failed to save file to media library:', error);
        }
      }

      // Clean up temporary files
      try {
        await FileSystem.deleteAsync(saveDir, { idempotent: true });
      } catch (error) {
        console.error('Failed to cleanup temp files:', error);
      }

      setSelectedPhotos(new Set());
      toast.success(`Successfully saved ${savedFiles.length} photo${savedFiles.length > 1 ? 's' : ''} to your device`);
    } catch (error) {
      console.error('Failed to save photos:', error);
      toast.error('Failed to save photos. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleFavoriteSelected = async () => {
    const selectedPhotoIds = Array.from(selectedPhotos);

    try {
      for (const photoId of selectedPhotoIds) {
        try {
          await favoriteMedia({
            variables: {
              favoriteMediaId: photoId
            }
          });
          setFavoritedPhotos(prev => new Set([...prev, photoId]));
        } catch (error) {
          console.error(`Failed to favorite photo ${photoId}:`, error);
          toast.error(`Failed to favorite photo`);
          return;
        }
      }

      setSelectedPhotos(new Set());
      toast.success(`Successfully favorited ${selectedPhotoIds.length} photo${selectedPhotoIds.length > 1 ? 's' : ''}`);
    } catch (error) {
      console.error('Failed to favorite photos:', error);
      toast.error('Failed to favorite photos');
    }
  };

  const handleUnfavoriteSelected = async () => {
    const selectedPhotoIds = Array.from(selectedPhotos);

    try {
      for (const photoId of selectedPhotoIds) {
        try {
          await unfavoriteMedia({
            variables: {
              unfavoriteMediaId: photoId
            }
          });
        } catch (error) {
          console.error(`Failed to unfavorite photo ${photoId}:`, error);
          toast.error(`Failed to unfavorite photo`);
          return;
        }
      }

      setSelectedPhotos(new Set());
      await favoritesRefetch();
      toast.success(`Successfully unfavorited ${selectedPhotoIds.length} photo${selectedPhotoIds.length > 1 ? 's' : ''}`);
    } catch (error) {
      console.error('Failed to unfavorite photos:', error);
      toast.error('Failed to unfavorite photos');
    }
  };

  const handleDeleteSelected = async () => {
    try {
      setIsDeleting(true);
      const selectedPhotoIds = Array.from(selectedPhotos);

      if (albumType === 'FAVORITES') {
        // For favorites album, use unfavorite mutation
        for (const photoId of selectedPhotoIds) {
          try {
            await unfavoriteMedia({
              variables: {
                unfavoriteMediaId: photoId
              }
            });
          } catch (error) {
            console.error(`Failed to unfavorite photo ${photoId}:`, error);
            toast.error('Failed to remove photo from favorites. Please try again.');
            return;
          }
        }
        await favoritesRefetch();
      } else {
        // For regular albums, use removeMediaFromFolder mutation
        for (const photoId of selectedPhotoIds) {
          try {
            const response = await removeMediaFromFolder({
              variables: {
                mediaId: photoId,
                mediaFolderId: albumId
              }
            });

            const errors = response.data?.removeMediaFromMediaFolder?.errors;
            const authError = errors?.find((error: { field: string }) => error.field === 'authorization');

            if (authError) {
              toast.error("You don't have permission to delete this photo. Only the photo owner or album owner can delete photos.");
              setSelectedPhotos(new Set());
              setIsSelectionMode(false);
              return;
            }
          } catch (error) {
            console.error(`Failed to process photo ${photoId}:`, error);
            toast.error('Failed to delete the photo. Please try again.');
            return;
          }
        }
        await getMediaFolderByIdRefetch();
      }

      setSelectedPhotos(new Set());
      toast.success(`Successfully removed ${selectedPhotoIds.length} photo${selectedPhotoIds.length > 1 ? 's' : ''}`);
    } catch (error) {
      console.error('Failed to delete photos:', error);
      toast.error('Failed to delete selected photos. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  const showDeleteConfirmation = () => {
    setIsDeleteDialogVisible(true);
  };

  const hideDeleteDialog = () => {
    setIsDeleteDialogVisible(false);
  };

  const confirmDelete = () => {
    hideDeleteDialog();
    handleDeleteSelected();
  };

  useEffect(() => {
    setIsSelectionMode(selectedPhotos.size > 0);
  }, [selectedPhotos.size]);

  const handleCancelSelection = () => {
    setSelectedPhotos(new Set());
    setIsSelectionMode(false);
  };

  const handlePermanentDeleteSelected = async () => {
    const selectedPhotoIds = Array.from(selectedPhotos);

    try {
      for (const photoId of selectedPhotoIds) {
        try {
          const response = await permanentlyDeleteMedia({
            variables: {
              deleteMediaId: photoId
            }
          });

          const errors = response.data?.deleteMedia?.errors;
          if (errors?.length > 0) {
            const errorMessage = errors[0].message || 'Failed to delete photo';
            toast.error(errorMessage);
            return;
          }
        } catch (error) {
          console.error(`Failed to permanently delete photo ${photoId}:`, error);
          toast.error(`Failed to delete photo`);
          return;
        }
      }

      setSelectedPhotos(new Set());
      await deletedRefetch();
      toast.success(`Successfully deleted ${selectedPhotoIds.length} photo${selectedPhotoIds.length > 1 ? 's' : ''}`);
    } catch (error) {
      console.error('Failed to permanently delete photos:', error);
      toast.error('Failed to delete photos');
    }
  };

  const handleRestoreSelected = async () => {
    const selectedPhotoIds = Array.from(selectedPhotos);

    try {
      for (const photoId of selectedPhotoIds) {
        try {
          const response = await restoreMedia({
            variables: {
              mediaId: photoId
            }
          });

          const errors = response.data?.restoreMediaFromArchives?.errors;
          if (errors?.length > 0) {
            const errorMessage = errors[0].message || 'Failed to restore photo';
            toast.error(errorMessage);
            return;
          }
        } catch (error) {
          console.error(`Failed to restore photo ${photoId}:`, error);
          toast.error(`Failed to restore photo`);
          return;
        }
      }

      setSelectedPhotos(new Set());
      await deletedRefetch();
      toast.success(`Successfully restored ${selectedPhotoIds.length} photo${selectedPhotoIds.length > 1 ? 's' : ''}`);
    } catch (error) {
      console.error('Failed to restore photos:', error);
      toast.error('Failed to restore photos');
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerLeft: () => (
            <></>
          ),
          headerTitle: () => (
            isSelectionMode ? (
              <Text style={styles.selectionTitle}>
                {selectedPhotos.size} {selectedPhotos.size === 1 ? 'Photo' : 'Photos'} Selected
              </Text>
            ) : (
              <View style={styles.titleContainer}>
                <Text style={styles.titleText} numberOfLines={1} ellipsizeMode="tail">
                  {useTitle}
                </Text>
                <MultipleUsers size={Icons.size.lg} color={Colors.primary} />
              </View>
            )
          ),
          headerStyle: {
            backgroundColor: Colors.background.primary,
          },
          headerRight: () => (
            isSelectionMode ? (
              <Pressable onTouchStart={handleCancelSelection}>
                <Text style={styles.cancelButton}>Cancel</Text>
              </Pressable>
            ) : albumType === 'FAVORITES' ? (
              <Pressable onTouchStart={handleClose} style={styles.headerIcon}>
                <Cross size={Icons.size.md} color={Colors.text.secondary} />
              </Pressable>
            ) : albumType === 'DELETED' ? (
              <Pressable onTouchStart={handleClose} style={styles.headerIcon}>
                <Cross size={Icons.size.md} color={Colors.text.secondary} />
              </Pressable>
            ) : (
              <View style={styles.headerRightContainer}>
                <Pressable
                  onTouchStart={handleUpload}
                  style={styles.headerIcon}
                  disabled={isUploading}
                >
                  <Upload size={Icons.size.md} color={isUploading ? Colors.button.disabled : Colors.text.secondary} />
                </Pressable>
                <Pressable onTouchStart={handleClose} style={styles.headerIcon}>
                  <Cross size={Icons.size.md} color={Colors.text.secondary} />
                </Pressable>
              </View>
            )
          ),
        }}
      />
      <View style={styles.container}>
        {showUploadSuccess && (
          <Animated.View
            style={[
              styles.uploadingOverlay,
              {
                opacity: fadeAnim,
              },
            ]}
          >
            <View style={styles.uploadSuccessContent}>
              <View style={styles.successCircle}>
                <Check size={Icons.size.lg} color={Colors.white} />
              </View>
              <Text style={styles.uploadSuccessText}>
                {uploadedCount} {uploadedCount === 1 ? 'Photo' : 'Photos'} Added
              </Text>
            </View>
          </Animated.View>
        )}

        {isUploading && (
          <View style={styles.uploadingOverlay}>
            <View style={styles.uploadingContent}>
              <FastPartyActivityIndicator size="large" fullScreen={false} />
              <Text style={styles.uploadingCount}>
                {selectedImageUris.length} {selectedImageUris.length === 1 ? 'Photo' : 'Photos'}
              </Text>
              <Text style={styles.uploadingText}>Uploading...</Text>
            </View>
          </View>
        )}

        {isDeleting && (
          <View style={styles.uploadingOverlay}>
            <View style={styles.uploadingContent}>
              <FastPartyActivityIndicator size="large" fullScreen={false} />
              <Text style={styles.uploadingCount}>
                {selectedPhotos.size} {selectedPhotos.size === 1 ? 'Photo' : 'Photos'}
              </Text>
              <Text style={styles.uploadingText}>Deleting...</Text>
            </View>
          </View>
        )}

        {isSharing && (
          <View style={styles.uploadingOverlay}>
            <View style={styles.uploadingContent}>
              <FastPartyActivityIndicator size="large" fullScreen={false} />
              <Text style={styles.uploadingCount}>
                {selectedPhotos.size} {selectedPhotos.size === 1 ? 'Photo' : 'Photos'}
              </Text>
              <Text style={styles.uploadingText}>Preparing to share...</Text>
            </View>
          </View>
        )}

        {isSaving && (
          <View style={styles.uploadingOverlay}>
            <View style={styles.uploadingContent}>
              <FastPartyActivityIndicator size="large" fullScreen={false} />
              <Text style={styles.uploadingCount}>
                {selectedPhotos.size} {selectedPhotos.size === 1 ? 'Photo' : 'Photos'}
              </Text>
              <Text style={styles.uploadingText}>Preparing to save...</Text>
            </View>
          </View>
        )}

        {(getMediaFolderByIdLoading && albumType !== 'FAVORITES' && albumType !== 'DELETED') ? (
          <View style={styles.loadingContainer}>
            <FastPartyActivityIndicator size="large" fullScreen={false} />
            <Text style={styles.loadingText}>Loading photos...</Text>
          </View>
        ) : getMediaFolderByIdError && albumType !== 'FAVORITES' && albumType !== 'DELETED' ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Failed to load photos</Text>
          </View>
        ) : (
          <>
            {getPhotosData().length === 0 ? (
              <View style={styles.noPhotosContainer}>
                <NoPhotos/>
                <Text style={styles.noPhotosText}>No party pics yet. This gallery's waiting to shine!</Text>
                <Text style={styles.noPhotosSubText}>👉 Hint: "Add photos after your event!"</Text>
              </View>
            ) : (
              <FlatList
                data={getPhotosData()}
                renderItem={renderPhotoItem}
                keyExtractor={(item) => item.id}
                numColumns={3}
                contentContainerStyle={styles.gridContainer}
                refreshControl={
                  <RefreshControl
                    refreshing={refreshing}
                    onRefresh={onRefresh}
                    colors={[Colors.primary]}
                    tintColor={Colors.primary}
                  />
                }
                removeClippedSubviews={true}
                maxToRenderPerBatch={6}
                windowSize={3}
                initialNumToRender={9}
                updateCellsBatchingPeriod={50}
                onEndReached={handleLoadMore}
                onEndReachedThreshold={0.5}
                maintainVisibleContentPosition={{
                  minIndexForVisible: 0,
                  autoscrollToTopThreshold: 10,
                }}
                getItemLayout={(data, index) => ({
                  length: PHOTO_SIZE,
                  offset: PHOTO_SIZE * Math.floor(index / 3),
                  index,
                })}
              />
            )}
          </>
        )}
        {selectedPhotos.size > 0 ? (
          <SelectionActionBar
            selectedCount={selectedPhotos.size}
            onSave={handleSaveSelected}
            onShare={handleShareSelected}
            onDelete={albumType === 'DELETED' ? handlePermanentDeleteSelected : showDeleteConfirmation}
            onFavorite={handleFavoriteSelected}
            onUnfavorite={handleUnfavoriteSelected}
            onRestore={handleRestoreSelected}
            isFavoritesAlbum={albumType === 'FAVORITES'}
            isDeletedAlbum={albumType === 'DELETED'}
          />
        ) : null}

        <Portal>
          <Dialog visible={isDeleteDialogVisible} onDismiss={hideDeleteDialog}>
            <Dialog.Title>Delete Photos</Dialog.Title>
            <Dialog.Content>
              <Text>
                Are you sure you want to delete {selectedPhotos.size} {selectedPhotos.size === 1 ? 'photo' : 'photos'}? This action cannot be undone.
              </Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={hideDeleteDialog}>Cancel</Button>
              <Button onPress={confirmDelete} textColor="red">Delete</Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>
      </View>
    </>
  );
}
const styles = StyleSheet.create({
  // Layout
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-start',
    marginLeft: Platform.OS === 'ios' ? -16 : 0,
  },
  titleText: {
    color: Colors.text.tertiary,
    fontWeight: Typography.fontWeight.semibold,
    fontSize: Typography.fontSize.lg,
    textAlign: 'left',
    maxWidth: '80%',
    marginRight: Spacing.sm,
  },
  headerRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 'auto',
    marginRight: -8,
  },
  headerIcon: {
    marginHorizontal: Spacing.md,
  },
  booleanIcon: {
    marginLeft: Spacing.md,
  },
  // Overlays and Loading
  uploadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  uploadCount: {
    padding: Spacing.md,
    textAlign: 'center',
    color: Colors.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: Spacing.md,
    color: Colors.text.secondary,
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.md,
  },
  errorContainer: {
    marginTop: Spacing.xs,
  },
  errorText: {
    color: Colors.error,
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.md,
  },

  // Photo Grid
  gridContainer: {
    padding: 1,
  },
  photoContainer: {
    width: PHOTO_SIZE,
    height: PHOTO_SIZE,
    margin: 1,
    backgroundColor: Colors.background.secondary,
  },
  photo: {
    width: '100%',
    height: '100%',
    backgroundColor: Colors.background.secondary,
  },
  selectedPhoto: {
    opacity: 0.7,
  },
  selectionOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 3,
  },
  checkmark: {
    position: 'absolute',
    top: Spacing.md,
    right: Spacing.md,
    backgroundColor: Colors.white,
    borderRadius: Borders.radius.lg,
    padding: 2,
    zIndex: 4,
  },
  // Upload Success
  uploadSuccessContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  successCircle: {
    width: 64,
    height: 64,
    borderRadius: Borders.radius.circle,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  uploadSuccessText: {
    color: Colors.primary,
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    fontFamily: Typography.fontFamily.primary,
  },
  uploadingContent: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.xl,
  },
  uploadingCount: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.primary,
    marginTop: Spacing.lg,
    marginBottom: Spacing.xs,
    fontFamily: Typography.fontFamily.primary,
  },
  uploadingText: {
    fontSize: Typography.fontSize.md,
    color: Colors.text.secondary,
    fontWeight: Typography.fontWeight.medium,
    fontFamily: Typography.fontFamily.primary,
  },

  // Selection Action Bar
  selectionActionBar: {
    position: 'absolute',
    bottom: Spacing.xl,
    left: Spacing.xl,
    right: Spacing.xl,
    borderRadius: Borders.radius.pill,
    flexDirection: 'row',
    padding: Spacing.md,
    backgroundColor: Colors.white,
    shadowColor: Shadows.md.shadowColor,
    shadowOffset: Shadows.md.shadowOffset,
    shadowOpacity: Shadows.md.shadowOpacity,
    shadowRadius: Shadows.md.shadowRadius,
    elevation: Shadows.md.elevation,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  actionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
  },
  actionButtonText: {
    color: Colors.text.tertiary,
    fontSize: Typography.fontSize.sm,
    marginTop: Spacing.xs,
    fontWeight: Typography.fontWeight.medium,
    fontFamily: Typography.fontFamily.primary,
  },

  // Header and Selection
  selectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.tertiary,
    fontFamily: Typography.fontFamily.primary,
  },
  cancelButton: {
    color: Colors.primary,
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.regular,
    paddingHorizontal: Spacing.lg,
    fontFamily: Typography.fontFamily.primary,
  },
  favoriteIcon: {
    position: 'absolute',
    top: Spacing.md,
    right: Spacing.md,
    backgroundColor: Colors.overlay,
    borderRadius: Borders.radius.md,
    padding: Spacing.xs,
    zIndex: 2,
  },

  // Empty State
  noPhotosContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    width: '100%',
  },
  noPhotosText: {
    fontSize: Typography.fontSize.xl,
    color: Colors.text.secondary,
    fontFamily: Typography.fontFamily.primary,
    textAlign: 'center',
  },
  noPhotosSubText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.secondary,
    fontFamily: Typography.fontFamily.primary,
    textAlign: 'center',
  },
});