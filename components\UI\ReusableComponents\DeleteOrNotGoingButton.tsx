import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator } from 'react-native';
import Delete from '../../icons/Delete';
import Block from '../../icons/Block';
import { Spacing, Typography, Colors, Icons } from '@/constants/DesignSystem';

interface DeleteOrNotGoingButtonProps {
  isHost: boolean;
  onDelete: () => void;
  onNotGoing: () => void;
  loading?: boolean;
  disabled?: boolean;
  style?: any;
}

export const DeleteOrNotGoingButton: React.FC<DeleteOrNotGoingButtonProps> = ({
  isHost,
  onDelete,
  onNotGoing,
  loading = false,
  disabled = false,
  style
}) => {
  const handlePress = () => {
    if (disabled || loading) return;
    if (isHost) onDelete();
    else onNotGoing();
  };

  return (
    <TouchableOpacity
      style={[styles.option, style, disabled && { opacity: 0.5 }]}
      accessibilityRole="button"
      accessibilityLabel={isHost ? 'Delete Event' : 'Not Going'}
      onPress={handlePress}
      disabled={disabled || loading}
    >
      {isHost ? (
        <Delete size={Icons.size.lg} color={Colors.error} />
      ) : (
        <Block size={Icons.size.lg} color={Colors.error} />
      )}
      <Text style={styles.optionText}>
        {isHost ? 'Delete Event' : 'Not Going'}
      </Text>
      {loading && <ActivityIndicator size="small" color={Colors.error} style={{ marginLeft: 8 }} />}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  optionText: {
    fontSize: Typography.fontSize.md,
    marginLeft: Spacing.md,
    fontFamily: Typography.fontFamily.primary,
    color: Colors.error,
  },
});

export default DeleteOrNotGoingButton; 