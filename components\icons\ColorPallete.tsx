import * as React from "react";
import Svg, { G, <PERSON>, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const ColorPallete = ({
  size = 20,
  color = "#34434D",
  gradientStartColor = "#F4862B",
  gradientEndColor = "#D55B2E",
  variant = 'outline',
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <G clipPath="url(#ColorPallete_filled_clip)">
          <Path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M6.46373 1.4296C8.0346 0.748873 9.76233 0.512975 11.4582 0.747679C13.154 0.982383 14.7527 1.67866 16.0797 2.76044C17.4063 3.84205 18.4104 5.26743 18.982 6.88091C19.3414 7.89196 19.1816 9.04995 18.5616 9.92579C17.9418 10.8014 16.9033 11.337 15.8305 11.3343H13.3362H13.3343C12.8579 11.333 12.3966 11.5017 12.0334 11.8101C11.6702 12.1185 11.4289 12.5463 11.3529 13.0166C11.2769 13.487 11.3712 13.9691 11.6189 14.3761C11.8665 14.7832 12.2512 15.0885 12.7039 15.2373C12.7119 15.24 12.72 15.2428 12.728 15.2457C13.4619 15.5184 13.9753 16.1823 14.0247 16.9613C14.1324 17.8562 13.5485 18.7502 12.6768 18.9951C11.8623 19.2265 11.0194 19.3427 10.1727 19.3404C8.46095 19.3395 6.78236 18.8682 5.32029 17.9781C2.36669 16.1797 0.621052 12.8058 0.859419 9.35609C1.09778 5.90636 3.29088 2.80455 6.46373 1.4296ZM6.66441 9.99996C7.76982 9.99996 8.66594 9.10384 8.66594 7.99843C8.66594 6.89301 7.76982 5.9969 6.66441 5.9969C5.55899 5.9969 4.66288 6.89301 4.66288 7.99843C4.66288 9.10384 5.55899 9.99996 6.66441 9.99996ZM14.6704 5.99755C14.6704 7.10297 13.7743 7.99908 12.6688 7.99908C11.5634 7.99908 10.6673 7.10297 10.6673 5.99755C10.6673 4.89213 11.5634 3.99602 12.6688 3.99602C13.7743 3.99602 14.6704 4.89213 14.6704 5.99755ZM6.66425 14.6695C7.40118 14.6695 7.9986 14.0721 7.9986 13.3352C7.9986 12.5982 7.40118 12.0008 6.66425 12.0008C5.9273 12.0008 5.32989 12.5982 5.32989 13.3352C5.32989 14.0721 5.9273 14.6695 6.66425 14.6695Z"
            fill="url(#ColorPallete_filled_gradient)"
          />
          <Defs>
            <LinearGradient id="ColorPallete_filled_gradient" x1="10.0044" y1="-1.59512" x2="10.0044" y2="23.6349" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor} />
              <Stop offset={1} stopColor={gradientEndColor} />
            </LinearGradient>
            <ClipPath id="ColorPallete_filled_clip">
              <Path fill="#fff" d="M0 0h20v20H0z" />
            </ClipPath>
          </Defs>
        </G>
      ) : (
        <G clipPath="url(#ColorPallete_outline_clip)">
          <Path d="M12.0196 7.30766C12.7631 7.30766 13.3658 6.70496 13.3658 5.96151C13.3658 5.21806 12.7631 4.61536 12.0196 4.61536C11.2762 4.61536 10.6735 5.21806 10.6735 5.96151C10.6735 6.70496 11.2762 7.30766 12.0196 7.30766Z" stroke={color} strokeLinecap="round" strokeLinejoin="round"/>
          <Path d="M6.63499 14.0385C7.00672 14.0385 7.30807 13.7372 7.30807 13.3655C7.30807 12.9937 7.00672 12.6924 6.63499 12.6924C6.26326 12.6924 5.96191 12.9937 5.96191 13.3655C5.96191 13.7372 6.26326 14.0385 6.63499 14.0385Z" stroke={color} strokeLinecap="round" strokeLinejoin="round"/>
          <Path d="M6.63509 9.32695C7.37854 9.32695 7.98124 8.72425 7.98124 7.9808C7.98124 7.23734 7.37854 6.63464 6.63509 6.63464C5.89164 6.63464 5.28894 7.23734 5.28894 7.9808C5.28894 8.72425 5.89164 9.32695 6.63509 9.32695Z" stroke={color} strokeLinecap="round" strokeLinejoin="round"/>
          <Path d="M13.3926 17.1077C13.3776 16.8465 13.2868 16.5955 13.1314 16.385C12.976 16.1748 12.7627 16.0142 12.5176 15.923C11.9087 15.7229 11.3912 15.3122 11.0581 14.7647C10.725 14.2171 10.5981 13.5687 10.7004 12.936C10.8026 12.3033 11.1271 11.7278 11.6157 11.313C12.1042 10.8982 12.7247 10.6713 13.3656 10.6731H15.8829C16.3146 10.6743 16.7404 10.5716 17.1242 10.3738C17.5079 10.1759 17.8385 9.88868 18.0879 9.53629C18.3374 9.18388 18.4984 8.77666 18.5574 8.34897C18.6165 7.92127 18.5719 7.48564 18.4271 7.07884C17.8918 5.56707 16.9511 4.23152 15.708 3.21814C14.465 2.20474 12.9674 1.55249 11.3787 1.33262C9.7901 1.11275 8.17159 1.33374 6.70004 1.97143C5.22848 2.60913 3.9605 3.63899 3.03461 4.94852C2.10873 6.25805 1.56057 7.79686 1.45002 9.39683C1.33946 10.9968 1.67077 12.5964 2.40775 14.0208C3.14472 15.4452 4.25901 16.6397 5.62888 17.4737C6.99873 18.3078 8.57147 18.7493 10.1753 18.75C10.9673 18.7521 11.7557 18.6435 12.5176 18.4269C12.7974 18.3485 13.0397 18.1721 13.2003 17.9299C13.3609 17.6877 13.4292 17.396 13.3926 17.1077Z" stroke={color} strokeLinecap="round" strokeLinejoin="round"/>
          <Defs>
            <ClipPath id="ColorPallete_outline_clip">
              <Path fill="#fff" d="M0 0h20v20H0z" />
            </ClipPath>
          </Defs>
        </G>
      )}
    </Svg>
  );
};

export default ColorPallete;
