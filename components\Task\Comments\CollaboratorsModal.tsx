import { useEventStore } from '@/store/eventStore';
import React from 'react';
import { View, StyleSheet, Text, ActivityIndicator, Platform } from 'react-native';
import { Divider, List, Searchbar, Button } from 'react-native-paper';
import { useQuery, useMutation } from '@apollo/client';
import { GET_EVENT_COLLABORATORS } from './TaskComments.data';
import { BottomSheetModal, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { DELETE_TASK_COLLABORATOR, CREATE_TASK_COLLABORATORS } from './TaskComments.data';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Icons, Colors } from '@/constants/DesignSystem';
import { Check, Circle } from '@/components/icons';

export interface CollaboratorInterface {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  user?: {
    id: string;
    firstName?: string;
    lastName?: string | null;
  };
}

export interface CollaboratorSearchProps {
  taskId?: string;
  taskCollaborators?: CollaboratorInterface[];
  refetchTaskCollaborators: () => void;
  collaboratorsBottomSheetRef: React.RefObject<BottomSheetModal>;
}

const CollaboratorSearch = ({ taskId, taskCollaborators, refetchTaskCollaborators, collaboratorsBottomSheetRef }: CollaboratorSearchProps) => {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedCollaborators, setSelectedCollaborators] = React.useState<Set<string>>(() =>
    new Set(taskCollaborators?.map(collab => collab.user?.id || '').filter(Boolean))
  );

  const selectedEventId = useEventStore((state) => state.selectedEventId);
  const { data, loading, error } = useQuery(GET_EVENT_COLLABORATORS, {
    variables: { eventId: selectedEventId },
  });

  const [createTaskCollaborator] = useMutation(CREATE_TASK_COLLABORATORS);
  const [deleteTaskCollaborator] = useMutation(DELETE_TASK_COLLABORATOR);

  const eventCollaborators = data?.getEventCollaborators?.result?.collaborators || [];

  const filteredCollaborators = React.useMemo(() =>
    eventCollaborators.filter((collaborator: CollaboratorInterface) =>
      `${collaborator.firstName} ${collaborator.lastName || ''}`
        .toLowerCase()
        .includes(searchQuery.toLowerCase())
    ),
    [eventCollaborators, searchQuery]
  );

  const handleCollaboratorToggle = (collaboratorId: string) => {
    setSelectedCollaborators(prev => {
      const newSet = new Set(prev);
      if (newSet.has(collaboratorId)) {
        newSet.delete(collaboratorId);
      } else {
        newSet.add(collaboratorId);
      }
      return newSet;
    });
  };

  const handleSave = async () => {
    try {
      // Get all selected collaborators
      const selectedUsers = filteredCollaborators.filter((collaborator: CollaboratorInterface) =>
        selectedCollaborators.has(collaborator.id)
      );
      // Handle creations
      const existingUserIds = new Set(taskCollaborators?.map(tc => tc.user?.id) || []);
      const usersToCreate = selectedUsers.filter((user: CollaboratorInterface) => !existingUserIds.has(user.id));
      if (usersToCreate.length > 0) {
        await createTaskCollaborator({
          variables: {
            taskId: taskId, // You'll need to get this from props or context
            userIds: usersToCreate.map((user: CollaboratorInterface) => user.id)
          }
        }).then((res: any) => {
          console.log('Collaborators created successfully', res, res?.errors);
        }).catch((error: any) => {
          console.error('Error creating collaborators:', error);
        });
      }
      // Handle deletions
      const selectedUserIds = new Set(selectedUsers.map((user: CollaboratorInterface) => user.id));
      const collaboratorsToDelete = taskCollaborators?.filter(
        tc => tc.user?.id && !selectedUserIds.has(tc.user.id)
      ) || [];
      for (const collaborator of collaboratorsToDelete) {
        await deleteTaskCollaborator({
          variables: {
            deleteTaskCollaboratorId: collaborator.id
          }
        });
      }
      // Refresh the collaborators list and close the modal
      await refetchTaskCollaborators();
      collaboratorsBottomSheetRef.current?.close();
    } catch (error) {
      console.error('Error updating collaborators:', error);
      // Handle error (show toast or error message)
    }
  };

  if (error) {
    return (
      <View style={styles.container}>
        <Text>Error loading collaborators. Please try again.</Text>
      </View>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Select Task Collaborators</Text>
        </View>
        <View style={styles.searchBarContainer}>
          <Searchbar
            placeholder="Search collaborators"
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
            icon={() => null}
            inputStyle={styles.searchInput}
          />
        </View>
        <BottomSheetScrollView 
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={true}
          keyboardShouldPersistTaps="handled"
        >
          {loading ? (
            <ActivityIndicator />
          ) : (
            filteredCollaborators.map((collaborator: CollaboratorInterface) => (
              <View key={collaborator.id} >
                <List.Item
                  title={`${collaborator.firstName} ${collaborator.lastName || ''}`}
                  description={collaborator.email}
                  onPress={() => handleCollaboratorToggle(collaborator.id)}
                  titleStyle={{ marginLeft: 16 }}
                  right={() => (
                    <View style={styles.checkboxContainer}>
                      {selectedCollaborators.has(collaborator.id) ? (
                        <Check
                          size={Icons.size.md}
                          color={Colors.primary}
                          style={[styles.checkbox, styles.selectedCheckbox]}
                        />
                      ) : (
                        <Circle
                          size={Icons.size.md}
                          color={Colors.primary}
                          style={[styles.checkbox, styles.unselectedCheckbox]}
                        />
                      )}
                    </View>
                  )}
                />
                <Divider />
              </View>
            ))
          )}
        </BottomSheetScrollView>
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.saveButton}
          labelStyle={styles.saveButtonLabel}
        >
          Save
        </Button>
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: 8,
  },
  searchBarContainer: {
    backgroundColor: 'white',
    ...Platform.select({
      android: {
        elevation: 8,
        marginBottom: 16,
      },
      ios: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        marginBottom: 16,
      },
    }),
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  searchBar: {
    marginHorizontal: 0,
    backgroundColor: 'white',
    borderRadius: 0,
  },
  searchInput: {
    textAlign: 'left',
    marginLeft: -24,
    fontSize: 16,
  },
  saveButton: {
    backgroundColor: '#E2E7FB',
    marginTop: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    paddingVertical: 8,
    borderRadius: 28,
  },
  saveButtonLabel: {
    color: '#2E52E2',
    fontWeight: '700',
    fontSize: 16,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    backgroundColor: '#ffffff',
    marginBottom: 0,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#000000',
    textAlign: 'center',
  },
  checkboxContainer: {
    justifyContent: 'center',
    padding: 8,
  },
  checkbox: {
    padding: 2,
  },
  selectedCheckbox: {
    backgroundColor: '#BEC9F6',
    borderRadius: 50,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: '#768DEC',
  },
  unselectedCheckbox: {
    backgroundColor: 'white',
    borderRadius: 50,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: '#CFCECE',
  },
});

export default CollaboratorSearch;