import { View, ActivityIndicator, StyleSheet, ViewStyle } from 'react-native';
import { Colors } from '@/constants/DesignSystem';

interface FastPartyActivityIndicatorProps {
  size?: 'small' | 'large';
  color?: string;
  fullScreen?: boolean;
}

export function FastPartyActivityIndicator({
  size = 'large',
  color = Colors.primary || '#43A5BE', // Added fallback color
  fullScreen = true
}: FastPartyActivityIndicatorProps) {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    inlineContainer: {
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

  return (
    <View style={fullScreen ? styles.container : styles.inlineContainer}>
      <ActivityIndicator size={size} color={color} />
    </View>
  );
}

