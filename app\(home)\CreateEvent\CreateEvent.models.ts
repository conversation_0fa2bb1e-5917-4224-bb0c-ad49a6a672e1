export interface PartyType {
    id: string;
    name: string;
  }
  
  export interface EventType {
    id: string;
    name: string;
    partyTypes: PartyType[];
  }
  
  export interface Cohost {
    id: string;
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
    email?: string;
  }
  
  export interface Party {
    id: string;
    name: string;
  }
  export interface EventData {
    name: string;
    eventType: any;
    isMultiDay: boolean;
    date: Date;
    locationId: string;
    locationName: string;
    area: string;
    cohosts: any[];
    parties: any[];
  }
  
  export interface CreateEventData {
    name: string;
    eventType: EventType | null;
    isMultiDay: boolean | null;
    date: Date | null;
    locationId: string;
    locationName: string;
    area: string;
    cohosts: Cohost[];
    parties: Party[];
  }

export interface CreateEventInput {
    input: {
      coHosts: { firstName?: string; lastName?: string; email?: string; phone?: string }[] | null;
      description: string | null;
      eventType: string | null;
      location: string | null;
      name: string | null;
      // date: string | null;
    };
  }