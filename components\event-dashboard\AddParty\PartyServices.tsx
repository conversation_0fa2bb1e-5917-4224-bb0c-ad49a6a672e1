import React from 'react';
import { View } from 'react-native';
import { Text, Chip, useTheme } from 'react-native-paper';
import { VendorType } from '../../../app/(home)/EventsDashboard/AddParty.models';
import { AddPartyNames } from '../../../constants/displayNames';
import { Colors } from '@/constants/Colors';

interface PartyServicesProps {
  services: VendorType[];
  selectedServices: string[];
  onToggleService: (serviceId: string) => void;
}

export function PartyServices({ services, selectedServices, onToggleService }: PartyServicesProps) {
  const theme = useTheme();
 
  return (
    <>
      <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8, marginTop: 8 }}>
        {services.length === 0 && <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, textAlign: 'center', margin: 8, padding: 8 }}>{AddPartyNames.NO_SERVICES_AVAILABLE}</Text>}
        {services.map((service) => (
          <Chip
            key={service.id}
            selected={selectedServices.includes(service.id)}
            onPress={() => onToggleService(service.id)}
            mode= {selectedServices.includes(service.id) ? "flat" : "outlined"}
            style={{ backgroundColor: selectedServices.includes(service.id) ? Colors.custom.partyServicesChipColor : undefined}} // Custom background color for selected
          >
            {service.name}
          </Chip>
        ))}
      </View>
    </>
  );
}