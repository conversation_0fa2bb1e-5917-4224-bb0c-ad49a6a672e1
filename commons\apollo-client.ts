import { ApolloClient, InMemoryCache, HttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { userStorage } from '@/app/auth/userStorage';

const httpLink = new HttpLink({
  uri: `${process.env.EXPO_PUBLIC_API_URL}/graphql`,
});

const authLink = setContext(async (_, { headers }) => {
  try {
    const token = await userStorage.getToken();
    return {
      headers: {
        ...headers,
        authorization: token ? `Bearer ${token}` : '',
        'Content-Type': 'application/json',
      }
    };
  } catch (error) {
    console.error('Error getting token for Apollo Client:', error);
    return {
      headers: {
        ...headers,
        'Content-Type': 'application/json',
      }
    };
  }
});

export const FastPartyClient = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache()
});