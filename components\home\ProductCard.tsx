import React from 'react';
import { View, Text, StyleSheet, Image, Pressable, StyleProp, ViewStyle } from 'react-native';
import { Colors } from '@/constants/Colors';
import { MdPromotionByTagId } from '@/app/(home)/UserDashboard/promotions';


interface ProductCardProps {
  product: MdPromotionByTagId;
  onPress: (product: MdPromotionByTagId) => void;
  style?: StyleProp<ViewStyle>;
  variant?: 'default' | 'offer';
  partnerName?: string;
  isFullWidth?: boolean;
}

export function ProductCard({ 
  product, 
  onPress, 
  style, 
  variant = 'default',
  partnerName,
  isFullWidth = false
}: ProductCardProps) {
  const isOffer = variant === 'offer';

  return (
    <Pressable 
      style={[
        styles.container, 
        isOffer && styles.offerContainer,
        style
      ]}
      onPress={() => onPress(product)}
    >
      <View style={[
        styles.imageContainer,
        isOffer && styles.offerImageContainer,
        isFullWidth && styles.fullWidthImageContainer
      ]}>
        <Image
          source={{ uri: product.mediaUrl }}
          style={styles.image}
          resizeMode="cover"
        />
        {isOffer && (
          <>
            <View style={styles.overlay} />
            <View style={styles.offerContent}>
              <View style={styles.offerTitleContainer}>
                <Text style={styles.offerTitle} numberOfLines={1}>
                  {partnerName}
                </Text>
              </View>
            </View>
          </>
        )}
      </View>
      {!isOffer && (
        <View style={styles.content}>
          <Text style={styles.name} numberOfLines={2}>
            {product.name}
          </Text>
          <View style={styles.priceContainer}>
            <Text style={styles.price}>$99</Text>
            <Text style={styles.timeRemaining}>03:22:00 left</Text>
          </View>
        </View>
      )}
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 8,
    overflow: 'hidden',
  },
  offerContainer: {
    height: '100%',
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    aspectRatio: 1,
  },
  offerImageContainer: {
    aspectRatio: undefined,
    flex: 1,
    height: '100%',
  },
  fullWidthImageContainer: {
    aspectRatio: 1.59,
  },
  image: {
    width: '100%',
    height: '100%',
    backgroundColor: Colors.light.secondaryBackground,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  offerContent: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  offerTitleContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 3,
    maxWidth: '80%',
  },
  offerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'center',
    flexShrink: 1,
  },
  content: {
    padding: 12,
  },
  name: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
    marginBottom: 8,
    lineHeight: 20,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  timeRemaining: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    backgroundColor: Colors.light.secondaryBackground,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
}); 