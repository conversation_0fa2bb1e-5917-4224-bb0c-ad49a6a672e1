import { create } from 'zustand';

interface Template {
  id: string;
  imageUrl: string;
  name: string;
  tags: Array<{
    id: string;
    name: string;
    slug: string;
  }>;
}

interface TemplateStore {
  selectedTemplate: Template | null;
  selectedMediaId: string | null;
  selectedTemplateUrl: string | null;
  selectedMediaUrl: string | null;
  setSelectedTemplate: (template: Template | null) => void;
  setSelectedMedia: (mediaId: string | null, mediaUrl: string | null) => void;
  clearSelection: () => void;
}

export const useTemplateStore = create<TemplateStore>((set) => ({
  selectedTemplate: null,
  selectedMediaId: null,
  selectedTemplateUrl: null,
  selectedMediaUrl: null,
  setSelectedTemplate: (template) => set({ 
    selectedTemplate: template, 
    selectedTemplateUrl: template?.imageUrl || null,
    selectedMediaId: null,
    selectedMediaUrl: null
  }),
  setSelectedMedia: (mediaId, mediaUrl) => set({ 
    selectedMediaId: mediaId, 
    selectedMediaUrl: mediaUrl,
    selectedTemplate: null,
    selectedTemplateUrl: null
  }),
  clearSelection: () => set({ 
    selectedTemplate: null, 
    selectedMediaId: null,
    selectedTemplateUrl: null,
    selectedMediaUrl: null
  }),
})); 