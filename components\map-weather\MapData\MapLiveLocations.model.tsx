export interface GetLiveLocationsByPartyID {
    result: Result;
}

export interface Result {
    liveLocations: LiveLocation[];
}

export interface LiveLocation {
    ETA:               null | string;
    allowLiveTracking: string;
    coordinates:       Coordinates;
    guest:             Guest;
    id:                string;
}

export interface Coordinates {
    latitude:  number | null;
    longitude: number | null;
}

export interface Guest {
    user: User;
}

export interface User {
    firstName:      string;
    profilePicture: null | string;
    lastName:       null | string;
}
