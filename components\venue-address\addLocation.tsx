import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, RefreshControl, SectionList } from 'react-native';
import { Searchbar, Button } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { Borders, Colors, Icons, Spacing, Typography } from '@/constants/DesignSystem';
import SavedAddressCard from './savedAddressCard';
import { ADDRESS_BOOK_QUERY } from '@/graphql/queries/addressBook';
import { useMutation, useQuery } from '@apollo/client';
import { AddPartyRootStackList } from '@/app/(home)/EventsDashboard/AddPartyRootStackList';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { getCurrentLocationAndPlaceId, fetchPlaceName } from '@/services/googleMapsService';
import { LoadingIndicator } from '@/components/UI/ReusableComponents/LoadingIndicator'
import { useVenueStore } from '@/store/venueStore';
import { AddressBook } from '@/app/(home)/EventsDashboard/AddParty';
import { DELETE_VENUE_Details, UPDATE_VENUE_DETAILS } from '@/graphql/mutations/venueAddress';
import { StackScreenProps } from '@react-navigation/stack';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { Next, Location } from '@/components/icons';

export interface Suggestion {
    placeId: string;
    name: string;
    address: string;
}

interface UserLocation {
    latitude: number;
    longitude: number;
}

type SectionListItem = AddressBook | Suggestion;

interface AddressSection {
  title: string;
  data: AddressBook[];
  type: 'addressBook';
}

interface SuggestionSection {
  title: string;
  data: Suggestion[];
  type: 'googleSuggestion';
}

type AddLocationRouteProps = StackScreenProps<AddPartyRootStackList, 'AddLocation'>;

type SectionType = AddressSection | SuggestionSection;

export default function AddLocation({route}: AddLocationRouteProps) {
    const { partyId, eventGroupId, isEditing, onPressUpdate } = route.params;
    const [searchQuery, setSearchQuery] = useState('');
    const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
    const [currentLocation, setCurrentLocation] = useState<UserLocation | null>(null);
    const [currentLocationFetchedName, setCurrentLocationFetchedName] = useState<string | null>(null);
    const [addressBooks, setAddressBooks] = useState<AddressBook[]>([]);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const pageSize = 50;
    const inputRef = useRef(null);
    const [refreshing, setRefreshing] = useState(false);
    const setVenueDetails = useVenueStore((state) => state.setVenueDetails);
    const [updateVenueDetails] = useMutation(UPDATE_VENUE_DETAILS);
    const [deleteVenueDetails] = useMutation(DELETE_VENUE_Details);

    const createFilter = (query: string) => {
        if (!query) return null;
        return {
            label: query
        }
    }

    const { data: addressBookData, loading: addressBookLoading, fetchMore, refetch } = useQuery(ADDRESS_BOOK_QUERY, {
        variables: {
            pagination: {
                limit: pageSize,
                skip: 0
            },
            filter: createFilter(searchQuery)
        },
        fetchPolicy: 'network-only',
        skip: isEditing,
    });

    const navigation = useNavigation<NativeStackNavigationProp<AddPartyRootStackList>>();

    useEffect(() => {
        if (searchQuery) {
            setPage(1);
            setAddressBooks([]);
        } else {
            setPage(1);
            setAddressBooks([]);
            refetch({
                pagination: { limit: pageSize, skip: 0 },
                filter: null
            });
        }
    }, [searchQuery]);


    useEffect(() => {
        if (addressBookData?.getAddressBooks?.result?.addressBooks) {
            const newAddressBooks = addressBookData.getAddressBooks.result.addressBooks;
            setAddressBooks(prev => {
                const combined = page === 1 || searchQuery ? newAddressBooks : [...prev, ...newAddressBooks];
                const uniqueBooks = Array.from(new Map(combined.map((book: AddressBook) => [book.id, book])).values()) as AddressBook[];
                return uniqueBooks;
            });
            const pagination = addressBookData.getAddressBooks.pagination;
            setHasMore(pagination.currentPage < pagination.totalPages);
        }
    }, [addressBookData, page, searchQuery]);

    const loadMoreAddresses = async () => {
        if (!hasMore || isLoadingMore) return;

        setIsLoadingMore(true);
        try {
            const nextPage = page + 1;
            await fetchMore({
                variables: {
                    pagination: {
                        limit: pageSize,
                        skip: (nextPage - 1) * pageSize
                    },
                    filter: createFilter(searchQuery)
                }
            });
            setPage(nextPage);
        } catch (error) {
            console.error('Error loading more addresses:', error);
        } finally {
            setIsLoadingMore(false);
        }
    };

    useEffect(() => {
        const fetchCurrentLocation = async () => {
            setIsLoadingCurrentLocation(true);
            setCurrentLocationFetchedName(null);
            try {
                const {location, placeId: fetchedPlaceId} = await getCurrentLocationAndPlaceId();
                setCurrentLocation({
                    latitude: location.coords.latitude,
                    longitude: location.coords.longitude
                });
                setCurrentLocationPlaceId(fetchedPlaceId);

                if (fetchedPlaceId) {
                    const name = await fetchPlaceName(fetchedPlaceId);
                    setCurrentLocationFetchedName(name || 'Current Location');
                } else {
                    setCurrentLocationFetchedName('Current Location');
                }
            } catch (error) {
                console.error('Error fetching current location or its name:', error);
                setCurrentLocationPlaceId(null);
                setCurrentLocationFetchedName('Current Location');
            } finally {
                setIsLoadingCurrentLocation(false);
            }
        };
        fetchCurrentLocation();
    }, []);

    const fetchLocationSuggestions = async (input: string) => {
        if (!input) {
            setSuggestions([]);
            return;
        }
        const apiKey = process.env.EXPO_PUBLIC_GOOGLE_API_KEY;
        const url = process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_URL;

        try {
          let apiUrl = `${url}/place/autocomplete/json?input=${input}&key=${apiKey}`;
          if (currentLocation) {
            apiUrl += `&location=${currentLocation.latitude}%2C${currentLocation.longitude}&radius=5000`;
          }
          const response = await fetch(apiUrl);
          const json = await response.json();
    
          if (json.status === 'OK') {
            const results = json.predictions.map((prediction: any) => ({
              placeId: prediction.place_id,
              name: prediction.structured_formatting.main_text,
              address: prediction.description,
            }));
    
            setSuggestions(results);
          } else {
            setSuggestions([]);
          }
        } catch (error) {
          console.error('Error fetching suggestions:', error);
          setSuggestions([]);
        }
      };

    useEffect(() => {
        const debounceFetch = setTimeout(() => {
            if (searchQuery) {
                fetchLocationSuggestions(searchQuery);
                setPage(1);
                refetch({
                    pagination: { limit: pageSize, skip: 0 },
                    filter: createFilter(searchQuery)
                });
            } else {
                setSuggestions([]);
                setPage(1);
                refetch({
                    pagination: { limit: pageSize, skip: 0 },
                    filter: null
                });
            }
        }, 500);

        return () => clearTimeout(debounceFetch);
    }, [searchQuery, currentLocation, refetch]);

    const [currentLocationPlaceId, setCurrentLocationPlaceId] = useState<string | null>(null);
    const [isLoadingCurrentLocation, setIsLoadingCurrentLocation] = useState(true);

    const onRefresh = React.useCallback(async () => {
        setRefreshing(true);
        setPage(1);
        setHasMore(true);
        try {
            await refetch({
                pagination: {
                    limit: pageSize,
                    skip: 0
                },
                filter: createFilter(searchQuery)
            });
        } catch (error) {
            console.error('Error refreshing addresses:', error);
        } finally {
            setRefreshing(false);
        }
    }, [refetch, pageSize, searchQuery]);

    const renderFooter = () => {
        if (!isLoadingMore) return null;
        return (
            <View style={styles.footerLoader}>
                <ActivityIndicator size="small" color={Colors.text.primary} />
            </View>
        );
    };

    const handleOnPress = (addressBook: AddressBook) => {

        setVenueDetails({
            isNew: false,
            placeId: addressBook.venueAddress.placeId,
            name: addressBook.venueAddress.address,
            title: addressBook.label,
            directions: addressBook.venueAddress.directions,
            venueAddressId: addressBook.venueAddress.id
        })

        navigation.goBack()
    }

    const handleOnPressEdit = (addressBook: AddressBook) => {
        navigation.navigate('AddressDetails', {
            partyId: partyId,
            eventGroupId: eventGroupId ?? undefined,
            title: addressBook.label,
            address: addressBook.venueAddress.address,
            additionalDetails: addressBook.venueAddress.directions,
            isEditing: true,
            onPressUpdate: async (params: {placeId: string, label: string, name: string, directions: string}) => {
                await handleOnPressUpdate(addressBook, params);
                refetch();
            }
        })
    }

    const handleOnPressUpdate = async (addressBook: AddressBook, params: {placeId: string, label: string, name: string, directions: string}) => {
        await updateVenueDetails({
            variables:{
                updateVenueAddressId: addressBook.venueAddress.id,
                input: {
                  placeId: params.placeId,
                  name: params.name,
                  directions: params.directions
                },
                updateAddressBookId: addressBook.id,
                updateAddressBookInput2: {
                  label: params.label
                }
              }
        })
    }

    const handleOnPressDelete = async (addressBook: AddressBook) => {
        await deleteVenueDetails({
            variables: {
                deleteVenueAddressId: addressBook.venueAddress.id,
                deleteAddressBookId: addressBook.id
            }
        })
        refetch();
    }

    return (
        <BottomSheetModalProvider>
        <View style={styles.mainContainer}>
            <Searchbar
                placeholder='Search for a building, street name or area'
                placeholderTextColor={Colors.mediumGray}
                style={styles.input}
                value={searchQuery}
                onChangeText={setSearchQuery}
                onClearIconPress={() => setSearchQuery('')}
                inputStyle={{ fontSize: Typography.fontSize.md }}
                numberOfLines={1}
                ref={inputRef}
            />
            {searchQuery ? (
                <SectionList<SectionListItem, SectionType>
                    sections={
                        isEditing
                            ? [{ title: 'SUGGESTIONS', data: suggestions, type: 'googleSuggestion' }]
                            : [
                                { title: 'SAVED ADDRESSES', data: addressBooks, type: 'addressBook' },
                                { title: 'SUGGESTIONS', data: suggestions, type: 'googleSuggestion' }
                            ]
                    }
                    keyboardShouldPersistTaps="handled"
                    keyExtractor={(item: SectionListItem, index: number) => {
                        if ('placeId' in item) {
                            return `suggestion_${item.placeId}`;
                        } else {
                            return `addressbook_${(item as AddressBook).id}`;
                        }
                    }}
                    renderItem={({ item, section }: { item: SectionListItem; section: SectionType }) => {
                        if (section.type === 'addressBook') {
                            const addressBookItem = item as AddressBook;
                            return (
                                <SavedAddressCard
                                    address={addressBookItem}
                                    ellipsis={false}
                                    onPress={() => { handleOnPress(addressBookItem) }}
                                />
                            );
                        }
                        if (section.type === 'googleSuggestion') {
                            const suggestionItem = item as Suggestion;
                            return (
                                <TouchableOpacity onPress={() => navigation.navigate('ConfirmLocation', {
                                    placeId: suggestionItem.placeId,
                                    name: suggestionItem.name,
                                    partyId: partyId,
                                    eventGroupId: eventGroupId ?? undefined,
                                    isEditing: isEditing ?? false,
                                    onPressUpdate: onPressUpdate
                                })}>
                                    <View style={styles.suggestionItem}>
                                        <Location size={Icons.size.md} color={Colors.text.primary} />
                                        <Text style={styles.suggestionText}>{suggestionItem.address}</Text>
                                    </View>
                                </TouchableOpacity>
                            );
                        }
                        return null;
                    }}
                    renderSectionHeader={({ section }: { section: SectionType }) => {
                        if (section.data.length === 0) {
                            return null; 
                        }
                        return (
                            <View style={styles.savedAddressesHeader}>
                                <Text style={styles.savedAddressesText}>{section.title}</Text>
                            </View>
                        );
                    }}
                    ListEmptyComponent={
                        (addressBookLoading && suggestions.length === 0) ? <LoadingIndicator /> :
                        (!addressBookLoading && addressBooks.length === 0 && suggestions.length === 0) ? (
                            <View style={{ alignItems: 'center', paddingTop: Spacing.xl }}>
                                <Text style={styles.noAddressesText}>No results found for "{searchQuery}"</Text>
                            </View>
                        ) : null
                    }
                    onEndReached={({ distanceFromEnd }) => {
                        if (distanceFromEnd < 0) return;
                        loadMoreAddresses();
                    }}
                    onEndReachedThreshold={0.5}
                    ListFooterComponent={renderFooter}
                    refreshControl={
                        <RefreshControl
                            refreshing={refreshing}
                            onRefresh={onRefresh}
                            colors={[Colors.text.primary]}
                            tintColor={Colors.text.primary}
                        />
                    }
                    stickySectionHeadersEnabled={false}
                    style={styles.suggestionsContainer}
                />
            ) : (
            <>
                <Button
                    mode='contained'
                    onPress={() => {
                        if (currentLocationPlaceId && currentLocation) {
                            navigation.navigate('ConfirmLocation', { 
                                placeId: currentLocationPlaceId, 
                                name: currentLocationFetchedName || "Current Location",
                                partyId: partyId,
                                eventGroupId: eventGroupId ?? undefined
                            })
                        }}}
                    loading={isLoadingCurrentLocation}
                    disabled={isLoadingCurrentLocation || !currentLocationPlaceId}
                    style={styles.buttonMain}
                    labelStyle={{color: Colors.primary}}
                    theme={{ colors: { primary: Colors.primary } }}
                    icon={() => <Location size={Icons.size.md} color={Colors.primary} />}
                    contentStyle={{ alignItems: 'center' }}
                >
                    {
                    (!isLoadingCurrentLocation) && 
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'center',
                            flexGrow: 1,
                            flexShrink: 1,
                        }}
                        >
                            <Text style={styles.buttonText}>
                                Use your current location / add address
                            </Text>
                            <Next size={Icons.size.md} color={Colors.text.primary} />
                        </View>
                    }
                </Button>
                {!isEditing && (
                    <>
                        <View style={styles.savedAddressesHeader}>
                            <Text style={styles.savedAddressesText}>SAVED ADDRESSES</Text>
                        </View>
                        {addressBookLoading && page === 1 ? (
                            <LoadingIndicator />
                        ) : (
                            <FlatList
                                style={styles.savedAddressesContainer}
                                data={addressBooks}
                                renderItem={({ item }) => (
                                    <SavedAddressCard
                                        address={item}
                                        onEdit={() => {handleOnPressEdit(item)}}
                                        onDelete={() => {handleOnPressDelete(item)}}
                                        onPress={() =>{handleOnPress(item)}}
                                    />
                                )}
                                keyExtractor={(item) => item.id}
                                onEndReached={loadMoreAddresses}
                                onEndReachedThreshold={0.5}
                                ListFooterComponent={renderFooter}
                                refreshControl={
                                    <RefreshControl
                                        refreshing={refreshing}
                                        onRefresh={onRefresh}
                                        colors={[Colors.text.primary]}
                                        tintColor={Colors.text.primary}
                                    />
                                }
                                ListEmptyComponent={
                                    <View style={{ alignItems: 'center', paddingTop: Spacing.xl }}>
                                        <Text style={styles.noAddressesText}>You have no saved addresses</Text>
                                    </View>
                                }
                            />
                        )}
                    </>
                )}
            </>
        )}
        </View>
        </BottomSheetModalProvider>
    )
}

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: Colors.background.primary,
    },
    savedAddressesHeader: {
        backgroundColor: Colors.background.primary,
        borderBottomWidth: Borders.width.thin,
        borderColor: Colors.border.dark,
        marginHorizontal: Spacing.md,
        paddingTop: Spacing.md,
        paddingBottom: Spacing.sm,
        paddingHorizontal: Spacing.sm,
    },
    savedAddressesText: {
        color: Colors.text.secondary,
        fontSize: Typography.fontSize.md,
        fontWeight: Typography.fontWeight.bold,
    },
    savedAddressesContainer: {
        flex: 1,
        backgroundColor: Colors.background.primary,
        borderBottomWidth: Borders.width.thin,
        borderColor: Colors.border.medium,
    },
    input: {
        marginHorizontal: Spacing.md,
        marginVertical: Spacing.sm,
        backgroundColor: Colors.background.tertiary,
        borderRadius: Borders.radius.md
    },
    buttonMain: {
        backgroundColor: Colors.background.secondary,
        borderRadius: Borders.radius.md,
        width: '92%',
        height: 70,
        justifyContent: 'center',
        alignItems: 'center',
        alignSelf: 'center',
        marginVertical: Spacing.sm,
    },
    buttonText: {
        color: Colors.text.primary,
        fontSize: Typography.fontSize.md,
        marginRight: Spacing.xs,
    },
    noAddressesText: {
        color: Colors.text.secondary,
        fontSize: Typography.fontSize.md,
        fontWeight: Typography.fontWeight.bold,
    },
    suggestionsContainer: {
        flex: 1,
        marginHorizontal: Spacing.md,
    },
    suggestionItem: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: Spacing.md,
        gap: Spacing.sm,
    },
    suggestionText: {
        fontSize: Typography.fontSize.md,
        color: Colors.text.primary,
    },
    footerLoader: {
        paddingVertical: Spacing.md,
        alignItems: 'center',
    },
})