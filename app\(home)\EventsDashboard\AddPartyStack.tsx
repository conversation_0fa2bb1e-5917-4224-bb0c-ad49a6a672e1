import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Platform } from 'react-native';
import AddParty from './AddParty';
import PartyInviteTemplates from '../PartyInviteTemplates/PartyInviteTemplates';
import { AddPartyRootStackList } from './AddPartyRootStackList';
import { PaperProvider } from 'react-native-paper';
import AddLocation from '@/components/venue-address/addLocation';
import ConfirmLocation from '@/components/venue-address/confirmLocation';
import AddressDetails from '@/components/venue-address/addressDetails';
const AddPartyStack = createNativeStackNavigator<AddPartyRootStackList>();

const AddPartyPageStack = () => {
  return (
    <PaperProvider>
        <AddPartyStack.Navigator
        screenOptions={{
          headerShown: true,
        }}
      >
        <AddPartyStack.Screen
          name="AddParty"
          component={AddParty}
          options={{
            headerShown: false,
          }}
        />

          <AddPartyStack.Screen
            name="EditParty"
            component={AddParty}
            options={{
              headerShown: false
            }}
          />

        <AddPartyStack.Screen
          name="PartyInviteTemplate"
          component={PartyInviteTemplates}
          options={{
            headerShown: true, 
            title: 'Party Invite Templates',
            headerTitleAlign: 'center',
            headerTitleStyle: {
              fontFamily: 'Plus Jakarta Sans',
              fontSize: 16,
              color: '#000000',
            },  
          }}
          />
          <AddPartyStack.Screen
            name="AddLocation"
            component={AddLocation}
            options={{
              headerShown: true,
              title: 'Venue',
              headerTitleAlign: 'center',
              headerTitleStyle: {
                fontFamily: 'Plus Jakarta Sans',
                fontSize: 16,
                color: '#000000',
              },
            }}
          />
          <AddPartyStack.Screen
            name="ConfirmLocation"
            component={ConfirmLocation}
            options={{
              headerShown: true,
              title: 'Confirm Location',
              headerTitleAlign: 'center',
              headerTitleStyle: {
                fontFamily: 'Plus Jakarta Sans',
                fontSize: 16,
                color: '#000000',
              },
            }}
          />
          <AddPartyStack.Screen
            name="AddressDetails"
            component={AddressDetails}
            options={{
              headerShown: true,
              title: 'Address Details',
              headerTitleAlign: 'center',
              headerTitleStyle: {
                fontFamily: 'Plus Jakarta Sans',
                fontSize: 16,
                color: '#000000',
              },
            }}
          />
      </AddPartyStack.Navigator>
    </PaperProvider>
  );
};

export default AddPartyPageStack;

