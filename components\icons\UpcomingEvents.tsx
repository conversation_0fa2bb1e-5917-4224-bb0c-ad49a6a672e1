import * as React from "react";
import Svg, { <PERSON>, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const UpcomingEvents = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Upcoming_events-1_svg__a)">
      <Path
        fill="url(#Upcoming_events-1_svg__b)"
        stroke="transparent"
        d="M8.75.5c.434 0 .786.352.786.786v.785h.785c.651 0 1.179.528 1.179 1.179v7.071c0 .651-.528 1.179-1.179 1.179H1.68C1.028 11.5.5 10.972.5 10.321V3.25c0-.65.528-1.179 1.179-1.179h.785v-.785a.786.786 0 0 1 1.572 0v.785h3.929v-.785c0-.434.351-.786.785-.786M4.66 4.862a.422.422 0 0 0-.583.583l.054.067 1.101 1.102-1.101 1.104a.421.421 0 0 0 .596.595l1.4-1.4.053-.064a.424.424 0 0 0-.052-.533l-1.401-1.4zm2.528 0a.422.422 0 0 0-.584.583l.054.067L7.76 6.614 6.658 7.718a.422.422 0 0 0 .596.595l1.401-1.4.052-.064a.42.42 0 0 0-.052-.533l-1.401-1.4z"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Upcoming_events-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.828}
        y2={14.029}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Upcoming_events-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Upcoming_events_svg__a)"
    >
      <Path d="M1.77 2.154a.77.77 0 0 0-.77.77v7.307a.77.77 0 0 0 .77.769h8.46a.77.77 0 0 0 .77-.77V2.924a.77.77 0 0 0-.77-.77H8.693M3.308 1v2.308M8.692 1v2.308M3.308 2.154h3.846" strokeWidth={strokeWidth} fill="none" />
      <Path d="m6.494 8.434 1.492-1.492L6.494 5.45M4.462 8.215l1.492-1.492L4.462 5.23" strokeWidth={strokeWidth} fill="none" />
    </G>
    <Defs>
      <ClipPath id="Upcoming_events_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default UpcomingEvents;
