query GetPartyById($getPartyByIdId: ID!) {
  getPartyById(id: $getPartyByIdId) {
    ... on PartyResponse {
      message
      result {
        party {
          id
          name
          weather {
            weatherUrl
            avgTempC
            avgTempF
            condition {
              icon
              text
            }
          }
          time
          partyType {
            portraitImage
          }
          event {
            mainHost {
              userId {
                id
                firstName
                lastName
                profilePicture
              }
            }
          }
        }
      }
    }
    ... on PartyErrorResponse {
      errors {
        field
        message
      }
      status
    }
  }
}