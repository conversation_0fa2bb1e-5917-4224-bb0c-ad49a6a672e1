import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  LayoutAnimation,
  UIManager,
  Platform,
} from 'react-native';
import { Activity } from '@/app/(home)/PartyDetails/NewPartyDetailsResponse.model';
import ActivityCommentRow from './ActivityCommentRow';
import { useMutation } from '@apollo/client';
import { DELETE_MESSAGE } from './ActivityFeed.data';
import { useUserStore } from '@/app/auth/userStore';
import { useToast } from '@/components/Toast';
import { useActionSheet } from '@expo/react-native-action-sheet';
import CurvedLineConnector from './CurvedLineConnector';
import { More } from '@/components/icons';
import { Colors, Icons } from '@/constants/DesignSystem';

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface ActivitiesContainerProps {
  activities: Activity[];
  partyId: string;
  userRole: string;
  handleEdit: (id: string, message: string) => void;
  onLocation: (isEnabled: boolean) => Promise<void>;
}

const ActivitiesContainer: React.FC<ActivitiesContainerProps> = ({
  activities,
  partyId,
  userRole,
  handleEdit,
  onLocation,
}) => {
  const [avatarCoordinates, setAvatarCoordinates] = useState<Record<string, { x: number; y: number }>>({});

  const [messages, setMessages] = useState<Activity[]>([]);
  const [expandedActivities, setExpandedActivities] = useState<Set<string>>(new Set());
  const [deleteActivityMessage] = useMutation(DELETE_MESSAGE);
  const userData = useUserStore((state) => state.userData);
  const toast = useToast();
  const { showActionSheetWithOptions } = useActionSheet();
  const [mainMessageCoordinate, setMainMessageCoordinate] = useState<{ x: number; y: number } | null>(null);
  const [replyMessageCoordinate, setReplyMessageCoordinate] = useState<{ x: number; y: number } | null>(null);

  useEffect(() => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.linear);
    setMessages(activities);
  }, [activities]);

  const handleToggle = (activityId: string) => {
    setExpandedActivities((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(activityId)) {
        newSet.delete(activityId);
      } else {
        newSet.add(activityId);
      }
      return newSet;
    });
  };

  const handleAvatarLayout = (activityId: string, x: number, y: number) => {
    setAvatarCoordinates((prev) => ({
      ...prev,
      [activityId]: { x, y },
    }));
  };

  const isHost = (activity: Activity) => {
    return userRole === 'MAIN_HOST' && activity.type !== 'SYSTEM';
  }

  const isAuthor = (activity: Activity) => {
    return (userData?.id === activity.createdBy?.id) && activity.type !== 'SYSTEM';
  }

  const handleOpenSheet = (activity: Activity) => {
    const isHostActivity = isHost(activity);
    const isAuthorActivity = isAuthor(activity);

    const options: string[] = [];
    if (isAuthorActivity) {
      options.push('Edit');
    }
    if (isHostActivity || isAuthorActivity) {
      options.push('Delete');
    }
    options.push('Cancel');

    const cancelButtonIndex = options.indexOf('Cancel');
    const destructiveButtonIndex = options.indexOf('Delete');

    showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex,
        destructiveButtonIndex,
        title: 'What would you like to do with this comment?',
      },
      (buttonIndex?: number) => {
        if (buttonIndex === undefined) {
          return;
        }
        if (options[buttonIndex] === 'Edit') {
          handleEdit(activity.id, activity.text ?? '');
        } else if (options[buttonIndex] === 'Delete') {
          handleDelete(activity.id);
        }
      }
    );
  };

  const handleDelete = (activityId: string) => {
    const activity = messages.find((message) => message.id === activityId);

    if (!activity) {
      return;
    }

    if (userRole !== 'MAIN_HOST' && userData?.id !== activity.createdBy.id) {
      return;
    }

    if (activity.type === 'SYSTEM') {
      console.log('Cannot delete System Messages');
      return;
    }

    const deleteActivity = async () => {
      try {
        const deleteResponse = await deleteActivityMessage({
          variables: {
            deleteMessageId: activityId,
          },
        });
        const isMainMessage = messages.some(
          (message) => message.id === activityId && message.parentMessageId === null
        );

        let updatedMessages = messages.filter((message) => {
          return isMainMessage
            ? message.id !== activityId && message.parentMessageId !== activityId
            : message.id !== activityId;
        });
        LayoutAnimation.configureNext(LayoutAnimation.Presets.linear);
        setMessages(updatedMessages);
        toast.success('Comment deleted successfully');
      } catch (error) {
        console.log('Delete activity error:', error);
        alert('An error occurred while deleting the activity');
      }
    };

    Alert.alert(
      'Delete Activity',
      'Are you sure you want to delete this activity?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: deleteActivity,
        },
      ],
      { cancelable: true }
    );
  };

  return (
    <View style={styles.container}>
      {messages
        .filter((activity) => !activity.parentMessageId)
        .map((mainActivity) => {
          const threadActivities = messages.filter(
            (activity) => activity.parentMessageId === mainActivity.id
          );
          const isExpanded = expandedActivities.has(mainActivity.id);

          return (
            <View key={`main-${mainActivity.id}`} style={{ position: 'relative', marginBottom: 20 }}>
              {/* TODO: @Sraavan - We need these lines. Commenting for PR
              {mainActivity && replyMessageCoordinate && (

                <CurvedLineConnector
                  start={mainMessageCoordinate || { x: 0, y: 0 }}
                  end={replyMessageCoordinate || { x: 0, y: 0 }}
                />
              )} */}

              <View
                style={{ flexDirection: 'row' }}
                onLayout={(e) => {
                  const { x, y, width, height } = e.nativeEvent.layout;
                  setMainMessageCoordinate({ x: x + width / 2, y: y + height });
                }}
              >
                <ActivityCommentRow
                  activity={mainActivity}
                  canDelete={true}
                  partyId={partyId}
                />
                {(isHost(mainActivity) || isAuthor(mainActivity)) && (
                  <TouchableOpacity onPress={() => handleOpenSheet(mainActivity)} style={{ marginTop: 7 }}>
                    <More
                      size={Icons.size.md}
                      color={Colors.black}
                    />
                  </TouchableOpacity>
                )}
              </View>

              {threadActivities.map((reply) => (
                <View
                  key={`reply-${reply.id}`}
                  style={styles.threadMessage}
                  onLayout={(e) => {
                    const { x, y, width, height } = e.nativeEvent.layout;
                    setReplyMessageCoordinate({ x, y: y + height / 2 });
                  }}
                >
                  <View style={styles.row}>
                  <ActivityCommentRow
                    key={`row-${reply.id}`}
                    activity={reply}
                    canDelete={true}
                    partyId={partyId}
                  />
                  {(isHost(reply) || isAuthor(reply)) && (
                    <TouchableOpacity onPress={() => handleOpenSheet(reply)} style={{ marginTop: 7 }}>
                      <More
                        size={Icons.size.md}
                        color={Colors.black}
                      />
                    </TouchableOpacity>
                  )}
                  </View>
                </View>
              ))}
            </View>
          );
        })}
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingVertical: 10,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  threadMessage: {
    marginLeft: 35,
    paddingVertical: 25,
  },
  viewAllButtonText: {
    color: '#BBBBBB',
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: '800',
    marginTop: 8,
    marginBottom: 8,
    alignSelf: 'flex-start',
  },
  lineVertical: {
    position: 'absolute',
    left: 25,
    top: 50,
    height: '40%',
    width: 2,
    backgroundColor: 'transparent',
  },
  lineHorizontal: {
    position: 'absolute',
    left: 10,
    bottom: 20,
    height: 2,
    width: 20,
    backgroundColor: '#BBBBBB',
  },
  lineVisible: {
    backgroundColor: '#BBBBBB',
  },
});

export default ActivitiesContainer