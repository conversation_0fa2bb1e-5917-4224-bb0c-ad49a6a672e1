import { View, Pressable, Image } from 'react-native';
import { Surface, Text } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { styles } from '../TaskList/CreateTask.styles';
import { FilePreviewItemProps } from './Attachments.types';
import { useRouter } from 'expo-router';
import { DocumentPickerAsset } from 'expo-document-picker';
import { useAttachmentPreviewStore } from './attachmentPreview.store';
import { Cross, Video } from '@/components/icons';
import { Colors, Icons } from '@/constants/DesignSystem';

export function FilePreviewItem({
  file,
  taskId,
  index,
  onFileRemove,
  getFileTypeIcon,
  documentId,
}: FilePreviewItemProps) {
  const router = useRouter();
  const setSelectedFile = useAttachmentPreviewStore(state => state.setSelectedFile);
  const isExistingFile = 'documentUrl' in file;
  const fileUri = isExistingFile ? file.documentUrl : file.uri;
  const fileName = isExistingFile ? file.name : file.name;
  const fileType = isExistingFile ? file.documentType : file.mimeType;

  const isImage = isExistingFile
    ? (fileType?.toLowerCase().includes('image') || 
       /^https:\/\/.*\.blob\.core\.windows\.net\/.*\.(jpg|jpeg|png|gif|webp)$/i.test(fileUri))
    : fileType?.startsWith('image/');

  const isVideo = isExistingFile
    ? (fileType?.toLowerCase().includes('video') || 
       /^https:\/\/.*\.blob\.core\.windows\.net\/.*\.(mp4|mov|avi|mkv|webm)$/i.test(fileUri) ||
       /\.(mp4|mov|avi|mkv|webm)$/i.test(fileUri))
    : fileType?.startsWith('video/');

  const fileWithId = isExistingFile 
    ? {
        uri: file.documentUrl,
        name: file.name,
        mimeType: file.mimeType || file.documentType || 'application/octet-stream',
        size: file.size || 0,
        id: file.id,
        documentId: file.id
      } as DocumentPickerAsset & { id?: string; documentId?: string }
    : { 
        ...file as DocumentPickerAsset,
        documentId 
      };

  const handlePress = () => {
    setSelectedFile(fileWithId);
    router.push(`/(home)/Task/attachment-preview?taskId=${taskId}`);
  };

  const handleRemove = (e: any) => {
    e.stopPropagation();
    onFileRemove(fileWithId);
  };

  const renderPreviewContent = () => {
    if (isImage) {
      return (
        <Image 
          source={{ uri: fileUri }}
          style={styles.filePreviewImage}
          resizeMode="cover"
        />
      );
    }

    if (isVideo) {
      return (
        <>
          <View style={styles.fileIconContainer}>
            <Video size={Icons.size.md} color={Colors.primary} />
          </View>
          <View style={styles.fileNameOverlay}>
            <Text 
              numberOfLines={2} 
              ellipsizeMode="tail"
              style={styles.fileNameText}
            >
              {fileName}
            </Text>
          </View>
        </>
      );
    }

    return (
      <>
        <View style={styles.fileIconContainer}>
          <MaterialCommunityIcons 
            name={getFileTypeIcon(file).icon as keyof typeof MaterialCommunityIcons.glyphMap}
            size={24}
            color={getFileTypeIcon(file).color}
          />
        </View>
        <View style={styles.fileNameOverlay}>
          <Text 
            numberOfLines={2} 
            ellipsizeMode="tail"
            style={styles.fileNameText}
          >
            {fileName}
          </Text>
        </View>
      </>
    );
  };

  return (
    <Pressable onPress={handlePress}>
      <Surface mode="flat" style={styles.filePreviewBox}>
        <View style={styles.filePreviewContainer}>
          {renderPreviewContent()}
          <Pressable 
            style={styles.filePreviewOverlay}
            onPress={handleRemove}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <View style={styles.removeFileButton}>
              <Cross 
                size={Icons.size.md}
                color={Colors.black}
              />
            </View>
          </Pressable>
        </View>
      </Surface>
    </Pressable>
  );
} 