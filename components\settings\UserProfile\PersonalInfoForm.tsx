import { StyleSheet, View } from 'react-native'
import React, { useState } from 'react'
import { TextInput, Button, Text, Snackbar } from 'react-native-paper'
import { useUser } from '@clerk/clerk-expo'
import { useMutation } from '@apollo/client'
import { UPDATE_USER } from '../Settings.data'
import { useUserStore } from '@/app/auth/userStore'
import { userStorage } from '@/app/auth/userStorage'
import { PhoneNumberInput } from '@/components/UI/ReusableComponents/PhoneNumberInput'
import OTPVerificationModal from './OTPVerificationModal'
import * as z from 'zod'
import { regexValidators } from '@/app/(home)/utils/reusableFunctions'
import type { EmailAddressResource, PhoneNumberResource } from '@clerk/types'
import { CircleCheck } from '@/components/icons'
import { Colors, Icons} from '@/constants/DesignSystem'

interface PersonalInfoFormProps {
    userData: {
        email: string | null
        emailVerified: boolean
        phone: string | null
        phoneVerified: boolean
        firstName: string
        lastName: string
        profilePicture?: string | null
        externalId: string | null
        id: string | null
        isActive: boolean
        isRegistered: boolean
        // ... other fields
    }
}

const personalInfoSchema = z.object({
    firstName: z.string()
        .min(1, 'First name is required')
        .max(50, 'First name cannot exceed 50 characters'),
    lastName: z.string()
        .min(1, 'Last name is required')
        .max(50, 'Last name cannot exceed 50 characters'),
    email: z.string()
        .refine(val => val === '' || regexValidators.isValidEmail(val), 'Please Enter Valid Email Address')
        .optional()
        .or(z.literal('')),
    phoneNumber: z.string() 
        .regex(/^\+[1-9]\d{0,4}[0-9]{10}$/, 'Phone number must be 10 digits'),
})

const PersonalInfoForm = ({ userData }: PersonalInfoFormProps) => {
    const { user } = useUser()
    const [updateUser] = useMutation(UPDATE_USER)
    const [formData, setFormData] = useState({
        firstName: userData?.firstName || '',
        lastName: userData?.lastName || '',
        email: userData?.email || '',
        phoneNumber: userData?.phone || '',
        rawPhoneNumber: userData?.phone?.slice(-10) || ''
    })
    const [errors, setErrors] = useState<Record<string, string>>({})
    const [isLoading, setIsLoading] = useState(false)
    const [verificationModalVisible, setVerificationModalVisible] = useState(false)
    const [verificationType, setVerificationType] = useState<'phone' | 'email'>('phone')
    const [isResendingCode, setIsResendingCode] = useState(false)
    const [showToast, setShowToast] = useState(false)
    const [pendingVerification, setPendingVerification] = useState<{
        type: 'phone' | 'email'
        value: string
    } | null>(null)
    const [toastMessage, setToastMessage] = useState('')

    const validateForm = () => {
        try {
            personalInfoSchema.parse(formData)
            setErrors({})
            return true
        } catch (err) {
            if (err instanceof z.ZodError) {
                const newErrors: Record<string, string> = {}
                err.errors.forEach((error) => {
                    if (error.path) {
                        newErrors[error.path[0]] = error.message
                    }
                })
                setErrors(newErrors)
            }
            return false
        }
    }

    const handleVerificationRequest = async (type: 'phone' | 'email', value: string) => {
        if (!user) return;
        try {
            setIsResendingCode(true)
            setPendingVerification({ type, value })

            if (type === 'phone') {
                const phoneNumbers = user.phoneNumbers || [];
                let phoneResource: PhoneNumberResource | undefined | null = phoneNumbers.find(p => p.phoneNumber === value);

                if (!phoneResource) {
                     phoneResource = await user.createPhoneNumber({ phoneNumber: value });
                }
                if (phoneResource) {
                    await phoneResource.prepareVerification();
                }

            } else {
                const emailAddresses = user.emailAddresses || [];
                let emailResource: EmailAddressResource | undefined | null = emailAddresses.find(e => e.emailAddress === value);

                if (!emailResource) {
                    emailResource = await user.createEmailAddress({ email: value });
                }
                if (emailResource) {
                    await emailResource.prepareVerification({ strategy: 'email_code' }  );
                }
            }

            setVerificationType(type)
            setVerificationModalVisible(true)
        } catch (error: any) {
            console.error(`Error preparing ${type} verification:`, JSON.stringify(error, null, 2));
            const clerkError = error.errors?.[0];
            const errorMessage = clerkError?.longMessage || clerkError?.message || error.message || `Failed to send verification code to ${type}`;
            setErrors(prev => ({
                ...prev,
                [type]: errorMessage
            }))
            setPendingVerification(null);
        } finally {
            setIsResendingCode(false)
        }
    }

    const handleVerifyCode = async (code: string) => {
        if (!pendingVerification || !user) return
        try {
            setIsLoading(true)
            let isVerified = false
            let verifiedResource: PhoneNumberResource | EmailAddressResource | undefined | null;

            if (pendingVerification.type === 'phone') {
                const phoneNumbers = user.phoneNumbers || []
                verifiedResource = phoneNumbers.find(p => p.phoneNumber === pendingVerification.value)
                if (verifiedResource) {
                    await verifiedResource.attemptVerification({ code })
                    await user.reload();
                    const updatedResource = user.phoneNumbers.find(p => p.id === verifiedResource?.id);
                    isVerified = updatedResource?.verification?.status === 'verified';
                 }
            } else {
                const emailAddresses = user.emailAddresses || []
                verifiedResource = emailAddresses.find(e => e.emailAddress === pendingVerification.value)
                if (verifiedResource) {
                    await verifiedResource.attemptVerification({ code })
                    await user.reload();
                    const updatedResource = user.emailAddresses.find(e => e.id === verifiedResource?.id);
                    isVerified = updatedResource?.verification?.status === 'verified';
                }
            }

            if (isVerified && verifiedResource) {
                 const updatePayload = {
                     [pendingVerification.type === 'phone' ? 'phone' : 'email']: pendingVerification.value,
                     [pendingVerification.type === 'phone' ? 'phoneVerified' : 'emailVerified']: true
                 };

                const { data: updateResponse } = await updateUser({
                    variables: {
                        input: updatePayload
                    }
                })

                if (updateResponse?.updateUser?.result?.user) {
                    await userStorage.saveUser(updateResponse.updateUser.result.user)
                    useUserStore.getState().setUserData(updateResponse.updateUser.result.user)
                    setVerificationModalVisible(false)
                    setPendingVerification(null)
                    setToastMessage(`${pendingVerification.type === 'phone' ? 'Phone' : 'Email'} verified successfully`)
                    setShowToast(true)
                } else {
                     throw new Error('Backend update failed after verification.');
                 }
            } else {
                 throw new Error( 'Verification code is incorrect or expired.');
             }
        } catch (error: any) {
            console.error(`Error verifying ${pendingVerification?.type}:`, JSON.stringify(error, null, 2));
            const clerkError = error.errors?.[0];
            const errorMessage = clerkError?.longMessage || clerkError?.message || error.message || 'Verification failed';
            setErrors(prev => ({
                ...prev,
                verification: errorMessage
            }))
        } finally {
            setIsLoading(false)
        }
    }

    const handleSave = async () => {
        if (!validateForm()) return
        if (formData.phoneNumber !== userData?.phone ) {
            const phoneResource = user?.phoneNumbers.find(p => p.phoneNumber === formData.phoneNumber);
            if (!phoneResource || phoneResource.verification?.status !== 'verified') {
                 setToastMessage('Please verify your new phone number before saving changes.')
                 setShowToast(true)
                 return;
             }
        }
        if (formData.email && formData.email !== userData?.email) {
            const emailResource = user?.emailAddresses.find(e => e.emailAddress === formData.email);
             if (!emailResource || emailResource.verification?.status !== 'verified') {
                 setToastMessage('Please verify your new email address before saving changes.')
                 setShowToast(true)
                 return;
             }
        }

        try {
            setIsLoading(true)
            let isEmailVerified = false
            if (formData.email) {
                const emailAddresses = user?.emailAddresses || []
                const currentEmail = emailAddresses.find(e => e.emailAddress === formData.email)
                isEmailVerified = currentEmail?.verification?.status === 'verified' || false
            }

            const profileImageUrl = userData?.profilePicture 
            const { data: updateResponse } = await updateUser({
                variables: {
                    input: {
                        firstName: formData.firstName,
                        lastName: formData.lastName,
                        email: formData.email || null,
                        profilePicture: profileImageUrl,
                    }
                }
            })

            if (updateResponse?.updateUser?.result?.user) {
                await userStorage.saveUser(updateResponse.updateUser.result.user)
                useUserStore.getState().setUserData(updateResponse.updateUser.result.user)
                setToastMessage('User details updated successfully')
                setShowToast(true)
            }
        } catch (error: any) {
            console.log("error updating user data", error)
            setErrors(prev => ({
                ...prev,
                submit: error.message || 'Failed to update profile'
            }))
            setToastMessage('Failed to update user details')
            setShowToast(true)
        } finally {
            setIsLoading(false)
        }
    }

    const shouldShowPhoneVerification = () => {
        if (!user) return false;
        const phoneResource = user.phoneNumbers.find(p => p.phoneNumber === formData.phoneNumber);
        return !phoneResource || phoneResource.verification?.status !== 'verified';
    }

    const shouldShowEmailVerification = () => {
        if (!user || !formData.email) return false;
        const emailResource = user.emailAddresses.find(e => e.emailAddress === formData.email);
        return !emailResource || emailResource.verification?.status !== 'verified';
    }

    return (
        <View style={styles.container}>
            <TextInput
                mode="flat"
                label="First Name*"
                value={formData.firstName}
                onChangeText={(value) => {
                    setFormData(prev => ({ ...prev, firstName: value }))
                    setErrors(prev => ({ ...prev, firstName: '' }))
                }}
                error={!!errors.firstName}
                style={styles.input}
            />
            {errors.firstName && (
                <Text style={styles.errorText}>{errors.firstName}</Text>
            )}

            <TextInput
                mode="flat"
                label="Last Name*"
                value={formData.lastName}
                onChangeText={(value) => {
                    setFormData(prev => ({ ...prev, lastName: value }))
                    setErrors(prev => ({ ...prev, lastName: '' }))
                }}
                error={!!errors.lastName}
                style={styles.input}
            />
            {errors.lastName && (
                <Text style={styles.errorText}>{errors.lastName}</Text>
            )}

            <View style={styles.verificationField}>
                <View style={styles.phoneInputContainer}>
                    <PhoneNumberInput
                        value={formData.rawPhoneNumber}
                        onChangeText={(raw, formatted) => {
                            setFormData(prev => ({
                                ...prev,
                                rawPhoneNumber: raw,
                                phoneNumber: formatted
                            }))
                            setErrors(prev => ({ ...prev, phoneNumber: '' }))
                        }}
                        error={!!errors.phoneNumber}
                        errorText={errors.phoneNumber}
                        label="Phone Number*"
                        mode="flat"
                        style={styles.phoneInput}
                    />
                    {shouldShowPhoneVerification() ? (
                        <Button
                            mode="text"
                            onPress={() => handleVerificationRequest('phone', formData.phoneNumber)}
                            loading={isResendingCode && verificationType === 'phone'}
                            style={styles.verifyButton}
                            disabled={!formData.phoneNumber || !!errors.phoneNumber}
                        >
                            Verify
                        </Button>
                    ) : formData.phoneNumber ? (
                        <CircleCheck size={Icons.size.lg} variant='filled' gradientStartColor={Colors.primary} gradientEndColor={Colors.gradient.orange} position='absolute' right={12} bottom={16}/>
                    ) : null}
                </View>
            </View>

            <View style={styles.verificationField}>
                <TextInput
                    mode="flat"
                    label="Email"
                    value={formData.email}
                    onChangeText={(value) => {
                        setFormData(prev => ({ ...prev, email: value }))
                        setErrors(prev => ({ ...prev, email: '' }))
                    }}
                    error={!!errors.email}
                    style={[styles.input, styles.emailInput]}
                    keyboardType="email-address"
                    autoCapitalize="none"
                />
                {shouldShowEmailVerification() ? (
                    <Button
                        mode="text"
                        onPress={() => handleVerificationRequest('email', formData.email)}
                        loading={isResendingCode && verificationType === 'email'}
                        style={styles.verifyButton}
                        disabled={!formData.email || !!errors.email}
                    >
                        Verify
                    </Button>
                ) : formData.email ? (
                    <CircleCheck size={Icons.size.lg} variant='filled' gradientStartColor={Colors.primary} gradientEndColor={Colors.gradient.orange} position='absolute' right={12} bottom={16}/>
                ) : null}
            </View>
            {errors.email && (
                <Text style={styles.errorText}>{errors.email}</Text>
            )}

            <Button
                mode="contained"
                onPress={handleSave}
                loading={isLoading}
                style={styles.saveButton}
                textColor={Colors.black}
            >
                Save
            </Button>
            <Snackbar
                visible={showToast}
                onDismiss={() => setShowToast(false)}
                duration={3000}
                style={styles.snackbar}
            >
                {toastMessage}
            </Snackbar>

            <OTPVerificationModal
                visible={verificationModalVisible}
                onDismiss={() => setVerificationModalVisible(false)}
                onVerify={handleVerifyCode}
                type={verificationType}
                isResending={isResendingCode}
                onResend={() => {
                    if (pendingVerification) {
                        handleVerificationRequest(
                            pendingVerification.type,
                            pendingVerification.value
                        )
                    }
                }}
            />
        </View>
    )
}

export default PersonalInfoForm

const styles = StyleSheet.create({
    container: {
        gap: 16,
        width: '90%',
        alignSelf: 'center',
    },
    input: {
        backgroundColor: 'white',
    },
    errorText: {
        color: 'red',
        fontSize: 12,
        marginTop: -12,
        marginLeft: 8,
    },
    verificationField: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        gap: 8,
    },
    emailInput: {
        flex: 1,
    },
    phoneInputContainer: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'white',
    },
    phoneInput: {
        flex: 1,
        backgroundColor: 'white',
    },
    verifyButton: {
        position: 'absolute',
        right: 8,
        bottom: 4,
        height: 40,
        marginTop: 0,
    },
    saveButton: {
        marginTop: 8,
        borderRadius: 10,
        backgroundColor: Colors.primary
    },
    verifiedIcon: {
        position: 'absolute',
        right: 12,
        bottom: 16,
        marginTop: 0,
    },
    snackbar: {
        position: 'absolute',
        bottom: -80,  // Add some padding from bottom
        left: 16,
        right: 16,
    },
})