import React, { useRef } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { Rsvp } from '@/app/(home)/PartyDetails/NewPartyDetailsResponse.model'; // Assuming Rsvp is correctly imported
import InitialsAvatar from '@/components/Avatar/InitialsAvatar';
import { ProfilePicture } from './ProfilePicture';
import { Swipeable } from 'react-native-gesture-handler';
import { Delete } from '@/components/icons';
import { Colors, Icons } from '@/constants/DesignSystem';

interface InviteRowProps {
  rsvp: Rsvp;
  onDelete: (rsvp: Rsvp) => void;
}

const InviteRow: React.FC<InviteRowProps> = ({ rsvp, onDelete }) => {
  if (!rsvp) return null;
  const { guest, status } = rsvp;

  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'ACCEPTED':
        return styles.statusAccepted;
      case 'REJECTED':
        return styles.statusRejected;
      case 'MAYBE':
        return styles.statusMaybe;
      case 'PENDING':
        return styles.statusPending;
      default:
        return styles.statusPending;
    }
  };
  const swipeableRef = useRef<Swipeable>(null);
  const renderRightActions = () => (
    <TouchableOpacity
      style={styles.deleteButton}
      onPress={() => {
        swipeableRef.current?.close()
        onDelete(rsvp)
      }
      }
    >
      <Delete size={Icons.size.md} color={Colors.white} />
    </TouchableOpacity>
  );

  return (
    <Swipeable ref={swipeableRef} renderRightActions={renderRightActions}>
      <View style={styles.container}>
        <View style={styles.avatarContainer}>
          {guest.user.profilePicture ? (
            <ProfilePicture url={guest.user.profilePicture} />
          ) : (
            <InitialsAvatar firstName={guest.user.firstName} lastName={guest.user.lastName} />
          )}
        </View>

        <View style={styles.detailsContainer}>
          <Text style={styles.userName}>{`${guest.user.firstName} ${guest.user.lastName}`}</Text>
          <Text>{guest.user.phone}</Text>
        </View>

        {/* Right Section - RSVP Status */}
        <View style={styles.statusContainer}>
          <Text style={[styles.statusText, getStatusStyle(status)]}>
            {status} {rsvp.guest.additionalGuestsCount > 0 ? `+${rsvp.guest.additionalGuestsCount}` : ''}
          </Text>
        </View>
      </View>
    </Swipeable>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  avatarContainer: {
    marginRight: 10,
  },
  detailsContainer: {
    flex: 1,
  },
  statusContainer: {
    marginLeft: 'auto',
  },
  profilePic: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  userName: {
    fontWeight: 'bold',
    fontFamily: 'Plus Jakarta Sans',
  },
  statusText: {
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: 'bold',
  },
  statusAccepted: {
    color: 'green',
  },
  statusRejected: {
    color: 'red',
  },
  statusMaybe: {
    color: 'orange',
  },
  statusPending: {
    color: 'grey',
  },
  deleteButton: {
    backgroundColor: 'red',
    justifyContent: 'center',
    alignItems: 'center',
    width: 80,
  },
});

export default InviteRow;