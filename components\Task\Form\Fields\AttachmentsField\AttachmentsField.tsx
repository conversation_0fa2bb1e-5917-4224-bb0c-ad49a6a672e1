import { View, Platform } from 'react-native';
import { Text, Surface } from 'react-native-paper';
import { attachmentsFieldStyles } from './AttachmentsField.styles';
import { DocumentPickerAsset } from 'expo-document-picker';
import { Icons, Colors } from '@/constants/DesignSystem';
import { Add, File, Cross } from '@/components/icons';

interface AttachmentsFieldProps {
  selectedFiles: DocumentPickerAsset[];
  onAttachmentPress: () => void;
  onRemoveFile: (index: number) => void;
  theme: any;
}

export function AttachmentsField({
  selectedFiles,
  onAttachmentPress,
  onRemoveFile,
  theme,
}: AttachmentsFieldProps) {
  const plusIconSize = Platform.OS === 'ios' ? 20 : 20;
  const fileIconSize = Platform.OS === 'ios' ? 16 : 20;
  const closeIconSize = Platform.OS === 'ios' ? 16 : 20;

  return (
    <View style={attachmentsFieldStyles.attachmentsSection}>
      <Text style={attachmentsFieldStyles.attachmentsSectionTitle}>Attachments</Text>
      <Surface
      mode="flat"
      style={attachmentsFieldStyles.attachmentBox}
      onTouchEnd={onAttachmentPress}
    >
      <Add
        size={Icons.size.md}
        color={Colors.primary}
        style={{
          alignSelf: 'center',
          ...(Platform.OS === 'ios' && { width: 20, height: 20 }),
          ...(Platform.OS === 'android' && { width: 20, height: 20 }),
        }}
      />
    </Surface>
      
      {selectedFiles.length > 0 && (
        <View style={attachmentsFieldStyles.fileListContainer}>
          {selectedFiles.map((file, index) => (
            <View
              key={index}
              style={attachmentsFieldStyles.fileItem}
            >
              <File
                size={fileIconSize}
                color={Colors.primary}
              />
              <Text style={attachmentsFieldStyles.fileName}>{file.name}</Text>
              <Cross
                size={closeIconSize}
                color={Colors.primary}
                onPress={() => onRemoveFile(index)}
              />
            </View>
          ))}
        </View>
      )}
    </View>
  );
}