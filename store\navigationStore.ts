import { NAV_BAR_VIEW_TYPE } from '@/constants/bottomNavTabs';
import { create } from 'zustand';

interface NavigationState {
  viewType: NAV_BAR_VIEW_TYPE;
  selectedEventId: string | null;
  setViewType: (type: NAV_BAR_VIEW_TYPE) => void;
  setSelectedEventId: (id: string | null) => void;
}

export const useNavigationStore = create<NavigationState>((set) => ({
  viewType: NAV_BAR_VIEW_TYPE.USER,
  selectedEventId: null,
  setViewType: (type) => set((state) => ({
    viewType: type,
    selectedEventId: type === NAV_BAR_VIEW_TYPE.USER ? null : state.selectedEventId,
  })),
  setSelectedEventId: (id) => set({ selectedEventId: id }),
}));