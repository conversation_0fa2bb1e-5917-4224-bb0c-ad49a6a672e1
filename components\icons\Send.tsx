import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Send = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Send-1_svg__a)">
      <Path
        fill="url(#Send-1_svg__b)"
        fillRule="evenodd"
        d="M11.385 1.282c.11.11.145.274.089.419L7.545 11.915a.393.393 0 0 1-.724.021L5.178 8.322l2.417-2.417a.59.59 0 1 0-.833-.834L4.345 7.49.73 5.846a.393.393 0 0 1 .022-.725l10.214-3.928a.39.39 0 0 1 .419.089"
        clipRule="evenodd"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Send-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.161}
        y2={14.695}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Send-1_svg__a">
        <Path fill="#fff" d="M0 .667h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Send_svg__a)"
    >
      <Path d="M5.23 6.77 1 4.845 11 1 7.154 11zM5.23 6.77 7.54 4.461" />
    </G>
    <Defs>
      <ClipPath id="Send_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Send;
