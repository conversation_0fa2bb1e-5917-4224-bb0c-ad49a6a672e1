import {
    SafeAreaView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    ActivityIndicator,
    Alert,
    Modal,
    KeyboardAvoidingView,
    Pressable,
    Platform,
    Image
} from 'react-native';
import React from 'react';
import { StackScreenProps } from '@react-navigation/stack';
import { PartyDetailsRootList } from '@/components/create-event/Navigation/PartyDetailsRootList';
import { useNavigation } from '@react-navigation/native';

type ReactionsScreenProps = StackScreenProps<PartyDetailsRootList, 'ReactionsCard'>;

const ReactionsView: React.FC<ReactionsScreenProps> = ({ route }) => {
    const reactions = route.params.reactions;
    const navigation = useNavigation();

    const handlePressOutside = () => {
        navigation.goBack();
    };

    return (
        <Modal
            animationType="slide"
            transparent={true}
            onRequestClose={handlePressOutside}
        >
            <Pressable style={styles.overlay} onPress={handlePressOutside}>
                <KeyboardAvoidingView
                    behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                    style={styles.halfSheetContainer}
                >
                    <Pressable style={styles.halfSheet} onPress={() => { }}>
                        <View>
                            {reactions.map((reaction, index) => (
                                <View key={index} style={styles.reactionRow}>
                                    {/* Profile Picture */}
                                    {reaction.user.profilePicture ? (
                                        <Image
                                            source={{ uri: reaction.user.profilePicture }}
                                            style={styles.profilePicture}
                                        />
                                    ) : (
                                        <View style={styles.placeholderAvatar}>
                                            <Text style={styles.avatarText}>
                                                {reaction.user.firstName?.[0]}
                                                {reaction.user.lastName?.[0]}
                                            </Text>
                                        </View>
                                    )}

                                    {/* User Name */}
                                    <View style={styles.userInfo}>
                                        <Text style={styles.userName}>
                                            {reaction.user.firstName} {reaction.user.lastName}
                                        </Text>
                                    </View>

                                    {/* Emoji */}
                                    <Text style={styles.emoji}>{reaction.reactionUnicode}</Text>
                                </View>
                            ))}
                        </View>
                    </Pressable>
                </KeyboardAvoidingView>
            </Pressable>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: 'transparent',
        shadowOffset: { width: 1, height: 1 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
    },
    halfSheetContainer: {
        flex: 1,
        justifyContent: 'flex-end',
    },
    halfSheet: {
        backgroundColor: '#fff',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        padding: 20,
        paddingBottom: 40,
    },
    reactionRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 15,
        paddingVertical: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#F4F4F4',
    },
    profilePicture: {
        width: 40,
        height: 40,
        borderRadius: 20,
        marginRight: 10,
    },
    placeholderAvatar: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: '#E0E0E0',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 10,
    },
    avatarText: {
        color: '#FFFFFF',
        fontSize: 16,
        fontWeight: 'bold',
    },
    userInfo: {
        flex: 1,
    },
    userName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
    },
    emoji: {
        fontSize: 24,
        marginLeft: 10,
    },
});

export default ReactionsView;