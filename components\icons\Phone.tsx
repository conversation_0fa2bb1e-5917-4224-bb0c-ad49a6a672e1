import * as React from "react";
import Svg, { <PERSON>, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Phone = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Phone-1_svg__a)">
      <Path
        fill="url(#Phone-1_svg__b)"
        d="M4.514 11.066a2.69 2.69 0 0 1-3.378-.371l-.38-.372a.91.91 0 0 1 0-1.275L2.37 7.452a.903.903 0 0 1 1.267 0 .91.91 0 0 0 1.275 0L7.444 4.92a.895.895 0 0 0 0-1.275.904.904 0 0 1 0-1.266L9.048.773a.91.91 0 0 1 1.275 0l.372.38a2.694 2.694 0 0 1 .371 3.378 24.4 24.4 0 0 1-6.552 6.535"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Phone-1_svg__b"
        x1={5.996}
        x2={5.996}
        y1={-0.812}
        y2={14.021}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Phone-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G clipPath="url(#Phone_svg__a)">
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M4.652 10.601a2.45 2.45 0 0 1-3.07-.337l-.346-.338a.83.83 0 0 1 0-1.16l1.466-1.45a.82.82 0 0 1 1.152 0 .83.83 0 0 0 1.159 0l2.303-2.303a.814.814 0 0 0 0-********** 0 0 1 0-1.15l1.458-1.459a.83.83 0 0 1 1.16 0l.337.345a2.45 2.45 0 0 1 .338 3.07 22.2 22.2 0 0 1-5.957 5.942"
      />
    </G>
    <Defs>
      <ClipPath id="Phone_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Phone;
