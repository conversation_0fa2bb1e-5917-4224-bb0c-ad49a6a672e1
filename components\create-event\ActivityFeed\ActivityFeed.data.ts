import { gql } from "@apollo/client";

export const DELETE_MESSAGE = gql`
mutation DeleteMessage($deleteMessageId: ID!) {
  deleteMessage(id: $deleteMessageId) {
    ... on MessageResponse {
      message
    }
    ... on MessageErrorResponse {
      message
    }
  }
}
`

export const EDIT_MESSAGE = gql`
  mutation EditMessage($editMessageId: ID!, $input: EditMessageInput!) {
    editMessage(id: $editMessageId, input: $input) {
      ... on MessageResponse {
        message
      }
      ... on MessageErrorResponse {
        message
      }
    }
  }
`;

export const REMOVE_REACTION = gql`
  mutation RemoveReactionFromMessage($messageId: ID!, $reactionId: ID!) {
  removeReactionFromMessage(messageId: $messageId, reactionId: $reactionId) {
    ... on MessageResponse {
      message
    }
    ... on MessageErrorResponse {
      message
    }
  }
}
`