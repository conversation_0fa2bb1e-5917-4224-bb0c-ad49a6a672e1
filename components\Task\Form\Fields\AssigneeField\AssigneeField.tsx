// components/Task/Form/Fields/AssigneeField/AssigneeField.tsx
import { View, Pressable } from 'react-native';
import { Text, TextInput, List } from 'react-native-paper';
import { styles } from './AssigneeField.styles';
import { Assignee } from '@/components/Task/SharedInterfaces';
import { getAssigneeName } from '@/app/(home)/utils/reusableFunctions';
import { useRef, useState, useMemo, useCallback } from 'react';
import { BottomSheetModal, BottomSheetBackdrop, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useQuery } from '@apollo/client';
import { GET_EVENT_ASSIGNEES_BY_EVENT_ID } from '@/app/(home)/EventsDashboard/AddParty.data';
import { useEventStore } from '@/store/eventStore';
import { useFocusEffect } from '@react-navigation/native';
import { useKeyboard } from '@/commons/KeyboardContext';
import { Icons, Colors } from '@/constants/DesignSystem';
import { Check } from '@/components/icons';

interface AssigneeFieldProps {
  selectedAssignee: Assignee | null;
  onAssigneeSelect: (assignee: Assignee | null) => void;
  onSaveAssignee?: (assigneeId: string | null) => Promise<void>;
  onDeselectAssignee?: () => void;
  taskId?: string;
}

const CustomRadioButton = ({ isSelected }: { isSelected: boolean }) => (
  <Pressable 
    style={{
      width: 27,
      height: 27,
      borderRadius: 20,
      borderWidth: 2,
      borderColor:  isSelected ? '#768DEC' : '#CFCECE',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: isSelected ? '#BEC9F6' : 'transparent',
      marginLeft: 16,
    }}
  >
    {isSelected && (
      <Check 
        size={Icons.size.md} 
        color={Colors.primary}
      />
    )}
  </Pressable>
);

export function AssigneeField({ 
  selectedAssignee, 
  onAssigneeSelect, 
  onDeselectAssignee,
  onSaveAssignee,
  taskId 
}: AssigneeFieldProps) {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['80%'], []);
  const [searchQuery, setSearchQuery] = useState('');
  
  const selectedEventId = useEventStore((state) => state.selectedEventId);

  const { data: assigneesData, loading , refetch} = useQuery(GET_EVENT_ASSIGNEES_BY_EVENT_ID, {
    variables: {
      eventId: selectedEventId
    },
    skip: !selectedEventId,
    fetchPolicy: 'network-only',
  });

  useFocusEffect(
    useCallback(() => {
      if (selectedEventId) {
        refetch();
      }
    }, [selectedEventId, refetch])
  );

  const assignees = assigneesData?.getEventAssignees?.result?.assignees?.map((assignee: any) => ({
    id: assignee.id,
    firstName: assignee.firstName,
    lastName: assignee.lastName,
    email: assignee.email,
  })) || [];

  const filteredAssignees = assignees.filter((assignee: Assignee) =>
    getAssigneeName(assignee).toLowerCase().includes(searchQuery.toLowerCase())
  );

  const { isKeyboardActive } = useKeyboard();

  const handlePress = () => {
    if (isKeyboardActive) return;
    bottomSheetRef.current?.present();
  };

  const handleAssigneeSelect = async (assignee: Assignee) => {
    const previousAssignee = selectedAssignee;
    const isDeselecting = selectedAssignee?.id === assignee.id;
    
    if (isDeselecting) {
      onDeselectAssignee?.();
    } else {
      onAssigneeSelect(assignee);
    }
    
    bottomSheetRef.current?.dismiss();

    if (onSaveAssignee && taskId) {
      try {
        await onSaveAssignee(isDeselecting ? null : assignee.id);
      } catch (error: any) {
        console.log('Error saving assignee:', error);
        if (isDeselecting) {
          onAssigneeSelect(previousAssignee);
        } else {
          onDeselectAssignee?.();
        }
      }
    }
  };

  const getDisplayName = (assignee: Assignee | null) => {
    if (!assignee) return 'Select';
    const firstName = assignee.firstName || '';
    const lastName = assignee.lastName || '';
    return `${firstName} ${lastName}`.trim() || 'No name';
  };

  const handleDismiss = useCallback(() => {
    bottomSheetRef.current?.dismiss();
  }, []);

  return (
    <>
      <View style={[styles.inputWrapper]}>
        <Pressable 
          onTouchEnd={handlePress}
          disabled={isKeyboardActive}
          style={{ 
            width: selectedAssignee ? 
            ((getDisplayName(selectedAssignee).length * 1) < 25 ? (getDisplayName(selectedAssignee).length * 10) : '100%')
            : '40%',
          }}
        >
          <Text style={styles.labelText}>Assignee</Text>
          <Text style={selectedAssignee ? styles.valueText : styles.saveValueText} numberOfLines={1}>
            {getDisplayName(selectedAssignee)}
          </Text>
        </Pressable>
      </View>

      <BottomSheetModal
        ref={bottomSheetRef}
        index={1}
        snapPoints={snapPoints}
        enablePanDownToClose
        style={styles.bottomSheet}
        onDismiss={handleDismiss}
        backdropComponent={(props) => (
          <BottomSheetBackdrop
            {...props}
            appearsOnIndex={0}
            disappearsOnIndex={-1}
            pressBehavior="close"
            onPress={handleDismiss}
          />
        )}
      >
        <View style={styles.bottomSheetContainer}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Select Assignee</Text>
          </View>

          <TextInput
            placeholder="Ex. name"
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchInput}
            mode="flat"
            underlineColor="transparent"
            activeUnderlineColor="transparent"
            cursorColor="#000000"
          />
          
          <Text style={styles.infoText}>ⓘ You can only select one Assignee</Text>
          
          <BottomSheetScrollView contentContainerStyle={styles.scrollContent}>
            {loading ? (
              <Text style={styles.loadingText}>Loading...</Text>
            ) : filteredAssignees.length === 0 ? (
              <Text style={styles.noDataText}>No assignees found</Text>
            ) : (
              filteredAssignees.map((assignee: Assignee) => (
                <List.Item
                  key={assignee.id}
                  title={() => (
                    <View style={styles.titleContainer}>
                      <Text style={styles.listItemTitle} numberOfLines={3}>
                        {getAssigneeName(assignee)}
                      </Text>
                    </View>
                  )}
                  onTouchEnd={() => handleAssigneeSelect(assignee)}
                  right={() => (
                    <CustomRadioButton
                      isSelected={selectedAssignee?.id === assignee.id}
                    />
                  )}
                  style={styles.listItem}
                />
              ))
            )}
          </BottomSheetScrollView>
        </View>
      </BottomSheetModal>
    </>
  );
}