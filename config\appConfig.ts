import { APP_SETTINGS_VERSION } from '@/constants/bottomNavTabs';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  VERSION_SOURCE, 
  VersionSource, 
  ENVIRONMENT,
  Environment,
  STORAGE_KEYS 
} from '@/constants/appConfig.constants';

export const APP_CONFIG = {
  version: process.env.EXPO_PUBLIC_APP_SETTINGS_VERSION || APP_SETTINGS_VERSION.VERSION,
  environment: (process.env.EXPO_PUBLIC_ENVIRONMENT as Environment) || ENVIRONMENT.DEVELOPMENT,
  apiUrl: `${process.env.EXPO_PUBLIC_API_URL}/graphql`,
  isUsingEnvVersion: Boolean(process.env.EXPO_PUBLIC_APP_SETTINGS_VERSION),
} as const;

interface StoredVersionInfo {
  version: string;
  lastUpdated: string;
  source: VersionSource;
}

export async function initializeAppVersion(): Promise<StoredVersionInfo> {
  try {
    // Try to get version from env first
    if (process.env.EXPO_PUBLIC_APP_SETTINGS_VERSION) {
      const versionInfo: StoredVersionInfo = {
        version: process.env.EXPO_PUBLIC_APP_SETTINGS_VERSION,
        lastUpdated: new Date().toISOString(),
        source: VERSION_SOURCE.ENV
      };
      await AsyncStorage.setItem(STORAGE_KEYS.APP_VERSION, JSON.stringify(versionInfo));
      return versionInfo;
    }

    // Try to get version from storage
    const storedVersionString = await AsyncStorage.getItem(STORAGE_KEYS.APP_VERSION);
    if (storedVersionString) {
      const storedVersion: StoredVersionInfo = JSON.parse(storedVersionString);
      return {
        ...storedVersion,
        source: VERSION_SOURCE.STORAGE
      };
    }

    // Use fallback version if neither env nor storage has a version
    const fallbackInfo: StoredVersionInfo = {
      version: APP_SETTINGS_VERSION.VERSION,
      lastUpdated: new Date().toISOString(),
      source: VERSION_SOURCE.FALLBACK
    };
    await AsyncStorage.setItem(STORAGE_KEYS.APP_VERSION, JSON.stringify(fallbackInfo));
    return fallbackInfo;

  } catch (error) {
    console.error('Error initializing app version:', error);
    // Return fallback version in case of any errors
    return {
      version: APP_SETTINGS_VERSION.VERSION,
      lastUpdated: new Date().toISOString(),
      source: VERSION_SOURCE.FALLBACK
    };
  }
}

export async function getVersionDetails(): Promise<StoredVersionInfo> {
  try {
    const versionInfo = await initializeAppVersion();
    return versionInfo;
  } catch (error) {
    console.error('Error getting version details:', error);
    return {
      version: APP_SETTINGS_VERSION.VERSION,
      lastUpdated: new Date().toISOString(),
      source: VERSION_SOURCE.FALLBACK
    };
  }
}

export type AppConfig = typeof APP_CONFIG;