import { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { View, StyleSheet, Alert, Text, Pressable, Image } from 'react-native'
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet'
import { LoadingIndicator } from '@/components/UI/ReusableComponents/LoadingIndicator';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { CreateCircleForm } from '@/components/circle-dashboard/CreateCircleForm';
import ProfilePhotoSection from '@/components/settings/UserProfile/ProfilePhotoSection.';
import { useMutation } from '@apollo/client';
import { CREATE_CIRCLE, SEND_CIRCLE_INVITATION } from '@/components/user-circles/user-circles.data';
import { useToast } from '@/components/Toast';
import { useNavigation } from '@react-navigation/native';
import AddContactsSection from '@/components/settings/UserProfile/AddContactsSection';
import { UPDATE_CIRCLE_BY_ID, DELETE_EVENT_GROUP_INVITATION } from './CircleDetails.data';
import { s } from 'react-native-size-matters';
import { Colors, Typography, Spacing, Borders, Shadows, getButtonStyle, Icons } from '@/constants/DesignSystem';
import { TakePhoto, Cross, Edit } from '@/components/icons';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import { AddCircleRootStackList } from './AddCircleRootStackList';
import { Contact } from '@/components/create-circle/EditMembers';

export type CircleData = {
    name: string;
    description: string;
};

interface FormErrors {
    name?: string;
    description?: string;
}

type AddCircleProps = StackScreenProps<AddCircleRootStackList, 'AddCircle'>;

const AddCircle = ({ route }: AddCircleProps) => {
    const [circleLoading, setCircleLoading] = useState(false);
    const [circleImageUrl, setCircleImageUrl] = useState<string | null | undefined>();
    const [circleData, setCircleData] = useState<CircleData>({
        name: '',
        description: '',
    });
    const contactSheetRef = useRef<any>(null);
    const [formErrors, setFormErrors] = useState<FormErrors>({}); // Added formErrors state
    const [initialMembers, setInitialMembers] = useState<Contact[]>([]);

    const [selectedContacts, setSelectedContacts] = useState<Contact[]>([]);
    const [shouldBlockNavigation, setShouldBlockNavigation] = useState(true);

    const [createCircle] = useMutation(CREATE_CIRCLE);
    const [updateCircle] = useMutation(UPDATE_CIRCLE_BY_ID);
    const [sendCircleInvitation] = useMutation(SEND_CIRCLE_INVITATION);
    const [deleteEventGroupInvitation] = useMutation(DELETE_EVENT_GROUP_INVITATION);
    const toast = useToast();
    // Use any type for navigation to avoid TypeScript errors
    const navigation = useNavigation<StackNavigationProp<AddCircleRootStackList>>();
    const { fromScreen = '', details = [], onUpdate } = route?.params || {};

    useEffect(() => {
        if (fromScreen === 'CircleDetails') {
            setCircleData({
                name: details.name,
                description: details.description,
            });
            setSelectedContacts(details.members)
            setInitialMembers(details.members)
            setCircleImageUrl(details.imageUrl);
        }
    }, [fromScreen, details])

    useEffect(() => {
        const unsubscribe = navigation.addListener('beforeRemove', (e: any) => {
            if (!shouldBlockNavigation) {
                return;
            }
            if (
                !circleData.name &&
                !circleData.description &&
                selectedContacts.length === 0 &&
                !circleImageUrl
            ) {
                return;
            }

            e.preventDefault();

            Alert.alert(
                'Discard Changes?',
                'Are you sure you want to cancel? Unsaved changes will be lost.',
                [
                    { text: 'Cancel', style: 'cancel', onPress: () => { } },
                    {
                        text: 'Discard',
                        style: 'destructive',
                        onPress: () => {
                            unsubscribe();
                            setShouldBlockNavigation(false);
                            navigation.dispatch(e.data.action);
                        },
                    },
                ]
            );
        });

        return unsubscribe;
    }, [navigation, circleData, selectedContacts, circleImageUrl, shouldBlockNavigation]);

    const validateForm = () => {
        const errors: FormErrors = {};
        let isValid = true;

        if (!circleData.name.trim()) {
            errors.name = 'Circle name is required';
            isValid = false;
        }

        setFormErrors(errors);
        return isValid;
    };

    const handleSubmit = async () => {
        if (!validateForm()) {
            return;
        }
        setCircleLoading(true);
        setShouldBlockNavigation(false);

        try {
            const createEventGroupInput = {
                input: {
                    name: circleData.name,
                    type: "CIRCLE",
                    description: circleData.description,
                    imageUrl: circleImageUrl
                }
            };
            const createEventGroupResult = await createCircle({ variables: createEventGroupInput });

            const sendCircleInvitationInput = {
                input: {
                    eventGroupId: createEventGroupResult.data.createEventGroup.result?.eventGroup?.id,
                    members: selectedContacts.map(contact => ({
                        email: contact?.emails?.[0]?.email ?? contact?.email,
                        firstName: contact?.firstName ?? null,
                        lastName: contact?.lastName ?? null,
                        phone: contact?.phoneNumbers?.[0]?.number ?? contact?.phoneNumber,
                    })),
                }
            };

            const sendCircleInvitationResult = await sendCircleInvitation({ variables: sendCircleInvitationInput });

            if (createEventGroupResult.data?.createEventGroup?.status === "FAILURE") {
                throw new Error(createEventGroupResult.data.createEventGroup.message || "Failed to create circle");
            }

            if (sendCircleInvitationResult.data?.sendEventGroupInvitation?.status === "FAILURE") {
                throw new Error(sendCircleInvitationResult.data.sendEventGroupInvitation.message || "Failed to send circle invitation");
            }

            const circleId = createEventGroupResult.data.createEventGroup.result?.eventGroup?.id;

            if (!circleId) {
                throw new Error('Failed to create circle');
            } else {
                navigation.replace("CircleDetails", { circleId, showSuccessAnimation: true });
            }
        }
        catch (error) {
            console.error(error);
            toast.error('Failed to create circle');
        }
        finally {
            setCircleLoading(false);
        }

    }
    const handleAddMembers = () => {
        contactSheetRef.current?.openBottomSheet();
    }

    const setMembers = useCallback((contacts: Contact[]) => {
        setSelectedContacts(contacts);
    }, []);

    const handlePressEdit = () => {
        navigation.navigate('EditMembers', {
            members: selectedContacts,
            initialMembers: initialMembers,
            circleName: circleData.name,
            onGoBack: (updatedMembers: Contact[]) => {
                setSelectedContacts(updatedMembers);
            }
        });
    }

    const handleUpdateCircle = async () => {
        if (!validateForm()) {
            return;
        }
        setCircleLoading(true);
        setShouldBlockNavigation(false);

        try {
            const updatePromises = [];

            const coreDetailsPromise = updateCircle({
                variables: {
                    updateEventGroupId: details.id,
                    input: {
                        name: circleData.name,
                        description: circleData.description,
                        imageUrl: circleImageUrl,
                    },
                },
            });
            updatePromises.push(coreDetailsPromise);

            const initialMemberIds = new Set(initialMembers.map(m => m.id));
            const selectedMemberIds = new Set(selectedContacts.map(m => m.id));

            const newMembers = selectedContacts.filter(m => !initialMemberIds.has(m.id));
            if (newMembers.length > 0) {
                const sendInvitePromise = sendCircleInvitation({
                    variables: {
                        input: {
                            eventGroupId: details.id,
                            members: newMembers.map(m => ({
                                email: m.email ?? null,
                                firstName: m.firstName ?? null,
                                lastName: m.lastName ?? null,
                                phone: m.phoneNumbers?.[0]?.number ?? m.phoneNumber ?? m.phone,
                            })),
                        },
                    },
                });
                updatePromises.push(sendInvitePromise);
            }

            const removedMembers = initialMembers.filter(m => !selectedMemberIds.has(m.id));
            if (removedMembers.length > 0) {
                for (const member of removedMembers) {
                    if (member.invitationId) {
                        const deletePromise = deleteEventGroupInvitation({
                            variables: { deleteEventGroupInvitationId: member.invitationId },
                        });
                        updatePromises.push(deletePromise);
                    } else {
                        console.warn(`Cannot remove member ${member.id}: invitationId is missing.`);
                    }
                }
            }
            
            await Promise.all(updatePromises);

            toast.success('Circle details updated successfully!');
            onUpdate?.();
            navigation.goBack();

        } catch (error) {
            console.error('Update error:', error);
            toast.error('Something went wrong while updating the circle details');
        } finally {
            setCircleLoading(false);
        }
    };

    const newMembers = useMemo(() => {
        if (fromScreen !== 'CircleDetails') {
            return selectedContacts;
        }
        return selectedContacts.filter(contact =>
            !initialMembers.some(initialMember => initialMember.id === contact.id)
        );
    }, [selectedContacts, initialMembers, fromScreen]);

    const MemberList = ({ title, members, isNew, showEdit, onEditPress }: { title: string, members: Contact[], isNew: boolean, showEdit?: boolean, onEditPress?: () => void }) => (
        <View style={title === 'New Invitees' ? { marginTop: Spacing.md } : {}}>
            <View style={styles.selectedContactsHeaderContainer}>
                <Text style={styles.label}>{title}</Text>
                {showEdit && (
                    <Pressable onPress={onEditPress} style={styles.editContainer}>
                        <Edit size={Icons.size.md} color={Colors.text.secondary} />
                        <Text style={styles.editText}>{'Edit'}</Text>
                    </Pressable>
                )}
            </View>
            <View style={styles.selectedContactsContainer}>
                {members.map((contact: any, index: number) => {
                    const initials = `${contact.firstName?.[0] ?? ''}${contact.lastName?.[0] ?? ''}`.toUpperCase();
                    return (
                        <View key={`${contact.id}-${index}`} style={styles.contactChip}>
                            <View style={[styles.contactCircleSmall, isNew && styles.dottedBorder]}>
                                {contact.imageAvailable ? (
                                    <Image source={{ uri: contact.image?.uri }} style={styles.imageSmall} />
                                ) : (
                                    <Text style={[styles.monogram, { color: 'black' }]}>{initials}</Text>
                                )}
                            </View>
                        </View>
                    );
                })}
            </View>
        </View>
    );

    const existingMembers = useMemo(() => {
        if (fromScreen !== 'CircleDetails') {
            return [];
        }
        return selectedContacts.filter(contact =>
            initialMembers.some(initialMember => initialMember.id === contact.id)
        );
    }, [selectedContacts, initialMembers, fromScreen]);

    return (
        <GestureHandlerRootView style={{ flex: 1 }}>
            <View style={styles.mainContainer}>
                <BottomSheetModalProvider>
                    {circleLoading ? (
                        <View style={styles.loadingOverlay}>
                            <LoadingIndicator />
                        </View>
                    ) : (
                        <View style={{ flex: 1 }}>
                            <View style={styles.customHeader}>
                                <Text style={styles.headerTitle}>{fromScreen === 'CircleDetails' ? 'Edit Circle' : 'Create Circle'}</Text>
                                <View style={styles.spacer} />
                                <Pressable onPress={() => navigation.goBack()} style={styles.headerButton}>
                                    <Cross size={Icons.size.md} color={Colors.text.secondary} />
                                </Pressable>
                            </View>

                            <KeyboardAwareScrollView
                                enableOnAndroid
                                extraScrollHeight={100}
                                keyboardShouldPersistTaps='handled'
                                contentContainerStyle={{
                                    flexGrow: 1,
                                    paddingBottom: 120,
                                }}
                            >
                                <View style={{ marginTop: Spacing.md }}>
                                    <ProfilePhotoSection renderCustomContent={() => (
                                        <View style={styles.camera}>
                                            <TakePhoto size={Icons.size.xxl} color={Colors.primary} />
                                        </View>
                                    )} profileUrl={circleImageUrl ? circleImageUrl : null} setProfileImageUrl={(url) => {
                                        setCircleImageUrl(url);
                                    }
                                    } />
                                </View>
                                <View style={{ marginBottom: selectedContacts?.length > 0 ? s(25) : s(130) }}>
                                    <CreateCircleForm
                                        circleData={circleData as any}
                                        setCircleData={setCircleData as any}
                                        formErrors={formErrors}
                                    />
                                </View>
                                {fromScreen !== 'EditMembers' && selectedContacts.length > 0 && (
                                    <View style={styles.membersContainer}>
                                        {fromScreen === 'CircleDetails' ? (
                                            <>
                                                {existingMembers.length > 0 && (
                                                    <MemberList
                                                        title="Members"
                                                        members={existingMembers}
                                                        isNew={false}
                                                        showEdit
                                                        onEditPress={handlePressEdit}
                                                    />
                                                )}
                                                {newMembers.length > 0 && (
                                                    <MemberList
                                                        title="New Invitees"
                                                        members={newMembers}
                                                        isNew={true}
                                                    />
                                                )}
                                            </>
                                        ) : (
                                            <MemberList
                                                title="Members"
                                                members={newMembers}
                                                isNew={true}
                                                showEdit
                                                onEditPress={handlePressEdit}
                                            />
                                        )}
                                    </View>
                                )}
                                <AddContactsSection ref={contactSheetRef} members={selectedContacts} setMembers={setMembers} />
                            </KeyboardAwareScrollView>
                        </View>
                    )}
                    <View style={styles.footer}>
                        <Pressable
                            style={getButtonStyle('bottomButton')}
                            onPress={selectedContacts?.length > 0 ? fromScreen === 'CircleDetails' ? handleUpdateCircle : handleSubmit : handleAddMembers}
                        >
                            <Text style={{
                                fontFamily: Typography.fontFamily.primary,
                                fontWeight: Typography.fontWeight.medium,
                                fontSize: Typography.fontSize.md,
                                color: Colors.text.tertiary,
                                textAlign: 'center'
                            }}>
                                {selectedContacts?.length > 0 ? fromScreen === 'CircleDetails' ? 'Save Changes' : 'Create Circle' : 'Add Members'}
                            </Text>
                        </Pressable>
                    </View>
                </BottomSheetModalProvider>
            </View>
        </GestureHandlerRootView>
    )
}

export default AddCircle;

const styles = StyleSheet.create({
    loadingOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: Colors.overlay,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 9999,
    },
    footer: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        paddingVertical: Spacing.lg,
        paddingHorizontal: Spacing.lg,
        borderTopWidth: Borders.width.thin,
        borderColor: Colors.border.light,
        ...Shadows.sm,
    },
    button: {
        backgroundColor: Colors.primary,
        paddingVertical: Spacing.md,
        paddingHorizontal: Spacing.xl,
        borderRadius: Borders.radius.pill,
        alignItems: 'center',
        width: '100%',
    },
    camera: {
        width: 100,
        height: 100,
        backgroundColor: Colors.background.secondary,
        borderRadius: Borders.radius.circle,
        alignItems: 'center',
        justifyContent: 'center',
        borderColor: Colors.primary,
        borderWidth: Borders.width.medium,
        borderStyle: 'dashed',
        overflow: "hidden",
    },
    customHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: Spacing.lg,
        paddingVertical: Spacing.md,
        borderBottomWidth: Borders.width.thin,
        borderColor: Colors.border.light,
        ...Shadows.sm,
        zIndex: 1,
        marginTop: 30,
    },
    headerTitle: {
        fontSize: Typography.fontSize.lg,
        fontWeight: Typography.fontWeight.semibold,
        fontFamily: Typography.fontFamily.primary,
        color: Colors.text.tertiary,
        textAlign: 'left',
    },
    headerButton: {
        padding: Spacing.sm,
    },
    headerPlaceholder: {
        width: 40,
        padding: Spacing.sm,
    },
    spacer: {
        flex: 1
    },
    mainContainer: {
        flex: 1,
        backgroundColor: Colors.background.transparent
    },
    membersContainer: {
        paddingHorizontal: Spacing.sm,
    },
    label: {
        fontSize: Typography.fontSize.lg,
        marginLeft: Spacing.md,
        fontFamily: Typography.fontFamily.primary,
        fontWeight: Typography.fontWeight.semibold,
        color: Colors.text.tertiary,
    },
    editText: {
        fontFamily: Typography.fontFamily.primary,
        fontWeight: Typography.fontWeight.regular,
        color: Colors.text.secondary,
        fontSize: Typography.fontSize.sm,
    },
    monogram: {
        color: Colors.secondary,
        fontWeight: Typography.fontWeight.bold,
        fontSize: Typography.fontSize.md,
        fontFamily: Typography.fontFamily.primary,
    },
    editContainer: {
        flexDirection: 'row',
        borderColor: Colors.border.light,
        borderWidth: Borders.width.thin,
        borderRadius: Borders.radius.circle,
        paddingHorizontal: Spacing.md,
        paddingVertical: Spacing.xs,
        alignItems: 'center',
        gap: Spacing.sm,
        height: 24,
    },
    contactCircleSmall: {
        width: 70,
        height: 70,
        borderRadius: Borders.radius.circle,
        backgroundColor: Colors.background.tertiary,
        justifyContent: 'center',
        alignItems: 'center',
    },
    imageSmall: {
        width: 70,
        height: 70,
        borderRadius: Borders.radius.circle,
    },
    selectedContactsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        padding: Spacing.sm,
    },
    contactChip: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.background.transparent,
        paddingHorizontal: Spacing.xs,
        paddingVertical: Spacing.xs,
        borderRadius: Borders.radius.pill,
    },
    selectedContactsHeaderContainer: {
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
    },
    dottedBorder: {
        borderStyle: 'dashed',
        borderWidth: 1,
        borderColor: Colors.primary,
    },
});