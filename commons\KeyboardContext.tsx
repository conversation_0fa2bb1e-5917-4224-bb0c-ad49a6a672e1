import { createContext, useContext, useState, useEffect } from 'react';
import { Keyboard, Platform } from 'react-native';

interface KeyboardContextType {
  isKeyboardActive: boolean;
}

const KeyboardContext = createContext<KeyboardContextType>({ isKeyboardActive: false });

export function KeyboardProvider({ children }: { children: React.ReactNode }) {
  const [isKeyboardActive, setIsKeyboardActive] = useState(false);

  useEffect(() => {
    const showSubscription = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      () => {
        setIsKeyboardActive(true);
      }
    );
    const hideSubscription = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setIsKeyboardActive(false);
      }
    );

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  return (
    <KeyboardContext.Provider value={{ isKeyboardActive }}>
      {children}
    </KeyboardContext.Provider>
  );
}

export const useKeyboard = () => useContext(KeyboardContext);