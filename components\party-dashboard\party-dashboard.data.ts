import {gql} from '@apollo/client';

// Query to get a single party by ID
export const GET_PARTY_BY_ID = gql`
query GetPartyById($getPartyByIdId: ID!) {
    getPartyById(id: $getPartyByIdId) {
      ... on PartyResponse {
        status
        message
        result {
          party {
            id
            name
            time
            serviceLocation {
              city
            }
            partyType {
              landscapeImage
            }
            weather {
              avgTempC
            }
            vendorTypes {
              id
              icon {
                iconSrc
                id
              }
              name
            }
            coHosts {
              userId {
                email
                id
                firstName
                lastName
                phone
              }
            }
            totalBudget
            time
            id
            expectedGuestCount
            weather {
              avgTempC
               condition {
                icon
                text
              }
            }
            partyType {
              landscapeImage
            }
          }
        }
        status
        message
      }
      ... on PartyErrorResponse {
        status
        message
        errors {
          field
          message
        }
        status
        message
      }
    }
  }
`;

// Query to get all parties
export const GET_PARTIES = gql`
  query Parties {
    getParties {
      ... on PartiesResponse {
        result {
          parties {
            time
            serviceLocation {
              city
              state
              areas
            }
            vendorTypes {
              name
            }
            totalBudget
            id
            coHosts {
            userId {
              email
              }
            }
            partyType {
              name
            }
          }
        }
      }
      ... on PartiesErrorResponse {
        errors {
          field
          message
        }
      }
    }
  }
`;
export const DELETE_PARTY_BY_ID = gql`
  mutation Mutation($deletePartyId: ID!) {
  deleteParty(id: $deletePartyId) {
    ... on PartyResponse {
      result {
        party {
          id
        }
        }
      }
      ... on PartyErrorResponse {
        errors {
        field
        message
      }
    }
  }
}
`;

