import { View, Animated, Alert, TouchableOpacity } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useEffect, useRef, useState, useCallback } from 'react';
import { StyleSheet } from 'react-native';
import * as DocumentPicker from 'expo-document-picker';
import { uploadFiles, createDocumentAfterUpload, deleteDocument } from '@/app/(home)/Task/Task.utils';
import { MAX_FILE_SIZE_MB, validateFile } from './Attachments.utils';
import { useApolloClient, ApolloClient, NormalizedCacheObject } from '@apollo/client';
import { useUserStore } from '@/app/auth/userStore';
import { CircleCheck, Delete, Refresh } from '@/components/icons';
import { Icons, Colors } from '@/constants/DesignSystem';

interface FileUploadItemProps {
  file: DocumentPicker.DocumentPickerAsset & {
    type?: string;
    uploadedUrl?: string;
  };
  onRemove: (documentId: string) => void;
  onRetry?: () => void;
  onUploadComplete?: (status: 'success' | 'error', response?: any) => void;
  uploadDelay?: number;
}

type UploadStatus = 'queued' | 'uploading' | 'success' | 'error';

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
}

export function FileUploadItem({
  file,
  onRemove,
  onRetry,
  onUploadComplete,
  uploadDelay = 0
}: FileUploadItemProps) {
  const theme = useTheme();
  const apolloClient = useApolloClient() as ApolloClient<NormalizedCacheObject>;
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>('queued');
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const userId = useUserStore((state) => state.userData?.id) as string;
  // Add a ref to track if upload has been initiated
  const [uploadedKey, setUploadedKey] = useState<string | null>(null);
  const [uploadedUrl, setUploadedUrl] = useState<string | null>(null);
  const [documentId, setDocumentId] = useState<string | null>(null);
  const hasUploadStarted = useRef(false);

  const startUpload = useCallback(async () => {
    if (isUploading || hasUploadStarted.current) return;

    // Validate file size before upload
    const validation = validateFile(file);
    if (!validation.isValid) {
      setUploadError(validation.error || `File size exceeds ${MAX_FILE_SIZE_MB}MB limit`);
      setUploadStatus('error');
      onUploadComplete?.('error', { message: validation.error });
      return;
    }

    try {
      setIsUploading(true);
      hasUploadStarted.current = true;
      setUploadStatus('uploading');
      setUploadError(null);

      const fileToUpload = {
        name: file.name,
        type: file.mimeType || 'application/octet-stream',
        uri: file.uri,
        size: file.size || 0
      };

      const uploadResult = await uploadFiles([fileToUpload], "documents");

      if (uploadResult.failures.some((f: { filename: string }) => f.filename === file.name)) {
        const error = uploadResult.failures.find((f: { filename: string; message: string }) => f.filename === file.name);
        throw new Error(error?.message || 'Upload failed');
      }

      const uploadSuccess = uploadResult.successful.find((s: { originalName: string }) => s.originalName === file.name);
      if (!uploadSuccess) {
        throw new Error('File not found in response');
      }

      const documentResult = await createDocumentAfterUpload(uploadSuccess, userId, apolloClient);
      setDocumentId(documentResult.id);

      setUploadStatus('success');
      onUploadComplete?.('success', {
        ...uploadSuccess,
        documentId: documentResult.id,
        uploadedUrl: uploadSuccess.url,
        uploadedKey: uploadSuccess.key // Make sure URL is included in the response
      });
      setUploadedKey(uploadSuccess.key);
      setUploadedUrl(uploadSuccess.url);
      // Update the local file reference
      Object.assign(file, { uploadedUrl: uploadSuccess.url });



    } catch (error) {
      console.error('Upload/Document creation error:', error);
      setUploadError(error instanceof Error ? error.message : 'Upload failed');
      setUploadStatus('error');
      onUploadComplete?.('error', error);
    } finally {
      setIsUploading(false);
    }
  },
  // eslint-disable-next-line react-hooks/exhaustive-deps
  [file, onUploadComplete, apolloClient, userId]);

  // Handle retry
  const handleRetry = useCallback(() => {
    hasUploadStarted.current = false; // Reset the flag for retry
    startUpload();
  }, [startUpload]);

  // Initial upload
  useEffect(() => {
    let timeout: NodeJS.Timeout;

    if (uploadStatus === 'queued' && !hasUploadStarted.current) {
      timeout = setTimeout(() => {
        startUpload();
      }, uploadDelay);
    }

    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [startUpload, uploadDelay, uploadStatus]);



  const getFileIcon = () => {
    const extension = file.name.split('.').pop()?.toLowerCase();

    // Handle common file types
    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'webp':
        return 'file-image';
      case 'pdf':
        return 'file-pdf-box';
      case 'doc':
      case 'docx':
        return 'file-word';
      case 'xls':
      case 'xlsx':
        return 'file-excel';
      default:
        return 'file-document-outline';
    }
  };

  // Add this function to handle deletion
  const handleDelete = useCallback(() => {


    // First, remove from UI immediately
    if (documentId) {
      onRemove(documentId);
    }
    // If the file was successfully uploaded and we have the key, delete it in the background
    if (uploadedUrl && uploadedKey) {
      const key = uploadedKey;
      if (key) {
        // Fire and forget - don't await
        deleteDocument(key, apolloClient)
          .catch(error => {
            console.error('Background deletion failed:', {
              key,
              error,
              fileName: file.name
            });
          });
      }
    }

  },
  // eslint-disable-next-line react-hooks/exhaustive-deps
  [file, onRemove, uploadStatus, apolloClient]);

  // Update the remove button click handler
  const handleRemoveClick = useCallback(() => {
    Alert.alert(
      'Remove File',
      'Are you sure you want to remove this file?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Remove',
          onPress: handleDelete,
          style: 'destructive'
        }
      ],
      { cancelable: true }
    );
  }, [handleDelete]);

  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <View style={styles.fileInfo}>
          <View style={styles.iconContainer}>
            <MaterialCommunityIcons
              name={getFileIcon()}
              size={20}
              color={theme.colors.primary}
            />
          </View>
          <View style={styles.fileDetails}>
            <View style={styles.fileNameContainer}>
              <Text
                numberOfLines={1}
                style={styles.fileName}
              >
                {file.name}
              </Text>
              <Text style={styles.fileSize}>
                {formatFileSize(file.size || 0)}
              </Text>
              {uploadStatus === 'queued' && (
                <Text style={styles.queuedText}>Queued</Text>
              )}
              {uploadStatus === 'error' && (
                <Text style={styles.errorMessage} numberOfLines={2}>
                  {uploadError || 'Upload failed'}
                </Text>
              )}
            </View>
          </View>
        </View>

        <View style={styles.actions}>
          {uploadStatus === 'error' ? (
            <>
            <TouchableOpacity onPress={handleRemoveClick} disabled={isUploading}>
              <Delete size={Icons.size.md} color={Colors.error} />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleRetry} disabled={isUploading}>
              <Refresh size={Icons.size.md} color={Colors.primary} />
            </TouchableOpacity>
            </>
          ) : uploadStatus === 'success' ? (
            <>
              <TouchableOpacity onPress={handleRemoveClick} disabled={isUploading}>
                <Delete size={Icons.size.md} color={Colors.error} />
              </TouchableOpacity>
              <CircleCheck
                size={Icons.size.lg}
                color={Colors.primary}
              />
            </>
          ) : null}
        </View>
      </View>

      {(uploadStatus === 'uploading' || uploadStatus === 'queued') && (
        <View style={styles.progressContainer}>
          <Animated.View
            style={[
              styles.progressBar,
              {
                width: uploadStatus === 'uploading' ? '100%' : '0%',
                backgroundColor: uploadStatus === 'queued'
                  ? theme.colors.surfaceVariant
                  : theme.colors.primary,
              }
            ]}
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    padding: 12,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    marginBottom: 8,
    width: '100%',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 40,
    width: '100%',
  },
  fileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
    flexShrink: 1,
  },
  iconContainer: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    flexShrink: 0,
  },
  fileDetails: {
    flex: 1,
    marginLeft: 8,
    justifyContent: 'center',
    flexShrink: 1,
  },
  fileNameContainer: {
    justifyContent: 'center',
    flex: 1,
    flexShrink: 1,
  },
  fileName: {
    fontSize: 14,
    lineHeight: 20,
    color: '#000',
    flexShrink: 1,
  },
  queuedText: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
  },
  progressContainer: {
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    overflow: 'hidden',
    marginTop: 8,
    width: '100%',
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 40,
    flexShrink: 0,
  },
  actionButton: {
    margin: 0,
    marginLeft: -8,
  },
  successIcon: {
    marginRight: 4,
    width: 20,
    height: 20,
    textAlign: 'center',
    textAlignVertical: 'center',
  },
  errorMessage: {
    fontSize: 12,
    color: '#DC2626',
    marginTop: 2,
    lineHeight: 16,
    flexShrink: 1,
  },
  fileSize: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
    marginTop: 2,
  },
});