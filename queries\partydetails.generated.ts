import * as Types from '../generated/types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
};

export type Error = {
  __typename?: 'Error';
  field?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
};

export type Party = {
  __typename?: 'Party';
  id?: Maybe<Scalars['ID']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  weather?: Maybe<Weather>;
};

export type PartyErrorResponse = {
  __typename?: 'PartyErrorResponse';
  errors?: Maybe<Array<Maybe<Error>>>;
  status?: Maybe<ResponseStatus>;
};

export type PartyResponse = {
  __typename?: 'PartyResponse';
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<PartyWrapper>;
};

export type PartyResponseOrError = PartyErrorResponse | PartyResponse;

export type PartyWrapper = {
  __typename?: 'PartyWrapper';
  party?: Maybe<Party>;
};

export type Query = {
  __typename?: 'Query';
  getPartyById?: Maybe<PartyResponseOrError>;
};


export type QueryGetPartyByIdArgs = {
  id: Scalars['ID']['input'];
};

export enum ResponseStatus {
  Failure = 'FAILURE',
  Success = 'SUCCESS'
}

export type Weather = {
  __typename?: 'Weather';
  avgTempC?: Maybe<Scalars['Float']['output']>;
  avgTempF?: Maybe<Scalars['Float']['output']>;
  condition?: Maybe<WeatherCondition>;
  weatherUrl?: Maybe<Scalars['String']['output']>;
};

export type WeatherCondition = {
  __typename?: 'WeatherCondition';
  icon?: Maybe<Scalars['String']['output']>;
  text?: Maybe<Scalars['String']['output']>;
};

export type GetPartyByIdQueryVariables = Types.Exact<{
  getPartyByIdId: Types.Scalars['ID']['input'];
}>;


export type GetPartyByIdQuery = { __typename?: 'Query', getPartyById?: { __typename?: 'PartyErrorResponse', status?: Types.ResponseStatus | null, errors?: Array<{ __typename?: 'Error', field?: string | null, message?: string | null } | null> | null } | { __typename?: 'PartyResponse', message?: string | null, result?: { __typename?: 'PartyWrapper', party?: { __typename?: 'Party', id?: string | null, name?: string | null, weather?: { __typename?: 'Weather', weatherUrl?: string | null, avgTempC?: number | null, avgTempF?: number | null, condition?: { __typename?: 'WeatherCondition', icon?: string | null, text?: string | null } | null } | null } | null } | null } | null };


export const GetPartyByIdDocument = gql`
    query GetPartyById($getPartyByIdId: ID!) {
  getPartyById(id: $getPartyByIdId) {
    ... on PartyResponse {
      message
      result {
        party {
          id
          name
          weather {
            weatherUrl
            avgTempC
            avgTempF
            condition {
              icon
              text
            }
          }
        }
      }
    }
    ... on PartyErrorResponse {
      errors {
        field
        message
      }
      status
    }
  }
}
    `;

/**
 * __useGetPartyByIdQuery__
 *
 * To run a query within a React component, call `useGetPartyByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPartyByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPartyByIdQuery({
 *   variables: {
 *      getPartyByIdId: // value for 'getPartyByIdId'
 *   },
 * });
 */
export function useGetPartyByIdQuery(baseOptions: Apollo.QueryHookOptions<GetPartyByIdQuery, GetPartyByIdQueryVariables> & ({ variables: GetPartyByIdQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPartyByIdQuery, GetPartyByIdQueryVariables>(GetPartyByIdDocument, options);
      }
export function useGetPartyByIdLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPartyByIdQuery, GetPartyByIdQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPartyByIdQuery, GetPartyByIdQueryVariables>(GetPartyByIdDocument, options);
        }
export function useGetPartyByIdSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetPartyByIdQuery, GetPartyByIdQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetPartyByIdQuery, GetPartyByIdQueryVariables>(GetPartyByIdDocument, options);
        }
export type GetPartyByIdQueryHookResult = ReturnType<typeof useGetPartyByIdQuery>;
export type GetPartyByIdLazyQueryHookResult = ReturnType<typeof useGetPartyByIdLazyQuery>;
export type GetPartyByIdSuspenseQueryHookResult = ReturnType<typeof useGetPartyByIdSuspenseQuery>;
export type GetPartyByIdQueryResult = Apollo.QueryResult<GetPartyByIdQuery, GetPartyByIdQueryVariables>;