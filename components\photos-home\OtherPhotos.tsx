import { StyleSheet, Pressable, View } from 'react-native'
import { Divider, Text } from 'react-native-paper'
import { router } from 'expo-router'
import { Colors, Typography, Spacing, Borders, Icons } from '@/constants/DesignSystem'
import { Favourite, Delete, Next } from '../icons'

interface OtherPhotosSectionProps {
  icon: React.ReactNode
  title: string
  onPress: () => void
}

function OtherPhotosSection({ icon, title, onPress }: OtherPhotosSectionProps) {
  return (
    <Pressable
      style={({ pressed }) => [
        styles.sectionContainer,
        pressed && styles.pressed,
      ]}
      onPress={onPress}
    >
      <View style={styles.sectionContent}>
        {icon}
        <Text variant="titleSmall" style={styles.sectionTitle}>
          {title}
        </Text>
      </View>
      <Next size={Icons.size.sm} color={Colors.mediumGray} />
    </Pressable>
  )
}

export function OtherPhotos() {
  return (
    <View style={styles.container}>
      <Text variant="titleMedium" style={styles.header}>
        Others
      </Text>
      <View style={styles.sectionsContainer}>
        <OtherPhotosSection
          icon={<Favourite size={Icons.size.md} color={Colors.secondary} />}
          title="Favorites"
          onPress={() => router.push({
            pathname: '/(home)/photosAlbumView/photosAlbumView',
            params: { albumType: 'FAVORITES' }
          })}
        />
        <Divider style={{ marginHorizontal: Spacing.lg }} />
        <OtherPhotosSection
          icon={<Delete size={Icons.size.md} color={Colors.secondary} />}
          title="Deleted"
          onPress={() => router.push({
            pathname: '/(home)/photosAlbumView/photosAlbumView',
            params: { albumType: 'DELETED' }
          })}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: Spacing.md,
  },
  header: {
    marginBottom: Spacing.lg,
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.secondary,
  },
  sectionsContainer: {
    backgroundColor: Colors.background.tertiary,
    borderRadius: Borders.radius.md,
    overflow: 'hidden',
  },
  sectionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Spacing.md,
    backgroundColor: Colors.background.tertiary,
  },
  sectionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  sectionTitle: {
    color: Colors.text.secondary,
    fontFamily: Typography.fontFamily.primary,
    fontWeight: Typography.fontWeight.medium,
  },
  chevron: {
    fontSize: Typography.fontSize.xl,
    color: Colors.mediumGray,
  },
  pressed: {
    opacity: 0.7,
  },
})

export default OtherPhotos
