import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '@/constants/DesignSystem'; 

interface InitialsAvatarProps {
    firstName: string;
    lastName?: string | null;
}

const InitialsAvatar: React.FC<InitialsAvatarProps> = ({ firstName, lastName }) => {
    const getInitials = () => {
        const firstInitial = firstName ? firstName[0].toUpperCase() : '';
        const lastInitial = lastName ? lastName[0].toUpperCase() : '';
        return `${firstInitial}${lastInitial}`;
    };

    return (
        <View style={styles.circle}>
            <Text style={styles.text}>{getInitials()}</Text>
        </View>
    );
};

const styles = StyleSheet.create({
    circle: {
        width: 40, 
        height: 40,
        borderRadius: 20,
        backgroundColor: Colors.background.tertiary,
        justifyContent: 'center',
        alignItems: 'center',
    },
    text: {
        color: Colors.secondary, 
        fontSize: 12, 
        fontWeight: 'bold',
        fontFamily: 'Plus Jakarta Sans',
    }
});

export default InitialsAvatar;
