import * as React from "react";
import Svg, { <PERSON>, <PERSON>, De<PERSON>, <PERSON><PERSON><PERSON><PERSON>, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const AudioRecord = ({
  size,
  color,
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 13"
      fill="none"
      {...props}
    >
      {variant === 'filled' ? (
        <>
          <Path
            fill={hasGradient ? "url(#Audio_record-1_svg__a)" : color}
            fillRule="evenodd"
            d="M5.786.487a2.32 2.32 0 0 0-2.32 2.32v2.697a2.32 2.32 0 1 0 4.64 0V2.807a2.32 2.32 0 0 0-2.32-2.32m-.59 9.455v.955a.59.59 0 1 0 1.18 0v-.955a4.134 4.134 0 0 0 3.937-********** 0 1 0-1.18 0 2.953 2.953 0 0 1-2.954 2.953h-.787a2.953 2.953 0 0 1-2.953-2.952.59.59 0 1 0-1.18 0 4.134 4.134 0 0 0 3.936 4.129"
            clipRule="evenodd"
          />
          {hasGradient && (
            <Defs>
              <LinearGradient
                id="Audio_record-1_svg__a"
                x1={5.786}
                x2={5.786}
                y1={-0.84}
                y2={14.016}
                gradientUnits="userSpaceOnUse"
              >
                <Stop stopColor={gradientStartColor} />
                <Stop offset={1} stopColor={gradientEndColor} />
              </LinearGradient>
            </Defs>
          )}
        </>
      ) : (
        <>
          <G
            stroke={color}
            strokeLinecap="round"
            strokeLinejoin="round"
            clipPath="url(#Audio_record_svg__a)"
          >
            <Path d="M7.923 6.282a1.923 1.923 0 1 1-3.846 0V3.59a1.923 1.923 0 0 1 3.846 0z" />
            <Path d="M9.846 6.667a3.455 3.455 0 0 1-3.461 3.461h-.77a3.453 3.453 0 0 1-3.461-3.461M6 10.128v1.539" />
          </G>
          <Defs>
            <ClipPath id="Audio_record_svg__a">
              <Path fill="#fff" d="M0 .667h12v12H0z" />
            </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  )
};
export default AudioRecord;
