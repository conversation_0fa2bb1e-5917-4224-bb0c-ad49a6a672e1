import { Contact } from "@/components/create-circle/EditMembers";

export type AddCircleRootStackList = {
    AddCircle: {
        fromScreen?: string;
        details?: any;
        onUpdate?: () => void;
    }
    EditMembers: {
        members: Contact[];
        initialMembers: Contact[];
        circleName: string;
        onGoBack: (members: Contact[]) => void;
    }
    CircleDetails: { 
        circleId: string;
        showSuccessAnimation?: boolean;
    };
}