import { create } from 'zustand';
import { AttachmentFile } from './Attachments.types';
import * as DocumentPicker from 'expo-document-picker';

interface TaskAttachment {
  __typename?: "Document";
  description?: string | null;
  documentType?: string;
  documentUrl: string;
  name: string;
  id?: string;
}

interface CombinedAttachments {
  existing: TaskAttachment[];
  uploading: DocumentPicker.DocumentPickerAsset[];
}

interface AttachmentPreviewStore {
  selectedFile: AttachmentFile | null;
  setSelectedFile: (file: AttachmentFile | null) => void;
  combinedAttachmentsByTask: Record<string, CombinedAttachments>;
  setCombinedAttachmentsForTask: (taskId: string, attachments: CombinedAttachments) => void;
  createTemporaryTaskId: () => string;
  clearTemporaryTask: (tempId: string) => void;
}

export const useAttachmentPreviewStore = create<AttachmentPreviewStore>((set) => ({
  selectedFile: null,
  setSelectedFile: (file) => set({ selectedFile: file }),
  combinedAttachmentsByTask: {},
  setCombinedAttachmentsForTask: (taskId, attachments) => 
    set((state) => ({
      combinedAttachmentsByTask: {
        ...state.combinedAttachmentsByTask,
        [taskId]: attachments
      }
    })),
  createTemporaryTaskId: () => {
    const tempId = `temp-${Date.now()}`;
    set((state) => ({
      combinedAttachmentsByTask: {
        ...state.combinedAttachmentsByTask,
        [tempId]: { existing: [], uploading: [] }
      }
    }));
    return tempId;
  },
  clearTemporaryTask: (tempId) => 
    set((state) => {
      const { [tempId]: _, ...rest } = state.combinedAttachmentsByTask;
      return { combinedAttachmentsByTask: rest };
    })
})); 