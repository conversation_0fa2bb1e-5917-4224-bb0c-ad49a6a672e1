import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  ScrollView,
  Image as RNImage,
  StyleSheet,
  NativeSyntheticEvent,
  NativeScrollEvent,
  ActivityIndicator,
  Text,
  TouchableOpacity,
  ImageSourcePropType,
  Dimensions,
} from 'react-native';
import AnimatedDotsCarousel from 'react-native-animated-dots-carousel';
import { Colors, Spacing, Typography} from '@/constants/DesignSystem';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';

interface Image {
  id: string;
  image_src: ImageSourcePropType | string;
  onPress?: () => void;
}

interface ImageCarouselProps {
  images: Image[];
  showDotPagination?: boolean;
  showNumberPagination?: boolean;
  showCreateEventButton?: boolean;
  onCreateEventPress?: () => void;
  buttonText?: string;
  isOnBoarding?: boolean;
  currentIndex?: number;
  onIndexChange?: (index: number) => void;
}

interface ImageItemProps {
  image_src: ImageSourcePropType | string;
  containerWidth: number;
  showCreateEventButton?: boolean;
  onCreateEventPress?: () => void;
  buttonText?: string;
  onPress?: () => void;
}

function ImageItem({
  image_src,
  containerWidth,
  showCreateEventButton,
  onCreateEventPress,
  buttonText = 'Create Event',
  onPress
}: ImageItemProps) {
  const [isLoading, setIsLoading] = useState(true);
  const source = typeof image_src === 'string' ? { uri: image_src } : image_src;

  return (
    <TouchableOpacity
      style={[styles.imageContainer, { width: containerWidth }]}
      onPress={onPress}
      disabled={!onPress}
    >
      <RNImage
        source={source}
        style={styles.image}
        resizeMode="cover"
        onLoadStart={() => setIsLoading(true)}
        onLoadEnd={() => setIsLoading(false)}
        onError={(error) => console.error('Image loading error:', error.nativeEvent.error)}
      />
      {isLoading && (
        <View style={styles.loaderContainer}>
          <FastPartyActivityIndicator />
        </View>
      )}
      {showCreateEventButton && (
        <TouchableOpacity
          style={styles.createEventButton}
          onPress={onCreateEventPress}
        >
          <Text style={styles.buttonText}>{buttonText}</Text>
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
}

export function ImageCarousel({
  images,
  showDotPagination = true,
  showNumberPagination = false,
  showCreateEventButton = false,
  onCreateEventPress,
  buttonText = 'Create Event',
  isOnBoarding = false,
  currentIndex: controlledIndex,
  onIndexChange,
}: ImageCarouselProps) {
  const [internalIndex, setInternalIndex] = useState(0);
  const [containerWidth, setContainerWidth] = useState(Dimensions.get('window').width);
  const scrollViewRef = useRef<ScrollView>(null);

  const isControlled = controlledIndex !== undefined && onIndexChange !== undefined;
  const currentIndex = isControlled ? controlledIndex : internalIndex;

  useEffect(() => {
    if (scrollViewRef.current && containerWidth > 0 && currentIndex >= 0 && currentIndex < images.length) {
      scrollViewRef.current.scrollTo({ x: currentIndex * containerWidth, animated: true });
    }
  }, [currentIndex, containerWidth, images]);

  const handleUserScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    if (containerWidth > 0) {
      const newIndex = Math.round(offsetX / containerWidth);
      if (newIndex !== currentIndex) {
        if (isControlled && onIndexChange) {
          onIndexChange(newIndex);
        } else {
          setInternalIndex(newIndex);
        }
      }
    }
  };

  const onLayout = (event: any) => {
    setContainerWidth(event.nativeEvent.layout.width);
  };

  return (
    <View
      style={[styles.container, { height: isOnBoarding ? '100%' : 250 }]}
      onLayout={onLayout}
    >
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={handleUserScroll}
        scrollEventThrottle={16}
      >
        {images.map((image) => (
          <ImageItem
            key={image.id}
            image_src={image.image_src}
            containerWidth={containerWidth}
            showCreateEventButton={showCreateEventButton}
            onCreateEventPress={onCreateEventPress}
            onPress={image.onPress}
          />
        ))}
      </ScrollView>

      {showNumberPagination && (
        <View style={styles.numberPaginationContainer}>
          <View style={styles.numberPaginationBox}>
            <Text style={styles.numberPaginationText}>
              {`${currentIndex + 1}/${images.length}`}
            </Text>
          </View>
        </View>
      )}

      {showDotPagination && (
        <View style={[styles.dotsContainer]}>
          <AnimatedDotsCarousel
            length={images.length}
            currentIndex={currentIndex}
            maxIndicators={images.length > 4 ? 4 : images.length}
            interpolateOpacityAndColor
            activeIndicatorConfig={{
              color: isOnBoarding ? Colors.secondary : Colors.background.primary,
              margin: 3,
              opacity: 1,
              size: isOnBoarding ? Typography.fontSize.sm : Typography.fontSize.xs,
            }}
            inactiveIndicatorConfig={{
              color: Colors.mediumGray,
              margin: 3,
              opacity: 0.5,
              size: Typography.fontSize.xs,
            }}
            decreasingDots={[
              {
                config: {
                  color: Colors.mediumGray,
                  margin: 3,
                  opacity: 0.5,
                  size: Typography.fontSize.xs,
                },
                quantity: 1,
              },
              {
                config: {
                  color: Colors.mediumGray,
                  margin: 3,
                  opacity: 0.5,
                  size: Typography.fontSize.xs,
                },
                quantity: 1,
              },
            ]}
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: Colors.background.secondary,
    position: 'relative',
  },
  imageContainer: {
    height: '100%',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  loaderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background.primary,
  },
  dotsContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    alignItems: 'center',
    bottom: Spacing.ml,
    
  },
  numberPaginationContainer: {
    position: 'absolute',
    bottom: 20,
    width: '100%',
    alignItems: 'flex-end',
    paddingRight: '4%',
  },
  numberPaginationBox: {
    backgroundColor: Colors.overlay,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  numberPaginationText: {
    color: Colors.text.inverse,
    fontSize: 14,
    fontWeight: '600',
  },
  createEventButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: Colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  buttonText: {
    color: Colors.text.inverse,
    fontSize: 14,
    fontWeight: '600',
  },
});