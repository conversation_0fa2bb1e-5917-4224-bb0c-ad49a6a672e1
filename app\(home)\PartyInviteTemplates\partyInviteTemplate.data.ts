import { gql } from "@apollo/client";

export const GET_PARTY_INVITE_TEMPLATES = gql`
query GetMdInvitationTemplates($filter: MdInvitationTemplateFilterInput, $pagination: PaginationInput) {
  getMdInvitationTemplates(filter: $filter, pagination: $pagination) {
    ... on MdInvitationTemplatesResponse {
      result {
        mdInvitationTemplates {
          id
          imageUrl
          name
          tags {
            id
            name
            slug
          }
        }
      }
      pagination {
        totalItems
        totalPages
        pageSize
        currentPage
      }
    }
  }
}
`;
export const GET_TEMPLATE_TAGS = gql`
query GetAllMdInvitationTemplateTags {
  getAllMdInvitationTemplateTags {
    ... on MdInvitationTemplateTagsResponse {
      result {
        tags {
          name
          id
        }
      }
    }
  }
}
`;
export const CREATE_MEDIA = gql`
mutation CreateMedia($input: MediaInput!) {
  createMedia(input: $input) {
    ... on MediaResponse {
      result {
        media {
          id
          url
          title
        }
      }
    }
    ... on MediaErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}
`;