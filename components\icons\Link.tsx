import * as React from "react";
import Svg, {
  G,
  <PERSON>,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
} from "react-native-svg";
import { CommonProps } from ".";

const Link = ({
  size = 12,
  color = "#34434D",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G
            strokeLinecap="round"
            strokeLinejoin="round"
            clipPath="url(#Link_svg__a)"
          >
            <Path
              stroke="url(#Link_svg__b)"
              d="M5.19229 2.76923L6.37504 1.58639C7.49023 0.471203 9.29828 0.471203 10.4135 1.58639C11.5287 2.70159 11.5287 4.50966 10.4135 5.62486L9.23075 6.80769"
            />
            <Path
              stroke="url(#Link_svg__c)"
              d="M6.8076 9.23078L5.62485 10.4136C4.50966 11.5288 2.70159 11.5288 1.58639 10.4136C0.471203 9.29847 0.471204 7.49035 1.58639 6.37515L2.76914 5.19232"
            />
            <Path
              stroke="url(#Link_svg__d)"
              d="M7.61538 4.38458L6 5.99997"
            />
            <Path
              stroke="url(#Link_svg__e)"
              d="M4.38461 7.61541L6.00001 5.99998"
            />
          </G>
          <Defs>
            <LinearGradient
              id="Link_svg__b"
              x1={5.192}
              x2={10.414}
              y1={1.586}
              y2={6.808}
              gradientUnits="userSpaceOnUse"
            >
              <Stop stopColor={gradientStartColor} />
              <Stop offset={1} stopColor={gradientEndColor} />
            </LinearGradient>
            <LinearGradient
              id="Link_svg__c"
              x1={1.586}
              x2={6.808}
              y1={10.414}
              y2={5.192}
              gradientUnits="userSpaceOnUse"
            >
              <Stop stopColor={gradientStartColor} />
              <Stop offset={1} stopColor={gradientEndColor} />
            </LinearGradient>
            <LinearGradient
              id="Link_svg__d"
              x1={6}
              x2={7.615}
              y1={5.999}
              y2={4.384}
              gradientUnits="userSpaceOnUse"
            >
              <Stop stopColor={gradientStartColor} />
              <Stop offset={1} stopColor={gradientEndColor} />
            </LinearGradient>
            <LinearGradient
              id="Link_svg__e"
              x1={6}
              x2={4.384}
              y1={5.999}
              y2={7.615}
              gradientUnits="userSpaceOnUse"
            >
              <Stop stopColor={gradientStartColor} />
              <Stop offset={1} stopColor={gradientEndColor} />
            </LinearGradient>
            <ClipPath id="Link_svg__a">
              <Path fill="#fff" d="M0 0h12v12H0z" />
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G
            stroke={color}
            strokeLinecap="round"
            strokeLinejoin="round"
            clipPath="url(#Link_svg__a)"
          >
            <Path d="M5.19229 2.76923L6.37504 1.58639C7.49023 0.471203 9.29828 0.471203 10.4135 1.58639C11.5287 2.70159 11.5287 4.50966 10.4135 5.62486L9.23075 6.80769" />
            <Path d="M6.8076 9.23078L5.62485 10.4136C4.50966 11.5288 2.70159 11.5288 1.58639 10.4136C0.471203 9.29847 0.471204 7.49035 1.58639 6.37515L2.76914 5.19232" />
            <Path d="M7.61538 4.38458L6 5.99997" />
            <Path d="M4.38461 7.61541L6.00001 5.99998" />
          </G>
          <Defs>
            <ClipPath id="Link_svg__a">
              <Path fill="#fff" d="M0 0h12v12H0z" />
            </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  );
};

export default Link; 