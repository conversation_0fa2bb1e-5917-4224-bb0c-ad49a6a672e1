import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';

export const USER_STORAGE_KEY = 'user_data';
const TOKEN_KEY = 'user_token';

export interface UserData {
  phoneVerified: boolean;
  profilePicture: string | null;
  id: string;
  firstName: string;
  lastName: string;
  email: string | null;
  phone: string;
  emailVerified: boolean;
  externalId: string;
  isActive: boolean;
  isRegistered: boolean;
}

export const userStorage = {
  async saveUser(userData: UserData): Promise<void> {
    try {
      await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userData));
    } catch (error) {
      console.error('Error saving user data:', error);
    }
  },

  async getUser(): Promise<UserData | null> {
    try {
      const userData = await AsyncStorage.getItem(USER_STORAGE_KEY);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error getting user data:', error);
      return null;
    }
  },

  async clearUser(): Promise<void> {
    try {
      await AsyncStorage.removeItem(USER_STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing user data:', error);
    }
  },

  // Save token
  saveToken: async (token: string) => {
    try {
      await SecureStore.deleteItemAsync(TOKEN_KEY);
      await SecureStore.setItemAsync(TOKEN_KEY, token);
    } catch (error) {
      console.error('Error saving token:', error);
    }
  },

  // Get token
  getToken: async () => {
    try {
      return await SecureStore.getItemAsync(TOKEN_KEY);
    } catch (error) {
      console.error('Error getting token:', error);
      return null;
    }
  },

  // Clear token
  clearToken: async () => {
    try {
      await SecureStore.deleteItemAsync(TOKEN_KEY);
    } catch (error) {
      console.error('Error clearing token:', error);
    }
  }
};