export interface Tab {
    id: string;
    iconSrc?: string;
    name?: string;
  }
  
  export interface DashboardTabs {
    tasks?: Tab;
    apps?: Tab;
    eventsDb?: Tab;
    guests?: Tab;
    messages?: Tab;
    home?: Tab;
    myEvents?: Tab;
    publicEvents?: Tab;
  }
  
  export interface NavBarDashboard {
    tabs: DashboardTabs;
  }
  
  export interface NavBars {
    hostEventDashboard: NavBarDashboard;
    userDashboard: NavBarDashboard;
  }
  
  export interface Theme {
    backgroundColor: string;
    primaryColor: string;
    secondaryColor: string;
    textColor: string;
  }
  
  export interface Themes {
    dark: Theme;
    light: Theme;
  }
  
  export interface MdAppSettings {
    navBars: NavBars;
    themes: Themes;
    version: string;
  }
  
  export interface MdAppSettingsResult {
    mdAppSettings: MdAppSettings;
  }
  
  export interface MdAppSettingsResponse {
    result: MdAppSettingsResult;
  }
  
  export interface GetMdAppSettingsData {
    getMdAppSettings: MdAppSettingsResponse;
  }