import { ThemedView } from "@/components/UI/ThemedView";
import { ThemedText } from "@/components/UI/ThemedText";
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { usePartyDetails } from '../party-dashboard.hooks';
import { ActivityIndicator, Card, IconButton } from 'react-native-paper';
import { StyleSheet, View } from 'react-native';
import { router } from 'expo-router';
import { format } from 'date-fns';
import { PreferenceItem } from './PreferenceItem';
import { PREFERENCE_ITEM_LABELS } from "@/constants/partyDashboard";

interface OverviewProps {
    partyId: string;
}

export default function Overview({ partyId }: OverviewProps) {
    const { data, loading, error } = usePartyDetails(partyId);

    if (loading) {
        return (
            <ThemedView style={styles.container}>
                <ActivityIndicator />
            </ThemedView>
        );
    }

    if (error) {
        return (
            <ThemedView style={styles.container}>
                <ThemedText type="subtitle" style={styles.errorText}>
                    Error loading party details: {error.message}
                </ThemedText>
            </ThemedView>
        );
    }

    const party = data?.getPartyById?.result?.party;
    const handleNavigateToEdit = () => {
        router.push(`/EventsDashboard/AddParty?partyId=${partyId}`);
    };

    return (
        <GestureHandlerRootView style={styles.container}>
            <Card mode="contained" style={styles.card}>
                <View style={styles.headerContainer}>
                    <ThemedText type="title" style={styles.title}>
                        Party Preferences
                    </ThemedText>
                    <IconButton
                        icon="pencil"
                        size={20}
                        onPress={handleNavigateToEdit}
                        style={styles.editIcon}
                    />
                </View>
                <Card.Content>
                    <PreferenceItem 
                        label="Date"
                        value={party?.time ? format(new Date(party.time), 'do MMM, EEE') : 'Not set'}
                        isError={!party?.time}
                    />
                    <PreferenceItem 
                        label="Time"
                        value={party?.time ? format(new Date(party.time), 'h:mm a') : 'Not set'}
                        isError={!party?.time}
                    />
                    <PreferenceItem 
                        label="Location"
                        value={`${party?.serviceLocation?.city || 'City not set'}`}
                        isError={!party?.serviceLocation?.city }
                    />
                    <PreferenceItem 
                        label="No. of Guests"
                        value={party?.expectedGuestCount ? party.expectedGuestCount.toString() : 'Not set'}
                        isError={!party?.expectedGuestCount}
                    />
                    <PreferenceItem 
                        label="Budget"
                        value={party?.totalBudget ? `₹${party.totalBudget.toLocaleString()}` : 'Not set'}
                        isError={!party?.totalBudget}
                    />
                    <PreferenceItem 
                        label="Services"
                        value={party?.vendorTypes?.length ? 
                            party.vendorTypes.map(vendor => vendor.name).join(', ') : 
                            'No services selected'}
                        isError={!party?.vendorTypes?.length}
                        maxLines={2}
                    />
                    <PreferenceItem 
                        label={PREFERENCE_ITEM_LABELS.VENDOR_OVERVIEW_COHOST}
                        value={Array.isArray(party?.coHosts) ? party.coHosts : []}
                        isError={!party?.coHosts?.length}
                        maxLines={2}
                    />
                </Card.Content>
            </Card>
        </GestureHandlerRootView>
    );
}

const styles = StyleSheet.create({
    container: {
        width: '100%',
        padding: 16,
    },
    card: {
        borderRadius: 20,
        backgroundColor: '#ffffff',
    },
    headerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        columnGap: 16,
        paddingHorizontal: 16,
        paddingTop: 16,
        paddingBottom: 8,
    },
    title: {
        fontSize: 20,
        fontWeight: '500',
    },
    editIcon: {
        margin: 0,
    },
    errorText: {
        color: '#dc3545',
        textAlign: 'center',
    }
});
