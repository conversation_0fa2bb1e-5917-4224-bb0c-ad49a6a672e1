import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const Keyboard = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      fill="url(#Keyboard-1_svg__a)"
      d="M10.23 3.142c.425 0 .77.32.77.715v4.287c0 .394-.345.714-.77.714H1.77c-.425 0-.77-.32-.77-.714V3.857c0-.395.345-.715.77-.715zM2.786 6.594a.417.417 0 0 0 0 .834H3.5l.084-.009a.417.417 0 0 0 0-.816l-.084-.01zm2.143 0a.417.417 0 0 0 0 .834H7.07l.084-.009a.417.417 0 0 0 0-.816l-.084-.01zm3.571 0a.417.417 0 0 0 0 .834h.715l.084-.009a.418.418 0 0 0 0-.816l-.084-.01zM2.786 4.808a.418.418 0 0 0 0 .834H3.5l.084-.01a.417.417 0 0 0 0-.816L3.5 4.809zm1.904 0a.418.418 0 0 0 0 .834h.714l.084-.01a.417.417 0 0 0 0-.816l-.084-.008zm1.906 0a.418.418 0 0 0 0 .834h.714l.084-.01a.417.417 0 0 0 0-.816l-.084-.008zm1.904 0a.418.418 0 0 0 0 .834h.714l.084-.01a.417.417 0 0 0 0-.816l-.084-.008z"
    />
    <Defs>
      <LinearGradient
        id="Keyboard-1_svg__a"
        x1={6}
        x2={6}
        y1={2.452}
        y2={10.173}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <Path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M10.442 2.77H1.558a.81.81 0 0 0-.808.807v4.846c0 .446.362.808.808.808h8.884a.81.81 0 0 0 .808-.808V3.577a.81.81 0 0 0-.808-.808M4.788 7.01h2.424M2.365 7.01h.808m5.654 0h.808M2.365 4.99h.808M4.52 4.99h.807M6.673 4.99h.808M8.827 4.99h.808"
    />
        </>
      )}
    </Svg>
  );
};
export default Keyboard;
