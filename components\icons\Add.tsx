import * as React from "react";
import Svg, {
  G,
  Path,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
} from "react-native-svg";
import { CommonProps } from ".";

const Add = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            clipPath="url(#Add-1_svg__a)"
          >
            <Path stroke="url(#Add-1_svg__b)" d="M6 1.5v9" />
            <Path stroke="url(#Add-1_svg__c)" d="M1.5 5.972h9" />
          </G>
          <Defs>
            <LinearGradient
              id="Add-1_svg__b"
              x1={6.5}
              x2={6.5}
              y1={0.414}
              y2={12.569}
              gradientUnits="userSpaceOnUse"
            >
              <Stop stopColor={gradientStartColor} />
              <Stop offset={1} stopColor={gradientEndColor} />
            </LinearGradient>
            <LinearGradient
              id="Add-1_svg__c"
              x1={6}
              x2={6}
              y1={5.852}
              y2={7.202}
              gradientUnits="userSpaceOnUse"
            >
              <Stop stopColor={gradientStartColor} />
              <Stop offset={1} stopColor={gradientEndColor} />
            </LinearGradient>
            <ClipPath id="Add-1_svg__a">
              <Path fill="#fff" d="M0 0h12v12H0z" />
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G
            stroke={color}
            strokeLinecap="round"
            strokeLinejoin="round"
            clipPath="url(#Add_svg__a)"
          >
            <Path d="M6 .75v10.5M.75 5.968h10.5" />
          </G>
          <Defs>
            <ClipPath id="Add_svg__a">
              <Path fill="#fff" d="M0 0h12v12H0z" />
            </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  );
};
export default Add;
