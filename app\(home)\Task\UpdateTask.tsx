import { View } from 'react-native';
import { Text } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useQuery, useMutation } from '@apollo/client';
import { GET_TASK_BY_ID_FOR_UPDATE, UPDATE_TASK } from './Task.data';
import { LoadingIndicator } from '@/components/UI/ReusableComponents/LoadingIndicator';
import { TaskForm } from '@/components/Task/TaskForm';
import { useParties, useAssignees } from './hooks/useTaskData';
import { useEventStore } from '@/store/eventStore';
import { useToast } from '@/components/Toast/useToast';
import { TaskNames, TaskStatus } from '@/constants/Task.constants';
import { useRef, useState, useEffect } from 'react';
import TaskHeader from '@/components/Task/TaskHeader';
import { GET_TASK_COMMENTS_BY_TASK_ID } from '@/components/Task/Comments/TaskComments.data';

export default function UpdateTask() {
  const { taskId } = useLocalSearchParams<{ taskId: string }>();
  const router = useRouter();
  const toast = useToast();
  const selectedEventId = useEventStore((state) => state.selectedEventId);
  const formRef = useRef<any>(null);
  const [updateTaskStatus] = useMutation(UPDATE_TASK, {
    refetchQueries: [
      {
        query: GET_TASK_BY_ID_FOR_UPDATE,
        variables: { getTaskByIdId: taskId }
      }
    ],
    onError: () => {
      setTaskState(prev => ({
        ...prev,
        isCompleted: !prev.isCompleted
      }));
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    },
    onCompleted: async (data) => {
      const newStatus = data?.updateTask?.result?.task?.status;
      if (newStatus) {
        setTaskState({
          status: newStatus,
          isCompleted: newStatus === TaskStatus.COMPLETED
        });
      }
      await taskRefetch();
      taskCommentsRefetch?.();
    }
  });

  const [updateTaskTitle] = useMutation(UPDATE_TASK, {
    onError: () => {
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    }
  });

  const [updateTaskAssignee] = useMutation(UPDATE_TASK, {
    onError: () => {
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    }
  });

  const [updateTaskDueDate] = useMutation(UPDATE_TASK, {
    onError: () => {
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    }
  });

  const [updateTaskParty] = useMutation(UPDATE_TASK, {
    onError: () => {
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    }
  });

  const [updateTaskDescription] = useMutation(UPDATE_TASK, {
    onError: () => {
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    }
  });

  const [updateTaskAttachments] = useMutation(UPDATE_TASK, {
    onError: () => {
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    },
    onCompleted: () => {
      taskRefetch();
    }
  });

  const [updateTaskStatusChange] = useMutation(UPDATE_TASK, {
    onError: () => {
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    },
    onCompleted: () => {
      taskRefetch();
    }
  });

  const { loading: taskLoading, error: taskError, data: taskData, refetch: taskRefetch } = useQuery(GET_TASK_BY_ID_FOR_UPDATE, {
    variables: {
      getTaskByIdId: taskId
    },
    skip: !taskId,
    onCompleted: (data) => {
      console.log('Task Data Response:', {
        status: data?.getTaskById?.status,
        task: data?.getTaskById?.result?.task
      });
    }
  });

  const { error: taskCommentsError, data: taskCommentsData, refetch: taskCommentsRefetch } = useQuery(GET_TASK_COMMENTS_BY_TASK_ID, {
    variables: {
      getTaskByIdId: taskId
    },
    skip: !taskId,
    onCompleted: (data) => {
      console.log('Task Comments Response:', {
        comments: data?.getTaskById?.result?.task?.comments
      });
    }
  });
  if (taskCommentsError) {
   console.log("Error fetching task comments",taskCommentsError)
  }
  
  const { parties, partiesLoading, handleRefreshParties } = useParties(selectedEventId);
  const { assignees, assigneesLoading } = useAssignees(selectedEventId);

  const [taskState, setTaskState] = useState({
    status: taskData?.getTaskById?.result?.task?.status || TaskStatus.IN_PROGRESS,
    isCompleted: taskData?.getTaskById?.result?.task?.status === TaskStatus.COMPLETED
  });

  useEffect(() => {
    if (taskData?.getTaskById?.result?.task?.status) {
      setTaskState({
        status: taskData.getTaskById.result.task.status,
        isCompleted: taskData.getTaskById.result.task.status === TaskStatus.COMPLETED
      });
    }
  }, [taskData?.getTaskById?.result?.task?.status]);


  const handleUpdateTask = async (formData: {
    title: string;
    description: string;
    dueDate: string;
    assignedTo: string;
    partyId: string;
  }) => {

    router.back();
  };

  const handleMarkComplete = async () => {
    const newStatus = !taskState.isCompleted;
    setTaskState(prev => ({
      status: newStatus ? TaskStatus.COMPLETED : TaskStatus.IN_PROGRESS,
      isCompleted: newStatus
    }));
    
    try {
      await updateTaskStatus({
        variables: {
          updateTaskId: taskId,
          input: {
            status: newStatus ? TaskStatus.COMPLETED : TaskStatus.IN_PROGRESS
          },
        },
      });
    } catch (error: any) {
      console.log("Error updating task status", error);
      setTaskState(prev => ({
        status: prev.isCompleted ? TaskStatus.COMPLETED : TaskStatus.IN_PROGRESS,
        isCompleted: !newStatus
      }));
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    }
  };

  const handleSave = async () => {
    if (formRef.current) {
      formRef.current.handleSubmit();
    }
  };

  const handleTitleChange = async (newTitle: string) => {
    if (!taskId) return;
    
    try {
      await updateTaskTitle({
        variables: {
          updateTaskId: taskId,
          input: {
            title: newTitle,
          },
        },
      });
    } catch (error: any) {
      toast.error(TaskNames.TASK_UPDATE_FAILED);
      console.log(error);
    }
  };

  const handleAssigneeChange = async (assigneeId: string | null): Promise<void> => {
    if (!taskId) return;
    
    try {
      await updateTaskAssignee({
        variables: {
          updateTaskId: taskId,
          input: {
            assignedTo: assigneeId,
          },
        },
      });
      
      await taskRefetch();
    } catch (error: any) {
      console.log(error);
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    }
  };

  const handleDeselectAssignee = async (): Promise<void> => {
    try {
      await updateTaskAssignee({
        variables: {
          updateTaskId: taskId,
          input: {
            assignedTo: null,
          },
        },
      });
      
      await taskRefetch();
    } catch (error: any) {
      console.log(error);
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    }
  };

  const handleDueDateChange = async (date: string | null) => {
    if (!taskId) return;
    
    try {
      await updateTaskDueDate({
        variables: {
          updateTaskId: taskId,
          input: {
            dueDate: date,
          },
        },
      });
    } catch (error: any) {
      console.log("Error updating task due date", error);
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    }
  };

  const handlePartyChange = async (partyId: string | null) => {
    if (!taskId) return;
    
    try {
      await updateTaskParty({
        variables: {
          updateTaskId: taskId,
          input: {
            party: partyId,
          },
        },
      
      });
    } catch (error: any) {
      console.log("Error updating task party", error);
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    }
  };

  const handleDescriptionChange = async (description: string | null) => {
    if (!taskId) return;
    
    try {
      await updateTaskDescription({
        variables: {
          updateTaskId: taskId,
          input: {
            description: description,
          },
        },
      });
    } catch (error: any) {
      console.log("Error updating task description", error);
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    }
  };

  const handleAttachmentsChange = async (attachments: string[]) => {
    if (!taskId) return;
    
    try {
      const currentAttachments = taskData?.getTaskById?.result?.task?.attachments || [];
      const currentAttachmentIds = currentAttachments.map((attachment: { id: string } | string) => 
        typeof attachment === 'string' ? attachment : attachment.id
      ).filter(Boolean);
      
      const combinedAttachments = [...new Set([...currentAttachmentIds, ...attachments])];
      
      await updateTaskAttachments({
        variables: {
          updateTaskId: taskId,
          input: {
            attachments: combinedAttachments,
          },
        },
      });
      await taskRefetch();
    } catch (error: any) {
      console.log("Error updating task attachments", error);
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    }
  };

  const handleStatusChange = async (status: string) => {
    if (!taskId) return;
    
    const isCompleted = status === TaskStatus.COMPLETED;
    setTaskState({ status, isCompleted });
    
    try {
      await updateTaskStatusChange({
        variables: {
          updateTaskId: taskId,
          input: { status }
        },
        refetchQueries: [
          {
            query: GET_TASK_BY_ID_FOR_UPDATE,
            variables: { getTaskByIdId: taskId }
          }
        ]
      });
    } catch (error: any) {
      console.log("Error updating task status", error);
      // Revert on error
      setTaskState(prev => ({
        status: prev.status,
        isCompleted: prev.isCompleted
      }));
      toast.error(TaskNames.TASK_UPDATE_FAILED);
    }
  };

  if (taskLoading || partiesLoading || assigneesLoading) {
    return <LoadingIndicator />;
  }

  if (taskError || taskData?.getTaskById?.status !== 'SUCCESS') {
    console.error('Task Error:', {
      graphQLError: taskError,
      taskStatus: taskData?.getTaskById?.status
    });
    return (
      <View>
        <Text>Error loading task details</Text>
      </View>
    );
  }

  const task = taskData?.getTaskById?.result?.task;
  const initialData = task ? {
    title: task.title,
    description: task.description,
    dueDate: task.dueDate ? new Date(task.dueDate) : null,
    partyId: task.party?.id || null,
    attachments: task.attachments || [],
    assignedTo: task.assignedTo?.[0] ? {
      id: task.assignedTo[0].id,
      firstName: task.assignedTo[0].firstName,
      lastName: task.assignedTo[0].lastName
    } : null
  } : undefined;

  return (
    <View style={{ flex: 1 , zIndex: 1000}}>
      <TaskHeader 
        onComplete={handleMarkComplete}
        onSave={handleSave}
        isLoading={false}
        isCompleted={taskState.isCompleted}
        status={taskState.status}
      />
      <TaskForm
        ref={formRef}
        onSubmit={handleUpdateTask}
        onTitleChange={handleTitleChange}
        onAssigneeChange={handleAssigneeChange}
        onDeselectAssignee={handleDeselectAssignee}
        isLoading={false}
        parties={parties}
        isPartiesLoading={partiesLoading}
        hasPartiesError={false}
        assignees={assignees}
        isAssigneesLoading={assigneesLoading}
        taskId={taskId}
        editMode={true}
        initialData={initialData}
        onMenuPress={handleRefreshParties}
        navigation={router}
        onDueDateChange={handleDueDateChange}
        onPartyChange={handlePartyChange}
        onDescriptionChange={handleDescriptionChange}
        initialStatus={task?.status || TaskStatus.IN_PROGRESS}
        status={taskState.status}
        onStatusChange={handleStatusChange}
        taskRefetch={taskRefetch}
        refetchComments={taskCommentsRefetch}
        commentsList={taskCommentsData?.getTaskById?.result?.task?.comments ? taskCommentsData?.getTaskById?.result?.task?.comments : []}
        onAttachmentsChange={handleAttachmentsChange}
    />
    </View>
  );
}

