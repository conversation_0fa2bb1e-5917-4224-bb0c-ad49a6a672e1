import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Switch, TextInput, Card } from 'react-native-paper';

interface RSVPSettingsProps {
  settings: {
    is_guest_list_public: boolean;
    additional_guest_allowed: boolean;
    additional_guest_limit: number;
    allow_guest_to_add_photo: boolean;
    send_auto_reminder_to_all_guests: boolean;
  };
  onSettingsChange: (settings: any) => void;
}

export function RSVPSettings({ settings, onSettingsChange }: RSVPSettingsProps) {
  const handleToggle = (key: string) => {
    onSettingsChange({
      ...settings,
      [key]: !settings[key as keyof typeof settings],
    });
  };

  const handleGuestLimitChange = (value: string) => {
    const numValue = parseInt(value) || 0;
    onSettingsChange({
      ...settings,
      additional_guest_limit: numValue,
    });
  };

  return (
    <View style={styles.container}>
      <Text variant="titleMedium" style={styles.title}>RSVP Settings</Text>

      <View style={styles.settingItem}>
        <Text>Show guest list to all the guests</Text>
        <Switch
          value={settings.is_guest_list_public}
          onValueChange={() => handleToggle('is_guest_list_public')}
        />
      </View>

      <View style={styles.settingItem}>
        <Text>Allow guests to bring people to event</Text>
        <Switch
          value={settings.additional_guest_allowed}
          onValueChange={() => handleToggle('additional_guest_allowed')}
        />
      </View>

      <View style={styles.settingItem}>
        <Text>Set max capacity for guest list</Text>
        <TextInput
          mode="outlined"
          value={settings.additional_guest_limit.toString()}
          onChangeText={handleGuestLimitChange}
          keyboardType="numeric"
          style={styles.numberInput}
        />
      </View>

      <View style={styles.settingItem}>
        <Text>Allow guests to add photos in the group</Text>
        <Switch
          value={settings.allow_guest_to_add_photo}
          onValueChange={() => handleToggle('allow_guest_to_add_photo')}
        />
      </View>

      <View style={styles.settingItem}>
        <Text>Auto Reminders to all Guests</Text>
        <Switch
          value={settings.send_auto_reminder_to_all_guests}
          onValueChange={() => handleToggle('send_auto_reminder_to_all_guests')}
        />
      </View>

      {settings.send_auto_reminder_to_all_guests && (
        <Card style={styles.reminderCard}>
          <Card.Content>
            <Text variant="titleSmall">Reminders to RSVP</Text>
            <Text variant="bodySmall">2 weeks, 1 week and 1 day before event</Text>
            
            <View style={styles.divider} />
            
            <Text variant="titleSmall">Event Reminders</Text>
            <Text variant="bodySmall">24 hours before, 3 hours before and 1 hour before event</Text>
          </Card.Content>
        </Card>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: 'white',
  },
  title: {
    marginBottom: 16,
    paddingBottom: 8,
    fontWeight: 'bold',
    fontSize: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  numberInput: {  
    width: 70,
    height: 40,
  },
  reminderCard: {
    marginTop: 16,
    backgroundColor: '#F5F5F5',
  },
  divider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginVertical: 12,
  },
});