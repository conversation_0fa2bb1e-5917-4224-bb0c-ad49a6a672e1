import { useSignIn , useAuth} from '@clerk/clerk-expo'
import { <PERSON>, useRouter } from 'expo-router'
import { View, StyleSheet } from 'react-native'
import { Text, TextInput, Button as PaperButton } from 'react-native-paper'
import { InfoLabel } from '@/components/UI/ReusableComponents/InfoLabel'
import React from 'react'
import { SafeAreaView } from 'react-native-safe-area-context'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { PhoneCodeFactor, SignInFirstFactor } from '@clerk/types'
import WelcomeBanner from '@/components/WelcomeBanner'
import { PhoneNumberInput } from '@/components/UI/ReusableComponents/PhoneNumberInput'
import { SignUpNames } from '@/constants/displayNames'
import { GET_USER } from '@/app/auth/users.data'
import { Colors, Typography, Spacing, Borders } from '@/constants/DesignSystem'
import { userStorage } from './auth/userStorage'
import { useLazyQuery } from '@apollo/client'
import { useUserStore } from './auth/userStore'
import * as z from 'zod'

const signInSchema = z.object({
  phoneNumber: z.string()
    .regex(/^\+[1-9]\d{0,4}[0-9]{10}$/, 'Phone number must be 10 digits'),
})

export default function Page() {
  const { signIn, setActive, isLoaded } = useSignIn()
  const router = useRouter()

  const { getToken}= useAuth();
  const [rawPhoneNumber, setRawPhoneNumber] = React.useState('')
  const [formattedPhoneNumber, setFormattedPhoneNumber] = React.useState('')
  const [verificationCode, setVerificationCode] = React.useState('')
  const [showVerificationCodeInput, setShowVerificationCodeInput] = React.useState(false)
  const [error, setError] = React.useState('')
  const [phoneNumberError, setPhoneNumberError] = React.useState('')
  const [verificationCodeError, setVerificationCodeError] = React.useState('')
  const [isResending, setIsResending] = React.useState(false)
  const [countdown, setCountdown] = React.useState(30)
  const [canResend, setCanResend] = React.useState(false)
  const [formErrors, setFormErrors] = React.useState<Record<string, string>>({})

  const [getUser] = useLazyQuery(GET_USER);

  const handlePhoneNumberChange = (raw: string, formatted: string) => {
    setRawPhoneNumber(raw)
    setFormattedPhoneNumber(formatted)
  }

  React.useEffect(() => {
    let timer: NodeJS.Timeout

    if (showVerificationCodeInput && countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            setCanResend(true)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }

    return () => {
      if (timer) clearInterval(timer)
    }
  }, [showVerificationCodeInput, countdown])

  const validatePhoneNumber = () => {
    try {
      signInSchema.parse({ phoneNumber: formattedPhoneNumber })
      setFormErrors({})
      return true
    } catch (err) {
      if (err instanceof z.ZodError) {
        const newErrors: Record<string, string> = {}
        err.errors.forEach((error) => {
          if (error.path) {
            newErrors[error.path[0]] = error.message
          }
        })
        setFormErrors(newErrors)
      }
      return false
    }
  }

  const onRequestCode = React.useCallback(async () => {
    if (!isLoaded || !signIn) return
    setPhoneNumberError('')
    setError('')

    if (!validatePhoneNumber()) return

    try {
      const { supportedFirstFactors } = await signIn.create({
        identifier: formattedPhoneNumber,
      })

      const isPhoneCodeFactor = (factor: SignInFirstFactor): factor is PhoneCodeFactor =>
        factor.strategy === 'phone_code'

      const phoneCodeFactor = supportedFirstFactors?.find(isPhoneCodeFactor)

      if (phoneCodeFactor) {
        const { phoneNumberId } = phoneCodeFactor
        await signIn.prepareFirstFactor({
          strategy: 'phone_code',
          phoneNumberId,
        })
        setShowVerificationCodeInput(true)
        setCountdown(30)
        setCanResend(false)
      }
    } catch (err: any) {
      // Handle phone-specific errors
      if (err.message?.toLowerCase().includes('phone') || err.message?.toLowerCase().includes('account')) {
        setPhoneNumberError(err.message)
      } else {
        setError(err.message || 'Failed to send verification code')
      }
    }
  }, [isLoaded, signIn, formattedPhoneNumber])

  const handleResendCode = React.useCallback(async () => {
    if (!isLoaded || !signIn || isResending) return

    try {
      setIsResending(true)
      const { supportedFirstFactors } = await signIn.create({
        identifier: formattedPhoneNumber,
      })

      const phoneCodeFactor = supportedFirstFactors?.find(
        (factor): factor is PhoneCodeFactor => factor.strategy === 'phone_code'
      )

      if (phoneCodeFactor) {
        await signIn.prepareFirstFactor({
          strategy: 'phone_code',
          phoneNumberId: phoneCodeFactor.phoneNumberId,
        })
        setError('Verification code resent successfully')
        setCountdown(30)
        setCanResend(false)
      }
    } catch (err: any) {
      setError(err.message || 'Failed to resend verification code')
    } finally {
      setIsResending(false)
    }
  }, [isLoaded, signIn, formattedPhoneNumber])

  const onSignInPress = React.useCallback(async () => {
    if (!isLoaded || !signIn) return

    // Clear previous errors
    setVerificationCodeError('')
    setError('')

    try {
      const signInAttempt = await signIn.attemptFirstFactor({
        strategy: 'phone_code',
        code: verificationCode,
      })
      if (signInAttempt.status === 'complete') {
        await setActive({ session: signInAttempt.createdSessionId })

        const token = await getToken();

        if(token){
          await userStorage.saveToken(token);
        }

        const { data: userDataByPhone } = await getUser({
          variables: {
            filters: { phone: formattedPhoneNumber }
          }
        });

        const userData = userDataByPhone?.getUsers?.result?.users[0]
        if (userData) {
          await userStorage.saveUser(userData);
          useUserStore.getState().setUserData(userData);
        }

        router.replace('/(home)/(tabs)')
      }
       else {
        setVerificationCodeError('Verification failed. Please try again.')
      }
    } catch (err: any) {
      // Handle verification code specific errors
      if (err.message?.toLowerCase().includes('code')) {
        setVerificationCodeError(err.message)
      } else {
        setError(err.message || 'Failed to verify code')
      }
    }
  }, [isLoaded, signIn, verificationCode])
  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView keyboardShouldPersistTaps="handled">
        <WelcomeBanner />
        <View style={styles.formContainer}>
          <InfoLabel
            message={error}
            visible={!!error}
            type="error"
            style={styles.infoLabel}
          />
          {showVerificationCodeInput ? (
            <View style={styles.inputGroup}>
              <TextInput
                mode='outlined'
                keyboardType="number-pad"
                autoFocus={true}
                value={verificationCode}
                label="Verification Code"
                onChangeText={(code) => {
                  setVerificationCode(code)
                  setVerificationCodeError('')
                }}
                error={!!verificationCodeError}
                outlineColor={Colors.border.medium}
                activeOutlineColor={Colors.primary}
                outlineStyle={{ borderRadius: Borders.radius.md }}
                contentStyle={{ paddingHorizontal: Spacing.sm }}
                theme={{ colors: { background: Colors.background.primary } }}
              />
              {verificationCodeError ? (
                <Text style={styles.errorText}>{verificationCodeError}</Text>
              ) : null}
              <View style={styles.buttonGroup}>
                <View style={styles.primaryButtonContainer}>
                  <PaperButton
                    mode='contained'
                    onPress={onSignInPress}
                    style={styles.primaryButton}
                    buttonColor={Colors.button.primary}
                    textColor={Colors.white}
                  >
                    {SignUpNames.SIGN_IN}
                  </PaperButton>
                </View>
                {!canResend ? (
                  <Text style={styles.countdownText}>
                    Resend code in {countdown}s
                  </Text>
                ) : (
                  <View style={styles.secondaryButtonContainer}>
                    <PaperButton
                      mode='outlined'
                      onPress={handleResendCode}
                      loading={isResending}
                      disabled={isResending}
                      style={styles.secondaryButton}
                      textColor={Colors.primary}
                    >
                      Resend Code
                    </PaperButton>
                  </View>
                )}
              </View>
            </View>
          ) : (
            <View style={styles.inputGroup}>
              <PhoneNumberInput
                value={rawPhoneNumber}
                onChangeText={(raw, formatted) => {
                  handlePhoneNumberChange(raw, formatted)
                  setPhoneNumberError('')
                  setFormErrors({})
                }}
                label="Phone Number"
                placeholder="Enter phone number"
                mode="outlined"
              />
              {formErrors.phoneNumber && <Text style={styles.errorText}>{formErrors.phoneNumber}</Text>}
              <View style={styles.primaryButtonContainer}>
                <PaperButton
                  mode='contained'
                  onPress={onRequestCode}
                  style={styles.primaryButton}
                  buttonColor={Colors.button.primary}
                  textColor={Colors.white}
                >
                  Request Code
                </PaperButton>
              </View>
            </View>
          )}
          <View style={styles.signUpContainer}>
            <Text style={styles.signUpText}>{SignUpNames.DONT_HAVE_AN_ACCOUNT}</Text>
            <Link href="/sign-up">
              <Text variant='bodyLarge' style={styles.signUpLink}>{SignUpNames.SIGN_UP}</Text>
            </Link>
          </View>
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  infoLabel: {
    marginBottom: Spacing.md,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
    justifyContent: 'center',
    padding: Spacing.xxxl,
  },
  formContainer: {
    justifyContent: 'center',
    gap: Spacing.xl,
    marginTop: Spacing.xl,
  },
  inputGroup: {
    gap: Spacing.md,
  },
  errorText: {
    color: Colors.error,
    fontSize: Typography.fontSize.sm,
    marginLeft: Spacing.sm,
  },
  buttonGroup: {
    gap: Spacing.sm,
  },
  primaryButtonContainer: {
    justifyContent: 'center',
    width: '70%',
    alignSelf: 'center',
    marginTop: Spacing.md,
  },
  primaryButton: {
    borderRadius: Borders.radius.pill,
    paddingHorizontal: Spacing.lg,
  },
  secondaryButtonContainer: {
    justifyContent: 'center',
    width: '70%',
    alignSelf: 'center',
    marginTop: Spacing.sm,
  },
  secondaryButton: {
    borderRadius: Borders.radius.pill,
    borderColor: Colors.primary,
    paddingHorizontal: Spacing.lg,
  },
  countdownText: {
    textAlign: 'center',
    color: Colors.text.secondary,
    marginTop: Spacing.sm,
  },
  signUpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: Spacing.xs,
  },
  signUpText: {
    justifyContent: 'center',
    marginTop: Spacing.xs,
    color: Colors.text.primary,
  },
  signUpLink: {
    color: Colors.primary,
  },
});