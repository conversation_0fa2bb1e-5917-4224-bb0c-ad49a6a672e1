import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { TaskForm } from '@/components/Task/TaskForm';
import { useMutation } from '@apollo/client';
import { CREATE_TASK } from './Task.data';
import { useEventStore } from '@/store/eventStore';
import { TaskNames, TaskStatus } from '@/constants/Task.constants';
import { useToast } from '@/components/Toast/useToast';
import { useUserStore } from '@/app/auth/userStore';
import { useParties, useAssignees } from './hooks/useTaskData';

type CreateTaskRouteParams = {
  selectedAssignee?: string;
  formData?: string;
};

type CreateTaskScreenRouteProp = RouteProp<Record<string, CreateTaskRouteParams>, string>;

export default function CreateTaskRoute() {
  const navigation = useNavigation();
  const route = useRoute<CreateTaskScreenRouteProp>();
  const [createTask, { loading }] = useMutation(CREATE_TASK);
  const selectedEventId = useEventStore((state) => state.selectedEventId);
  const userData = useUserStore((state) => state.userData);
  const toast = useToast();

  const { parties, partiesLoading, handleRefreshParties } = useParties(selectedEventId);
  const { assignees, assigneesLoading } = useAssignees(selectedEventId);

  const params = route.params || {};

  const formDataFromParams = params.formData ? JSON.parse(params.formData) : null;

  const handleCreateTask = async (formData: {
    title: string;
    description: string;
    dueDate: string;
    assignedTo: string;
    partyId: string;
    attachments: string[];
  }) => {
    if (!userData?.id) {
      toast.error(TaskNames.USER_NOT_AUTHENTICATED);
      return;
    }

    try {
      const variables = {
        input: {
          title: formData.title,
          description: formData.description || null,
          dueDate: formData.dueDate || null,
          assignedTo: formData.assignedTo || null,
          party: formData.partyId,
          status: TaskStatus.IN_PROGRESS,
          attachments: formData.attachments || [],
        },
      };
      const response = await createTask({ variables });
      if (response.data?.createTask?.status === 'SUCCESS') {
        toast.success(TaskNames.TASK_CREATED);
        setTimeout(() => {
          navigation.goBack();
        }, 1000);
      } else {
        const errorMessage = response.data?.createTask?.message || TaskNames.TASK_CREATION_FAILED;
        toast.error(errorMessage);
      }
    } catch (error: any) {
      console.error('Error creating task:', error);
      toast.error(TaskNames.UNEXPECTED_ERROR);
    }
  };

  return (
    <TaskForm
      onSubmit={handleCreateTask}
      isLoading={loading}
      navigation={navigation}
      parties={parties}
      isPartiesLoading={partiesLoading}
      assignees={assignees}
      isAssigneesLoading={assigneesLoading}
      editMode={false}
      errorMessage={null}
      initialData={formDataFromParams}
      onMenuPress={handleRefreshParties}
    />
  );
}