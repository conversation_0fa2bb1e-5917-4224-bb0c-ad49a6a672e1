import axios from 'axios';

export interface TenorMediaFormat {
  url: string;
  duration: number;
  preview: string;
  dims: [number, number];
  size: number;
}

export interface TenorMediaFormats {
  tinygif: TenorMediaFormat;
  nanogif: TenorMediaFormat;
  gif: TenorMediaFormat;
}

export interface TenorResult {
  id: string;
  title: string;
  media_formats: TenorMediaFormats;
  created: number;
  content_description: string;
  itemurl: string;
  url: string;
  tags: string[];
  flags: string[];
  hasaudio: boolean;
  content_description_source: string;
}

export interface TenorApiResponse {
  locale: string;
  results: TenorResult[];
  next: string;
}

const TENOR_API_URL = process.env.EXPO_PUBLIC_TENOR_API;
const TENOR_API_FEATURED_URL = `${TENOR_API_URL}/featured`;
const TENOR_API_SEARCH_URL = `${TENOR_API_URL}/search`;

const defaultCountry = "IN";
const defaultMediaFilter = "gif,tinygif,nanogif";
const defaultRandom = true;
const defaultContentFilter = "high";


const DEFAULT_TENOR_API_KEY = process.env.EXPO_PUBLIC_GOOGLE_API_KEY;
const DEFAULT_TENOR_CLIENT_KEY = 'fast_party_mobile';

export interface TenorApiParams {
  q?: string;
  limit?: number;
  pos?: string;
  media_filter?: string;
  locale?: string;
  country?: string;
  random?: boolean;
  ar_range?: string;
  client_key?: string;
  key?: string;
  contentfilter?: string;
}

async function baseFetchTenorApi(
  baseUrl: string | undefined,
  endpointName: string,
  params?: TenorApiParams,
): Promise<TenorApiResponse> {
  if (!baseUrl) {
    const envVarName = endpointName === 'Featured' ? 'EXPO_PUBLIC_TENOR_FEATURED_URL' : 'EXPO_PUBLIC_TENOR_SEARCH_URL';
    const errorMessage = `Tenor ${endpointName} API endpoint URL is not configured. Set the ${envVarName} environment variable.`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }

  const effectiveApiKey = params?.key || DEFAULT_TENOR_API_KEY;
  const effectiveClientKey = params?.client_key || DEFAULT_TENOR_CLIENT_KEY;

  if (!effectiveApiKey) {
    const errorMessage =
      `Tenor API key is not configured. Provide it in params, set EXPO_PUBLIC_GOOGLE_API_KEY env var, or ensure a default is hardcoded.`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
  if (!effectiveClientKey) {
     console.warn('Tenor Client key is not explicitly configured. Relying on API key association or defaults.');
  }

  const queryParams: Record<string, any> = {
    ...params,
    key: effectiveApiKey,
    client_key: effectiveClientKey, 
  };

  Object.keys(queryParams).forEach((key) => {
    if (queryParams[key] === undefined) {
      delete queryParams[key];
    }
  });

  try {
    const response = await axios.get<TenorApiResponse>(baseUrl, {
      params: queryParams,
    });
    return response.data;
  } catch (error) {
    const attemptedUrl =
      axios.isAxiosError(error) && error.config
        ? axios.getUri({ url: baseUrl, params: queryParams })
        : baseUrl;

    if (axios.isAxiosError(error)) {
      console.error(
        `Error fetching GIFs from Tenor ${endpointName} API. URL: ${attemptedUrl}`,
        'Status:',
        error.response?.status,
        'Response Data:',
        error.response?.data || error.message,
      );
      throw new Error(
        `Failed to fetch GIFs from Tenor ${endpointName} API (${attemptedUrl}): ${
          error.response?.statusText || error.message
        }`,
      );
    } else {
      console.error(
        `An unexpected error occurred while trying to fetch GIFs from Tenor ${endpointName} API (${attemptedUrl}):`,
        error,
      );
      throw new Error(
        `An unexpected error occurred while fetching GIFs from Tenor ${endpointName} API (${attemptedUrl}).`,
      );
    }
  }
}

export async function fetchFeaturedTenorGifs(
  params?: TenorApiParams,
): Promise<TenorApiResponse> {
    const paramsWithDefaults = {
        ...params,
        country: params?.country || defaultCountry,
        media_filter: params?.media_filter || defaultMediaFilter,
        random: params?.random || defaultRandom,
        contentfilter: params?.contentfilter || defaultContentFilter,
    };
  return baseFetchTenorApi(TENOR_API_FEATURED_URL, 'Featured', paramsWithDefaults);
}

export async function searchTenorGifs(
  params?: TenorApiParams,
): Promise<TenorApiResponse> {
  if (!params?.q?.trim()) {
    console.warn('Search query is empty. Returning empty results.');
    return {
      locale: params?.locale || 'en_US',
      results: [],
      next: ''
    };
  }
  const paramsWithDefaults = {
    ...params,
    country: params?.country || defaultCountry,
    media_filter: params?.media_filter || defaultMediaFilter,
    random: params?.random || defaultRandom,
    contentfilter: params?.contentfilter || defaultContentFilter,
  };

  return baseFetchTenorApi(TENOR_API_SEARCH_URL, 'Search', paramsWithDefaults);
}