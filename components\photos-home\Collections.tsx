import { StyleSheet, View } from 'react-native'
import { Text } from 'react-native-paper'
import { ScrollView } from 'react-native-gesture-handler'
import { CollectionCard } from './CollectionCard'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import { Colors, Typography, Spacing, Icons } from '@/constants/DesignSystem'
import { Next } from '@/components/icons'

// Mock data
interface CollectionsProps {
  collectionsList: CollectionItem[];
  loggedInUserId?: string;
}

export interface CollectionItem {
  id: string;
  name: string;
  albumCover: string[];
  owner: {
    id: string;
  }[]
}

export default function Collections({ collectionsList, loggedInUserId }: CollectionsProps) {
  return (
    <View style={styles.container}>
      <View style={styles.sectionHeader}>
        <Text variant="titleMedium" style={styles.sectionTitle}>
          Collections
        </Text>
        <View style={styles.viewAllContainer}>
          <Text variant="bodyMedium" style={styles.sectionSubtitle}>View all</Text>
          <Next size={Icons.size.md} color={Colors.mediumGray} style={styles.viewAllIcon} />
        </View>
      </View>

      <GestureHandlerRootView style={styles.gestureContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          style={styles.scrollView}
        >
          {collectionsList.map((collection: CollectionItem) => (
            <CollectionCard
              key={collection?.id}
              name={collection?.name}
              media={collection?.albumCover}
              onPress={() => console.log(`Collection ${collection?.id} pressed`)}
              ownerId={collection?.owner}
              loggedInUserId={loggedInUserId}
            />
          ))}
        </ScrollView>
      </GestureHandlerRootView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: Spacing.lg,
    height: 260,
    marginBottom: -20,
  },
  gestureContainer: {
    flex: 1,
    minHeight: 220,
  },
  scrollView: {
    flex: 1,
  },
  sectionTitle: {
    marginBottom: Spacing.lg,
    fontWeight: Typography.fontWeight.bold,
    paddingHorizontal: Spacing.xs,
    color: Colors.text.tertiary,
  },
  sectionHeader: {
    width: '99%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  scrollContent: {
    paddingHorizontal: Spacing.xs,
  },
  sectionSubtitle: {
    paddingHorizontal: Spacing.xs,
    marginBottom: Spacing.xl,
    fontSize: Typography.fontSize.sm,
    color: Colors.mediumGray,
    fontWeight: Typography.fontWeight.bold,
  },
  viewAllContainer: {
    flexDirection: 'row',
  },
  viewAllIcon: {
    marginTop: 2,
    marginLeft: -6
  }
})