import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const ExternalBrowser = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      clipPath="url(#External_browser-1_svg__a)"
    >
      <Path
        stroke="url(#External_browser-1_svg__b)"
        d="M10.5 6.692v3.116a.69.69 0 0 1-.692.692H2.192a.69.69 0 0 1-.692-.692V2.192a.69.69 0 0 1 .692-.692h3.116"
      />
      <Path
        stroke="url(#External_browser-1_svg__c)"
        d="M8.077 1.5H10.5v2.423"
      />
      <Path stroke="url(#External_browser-1_svg__d)" d="M10.5 1.5 6 6" />
    </G>
    <Defs>
      <LinearGradient
        id="External_browser-1_svg__b"
        x1={6}
        x2={6}
        y1={0.414}
        y2={12.569}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="External_browser-1_svg__c"
        x1={9.288}
        x2={9.288}
        y1={1.208}
        y2={4.48}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="External_browser-1_svg__d"
        x1={8.25}
        x2={8.25}
        y1={0.957}
        y2={7.034}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="External_browser-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#External_browser_svg__a)"
    >
      <Path d="M11 6.77v3.46a.77.77 0 0 1-.77.77H1.77a.77.77 0 0 1-.77-.77V1.77A.77.77 0 0 1 1.77 1h3.46M8.308 1H11v2.692M11 1 6 6" />
    </G>
    <Defs>
      <ClipPath id="External_browser_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default ExternalBrowser;
