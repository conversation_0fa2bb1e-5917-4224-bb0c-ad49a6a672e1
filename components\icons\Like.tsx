import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const Like = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      fill="url(#Like-1_svg__a)"
      fillRule="evenodd"
      d="M3.952 10.716c.266.127.556.192.85.192h4.23c.974 0 1.803-.71 1.954-1.672l.495-3.167a1.582 1.582 0 0 0-1.563-1.826H7.32V2.378a1.286 1.286 0 0 0-2.408-.628L3.27 4.685c-.099.177-.151.376-.151.58v4.304c0 .458.263.875.676 1.072zM1.288 4.829a.79.79 0 0 0-.788.789v4.044a.79.79 0 0 0 .788.788h.393a.396.396 0 0 0 .395-.395v-4.83a.396.396 0 0 0-.395-.396z"
      clipRule="evenodd"
    />
    <Defs>
      <LinearGradient
        id="Like-1_svg__a"
        x1={6}
        x2={6}
        y1={-0.093}
        y2={13.165}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <Path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M1.385 5.078h1.822v4.853H1.385A.384.384 0 0 1 1 9.546V5.463a.385.385 0 0 1 .385-.385M3.207 5.078l1.954-3.123a.84.84 0 0 1 .723-.4.853.853 0 0 1 .884.846V4.64h3.376a.885.885 0 0 1 .846 1.016l-.615 3.968a.88.88 0 0 1-.87.769H4.462a1.54 1.54 0 0 1-.692-.162l-.554-.276M3.207 5.078V9.93"
    />
        </>
      )}
    </Svg>
  );
};
export default Like;
