import * as React from "react";
import Svg, { <PERSON>, <PERSON>, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Guests = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Guests-1_svg__a)">
      <Path
        fill="url(#Guests-1_svg__b)"
        fillRule="evenodd"
        d="M5.998.507a4.026 4.026 0 1 0 0 8.053 4.026 4.026 0 0 0 0-8.053m.159 1.904.536 1.08a.17.17 0 0 0 .138.099l1.191.18a.18.18 0 0 1 .1.313l-.88.836a.18.18 0 0 0 0 .162l.169 1.185a.18.18 0 0 1-.268.193l-1.06-.561a.21.21 0 0 0-.175 0l-1.06.561a.18.18 0 0 1-.269-.193l.2-1.185a.18.18 0 0 0-.032-.162l-.88-.842a.18.18 0 0 1 .1-.306l1.192-.175a.17.17 0 0 0 .137-.1l.537-1.079a.18.18 0 0 1 .324-.006m4.008 4.901a5.02 5.02 0 0 1-2.88 2.063l1.11 1.922a.393.393 0 0 0 .719-.095l.4-1.492 1.492.4a.393.393 0 0 0 .441-.576zm-5.45 2.064a5.02 5.02 0 0 1-2.882-2.06L.553 9.534a.393.393 0 0 0 .442.576l1.491-.4.4 1.492a.393.393 0 0 0 .72.095z"
        clipRule="evenodd"
        stroke="transparent"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Guests-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.819}
        y2={14.018}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Guests-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Guests_svg__a)"
    >
      <Path d="M10 4.774a4.003 4.003 0 1 1-8.005 0 4.003 4.003 0 0 1 8.006 0" strokeWidth={strokeWidth} fill="none" />
      <Path d="m6.162 2.6.55 1.11a.17.17 0 0 0 .142.103l1.225.185a.186.186 0 0 1 .102.321l-.904.86a.18.18 0 0 0 0 .166l.173 1.218a.186.186 0 0 1-.275.199l-1.09-.577a.22.22 0 0 0-.18 0l-1.09.577a.186.186 0 0 1-.276-.199l.206-1.218a.18.18 0 0 0-.033-.167l-.904-.865a.186.186 0 0 1 .103-.315l1.225-.18a.17.17 0 0 0 .14-.102l.552-1.109a.186.186 0 0 1 .334-.006M2.53 6.738.75 9.82l1.923-.515.516 1.923 1.544-2.675M9.47 6.738l1.78 3.083-1.924-.515-.515 1.923-1.545-2.675" strokeWidth={strokeWidth} fill="none" />
    </G>
    <Defs>
      <ClipPath id="Guests_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Guests;
