import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const Invite = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      fill="url(#Invite-1_svg__a)"
      fillRule="evenodd"
      d="M.5 2.66c0-.65.528-1.178 1.179-1.178h8.642c.651 0 1.179.528 1.179 1.179v.268L6.301 6.396a.57.57 0 0 1-.301.08.57.57 0 0 1-.301-.08L.5 2.929zm0 1.45v5.23c0 .65.528 1.178 1.179 1.178h8.642c.651 0 1.179-.528 1.179-1.179V4.11L6.843 7.216l-.004.003c-.247.16-.544.24-.839.24s-.592-.08-.839-.24l-.004-.003z"
      clipRule="evenodd"
    />
    <Defs>
      <LinearGradient
        id="Invite-1_svg__a"
        x1={6}
        x2={6}
        y1={0.392}
        y2={12.595}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <Path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M10.23 1.962H1.77A.77.77 0 0 0 1 2.73v6.538c0 .425.344.77.77.77h8.46a.77.77 0 0 0 .77-.77V2.731a.77.77 0 0 0-.77-.77"
    />
    <Path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="m1 2.923 4.508 2.711A1 1 0 0 0 6 5.76c.18 0 .354-.045.492-.126L11 2.923"
    />
        </>
      )}
    </Svg>
  );
};
export default Invite;
