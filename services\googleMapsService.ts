import * as Location from 'expo-location';

const googleMapsApiUrl = process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_URL;
const apiKey = process.env.EXPO_PUBLIC_GOOGLE_API_KEY;

const getLocationPermissions = async () => {
    try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
            console.error('Location permission denied');
            throw new Error('Permission to access location was denied.');
        }
    } catch (error) {
        console.error('Error requesting location permissions:', error);
        throw new Error('Failed to get location permissions.');
    }
}

export const getCurrentLocationAndPlaceId = async () => {
    try {
        await getLocationPermissions();
        
        const location = await Location.getCurrentPositionAsync({});
        const placeId = await getPlaceIdForCurrentLocation(location.coords.latitude, location.coords.longitude)

        return {location, placeId}
    } catch (error) {
        console.error('Error getting current location:', error);
        throw new Error('Failed to fetch current location.');
    }
}

export const getPlaceIdForCurrentLocation = async (latitude: number, longitude: number): Promise<string | null> => {
    try {
        const url = `${googleMapsApiUrl}/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`;
        const response = await fetch(url);
        const data = await response.json();

        if (data.status === 'OK' && data.results && data.results.length > 0) {
            return data.results[0].place_id;
        } else {
            console.error('Error fetching Place ID for current location:', data.status);
            return null;
        }
    } catch (error) {
        console.error('Error fetching Place ID for current location:', error);
        return null;
    }
}

export const getPlaceDetails = async (placeId: string) => {
    const url = `${googleMapsApiUrl}/place/details/json?place_id=${placeId}&key=${apiKey}`;
    const response = await fetch(url);
    const data = await response.json();
    return data;
}

export const fetchPlaceName = async (placeId: string): Promise<string | null> => {
    if (!placeId) return null;

    const url = `${googleMapsApiUrl}/place/details/json?place_id=${placeId}&fields=name,formatted_address&key=${apiKey}`;
    try {
        const response = await fetch(url);
        const json = await response.json();

        if (json.status === 'OK' && json.result) {
            return json.result.name || json.result.formatted_address || null;
        } else {
            console.error('Error fetching place details from service:', json.status, json.error_message);
            return null;
        }
    } catch (error) {
        console.error('Error in fetchPlaceName service:', error);
        return null;
    }
};
