import React, { useState } from 'react';
import { View } from 'react-native';
import { TextInput, List, useTheme, Text } from 'react-native-paper';
import { StyledAccordionProps } from '../../../app/(home)/EventsDashboard/AddParty.models';
import { AddPartyNames } from '../../../constants/displayNames';
import { BaseType } from '@/app/(home)/EventsDashboard/AddParty.models';
export function PartyTypesAccordion<T extends BaseType>({ label, value, options, onSelect }: StyledAccordionProps<T>) {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(false);

  return (
    <View style={{marginBottom:4}}>
      <TextInput
        label={label}
        value={value}
        editable={false}
        right={<TextInput.Icon icon={expanded ? "chevron-up" : "chevron-down"} onPress={() => setExpanded(!expanded)} />}
      />
      {expanded && (
        <View style={{
          backgroundColor: theme.colors.surface,
          borderBottomLeftRadius: 4,
          borderBottomRightRadius: 4,
          elevation: 2,
          borderColor: theme.colors.outline,
          borderBottomWidth: 1,
          borderLeftWidth: 1,
          borderRightWidth: 1,
        }}>
          {options?.length === 0 && <Text style={{ color: theme.colors.onSurface, textAlign: 'center', margin: 8, padding: 8 }}>{AddPartyNames.NO_PARTY_TYPES_AVAILABLE}</Text>}
          {options.map((option) => (
            <List.Item
              key={option.id}
              title={option.name}
              onPress={() => {
                onSelect(option);
                setExpanded(false);
              }}
              titleStyle={{
                color: theme.colors.onSurface,
              }}
            />
          ))}
        </View>
      )}
    </View>
  );
}