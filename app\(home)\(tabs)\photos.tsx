import { StyleSheet, View, ScrollView, Platform, RefreshControl } from 'react-native'
import { Appbar, Text } from 'react-native-paper'
import { SafeAreaView } from 'react-native-safe-area-context'
import Collections from '@/components/photos-home/Collections'
import { StatusBar } from 'expo-status-bar'
import { Albums } from '@/components/photos-home/Albums'
import { Colors, Typography, Spacing } from '@/constants/DesignSystem'
import OtherPhotos from '@/components/photos-home/OtherPhotos'
import { useQuery } from '@apollo/client'
import { GET_ALBUMS, GET_COLLECTIONS } from '@/components/photos-home/media.data'
import { useUserStore } from '@/app/auth/userStore'
import { MEDIA_CATEGORIES, ALBUM_TYPES } from '@/constants/media'
import { useState, useCallback } from 'react'
import AppHeaderIcons from '@/components/AppHeaderIcons'
import { useFocusEffect } from '@react-navigation/native'

export default function Photos() {
  const userData = useUserStore((state) => state.userData);
  const { data: hostAlbums, loading: hostAlbumsLoading, error: hostAlbumsError, refetch: refetchHostAlbums } = useQuery(GET_ALBUMS, {
    variables: {
      filter: {
        category: MEDIA_CATEGORIES.ALBUM,
        userId: userData?.id,
        userType: ALBUM_TYPES.HOST
      }
    }
  })
  const {data: guestAlbums, loading: guestAlbumsLoading, error: guestAlbumsError, refetch: refetchGuestAlbums} = useQuery(GET_ALBUMS, {
    variables: {
      filter: {
        category: MEDIA_CATEGORIES.ALBUM,
        userId: userData?.id,
        userType: ALBUM_TYPES.GUEST
      }
    }
    })
    const {data: collections, loading: collectionsLoading, error: collectionsError, refetch: refetchCollections} = useQuery(GET_COLLECTIONS, {
      variables: {
        filter: {
          category: MEDIA_CATEGORIES.COLLECTION,
          userId: userData?.id,
        }
      }
    })

  const [isRefreshing, setIsRefreshing] = useState(false);

  const refetchAllData = useCallback(async () => {
    try {
      setIsRefreshing(true);
      await Promise.all([
        refetchCollections(),
        refetchHostAlbums(),
        refetchGuestAlbums()
      ]);
    } catch (error) {
      console.error('Error refetching data:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [refetchCollections, refetchHostAlbums, refetchGuestAlbums]);

  useFocusEffect(
    useCallback(() => {
      refetchAllData();
    }, [refetchAllData])
  );

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="auto" />
      <Appbar.Header style={styles.header}>
        <Appbar.Content
          title="Photos"
          titleStyle={styles.headerTitle}
          style={styles.headerContent}
        />
        <View style={styles.iconContainer}>
          <AppHeaderIcons />
        </View>
      </Appbar.Header>
      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={refetchAllData}
            tintColor={Colors.primary}
            colors={[Colors.primary]}
          />
        }
      >
        {collections?.getMediaFolders?.result?.mediaFolders?.length === 0 && hostAlbums?.getMediaFolders?.result?.mediaFolders?.length === 0 && guestAlbums?.getMediaFolders?.result?.mediaFolders?.length === 0 &&
        <Text style={styles.emptyStateText}>No events attended yet, The event albums will appear when you attend events.</Text>}
        {collections?.getMediaFolders?.result?.mediaFolders?.length > 0 && <Collections collectionsList={collections?.getMediaFolders?.result?.mediaFolders} loggedInUserId={userData?.id} />}
        {hostAlbums?.getMediaFolders?.result?.mediaFolders?.length > 0 && <Albums albums={hostAlbums?.getMediaFolders?.result?.mediaFolders} albumType="host" />}
        {guestAlbums?.getMediaFolders?.result?.mediaFolders?.length > 0 && <Albums albums={guestAlbums?.getMediaFolders?.result?.mediaFolders} albumType="guest" />}
        <OtherPhotos />
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  // Layout
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.xl,
  },

  // Header
  header: {
    backgroundColor: Colors.background.primary,
    elevation: 0,
    marginTop: -32,
    marginLeft: Platform.OS === 'ios' ? 0 : '16%',
  },
  headerContent: {
    alignItems: 'flex-start',
    marginLeft: -36,
  },
  headerTitle: {
    fontWeight: Typography.fontWeight.bold,
    fontSize: Typography.fontSize.xl,
    color: Colors.text.secondary,
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  rightIcon: {
    marginLeft: -8,
  },

  // Content
  emptyStateText: {
    textAlign: 'center',
    marginVertical: Spacing.lg,
    justifyContent: 'center',
    alignItems: 'center',
    color: Colors.primary,
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.md,
  }
})