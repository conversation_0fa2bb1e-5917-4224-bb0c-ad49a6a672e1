import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Favourite = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Favourite-1_svg__a)">
      <Path
        fill="url(#Favourite-1_svg__b)"
        fillRule="evenodd"
        d="M6 .237a.89.89 0 0 0-.806.511l-1.36 2.747-.011.02-.004.002-.016.002-2.996.444a.891.891 0 0 0-.53 1.532l2.194 2.116.005.005a.03.03 0 0 1 .008.023v.003l-.523 3.059a.9.9 0 0 0 .824 ********* 0 0 0 .484-.103l2.7-1.427a.08.08 0 0 1 .062 0l2.7 1.427a.902.902 0 0 0 1.309-.948V10.7l-.523-3.059v-.015l.007-.011.005-.005 2.193-2.116a.892.892 0 0 0-.529-1.532l-2.996-.444-.02-.004-.002-.003-.008-.017L6.807.748A.89.89 0 0 0 6 .238"
        clipRule="evenodd"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Favourite-1_svg__b"
        x1={6}
        x2={6}
        y1={-1.153}
        y2={14.403}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Favourite-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G clipPath="url(#Favourite_svg__a)">
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m6.402.946 1.352 2.72a.43.43 0 0 0 .346.252l3.003.456a.456.456 0 0 1 .252.786L9.138 7.267a.44.44 0 0 0 0 .409l.424 2.987a.456.456 0 0 1-.676.487L6.213 9.735a.54.54 0 0 0-.44 0L3.1 11.15a.456.456 0 0 1-.676-.487l.503-2.987a.44.44 0 0 0-.079-.41L.631 5.145a.456.456 0 0 1 .252-.77l3.003-.44a.42.42 0 0 0 .346-.252L5.584.962a.456.456 0 0 1 .818-.016"
      />
    </G>
    <Defs>
      <ClipPath id="Favourite_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Favourite;
