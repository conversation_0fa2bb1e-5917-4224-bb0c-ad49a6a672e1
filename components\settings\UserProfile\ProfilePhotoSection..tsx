import { StyleSheet, View, TouchableOpacity, Pressable, Keyboard, Platform } from 'react-native'
import { useState, useRef, useMemo } from 'react'
import { Image } from 'expo-image'
import * as ImagePicker from 'expo-image-picker'
import { Button, Text } from 'react-native-paper'
import { uploadFiles } from '@/app/(home)/Task/Task.utils'
import { BottomSheetModal, BottomSheetBackdrop, BottomSheetView, BottomSheetHandleProps } from '@gorhom/bottom-sheet'
import { Colors, Typography, Spacing, Borders, Shadows, Icons } from '@/constants/DesignSystem'
import { Upload, TakePhoto, Delete } from '@/components/icons'

// Custom handle component for the bottom sheet
const CustomHandle = (_: BottomSheetHandleProps) => {
  return (
    <View style={styles.handleContainer}>
      <View style={styles.handle} />
    </View>
  )
}

const ProfilePhotoSection = ({
  profileUrl,
  setProfileImageUrl,
  renderCustomContent
}: {
  profileUrl: string | null,
  setProfileImageUrl: (url: string | null) => void,
  renderCustomContent?: () => JSX.Element
}) => {
  const [uploading, setUploading] = useState(false)
  const bottomSheetRef = useRef<BottomSheetModal>(null)

  const snapPoints = useMemo(() => {
    // Adjust snapPoints based on platform and whether a profile image exists
    if (Platform.OS === 'android') {
      return profileUrl ? ['35%'] : ['25%']
    } else {
      return profileUrl ? ['45%'] : ['35%']
    }
  }, [profileUrl])

  const requestPermissions = async (type: 'camera' | 'gallery') => {
    if (type === 'camera') {
      const { status } = await ImagePicker.requestCameraPermissionsAsync()
      if (status !== 'granted') {
        alert('Sorry, we need camera permissions to make this work!')
        return false
      }
    } else {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync()
      if (status !== 'granted') {
        alert('Sorry, we need media library permissions to make this work!')
        return false
      }
    }
    return true
  }

  const handleImagePick = async (type: 'camera' | 'gallery') => {
    try {
      const hasPermission = await requestPermissions(type)
      if (!hasPermission) {
        bottomSheetRef.current?.dismiss()
        return
      }

      const options: ImagePicker.ImagePickerOptions = {
        mediaTypes: 'Images' as any, // Using 'Images' directly as MediaTypeOptions is deprecated
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
      }

      const result = type === 'camera'
        ? await ImagePicker.launchCameraAsync(options)
        : await ImagePicker.launchImageLibraryAsync(options)

      if (!result.canceled) {
        setUploading(true)
        const file = {
          name: result.assets[0].uri.split('/').pop() || `image_${Date.now()}.jpg`,
          type: 'image/jpeg',
          uri: result.assets[0].uri,
          size: result.assets[0].fileSize || 0
        }

        try {
          const uploadResult = await uploadFiles([file], 'media')

          if (uploadResult.successful.length > 0) {
            const uploadedImage = uploadResult.successful[0]
            setProfileImageUrl(uploadedImage.url)
          }
        } catch (uploadError) {
          console.error('Upload failed:', uploadError)
        }

        setUploading(false)
      }
      bottomSheetRef.current?.dismiss()
    } catch (error) {
      console.error('Error picking image:', error)
      setUploading(false)
      bottomSheetRef.current?.dismiss()
    }
  }

  const handleRemovePhoto = () => {
    setProfileImageUrl(null)
    bottomSheetRef.current?.dismiss()
  }

  const openBottomSheet = () => {
    Keyboard.dismiss()
    bottomSheetRef.current?.present()
  }

  return (
    <View style={styles.container}>
      {profileUrl ? (
        <View style={styles.profileContainer}>
          <Image
            source={{ uri: profileUrl }}
            style={styles.image}
            contentFit="cover"
          />
          <Button
            mode="text"
            onPress={openBottomSheet}
            loading={uploading}
            disabled={uploading}
            style={styles.editButton}
            textColor={Colors.secondary}
          >
            Edit Photo
          </Button>
        </View>
      ) : (
        renderCustomContent ? (
          <Pressable onPress={openBottomSheet} disabled={uploading}>
            {renderCustomContent()}
          </Pressable>) : (
          <TouchableOpacity
            style={styles.imageContainer}
            onPress={openBottomSheet}
            disabled={uploading}
          >
            <View style={styles.placeholderContainer}>
              <Upload size={Icons.size.md} color={Colors.text.secondary} />
              <Text style={styles.uploadText}>Upload Photo</Text>
            </View>
          </TouchableOpacity>
        )
      )}

      <BottomSheetModal
        ref={bottomSheetRef}
        index={0}
        snapPoints={snapPoints}
        enablePanDownToClose
        handleComponent={Platform.OS === 'android' ? CustomHandle : undefined}
        backdropComponent={(props) => (
          <BottomSheetBackdrop
            {...props}
            appearsOnIndex={0}
            disappearsOnIndex={-1}
            pressBehavior="close"
          />
        )}
      >
        <BottomSheetView style={styles.bottomSheetContent}>
          <TouchableOpacity
            style={styles.optionButton}
            onPress={() => handleImagePick('camera')}
            disabled={uploading}
          >
            <TakePhoto size={Icons.size.md} color={Colors.secondary} />
            <Text style={styles.optionText}>Take Photo</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.optionButton}
            onPress={() => handleImagePick('gallery')}
            disabled={uploading}
          >
            <Upload size={Icons.size.md} color={Colors.secondary} />
            <Text style={styles.optionText}>Choose Photo</Text>
          </TouchableOpacity>

          {profileUrl && (
            <TouchableOpacity
              style={[styles.removeButton]}
              onPress={handleRemovePhoto}
              disabled={uploading}
            >
              <Delete size={Icons.size.md} color={Colors.error} />
              <Text style={[styles.optionText, styles.removeText]}>Remove Photo</Text>
            </TouchableOpacity>
          )}
        </BottomSheetView>
      </BottomSheetModal>
    </View>
  )
}

export default ProfilePhotoSection

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginBottom: Spacing.xxl,
  },
  profileContainer: {
    alignItems: 'center',
  },
  imageContainer: {
    width: 100,
    height: 100,
    borderRadius: Borders.radius.circle,
    overflow: 'hidden',
    backgroundColor: Colors.background.secondary,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: Borders.radius.circle,
  },
  placeholderContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: Borders.width.thin,
    borderStyle: 'dashed',
    borderColor: Colors.border.light,
    borderRadius: Borders.radius.circle,
  },
  uploadText: {
    marginTop: Spacing.sm,
    fontSize: Typography.fontSize.sm,
    fontFamily: Typography.fontFamily.primary,
    color: Colors.text.secondary,
    fontWeight: Typography.fontWeight.medium,
  },
  editButton: {
    marginTop: Spacing.sm,
  },
  bottomSheetContent: {
    flex: 1,
    padding: Spacing.lg,
    paddingBottom: Platform.OS === 'android' ? Spacing.md : Spacing.lg,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
    padding: Platform.OS === 'android' ? Spacing.lg : Spacing.lg,
    borderRadius: Borders.radius.md,
    marginBottom: Platform.OS === 'android' ? Spacing.lg : Spacing.lg,
    ...Shadows.sm,
  },
  optionText: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.medium,
    fontFamily: Typography.fontFamily.primary,
    color: Colors.text.tertiary,
  },
  removeButton: {
    backgroundColor: `${Colors.error}10`,
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
    padding: Platform.OS === 'android' ? Spacing.lg : Spacing.lg,
    borderRadius: Borders.radius.md,
    marginBottom: Platform.OS === 'android' ? Spacing.lg : Spacing.lg,
    ...Shadows.sm,
  },
  removeText: {
    color: Colors.error,
  },
  handleContainer: {
    paddingTop: Spacing.sm,
    paddingBottom: Platform.OS === 'android' ? 0 : Spacing.sm,
    alignItems: 'center',
    backgroundColor: Colors.background.primary,
  },
  handle: {
    width: 40,
    height: 4,
    borderRadius: Borders.radius.pill,
    backgroundColor: Colors.border.light,
  }
})