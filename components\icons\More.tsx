import * as React from "react";
import Svg, { Circle, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const More = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Circle
      cx={2}
      cy={6}
      r={1}
      fill="url(#More-1_svg__a)"
      stroke="url(#More-1_svg__b)"
      strokeWidth={0.893}
    />
    <Circle
      cx={6}
      cy={6}
      r={1}
      fill="url(#More-1_svg__c)"
      stroke="url(#More-1_svg__d)"
      strokeWidth={0.893}
    />
    <Circle
      cx={10}
      cy={6}
      r={1}
      fill="url(#More-1_svg__e)"
      stroke="url(#More-1_svg__f)"
      strokeWidth={0.893}
    />
    <Defs>
      <LinearGradient
        id="More-1_svg__a"
        x1={2}
        x2={2}
        y1={4.759}
        y2={7.46}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="More-1_svg__b"
        x1={2}
        x2={2}
        y1={4.759}
        y2={7.46}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="More-1_svg__c"
        x1={6}
        x2={6}
        y1={4.759}
        y2={7.46}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="More-1_svg__d"
        x1={6}
        x2={6}
        y1={4.759}
        y2={7.46}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="More-1_svg__e"
        x1={10}
        x2={10}
        y1={4.759}
        y2={7.46}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="More-1_svg__f"
        x1={10}
        x2={10}
        y1={4.759}
        y2={7.46}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <Circle cx={1.8} cy={6} r={1.05} stroke={color} />
    <Circle cx={6} cy={6} r={1.05} stroke={color} />
    <Circle cx={10.2} cy={6} r={1.05} stroke={color} />
        </>
      )}
    </Svg>
  );
};
export default More;
