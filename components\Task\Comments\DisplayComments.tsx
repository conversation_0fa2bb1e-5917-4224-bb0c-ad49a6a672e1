import { StyleSheet, View, Pressable, Animated } from 'react-native'
import { Surface, Text, Avatar, IconButton } from 'react-native-paper'
import React, { useState, useRef, useEffect, useCallback, createContext, useContext } from 'react'
import { formatDistanceToNow } from 'date-fns'
import { useMutation, useQuery } from '@apollo/client'
import { useUserStore } from '@/app/auth/userStore'
import { UserData } from '@/app/auth/userStorage'
import { CREATE_TASK_REACTION, DELETE_TASK_REACTION, UPDATE_TASK_COMMENT } from './TaskComments.data'
import { Colors } from '@/constants/Colors'
import { Modal, Portal } from 'react-native-paper'

export interface CommentInterface {
  id: string
  content: string
  createdAt: string
  createdBy: {
    firstName: string
    lastName: string
    id: string
  }
  mentions: any[]
  reactions: CommentReaction[]
  updatedAt: string
}

interface CommentReaction {
  id: string;
  reactionUnicode: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

const DEFAULT_REACTIONS = ['👍', '👎', '😆', '😢', '😡'];

interface CommentCardProps {
  comment: CommentInterface,
  taskId: string,
  userData: UserData | null,
  deleteCommentReaction: (reactionId: string) => void,
  createCommentReaction: (commentId: string, reactionUnicode: string) => void
}

interface DisplayCommentsProps {
  comments: CommentInterface[],
  taskId: string,
  refetchComments: () => void,
}

function getInitials(firstName: string, lastName: string): string {
  const firstInitial = firstName.charAt(0).toUpperCase();
  const lastInitial = lastName ? lastName.charAt(0).toUpperCase() : '';
  return `${firstInitial}${lastInitial}`;
}

function renderCommentWithMentions(content: string) {
  const parts = content.split(/(\*\*@[\w]+ [\w]+\*\*)/g)
  return parts.map((part, index) => {
    if (part.startsWith('**@') && part.endsWith('**')) {
      // This is a mention, render it with special styling
      const mention = part.slice(2, -2) // Remove ** markers
      return (
        <Text
          key={index}
          style={{
            color: Colors.custom.tasksSecondaryBg,
            fontWeight: '600'
          }}
        >
          {mention}
        </Text>
      )
    }
    return <Text key={index}>{part}</Text>
  })
}

// Define the LocalReactionState interface
interface LocalReactionState {
  [commentId: string]: {
    hasReaction: boolean;
    reactionUnicode?: string;
    timestamp: number;
    pendingOperation: 'add' | 'remove' | null;
  };
}

// Create a context for reaction state
interface ReactionContextType {
  reactionState: LocalReactionState;
  addReaction: (commentId: string, emoji: string) => void;
  removeReaction: (commentId: string) => void;
}

const ReactionContext = createContext<ReactionContextType | null>(null);

// Custom hook to use the reaction context
function useReactionContext() {
  const context = useContext(ReactionContext);
  if (!context) {
    throw new Error('useReactionContext must be used within a ReactionProvider');
  }
  return context;
}

const CommentCard = ({ comment, taskId, userData, deleteCommentReaction, createCommentReaction }: CommentCardProps) => {
  const [showReactionPicker, setShowReactionPicker] = useState(false);
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Use the reaction context
  const { reactionState, addReaction, removeReaction } = useReactionContext();

  // Check if we have a local reaction state for this comment
  const localReaction = reactionState[comment.id];

  // Find the user's reaction from server data
  const serverReaction = comment.reactions.find(r => r.user.id === userData?.id);

  // Determine if the user has reacted based on local state first, then fallback to server data
  const hasReacted = localReaction ?
    localReaction.hasReaction :
    !!serverReaction;

  // Determine which reaction to display
  const displayReaction = localReaction?.hasReaction ?
    { id: 'local', reactionUnicode: localReaction.reactionUnicode || '👍' } :
    serverReaction;

  const initials = getInitials(comment.createdBy?.firstName, comment.createdBy?.lastName);
  const fullName = comment.createdBy ?
    `${comment.createdBy.firstName || ''} ${comment.createdBy.lastName || ''}`.trim() :
    'Anonymous';
  const timeAgo = formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true });

  // Bubble animation function
  const triggerBubbleAnimation = () => {
    // Reset scale to 1 first
    scaleAnim.setValue(1);

    // Create a sequence of animations for the bubble effect
    Animated.sequence([
      // Quickly grow to 1.5x size
      Animated.timing(scaleAnim, {
        toValue: 1.5,
        duration: 150,
        useNativeDriver: true
      }),
      // Slightly overshoot back down
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true
      }),
      // Settle back to normal size
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true
      })
    ]).start();
  };

  // Handle adding a reaction
  const handleAddReaction = (emoji: string) => {
    setShowReactionPicker(false);

    // Update local state and trigger API call
    addReaction(comment.id, emoji);

    // Trigger animation
    triggerBubbleAnimation();
  };

  // Handle removing a reaction
  const handleRemoveReaction = () => {
    // Update local state and trigger API call
    removeReaction(comment.id);
  };

  // Calculate total reactions count (considering local state)
  const getTotalReactionsCount = () => {
    // Start with server count
    let count = comment.reactions.length;

    // If local state says we have a reaction but server doesn't show it, add 1
    if (localReaction?.hasReaction && !serverReaction) {
      count += 1;
    }

    // If local state says we don't have a reaction but server shows it, subtract 1
    if (localReaction && !localReaction.hasReaction && serverReaction) {
      count -= 1;
    }

    return count;
  };

  const ReactionPicker = () => (
    <Portal>
      <Modal
        visible={showReactionPicker}
        onDismiss={() => setShowReactionPicker(false)}
        contentContainerStyle={styles.reactionPickerModal}
      >
        <View style={styles.reactionPickerContainer}>
          {DEFAULT_REACTIONS.map((emoji) => (
            <Pressable
              key={emoji}
              onPress={() => handleAddReaction(emoji)}
              style={styles.emojiButton}
            >
              <Text style={styles.emojiText}>{emoji}</Text>
            </Pressable>
          ))}
        </View>
      </Modal>
    </Portal>
  );

  return (
    <View style={styles.commentCard}>
      <Avatar.Text
        size={32}
        label={initials}
        style={styles.avatar}
        theme={{ colors: { primary: Colors.custom.tasksSecondaryBg } }}
      />
      <View style={styles.commentContent}>
        <View style={styles.commentHeader}>
          <View style={styles.nameAndTime}>
            <Text variant="bodyMedium" style={styles.userName}>{fullName}</Text>
            <Text variant="bodySmall" style={styles.timestamp}>{timeAgo}</Text>
          </View>
          <View style={styles.reactions}>
            {getTotalReactionsCount() > 0 ? (
              <Text variant="bodySmall" style={{
                color: hasReacted ? Colors.custom.tasksSecondaryBg : Colors.custom.black,
                marginTop: 4,
                marginRight: -10
              }}>
                {getTotalReactionsCount()}
              </Text>
            ) : null}

            {hasReacted && displayReaction ? (
              <Animated.View style={{
                transform: [{ scale: scaleAnim }]
              }}>
                <Pressable
                  onPress={handleRemoveReaction}
                  style={styles.reactionButton}
                >
                  <Text style={styles.reactionEmoji}>{displayReaction.reactionUnicode}</Text>
                </Pressable>
              </Animated.View>
            ) : (
              <Pressable onPress={() => setShowReactionPicker(true)} style={styles.likeButton}>
                <IconButton
                  icon="emoticon-outline"
                  size={20}
                  iconColor={Colors.custom.tasksSecondaryBg}
                />
              </Pressable>
            )}
          </View>
        </View>
        <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
          {renderCommentWithMentions(comment.content)}
        </View>
      </View>
      <ReactionPicker />
    </View>
  );
};

const DisplayComments = ({ comments, taskId, refetchComments }: DisplayCommentsProps) => {
  const userData = useUserStore((state) => state.userData);
  const [deleteCommentReaction] = useMutation(DELETE_TASK_REACTION);
  const [createReaction] = useMutation(CREATE_TASK_REACTION);
  const [updateTaskComment] = useMutation(UPDATE_TASK_COMMENT);

  // Local reaction state
  const [reactionState, setReactionState] = useState<LocalReactionState>({});

  // Queue to track pending operations
  const pendingOpsRef = useRef<{commentId: string, type: 'add' | 'remove', emoji?: string}[]>([]);
  const isProcessingRef = useRef(false);

  // Process the next operation in the queue
  const processNextOperation = useCallback(() => {
    if (pendingOpsRef.current.length === 0 || isProcessingRef.current) {
      return;
    }

    isProcessingRef.current = true;
    const nextOp = pendingOpsRef.current[0];

    if (nextOp.type === 'add') {
      // Handle add operation
      createReaction({
        variables: {
          input: {
            reactionUnicode: nextOp.emoji
          }
        }
      }).then((res: any) => {
        const reaction = res?.data?.createReaction?.result?.reaction;
        if (!reaction?.id) {
          return;
        }

        const currentComment = comments.find((c) => c.id === nextOp.commentId);
        if (!currentComment) {
          return;
        }

        // Filter out any existing reactions from this user
        const existingReactionIds = currentComment?.reactions
          ?.filter((r: any) => r.user?.id !== userData?.id)
          ?.map((r: any) => r.id)
          ?.filter(Boolean) || [];

        const updatedReactions = [...existingReactionIds, reaction.id];

        // Update the reaction in the comment
        updateTaskComment({
          variables: {
            taskId: taskId,
            commentId: nextOp.commentId,
            input: {
              reactions: updatedReactions
            }
          }
        }).then(() => {
          // Remove this operation from the queue
          pendingOpsRef.current.shift();
          isProcessingRef.current = false;

          // Process the next operation if any
          processNextOperation();
        }).catch((err) => {
          console.log('error updating comment reaction', err);
          pendingOpsRef.current.shift();
          isProcessingRef.current = false;
          processNextOperation();
        });
      }).catch((err) => {
        console.log('error creating comment reaction', err);
        pendingOpsRef.current.shift();
        isProcessingRef.current = false;
        processNextOperation();
      });
    } else if (nextOp.type === 'remove') {
      // Find the reaction ID from the server data
      const comment = comments.find(c => c.id === nextOp.commentId);
      const reaction = comment?.reactions.find(r => r.user.id === userData?.id);

      if (reaction?.id) {
        deleteCommentReaction({
          variables: {
            deleteReactionId: reaction.id
          }
        }).then(() => {
          pendingOpsRef.current.shift();
          isProcessingRef.current = false;
          processNextOperation();
        }).catch((err) => {
          console.log('error deleting comment reaction', err);
          pendingOpsRef.current.shift();
          isProcessingRef.current = false;
          processNextOperation();
        });
      } else {
        // No reaction to delete, just move on
        pendingOpsRef.current.shift();
        isProcessingRef.current = false;
        processNextOperation();
      }
    }
  }, [comments, createReaction, deleteCommentReaction, taskId, updateTaskComment, userData?.id]);

  // Add a reaction
  const addReaction = useCallback((commentId: string, emoji: string) => {
    // Update local state immediately
    setReactionState(prev => ({
      ...prev,
      [commentId]: {
        hasReaction: true,
        reactionUnicode: emoji,
        timestamp: Date.now(),
        pendingOperation: 'add'
      }
    }));

    // Add to operation queue
    pendingOpsRef.current.push({
      commentId,
      type: 'add',
      emoji
    });

    // Process the queue
    processNextOperation();
  }, [processNextOperation]);

  // Remove a reaction
  const removeReaction = useCallback((commentId: string) => {
    // Update local state immediately
    setReactionState(prev => ({
      ...prev,
      [commentId]: {
        hasReaction: false,
        timestamp: Date.now(),
        pendingOperation: 'remove'
      }
    }));

    // Add to operation queue
    pendingOpsRef.current.push({
      commentId,
      type: 'remove'
    });

    // Process the queue
    processNextOperation();
  }, [processNextOperation]);

  // When component unmounts, refetch to sync server state
  useEffect(() => {
    return () => {
      if (pendingOpsRef.current.length === 0) {
        refetchComments();
      }
    };
  }, [refetchComments]);

  // Create the context value
  const contextValue = {
    reactionState,
    addReaction,
    removeReaction
  };

  return (
    <ReactionContext.Provider value={contextValue}>
      <Surface style={styles.surfaceContainer} mode='flat'>
        {comments?.map((comment, index) => (
          <View key={comment.id} style={[
            styles.commentWrapper,
            index !== comments.length - 1 && styles.commentSpacing
          ]}>
            <CommentCard
              comment={comment}
              taskId={taskId}
              userData={userData}
              deleteCommentReaction={(id) => deleteCommentReaction({ variables: { deleteReactionId: id } })}
              createCommentReaction={(commentId, emoji) =>
                createReaction({ variables: { input: { reactionUnicode: emoji } } })
              }
            />
          </View>
        ))}
      </Surface>
    </ReactionContext.Provider>
  );
};

export default DisplayComments

const styles = StyleSheet.create({
  surfaceContainer: {
    padding: 20,
    paddingBottom: 24,
    width: '100%',
    height: 'auto',
    backgroundColor: Colors.custom.tasksBackground,
  },
  title: {
    marginBottom: 16,
  },
  commentWrapper: {
    width: '100%',
  },
  commentSpacing: {
    marginBottom: 4,
  },
  commentCard: {
    flexDirection: 'row',
  },
  avatar: {
    marginRight: 12,
    marginTop: 8,
  },
  commentContent: {
    flex: 1,
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  nameAndTime: {
    flexDirection: 'column',
  },
  userName: {
    fontWeight: '500',
    marginBottom: 2,
  },
  timestamp: {
    color: '#666',
  },
  reactions: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 80,
    justifyContent: 'flex-end',
  },
  likeButton: {
    zIndex: 1000,
  },
  reactionIcon: {
    width: 24,
    height: 24,
    marginHorizontal: 8
  },
  reactionPickerModal: {
    backgroundColor: 'white',
    padding: 10,
    margin: 20,
    borderRadius: 8,
    alignSelf: 'center',
    width: 'auto',
  },
  reactionPickerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emojiButton: {
    padding: 8,
    marginHorizontal: 4,
  },
  emojiText: {
    fontSize: 24,
  },
  reactionButton: {
    marginLeft: 12,
    marginRight: 16,
    paddingVertical: 2,
  },
  reactionEmoji: {
    fontSize: 16,
  },
})