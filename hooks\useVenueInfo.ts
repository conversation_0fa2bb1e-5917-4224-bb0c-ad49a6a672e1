import { useLazyQuery } from '@apollo/client';
import { GET_VENUE_INFO } from '@/graphql/queries/AskAntsy';
import { useToast } from '@/components/Toast';

interface VenueInfoResult {
  data: string | null;
  loading: boolean;
  type: string;
}

export const useVenueInfo = (partyId: string) => {
  const [getVenueInfo] = useLazyQuery(GET_VENUE_INFO);
  const toast = useToast();

  const fetchVenueInfo = async (
    short: boolean, 
    onResponse: (response: VenueInfoResult) => void
  ) => {
    try {
      onResponse({ data: null, loading: true, type: 'venue' });
      
      const { data } = await getVenueInfo({ 
        variables: { 
          input: { partyId },
          short
        }, 
        fetchPolicy: 'network-only' 
      });
      
      onResponse({ 
        data: data.venueDetails.result.answer, 
        loading: false,
        type: 'venue'
      });
      
      return !short;
    } catch (error) {
      onResponse({ data: null, loading: false, type: 'venue' });
      toast.error('Failed to get venue information');
      return false;
    }
  };

  return {
    fetchVenueInfo
  };
}; 