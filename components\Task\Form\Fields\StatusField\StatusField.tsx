import { View, Pressable } from 'react-native';
import { Text } from 'react-native-paper';
import { partyFieldStyles } from '../PartyField/PartyField.styles';
import { useToast } from '@/components/Toast/useToast';
import { BottomSheetModal, BottomSheetBackdrop, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useRef, useMemo, useEffect } from 'react';
import { TaskStatus } from '@/constants/Task.constants';
import { useKeyboard } from '@/commons/KeyboardContext';
import { Icons, Colors } from '@/constants/DesignSystem';
import { Check } from '@/components/icons';

interface StatusFieldProps {
  selectedStatus: string;
  onStatusSelect: (status: string) => void;
  onSaveStatus?: (status: string) => Promise<void>;
  taskId?: string;
  inputTheme: any;
  isLoading?: boolean;
  dueDate?: Date | null;
}

const CustomRadioButton = ({ isSelected, onPress }: { isSelected: boolean; onPress: () => void }) => (
  <Pressable 
    onPress={onPress}
    style={{
      width: 27,
      height: 27,
      borderRadius: 20,
      borderWidth: 2,
      borderColor: isSelected ? '#768DEC' : '#CFCECE',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: isSelected ? '#BEC9F6' : 'transparent',
      marginLeft: 16,
    }}
  >
    {isSelected && (
      <Check 
        size={Icons.size.md} 
        color={Colors.primary}
      />
    )}
  </Pressable>
);

function getStatusColor(status: string | undefined): string {
  switch (status) {
    case TaskStatus.IN_PROGRESS:
      return '#FEBD73';
    case TaskStatus.COMPLETED:
      return '#15B097';
    case TaskStatus.CLOSED:
      return '#CFCECE';
    default:
      return '#000000';
  }
}

export function StatusField({ 
  selectedStatus,
  onStatusSelect,
  onSaveStatus,
  taskId,
  dueDate,
}: StatusFieldProps) {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['42%'], []);
  const toast = useToast();
  const isOverdue = dueDate ? dueDate < new Date() : false;
  const { isKeyboardActive } = useKeyboard();

  const statuses = useMemo(() => [
    { id: TaskStatus.IN_PROGRESS, label: 'In Progress', order: 1 },
    { id: TaskStatus.COMPLETED, label: 'Completed', order: 2 },
    { id: TaskStatus.CLOSED, label: 'Closed', order: 3 },
  ], []);

  useEffect(() => {
    if (selectedStatus && !statuses.find(s => s.id === selectedStatus)) {
      onStatusSelect(TaskStatus.IN_PROGRESS);
    }
  }, [selectedStatus]);

  const handlePress = () => {
    if (isKeyboardActive) return;
    bottomSheetRef.current?.present();
  };

  const handleStatusSelect = async (statusId: string) => {
    const previousStatus = selectedStatus;
    
    if (!statuses.find(s => s.id === statusId)) {
      toast.error('Invalid status selected');
      return;
    }

    onStatusSelect(statusId);
    bottomSheetRef.current?.dismiss();
    
    if (onSaveStatus && taskId) {
      try {
        await onSaveStatus(statusId);
      } catch (error: any) {
        console.log(error);
        onStatusSelect(previousStatus);
        toast.error('Failed to update status');
      }
    }
  };

  const getStatusLabel = (statusId: string) => {
    return statuses.find(s => s.id === statusId)?.label || 'Select Status';
  };

  return (
    <>
      <View style={[partyFieldStyles.inputWrapper, 
        {top: -20}]}>
        <Pressable 
          onTouchEnd={handlePress}
          disabled={isKeyboardActive}
          style={{
            width: '55%',
          }}
        >
          <Text style={partyFieldStyles.labelText}>Status</Text>
          <Text 
            style={[
              selectedStatus ? partyFieldStyles.valueText : partyFieldStyles.selectText,
              { color: getStatusColor(selectedStatus) },
              (isOverdue && selectedStatus === TaskStatus.IN_PROGRESS) && { color: '#E4626F' },
            ]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {getStatusLabel(selectedStatus)}
          </Text>
        </Pressable>
      </View>

      <BottomSheetModal
        ref={bottomSheetRef}
        index={1}
        snapPoints={snapPoints}
        enablePanDownToClose
        backdropComponent={(props) => (
          <BottomSheetBackdrop
            {...props}
            appearsOnIndex={0}
            disappearsOnIndex={-1}
            pressBehavior="close"
          />
        )}
      >
        <View style={[partyFieldStyles.bottomSheetContainer, {
        }]}>
          <View style={partyFieldStyles.header}>
            <Text style={partyFieldStyles.headerTitle}>Select Status</Text>
          </View>

          <BottomSheetScrollView contentContainerStyle={partyFieldStyles.scrollContent}>
            {statuses.map((status) => (
              <Pressable
                key={status.id}
                onPress={() => handleStatusSelect(status.id)}
                style={partyFieldStyles.listItem}
              >
                <Text style={partyFieldStyles.listItemTitle} numberOfLines={4}>
                  {status.label}
                </Text>
                <CustomRadioButton
                  isSelected={selectedStatus === status.id}
                  onPress={() => handleStatusSelect(status.id)}
                />
              </Pressable>
            ))}
          </BottomSheetScrollView>
        </View>
      </BottomSheetModal>
    </>
  );
} 