import React, { useState } from 'react';
import { View, TextInput, StyleSheet } from 'react-native';

export function CreateCircle() {

  const [circle, setCircle] = useState({
    name: '',
    description: '',
  });

  const handleInputChange = (field: string, value: string) => {
    setCircle({
      ...circle,
      [field]: value,
    });
  };

  return (
    <View style={styles.container}>
      <TextInput
        style={styles.input}
        value={circle.name}
        onChangeText={(text) => handleInputChange('name', text)}
        maxLength={24}
        placeholder="Circle name"
      />
      <TextInput
        style={[styles.input, styles.descriptionInput]}
        value={circle.description}
        onChangeText={(text) => handleInputChange('description', text)}
        maxLength={200}
        multiline={true}
        placeholder="Short description"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
    flex: 1,
    marginTop: 100,
    gap: 10
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 8,
  },
  descriptionInput: {
    height: 100,
    textAlignVertical: 'top',
  },
});
