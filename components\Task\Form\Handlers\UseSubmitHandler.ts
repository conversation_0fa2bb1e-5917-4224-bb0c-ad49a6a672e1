import { useCallback } from 'react';

interface FormData {
  title: string;
  description: string;
  dueDate: string;
  assignedTo: string;
  partyId: string;
}

interface UseSubmitHandlerProps {
  onSubmit: (formData: FormData) => void;
  title?: string;
  description?: string;
  dueDate?: string;
  assignedTo?: string;
  partyId?: string;
}

export function useSubmitHandler({
  onSubmit,
  title,
  description,
  dueDate,
  assignedTo,
  partyId,
}: UseSubmitHandlerProps) {
  const handleSubmit = useCallback(() => {
    const formData: FormData = {
      title: title || '',
      description: description || '',
      dueDate: dueDate || '',
      assignedTo: assignedTo || '',
      partyId: partyId || '',
    };

    onSubmit(formData);
  }, [title, description, dueDate, assignedTo, partyId, onSubmit]);

  return { handleSubmit };
}