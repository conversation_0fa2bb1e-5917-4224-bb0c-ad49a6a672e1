import { StyleSheet, View } from 'react-native';
import { Divider, Button } from 'react-native-paper';
import { ThemedText } from "@/components/UI/ThemedText";
import { CoHost } from '../party-dashboard.model';
import { formatPreferenceValue } from './preference.helper';
import { PREFERENCE_ITEM_LABELS } from '@/constants/partyDashboard';
import { useState } from 'react';

interface PreferenceItemProps {
    label: string;
    value: string | CoHost[] | number;
    isError?: boolean;
    maxLines?: number;
}

export function PreferenceItem({ 
    label, 
    value, 
    isError,
    maxLines = 1 
}: PreferenceItemProps) {
    const [isExpanded, setIsExpanded] = useState(false);
    const { displayValue, error } = formatPreferenceValue(label, value, isError);
    const [textHeight, setTextHeight] = useState(0);
    const [maxHeight, setMaxHeight] = useState(0);

    // Only show expand/collapse for vendors and co-hosts
    const isExpandable = label === 'Services' || label === PREFERENCE_ITEM_LABELS.VENDOR_OVERVIEW_COHOST;
    const isTextTruncated = isExpandable && textHeight > maxHeight && !isExpanded;
    return (
        <View>
            <Divider style={styles.divider} />
            <View style={styles.preferenceItem}>
                <View style={styles.labelContainer}>
                    <ThemedText type="default" style={styles.label}>
                        {label}
                    </ThemedText>
                    <ThemedText type="default" style={styles.separator}>:</ThemedText>
                </View>
                <View style={styles.valueContainer}>
                    <ThemedText 
                        type="default" 
                        style={[
                            styles.value,
                            error && styles.errorValue,
                            label === PREFERENCE_ITEM_LABELS.VENDOR_OVERVIEW_COHOST && styles.coHostValue
                        ]}
                        numberOfLines={isExpandable ? (isExpanded ? undefined : maxLines) : 1}
                        onTextLayout={(e) => {
                            if (isExpandable) {
                                setTextHeight(e.nativeEvent.lines.reduce((acc, line) => acc + line.height, 0));
                            }
                        }}
                        onLayout={(e) => {
                            if (isExpandable) {
                                const lineHeight = 20; // Approximate line height
                                setMaxHeight(lineHeight * maxLines);
                            }
                        }}
                    >
                        {displayValue}
                    </ThemedText>
                    {isTextTruncated && (
                        <Button
                            onPress={() => setIsExpanded(true)}
                            mode="text"
                            compact
                            style={styles.showMoreButton}
                        >
                            Show more
                        </Button>
                    )}
                    {isExpandable && isExpanded && (
                        <Button
                            onPress={() => setIsExpanded(false)}
                            mode="text"
                            compact
                            style={styles.showMoreButton}
                        >
                            Show less
                        </Button>
                    )}
                </View>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    preferenceItem: {
        flexDirection: 'row',
        paddingVertical: 12,
    },
    labelContainer: {
        flexDirection: 'row',
        width: 100,
        alignItems: 'flex-start',
    },
    valueContainer: {
        flex: 1,
    },
    label: {
        fontSize: 14,
        color: '#666666',
    },
    separator: {
        marginHorizontal: 8,
        color: '#666666',
    },
    value: {
        fontSize: 14,
        color: '#333333',
    },
    errorValue: {
        color: '#dc3545',
    },
    divider: {
        backgroundColor: '#e0e0e0',
        height: 1,
    },
    coHostValue: {
        flexWrap: 'wrap',
        lineHeight: 20,
    },
    showMoreButton: {
        marginTop: 4,
        marginLeft: -8,
        alignSelf: 'flex-start',
    },
});
