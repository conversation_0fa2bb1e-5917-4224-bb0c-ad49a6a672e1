import { View, Pressable, TextInput, ActivityIndicator } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { styles } from '@/app/(home)/Task/tasks.styles';
import { useRouter } from 'expo-router';
import { useFilterStore } from '@/services/filterService';
import { Icons, Colors } from '@/constants/DesignSystem';
import { Search } from '@/components/icons';

interface SearchBarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onSubmit: (query: string) => void;
  isSearching?: boolean;
}

export function SearchBar({ 
  searchQuery, 
  onSearchChange, 
  onSubmit,
  isSearching 
}: SearchBarProps) {
  const router = useRouter();
  const filters = useFilterStore((state) => state.filters);

  const handleSubmit = () => {
    onSubmit(searchQuery);
  };

  const handleFilterPress = () => {
    router.push({
      pathname: '/Task/Filter',
      params: { 
        initialFilters: filters ? JSON.stringify(filters) : undefined 
      }
    });
  };

  return (
    <View style={[styles.searchContainer]}>
      <View style={[styles.searchWrapper]}>
        {isSearching ? (
          <ActivityIndicator size="small" color="#666666" style={{ marginRight: 8 }} />
        ) : (
          <Search size={Icons.size.md} color={Colors.text.secondary} style={{ marginRight: 8 }} />
        )}
        <TextInput
          placeholder="search"
          onChangeText={onSearchChange}
          value={searchQuery}
          style={styles.searchInput}
          placeholderTextColor="#000000"
          returnKeyType="search"
          onSubmitEditing={handleSubmit}
        />
      </View>
      <Pressable 
        style={styles.filterIconButton}
        onPress={handleFilterPress}
      >
        <MaterialCommunityIcons 
          name="tune" 
          size={20} 
          color="#000000"
        />
      </Pressable>
    </View>
  );
} 