import React, { useState } from 'react';

interface UsePaginationProps {
  pageSize?: number;
  totalItems?: number;
}

export function usePagination({ pageSize = 10, totalItems = 0 }: UsePaginationProps) {
  const [page, setPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);

const getPaginationVariables = React.useCallback((pageNumber: number) => ({
  pagination: {
    skip: (pageNumber - 1) * pageSize,
    limit: pageSize,
    },
  }), [pageSize]);  
  
  const updateHasMore = (totalItems: number, currentItemsCount: number) => {
    const hasMore = currentItemsCount < totalItems;
    setHasMoreData(hasMore);
  };
  

  function resetPagination() {
    setPage(1);
    setIsLoadingMore(false);
    setHasMoreData(true);
  }

  return {
    page,
    setPage,
    isLoadingMore,
    setIsLoadingMore,
    hasMoreData,
    setHasMoreData,
    getPaginationVariables,
    updateHasMore,
    pageSize,
    resetPagination
  };
}