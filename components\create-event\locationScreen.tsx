import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Text, TextInput } from 'react-native-paper';
import { SearchModal } from '../UI/ReusableComponents/SerachModal';
import { LocationItem } from '@/app/(home)/EventsDashboard/AddParty.models';
import { useQuery } from '@apollo/client';
import { GET_LOCATIONS } from '@/app/(home)/EventsDashboard/AddParty.data';
import { WINDOW_HEIGHT } from '@gorhom/bottom-sheet';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';

const AreaSearchModal = SearchModal<LocationItem>();

interface LocationScreenProps {
  locationId: string;
  locationName: string;
  onLocationSelect: (locationId: string, locationName: string) => void;
}

export function LocationScreen({
  locationId,
  locationName,
  onLocationSelect
}: LocationScreenProps) {
  const [showAreaSearch, setShowAreaSearch] = React.useState(false);
  const { data: locations, loading: locationsLoading, error: locationsError } = useQuery(GET_LOCATIONS);

  const locationItems: LocationItem[] = React.useMemo(() =>
    locations?.getMdServiceLocations?.result?.mdServiceLocations?.map((location: any) => ({
      id: location?.id,
      title: location?.city,
    })) || [],
    [locations]
  );

  if (locationsLoading) {
    return <FastPartyActivityIndicator />;
  }

  if (locationsError) {
    return (
      <View style={{ height: WINDOW_HEIGHT - 200, padding: 16, justifyContent: 'center' }}>
        <Text variant="bodyLarge" style={{ color: 'red' }}>
          Error loading locations. Please try again.
        </Text>
      </View>
    );
  }

  return (
    <View style={{ height: WINDOW_HEIGHT - 200, padding: 16 }}>
      <View style={{ flex: 1 }}>
        <Text variant="headlineMedium" style={{ marginBottom: 24 }}>
          Where is your event?
        </Text>

        <TouchableOpacity onPress={() => setShowAreaSearch(true)} activeOpacity={0.7}>
          <TextInput
            label="Area/Location"
            value={locationName}
            editable={false}
            right={<TextInput.Icon icon="chevron-down" onPress={() => setShowAreaSearch(true)} />}
          />
        </TouchableOpacity>
      </View>

      <AreaSearchModal
        visible={showAreaSearch}
        onDismiss={() => setShowAreaSearch(false)}
        items={locationItems}
        onSelect={(item) => {
          onLocationSelect(item.id, item.title);
          setShowAreaSearch(false);
        }}
        searchPlaceholder="Search area..."
        noOptionsText="No areas available"
      />
    </View>
  );
}