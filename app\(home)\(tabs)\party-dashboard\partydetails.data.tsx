import gql from 'graphql-tag';

export const GET_PARTY_DETAILS = gql`
 query GetPartyById($getPartyByIdId: ID!) {
  getPartyById(id: $getPartyByIdId) {
    ... on PartyResponse {
      message
      result {
        party {
          id
          name
          weather {
            weatherUrl
            avgTempC
            avgTempF
            condition {
              icon
              text
            }
          }
          time
          partyType {
            portraitImage
          }
          event {
            id
            mainHost {
              userId {
                id
                firstName
                lastName
                profilePicture
              }
            }
            location {
              name
              city
            }
          }
          venueAddress {
            coordinates {
              latitude
              longitude
            }
            id
            address
            city
            state
            name
            directions
          }
          activity {
            id
            type
            text
            media {
              url
              title
              uploadedAt
              owner {
                role
                firstName
                profilePicture
              }
            }
            createdAt
            createdBy {
              firstName
              lastName
              role
              id
            }
            reactions {
              reactionUnicode
              updatedAt
              user {
                id
                firstName
                lastName
                profilePicture
                phone
                email
              }
              id
            }
            parentMessageId
          }
          rsvps {
            id
            invitation {
              message
             
            }
            guest {
              id
              user {
                firstName
                profilePicture
                email
                phone
                lastName
                role
                id
              }
              additionalGuestsCount
            }
            status
            message
          }
          invitation {
            _id
            media {
              url
            }
            message
            savedGuests {
              id
              user {
                firstName
                phone
                lastName
                profilePicture
                id
              }
            }
          }
          userRole
          coHosts {
            id
            userId {
              id
              firstName
              lastName
            }
          }
          muted
        }
      }
    }
    ... on PartyErrorResponse {
      errors {
        field
        message
      }
      status
    }
  }
}
`

export const POST_COMMENT = gql`
mutation CreateMessage($input: CreateMessageInput!) {
  createMessage(input: $input) {
    ... on MessageResponse {
      message
    }
    ... on MessageErrorResponse {
      message
    }
  }
}`

export const GET_UPDATED_MESSAGES = gql`
query GetPartyById($getPartyByIdId: ID!) {
  getPartyById(id: $getPartyByIdId) {
    ... on PartyResponse {
      message
      result {
        party {
          activity {
            id
            type
            text
            media {
              url
              title
              uploadedAt
              owner {
                role
                firstName
                profilePicture
              }
            }
            createdAt
            createdBy {
              firstName
              lastName
              role
              id
            }
            reactions {
              reactionUnicode
              updatedAt
              user {
                id
                firstName
                lastName
                profilePicture
                phone
                email
              }
              id
            }
            parentMessageId
          }
          userRole
        }
      }
    }
    ... on PartyErrorResponse {
      errors {
        field
        message
      }
      status
    }
  }
}
`

export const GET_MEDIA_FOLDERS_FOR_EVENT = gql`
  query GetMediaFolders($filter: MediaFolderFilterInput) {
  getMediaFolders(filter: $filter) {
    ... on MediaFoldersResponse {
      result {
        mediaFolders {
          id
        }
      }
    }
  }
}
`;