import * as React from "react";
import Svg, { <PERSON>, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Events = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Events-1_svg__a)">
      <Path
        fill="url(#Events-1_svg__b)"
        fillRule="evenodd"
        d="M4.036 1.286a.786.786 0 0 0-1.572 0v.785H1.68C1.028 2.071.5 2.6.5 3.25v7.071c0 .651.528 1.179 1.179 1.179h8.642c.651 0 1.179-.528 1.179-1.179V3.25c0-.65-.528-1.179-1.179-1.179h-.785v-.785a.786.786 0 1 0-1.572 0v.785H4.036zm1.955 3.24a.39.39 0 0 1 .357.213l.002.003.584 1.177h.003l1.295.197a.394.394 0 0 1 .216.677l-.912.866a.2.2 0 0 1 .014.05l.183 1.285a.393.393 0 0 1-.581.416l-1.144-.606a.04.04 0 0 0-.022 0l-1.144.606a.393.393 0 0 1-.58-.419l.217-1.287.004-.02-.938-.899-.004-.004a.393.393 0 0 1 .217-.665h.003l1.298-.19.001-.002.584-1.173a.39.39 0 0 1 .347-.224"
        clipRule="evenodd"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Events-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.828}
        y2={14.029}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Events-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Events_svg__a)"
    >
      <Path d="M1.77 2.154a.77.77 0 0 0-.77.77v7.307a.77.77 0 0 0 .77.769h8.46a.77.77 0 0 0 .77-.77V2.924a.77.77 0 0 0-.77-.77H8.693M3.308 1v2.308M8.692 1v2.308M3.308 2.154h3.846" />
      <Path d="m6.191 4.194.644 1.295a.2.2 0 0 0 .165.12l1.429.217a.217.217 0 0 1 .12.374L7.493 7.202a.21.21 0 0 0 0 .195l.202 1.422a.217.217 0 0 1-.321.232l-1.272-.674a.26.26 0 0 0-.21 0l-1.272.674a.217.217 0 0 1-.322-.232l.24-1.422a.21.21 0 0 0-.038-.195l-1.055-1.01a.217.217 0 0 1 .12-.366l1.429-.21a.2.2 0 0 0 .165-.12l.643-1.294a.217.217 0 0 1 .39-.008" />
    </G>
    <Defs>
      <ClipPath id="Events_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Events;
