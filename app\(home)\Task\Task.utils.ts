import { ApolloClient, HttpLink, NormalizedCacheObject, gql } from "@apollo/client";
import { setContext } from '@apollo/client/link/context';
import { Platform } from 'react-native';

import { CREATE_DOCUMENT, DELETE_DOCUMENT } from "./Task.data";
import { FastPartyClient } from "@/commons/apollo-client";
import { userStorage } from "@/app/auth/userStorage";

export async function createDocumentAfterUpload(
    uploadResponse: UploadSuccess,
    client: ApolloClient<NormalizedCacheObject>
  ): Promise<DocumentResponse> {
    try {
      const result = await client.mutate({
        mutation: CREATE_DOCUMENT,
        variables: {
          input: {
            documentUrl: uploadResponse.url,
            documentType: "OTHER",
            name: uploadResponse.originalName
          }
        }
      });

      return result.data.createDocument.result.document;
    } catch (error) {
      console.error('Error creating document:', error);
      throw new Error('Failed to create document');
    }
  }

  export async function uploadFiles(files: UploadFileInput[], containerName: string) {
    try {
      const formData = new FormData();

      const operations = {
        query: "mutation Mutation($containerName: String, $files: [Upload!]!) {\r\n    uploadFiles(containerName: $containerName, files: $files) {\r\n      ... on FileUploadResponse {\r\n        result {\r\n          bulkResult {\r\n            successful {\r\n              url\r\n              originalName\r\n              key\r\n            }\r\n            failures {\r\n              message\r\n              filename\r\n            }\r\n            totalProcessed\r\n            successCount\r\n            failureCount\r\n          }\r\n        }\r\n      }\r\n      ... on FileErrorResponse {\r\n        status\r\n        message\r\n        errors {\r\n          field\r\n          message\r\n        }\r\n      }\r\n    }\r\n  }",
        operationName: "Mutation",
        variables: {
          containerName: containerName,
          files: Array(files.length).fill(null)
        }
      };

      formData.append('operations', JSON.stringify(operations));

      const map: { [key: string]: string[] } = {};
      files.forEach((_, index) => {
        map[index.toString()] = [`variables.files.${index}`];
      });
      formData.append('map', JSON.stringify(map));

      // Add files with numeric keys
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // Log file details for debugging
        console.log(`File ${i} details:`, {
          name: file.name,
          type: file.type,
          uri: file.uri,
          size: file.size
        });

        // For Android, ensure the file object is properly formatted
        const fileToUpload = Platform.OS === 'android' 
          ? {
              uri: file.uri,
              type: file.type || 'application/octet-stream',
              name: file.name
            }
          : {
              uri: file.uri,
              type: file.type || 'application/octet-stream',
              name: file.name
            };

        formData.append(i.toString(), fileToUpload as any);
      }

      // Get the configured URI from FastPartyClient
      const httpLink = FastPartyClient.link as unknown as HttpLink;
      const uri = (httpLink as any).options?.uri || `${process.env.EXPO_PUBLIC_API_URL}/graphql`;

      console.log('Upload URL:', uri);

      // Get the token and set headers
      const token = await userStorage.getToken();

      // Set proper headers for multipart/form-data upload
      const headers: Record<string, string> = {
        authorization: token ? `Bearer ${token}` : '',
        'Accept': 'application/json',
        'Content-Type': 'multipart/form-data',
      };

      // Set timeout for fetch request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

      console.log('Sending upload request with headers:', headers);
      console.log('FormData entries:', Array.from(formData as any).map(([key, value]: [string, any]) => ({
        key,
        value: value instanceof File ? { name: value.name, type: value.type, size: value.size } : value
      })));

      try {
        const response = await fetch(uri, {
          method: 'POST',
          headers,
          body: formData,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          console.error('Upload response not OK:', {
            status: response.status,
            statusText: response.statusText
          });

          // Try to get error details from response
          const errorText = await response.text();
          console.error('Error response:', errorText);
          throw new Error(`Upload failed with status ${response.status}: ${errorText}`);
        }

        const result = await response.json();

        if (result.errors) {
          console.error('GraphQL Errors:', result.errors);
          throw new Error(result.errors[0].message);
        }

        if (!result.data?.uploadFiles?.result?.bulkResult) {
          console.error('Upload response missing expected data:', result);
          throw new Error('Upload failed: Invalid response format');
        }

        return result.data.uploadFiles.result.bulkResult;
      } catch (fetchError) {
        console.error('Fetch error details:', fetchError);
        if (fetchError instanceof Error) {
          // Add more context to the error
          throw new Error(`Network request failed: ${fetchError.message}`);
        }
        throw fetchError;
      }
    } catch (error) {
      console.error('Upload error details:', error);

      // Rethrow with more descriptive message
      if (error instanceof Error) {
        throw new Error(`File upload failed: ${error.message}`);
      } else {
        throw new Error('File upload failed with unknown error');
      }
    }
  }
  export interface CreateDocumentInput {
    documentUrl: string;
    documentType: string;
  }

  export interface DocumentResponse {
    id: string;
    name: string;
  }

  export interface FileUploadResponse {
    uploadFiles: {
      result: {
        bulkResult: {
          successful: {
            url: string;
            originalName: string;
            key: string;
          }[];
          failures: {
            message: string;
            filename: string;
          }[];
          totalProcessed: number;
          successCount: number;
          failureCount: number;
        };
      };
    };
  }

  export interface UploadFileInput {
    name: string;
    type: string;
    uri: string;
    size: number;
  }

  export interface UploadSuccess {
    url: string;
    originalName: string;
    key: string;
  }

  export async function deleteDocument(
    key: string,
    client: ApolloClient<NormalizedCacheObject>
  ): Promise<void> {
    try {
      console.log('Delete mutation request:', {
        mutation: DELETE_DOCUMENT.loc?.source.body,
        variables: { deleteTaskAttachmentId: key }
      });

      const result = await client.mutate({
        mutation: DELETE_DOCUMENT,
        variables: {
          deleteTaskAttachmentId: key
        }
      });

      console.log('Delete mutation response:', {
        data: result.data,
        errors: result.errors
      });
    } catch (error) {
      console.error('Delete mutation error:', error);
    }
  }