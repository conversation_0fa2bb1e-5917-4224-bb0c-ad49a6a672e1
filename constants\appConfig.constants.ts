export const VERSION_SOURCE = {
  ENV: 'env',
  STORAGE: 'storage',
  FALLBACK: 'fallback',
} as const;

export type VersionSource = typeof VERSION_SOURCE[keyof typeof VERSION_SOURCE];

export const ENVIRONMENT = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  STAGING: 'staging',
} as const;

export type Environment = typeof ENVIRONMENT[keyof typeof ENVIRONMENT];

export const STORAGE_KEYS = {
  APP_VERSION: '@app_version',
  LAST_UPDATE: '@last_version_update',
} as const;
