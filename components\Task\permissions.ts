// permissions.ts
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
import { Alert, Linking, Platform } from 'react-native';

interface PermissionResult {
  granted: boolean;
  canAskAgain: boolean;
}

export const PERMISSION_TYPES = {
  CAMERA: 'camera',
  MEDIA_LIBRARY: 'mediaLibrary',
  STORAGE: 'storage',
} as const;

export const PermissionsHandler = {
  async checkAndRequestPermission(
    permissionType: keyof typeof PERMISSION_TYPES
  ): Promise<PermissionResult> {
    try {
      switch (permissionType) {
        case 'CAMERA':
          return await this.handleCameraPermission();
        case 'MEDIA_LIBRARY':
          return await this.handleMediaLibraryPermission();
        case 'STORAGE':
          return await this.handleStoragePermission();
        default:
          throw new Error(`Unknown permission type: ${permissionType}`);
      }
    } catch (error) {
      console.error(`Error checking ${permissionType} permission:`, error);
      return { granted: false, canAskAgain: true };
    }
  },

  async handleCameraPermission(): Promise<PermissionResult> {
    const { status: existingStatus } = await ImagePicker.getCameraPermissionsAsync();
    
    if (existingStatus === 'granted') {
      return { granted: true, canAskAgain: true };
    }

    const { status, canAskAgain } = await ImagePicker.requestCameraPermissionsAsync();
    
    if (status === 'denied' && !canAskAgain) {
      this.showPermissionSettings('Camera');
    }

    return { 
      granted: status === 'granted',
      canAskAgain 
    };
  },

  async handleMediaLibraryPermission(): Promise<PermissionResult> {
    const { status: existingStatus } = await MediaLibrary.getPermissionsAsync();
    
    if (existingStatus === 'granted') {
      return { granted: true, canAskAgain: true };
    }

    const { status, canAskAgain } = await MediaLibrary.requestPermissionsAsync();
    
    if (status === 'denied' && !canAskAgain) {
      this.showPermissionSettings('Photo Library');
    }

    return { 
      granted: status === 'granted',
      canAskAgain 
    };
  },

  async handleStoragePermission(): Promise<PermissionResult> {
    // iOS doesn't need explicit storage permission
    if (Platform.OS === 'ios') {
      return { granted: true, canAskAgain: true };
    }

    // For Android, we check if we have permission to read external storage
    const { status: existingStatus } = await MediaLibrary.getPermissionsAsync();
    
    if (existingStatus === 'granted') {
      return { granted: true, canAskAgain: true };
    }

    const { status, canAskAgain } = await MediaLibrary.requestPermissionsAsync();
    
    if (status === 'denied' && !canAskAgain) {
      this.showPermissionSettings('Storage');
    }

    return { 
      granted: status === 'granted',
      canAskAgain 
    };
  },

  showPermissionSettings(permissionName: string): void {
    Alert.alert(
      `${permissionName} Permission Required`,
      `We need ${permissionName.toLowerCase()} access to continue. Please enable it in your device settings.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Open Settings', 
          onPress: () => Linking.openSettings() 
        }
      ]
    );
  }
};