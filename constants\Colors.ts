/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#7A1CAC';
const tintColorDark = '#fff';

export const Colors = {
  light: {
    text: '#11181C',
    textSecondary: '#687076',
    background: '#fff',
    secondaryBackground: '#f5f5f5',
    tint: tintColorLight,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,
    cardShadow: '#000',
    starColor: '#FFD700',
    reviewCountColor: '#888',
    priceColor: '#4CAF50',
    paginationDotBackground: '#fff',
    notificationBadge: '#FF0000',
    eventHeaderOverlay: 'rgba(0, 0, 0, 0.5)',
    buttonBackground: 'rgba(103, 80, 164, 1)',
    success: '#4CAF50',
    createEventButtonBackground: '#f1ecff',
    error: '#F44336',
    border: '#E0E0E0',
    taskTitle: '#101828',
    taskBorder: '#EAECF0',
    taskStatusInProgress: '#FEBD73',
    taskStatusOverdue: '#E4626F',
    taskStatusCompleted: '#15B097',
    taskStatusClosed: '#CFCECE',
    taskCheckboxUnchecked: '#D0D5DD',
    taskCompletedBorder: '#0B7B69',
    taskCompletedBackground: '#A4F4E7',
    taskClosedText: '#BAB8B8',
    taskAssignee: '#6941C6',
    taskIcon: '#667085',
    taskSearchBackground: '#E2E7FB',
    taskFilterText: '#344054',
  },
  dark: {
    text: '#ECEDEE',
    textSecondary: '#9BA1A6',
    background: '#151718',
    secondaryBackground: '#2a2d2f', // Added secondary background color for dark mode
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
  },
  custom: {
    'black': '#000',
    'white': '#fff',
    'red': '#FF0000',
    'overlay': 'rgba(0,0,0,0.3)',
    'purplish':"#f1ecff",
    'hostBackground': '#EDB731',
    'guestBackground': '#059B71',
    'searchBarActive': '#6b46c1',
    'searchBarInactive': '#E2E8F0',
    'createEventButtonText': '#555459',
    'createEventButtonBackground': '#f1ecff',
    'semiTransparentBlack': 'rgba(0, 0, 0, 0.5)', 
    'eventCardSecondaryText': '#9AABF1',
    'linearGradient': '#565656',
    'partyServicesChipColor': '#A28AE0',
    'tasksBackground': '#E2E7FB',
    'tasksSecondaryBg': '#5270E7',
    'lightGray': '#808080',
    'blue': '#5270E7',
    'settingsIcons': '#768DEC',
    'orange': '#FFA500'
  }
};