// DownloadButton.tsx

import React, { useState } from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import { Colors, Typography, Spacing, Borders, Shadows, Icons } from '@/constants/DesignSystem';

type DownloadButtonProps = {
    selectedPhotos?: string[] | undefined;
    isIconVisible?: boolean;
    photoUrls?: { [key: string]: string };
    onDownload?: () => void;
    
};

export default function DownloadButton({ selectedPhotos, isIconVisible, photoUrls, onDownload }: DownloadButtonProps) {
    const [isDownloading, setIsDownloading] = useState(false);

    const handleDownload = async () => {
        if (!selectedPhotos?.length || !photoUrls) {
            alert('No photos selected for downloading.');
            return;
        }

        try {
            setIsDownloading(true);
            
            const { status } = await MediaLibrary.requestPermissionsAsync();
            if (status !== 'granted') {
                alert('Permission to access media library was denied');
                return;
            }

            for (const photoId of selectedPhotos) {
                const photoUrl = photoUrls[photoId];
                if (!photoUrl) continue;

                const filename = photoUrl.split('/').pop() || 'photo.jpg';
                const fileUri = `${FileSystem.documentDirectory}${filename}`;

                await FileSystem.downloadAsync(photoUrl, fileUri);

                await MediaLibrary.saveToLibraryAsync(fileUri);

                await FileSystem.deleteAsync(fileUri, { idempotent: true });
            }

            alert(`${selectedPhotos.length} photo(s) downloaded successfully!`);

        } catch (error) {
           
            alert('Failed to download photos. Please try again.');
        } finally {
            setIsDownloading(false);
            onDownload?.();
        }
    };

    return (
        isIconVisible && (
        <TouchableOpacity 
            style={[styles.actionButton, isDownloading && styles.disabledButton]} 
            onPress={handleDownload}
            disabled={isDownloading}
        >
            {isDownloading ? (
                <ActivityIndicator color={Colors.text.tertiary} />
            ) : (
                <Ionicons
                    name="download-outline"
                    size={Icons.size.xl}
                    color={Colors.text.tertiary}
                />
            )}
            <Text style={styles.actionButtonText}>Download</Text>
        </TouchableOpacity>
    ));
}

const styles = StyleSheet.create({
    actionButton: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: Spacing.md,
        paddingHorizontal: Spacing.lg,
    },
    actionButtonText: {
        color: Colors.text.tertiary,
        fontSize: Typography.fontSize.sm,
        marginTop: Spacing.xs,
        fontWeight: Typography.fontWeight.medium,
        fontFamily: Typography.fontFamily.primary,
    },
    disabledButton: {
        opacity: 0.7,
    },
});

