import { Pressable, View } from 'react-native';
import { Text } from 'react-native-paper';
import { styles } from "../Filter.styles";
import { DateRangeSelector } from '@/components/UI/ReusableComponents/DateRangeSelector';
import { Icons, Colors } from '@/constants/DesignSystem';
import { DropDown, Cross } from '@/components/icons'; 

interface DateFilterProps {
  selectedDateRange: string;
  isDateRangeSelectorVisible: boolean;
  setIsDateRangeSelectorVisible: (visible: boolean) => void;
  dateRange: {
    startDate: Date;
    endDate: Date;
  };
  onDateRangeConfirm: (startDate: Date, endDate: Date) => void;
  isDateRangeSelected: boolean;
  onResetDate: () => void;
}

export function DateFilter({
  isDateRangeSelected,
  selectedDateRange,
  isDateRangeSelectorVisible,
  setIsDateRangeSelectorVisible,
  dateRange,
  onDateRangeConfirm,
  onResetDate,
}: DateFilterProps) {
  return (
    <>
      <View style={[styles.oddGridItem, { width: '30%'}]}>
        <Text style={styles.gridItemTextLabel}>Date</Text>
      </View>

      <View style={[styles.evenGridItem, { width: '70%'}]}>
        <Pressable
          onTouchStart={() => {
            if (isDateRangeSelected) {
              onResetDate();
            } else {
              setIsDateRangeSelectorVisible(true);
            }
          }}
          style={({ pressed }) => [
            styles.dateButton,
            pressed && styles.dateButtonPressed,
            isDateRangeSelected ? {backgroundColor: '#9AABF1'} : {backgroundColor: 'white'}
          ]}
        >
          <View style={styles.dateButtonContent}>
            <Text style={[styles.dateButtonText]}>{selectedDateRange}</Text>
            {isDateRangeSelected ? (
              <Cross 
                style={styles.icon}
                size={Icons.size.md} 
                color={Colors.text.secondary} 
              />
            ) : (
              <DropDown 
                style={styles.icon}
                size={Icons.size.md} 
                color={Colors.text.secondary} 
              />
            )}
          </View>
        </Pressable>
      </View>

      <DateRangeSelector
        visible={isDateRangeSelectorVisible}
        onDismiss={() => setIsDateRangeSelectorVisible(false)}
        startDate={dateRange.startDate}
        endDate={dateRange.endDate}
        onConfirm={onDateRangeConfirm}
        title="Select Date Range"
        minimumDate={new Date()}
      />
    </>
  );
} 