import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Copy = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Copy-1_svg__a)">
      <Path
        fill="url(#Copy-1_svg__b)"
        fillRule="evenodd"
        d="M1.679.5C1.028.5.5 1.028.5 1.679v6.285c0 .651.528 1.179 1.179 1.179h6.285c.651 0 1.179-.528 1.179-1.179V1.68C9.143 1.028 8.615.5 7.964.5zM11.5 3.84a.59.59 0 1 0-1.179 0v5.892a.59.59 0 0 1-.589.59H3.84a.59.59 0 0 0 0 1.178h5.893c.977 0 1.768-.791 1.768-1.768z"
        clipRule="evenodd"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Copy-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.828}
        y2={14.029}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Copy-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Copy_svg__a)"
    >
      <Path d="M8.308 1H1.769A.77.77 0 0 0 1 1.77v6.538c0 .425.344.769.77.769h6.538a.77.77 0 0 0 .769-.77V1.77A.77.77 0 0 0 8.307 1" />
      <Path d="M11 3.308v6.923a.77.77 0 0 1-.77.769H3.309" />
    </G>
    <Defs>
      <ClipPath id="Copy_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Copy;
