import React from 'react';
import { View, Platform } from 'react-native';
import { Mo<PERSON>, Text, Button, useTheme } from 'react-native-paper';
import DateTimePicker from '@react-native-community/datetimepicker';

interface TimeModalProps {
  visible: boolean;
  onDismiss: () => void;
  value: Date;
  onChange: (date: Date) => void;
  title: string;
  minimumDate: Date;
  selectedDate: Date;
}

export function TimePickerModal({
  visible,
  onDismiss,
  value,
  onChange,
  title,
  minimumDate,
  selectedDate,
}: TimeModalProps) {
  const theme = useTheme();

  const isToday = (date: Date) => {
    return date.getDate() === minimumDate.getDate() &&
      date.getMonth() === minimumDate.getMonth() &&
      date.getFullYear() === minimumDate.getFullYear();
  };

  const handleChange = (_: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      onDismiss();
    }
    if (selectedDate) {
      const newTime = new Date(selectedDate);
      
      // If selected date is today, validate time
      if (isToday(value)) {
        if (newTime.getTime() < minimumDate.getTime()) {
          // If selected time is in the past, set it to current time
          onChange(new Date(minimumDate));
          return;
        }
      }
      
      // Set the selected time on the selected date
      const finalDateTime = new Date(value);
      finalDateTime.setHours(
        newTime.getHours(),
        newTime.getMinutes(),
        0,
        0
      );
      
      onChange(finalDateTime);
    }
  };

  // Get minimum time for today
  const getMinTime = () => {
    if (isToday(selectedDate)) {
      return minimumDate;
    }
    return undefined;
  };

  if (Platform.OS === 'android') {
    if (!visible) return null;
    
    return (
      <DateTimePicker
        value={value}
        mode="time"
        is24Hour={false}
        display="default"
        onChange={handleChange}
        minimumDate={getMinTime()}
        themeVariant={theme.dark ? 'dark' : 'light'}
        accentColor={theme.colors.primary}
      />
    );
  }

  return (
    <Modal
      visible={visible}
      onDismiss={onDismiss}
      contentContainerStyle={{ 
        backgroundColor: theme.colors.background,
        padding: 20,
        margin: 20,
        borderRadius: 8
      }}
    >
      <View>
        <Text variant="titleMedium" style={{ marginBottom: 16, color: theme.colors.onBackground }}>
          {title}
        </Text>
        <DateTimePicker
          value={value}
          mode="time"
          display="spinner"
          onChange={handleChange}
          minimumDate={getMinTime()}
          textColor={theme.colors.onBackground}
          accentColor={theme.colors.primary}
          themeVariant={theme.dark ? 'dark' : 'light'}
        />
        <Button 
          mode="contained" 
          onPress={onDismiss}
          style={{ marginTop: 16 }}
        >
          Done
        </Button>
      </View>
    </Modal>
  );
}