import { StyleSheet, View, Alert, Keyboard } from 'react-native'
import React, { useState, useEffect, useRef, useMemo } from 'react'
import { useQuery } from '@apollo/client'
import { GET_PARTY_BY_ID } from './AddParty.data'
import { Appbar, TouchableRipple } from 'react-native-paper'
import AddPartyForm from '@/components/event-dashboard/AddPartyForm'
import { ScrollView } from 'react-native-gesture-handler'
import { DateTimePickerModal } from '@/components/UI/ReusableComponents/DateTimePickerModal'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import { useUserStore } from '@/app/auth/userStore'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { BottomSheetModal } from '@gorhom/bottom-sheet'
import { BottomSheetModalProvider, BottomSheetBackdrop, BottomSheetScrollView, BottomSheetView } from '@gorhom/bottom-sheet'
import AddCohostsForm from '@/components/event-dashboard/AddParty/AddCohostsForm'
import { UPDATE_INVITATION, CREATE_MEDIA, CREATE_EVENT, ADD_PARTY, UPDATE_PARTY } from './AddParty.data'
import { useMutation } from '@apollo/client'
import { useToast } from '@/components/Toast/useToast'
import { ImageTemplate } from '@/components/party-components/ImageTemplate'
import { useTemplateStore } from '../PartyInviteTemplates/templateStore'
import { LoadingIndicator } from '@/components/UI/ReusableComponents/LoadingIndicator'
import { Portal } from 'react-native-paper'
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import { useNavigation } from '@react-navigation/native'
import { HomeRootStackList } from '@/app/Home/HomeNavigation'
import { Colors, Spacing, Shadows, Typography, Borders, Icons } from '@/constants/DesignSystem'
import { useVenueStore } from '@/store/venueStore'
import { CREATE_VENUE_ADDRESS, CREATE_ADDRESS_BOOK } from '@/graphql/mutations/venueAddress'
import { ADDRESS_BOOK_QUERY } from '@/graphql/queries/addressBook';
import { AddPartyRootStackList } from './AddPartyRootStackList'
import { BackArrow } from '@/components/icons'

interface PartyInfo {
    name: string;
    dateTime: string | Date | undefined;
    area: string | undefined;
    cohosts: Array<{
        id: string;
        firstName: string;
        lastName?: string;
        phoneNumber: string;
        email?: string;
    }>;
    event: {
        mainHost: {
            userId: {
                id: string;
                email: string;
                firstName: string;
                lastName: string;
                phone: string;
            }
        }
    };
    invitation?: {
        _id: string;
        media?: Array<{
            url: string;
            title?: string;
        }>;
    };
    venueAddress?: {
        name: string;
    };
}

interface FormErrors {
    name?: string;
    dateTime?: string;
}

export interface AddressBook {
    id: string;
    label: string;
    venueAddress: {
        id: string;
        placeId: string;
        address: string;
        directions: string;
    }
}

type AddEditPartyNavigationProps = StackScreenProps<AddPartyRootStackList, 'AddParty' | 'EditParty'>;

interface AddPartyRouteParams {
    partyId?: string;
    eventGroupId?: string;
    isCohost?: boolean;
}

const AddParty: React.FC<AddEditPartyNavigationProps> = ({ route }) => {
    const navigation = useNavigation<StackNavigationProp<HomeRootStackList>>();

    const { partyId, eventGroupId, isCohost } = route.params as AddPartyRouteParams || {};

    const effectivePartyId = partyId;
    const userData = useUserStore((state) => state.userData);
    const [formErrors, setFormErrors] = useState<FormErrors>({});
    const [addressBook, setAddressBook] = useState<AddressBook>();
    const [isLoading, setIsLoading] = useState(effectivePartyId ? true : false);

    const venueDetails = useVenueStore((state) => state.venueDetails);
    const clearVenueDetails = useVenueStore((state) => state.clearVenueDetails);

    // Query to fetch party details when in edit mode
    const { data: party, loading: partyLoading } = useQuery(GET_PARTY_BY_ID, {
        variables: { getPartyByIdId: effectivePartyId },
        skip: !effectivePartyId,
        fetchPolicy: 'network-only'
    });

    const { data: addressBookData, loading: addressBookLoading } = useQuery(ADDRESS_BOOK_QUERY, {
        variables: {
            filter: {
                venueAddressId: party?.getPartyById?.result?.party?.venueAddress?.id
            }
        },
        skip: !party?.getPartyById?.result?.party?.venueAddress?.id
    });
    
    useEffect(() => {
        if (addressBookData?.getAddressBooks?.result?.addressBooks) {
            setAddressBook(addressBookData.getAddressBooks.result.addressBooks[0]);
        }
    }, [addressBookData]);

    useEffect(() => {
        if (effectivePartyId && !partyLoading) {
            setIsLoading(false);
        }
    }, [effectivePartyId, partyLoading]);

    // Initialize state
    const [partyInfo, setPartyInfo] = useState<PartyInfo>({
        name: '',
        dateTime: undefined,
        area: undefined,
        cohosts: [],
        event: {
            mainHost: {
                userId: {
                    id: userData?.id || '',
                    email: userData?.email || '',
                    firstName: userData?.firstName || '',
                    lastName: userData?.lastName || '',
                    phone: userData?.phone || ''
                }
            }
        },
        invitation: undefined
    });
    const [isDatePickerVisible, setDatePickerVisible] = useState(false);
    const [selectedDateTime, setSelectedDateTime] = useState<Date | null>(null);
    const [venue, setVenue] = useState<string | null>(null);
    const selectedTemplate = useTemplateStore((state) => state.selectedTemplate);
    const selectedMediaId = useTemplateStore((state) => state.selectedMediaId);
    const selectedMediaUrl = useTemplateStore((state) => state.selectedMediaUrl);
    const clearSelection = useTemplateStore((state) => state.clearSelection);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const hasUnsavedChangesRef = useRef(false);
    useEffect(() => {
        hasUnsavedChangesRef.current = hasUnsavedChanges;
    }, [hasUnsavedChanges]);
    const [isCreating, setIsCreating] = useState(false);
    const [selectedTemplateInfo, setSelectedTemplateInfo] = useState<{
        templateId?: string;
        mediaId?:string;
        mediaUrl?: string;
        name?: string;
    }>({});

    const [updateInvitation] = useMutation(UPDATE_INVITATION);
    const [createMedia] = useMutation(CREATE_MEDIA);
    const [createEvent] = useMutation(CREATE_EVENT);
    const [createParty] = useMutation(ADD_PARTY);
    const [updateParty] = useMutation(UPDATE_PARTY);
    const [createVenueAddress] = useMutation(CREATE_VENUE_ADDRESS);
    const [createAddressBook] = useMutation(CREATE_ADDRESS_BOOK);

    const toast = useToast();

    // Helper function to create media
    const createMediaEntry = async (mediaUrl: string, title?: string) => {
        const mediaResult = await createMedia({
            variables: {
                input: {
                    url: mediaUrl,
                    title: title || `Party Invitation ${new Date().toISOString()}`
                }
            }
        });

        if (mediaResult.data?.createMedia?.status === "FAILURE") {
            throw new Error(mediaResult.data?.createMedia?.message);
        }

        return mediaResult.data?.createMedia?.result?.media?.id;
    };

    // Helper function to update invitation with media
    const updateInvitationWithMedia = async (invitationId: string, mediaId: string) => {
        const updateInvitationResult = await updateInvitation({
            variables: {
                invitationId: invitationId,
                invitation: {
                    media: mediaId
                }
            }
        });

        if (updateInvitationResult.data?.updateInvitation?.status === "FAILURE") {
            throw new Error(updateInvitationResult.data?.updateInvitation?.message);
        }
    };

    // Helper function to handle media updates
    const handleMediaUpdate = async (invitationId: string | undefined, mediaUrl: string | undefined, title?: string) => {
        if (!invitationId || !mediaUrl) {
            throw new Error('Invalid invitation or media data');
        }

        try {
            const mediaId = await createMediaEntry(mediaUrl, title);
            if (mediaId) {
                await updateInvitationWithMedia(invitationId, mediaId);
            }
        } catch (error) {
            throw new Error('Failed to update invitation media');
        }
    };

    // Helper function to handle party update
    const handlePartyUpdate = async () => {
        if (!selectedDateTime) {
            throw new Error('Party time is required');
        }

        let venueDetailsResult;
        if(venueDetails?.isNew) {
            venueDetailsResult = await createVenueAddress({
                variables: {
                    input: {
                        name: venueDetails?.name,
                        placeId: venueDetails?.placeId,
                        directions: venueDetails?.directions
                    }
                }
            })
    
            await createAddressBook({
                variables: {
                    input: {
                        label: venueDetails?.title,
                        venueAddress: venueDetailsResult.data.createVenueAddress.result.venueAddress.id
                    }
                }
            })
        }

        const updateInput = {
            updatePartyId: effectivePartyId,
            input: {
                name: partyInfo.name,
                time: selectedDateTime.toISOString(),
                coHosts: partyInfo.cohosts?.map((cohost: any) => ({
                    email: cohost.email || undefined,
                    firstName: cohost.firstName,
                    lastName: cohost.lastName || undefined,
                    phone: cohost.phoneNumber
                })) || [],
                venueAddress: venueDetails?.isNew ? venueDetailsResult!.data.createVenueAddress.result.venueAddress.id : venueDetails?.venueAddressId
            }
        };

        const partyResult = await updateParty({
            variables: updateInput
        });

        if (partyResult.data?.updateParty?.status === "FAILURE") {
            throw new Error(partyResult.data?.updateParty?.errors?.[0]?.message || 'Failed to update party');
        }

        return partyResult;
    };

    // Helper function to handle party creation
    const handlePartyCreation = async () => {
        if (!selectedDateTime) {
            throw new Error('Party time is required');
        }

        const eventInput = {
            input: {
                name: partyInfo.name,
                description: partyInfo.name,
                startDate: selectedDateTime.toISOString().split('T')[0],
                ...(eventGroupId && { eventGroupId })
            }
        };

        const eventResult = await createEvent({ variables: eventInput });

        if (eventResult.data?.createEvent?.status === "FAILURE") {
            throw new Error(eventResult.data?.createEvent?.message || 'Failed to create event');
        }

        const eventId = eventResult.data?.createEvent?.result?.event?.id;
        if (!eventId) {
            throw new Error('Failed to create event');
        }

        const formattedCohosts = partyInfo?.cohosts?.map((cohost: any) => ({
            email: cohost.email || undefined,
            firstName: cohost.firstName,
            lastName: cohost.lastName || undefined,
            phone: cohost.phoneNumber
        })) || [];

        let venueDetailsResult;
        if(venueDetails?.isNew) {
            venueDetailsResult = await createVenueAddress({
                variables: {
                    input: {
                        name: venueDetails?.name,
                        placeId: venueDetails?.placeId,
                        directions: venueDetails?.directions
                    }
                }
            })
    
            await createAddressBook({
                variables: {
                    input: {
                        label: venueDetails?.title,
                        venueAddress: venueDetailsResult.data.createVenueAddress.result.venueAddress.id
                    }
                }
            })
        }

        const partyInput = {
            input: {
                name: partyInfo.name,
                time: selectedDateTime.toISOString(),
                coHosts: formattedCohosts,
                eventId: eventId,
                venueAddress: venueDetails?.isNew ? venueDetailsResult!.data.createVenueAddress.result.venueAddress.id : venueDetails?.venueAddressId
            }
        };

        const partyResult = await createParty({ variables: partyInput });

        if (partyResult.data?.createParty?.message === "FAILURE") {
            throw new Error(partyResult.data?.createParty?.errors?.[0]?.message || 'Failed to create party');
        }

        return partyResult;
    };

    // Populate form data when editing existing party
    useEffect(() => {
        if (effectivePartyId && party?.getPartyById?.result?.party) {
            const partyData = party.getPartyById.result.party;

            // Format cohosts data
            const existingCohosts = partyData?.coHosts?.map((cohost: any) => ({
                id: cohost?.userId?.id,
                firstName: cohost?.userId?.firstName,
                lastName: cohost?.userId?.lastName || '',
                phoneNumber: cohost?.userId?.phone,
                email: cohost?.userId?.email || ''
            })) || [];

            // Set initial template info if party has an invitation
            if (partyData.invitation?.media?.[0]) {
                setSelectedTemplateInfo({
                    mediaUrl: partyData.invitation.media[0].url,
                    name: partyData.invitation.media[0].title
                });
            }

            // Set party info
            setPartyInfo({
                ...partyData,
                name: partyData.name,
                cohosts: existingCohosts,
                invitation: partyData.invitation,
                event: partyData.event || {
                    mainHost: {
                        userId: {
                            id: userData?.id || '',
                            email: userData?.email || '',
                            firstName: userData?.firstName || '',
                            lastName: userData?.lastName || '',
                            phone: userData?.phone || ''
                        }
                    }
                }
            });

            // Set date and venue
            if (partyData.time) {
                setSelectedDateTime(new Date(partyData.time));
            }
            if (partyData.venueAddress?.name) {
                setVenue(partyData.venueAddress.name);
            }
        }
    }, [effectivePartyId, party, userData]);

    // Modify the template selection effect to be simpler and more robust
    useEffect(() => {
        const handleTemplateSelection = async () => {
            try {
                if (selectedTemplate) {
                    setSelectedTemplateInfo({
                        templateId: selectedTemplate.id,
                        mediaUrl: selectedTemplate.imageUrl,
                        name: selectedTemplate.name
                    });
                } else if (selectedMediaId && selectedMediaUrl) {
                    setSelectedTemplateInfo({
                        mediaId: selectedMediaId,
                        mediaUrl: selectedMediaUrl
                    });
                }
            } catch (error) {
                // Handle error silently
            } finally {
                clearSelection();
            }
        };

        handleTemplateSelection();
    }, [selectedTemplate, selectedMediaId, selectedMediaUrl, clearSelection]);

    useEffect(() => {
        if (party?.getPartyById?.result?.party) {
            // For edit mode - compare with original party data
            const originalParty = party.getPartyById.result.party;
            const isChanged =
                partyInfo.name !== originalParty.name ||
                partyInfo.cohosts.length !== (originalParty.coHosts?.length || 0) ||
                new Date(selectedDateTime || '').getTime() !== new Date(originalParty.time || '').getTime() ||
                venue !== (originalParty.venueAddress?.name || null) ||
                selectedTemplateInfo.mediaUrl !== (originalParty.invitation?.media?.[0]?.url || null);

            setHasUnsavedChanges(isChanged);
        } else {
            // For create mode - check if any data has been entered
            const hasEnteredData =
                !!partyInfo.name ||
                !!selectedDateTime ||
                !!venue ||
                partyInfo.cohosts.length > 0 ||
                !!selectedTemplateInfo.mediaUrl;

            setHasUnsavedChanges(hasEnteredData);
        }
    }, [partyInfo, selectedDateTime, venue, selectedTemplateInfo, party]);

    // Handle hardware back button and navigation gestures
    const unsubscribeRef = useRef<(() => void) | null>(null);
    useEffect(() => {
        const unsubscribe = navigation.addListener('beforeRemove', (e) => {
            if (!hasUnsavedChangesRef.current) {
                clearVenueDetails();
                return;
            }
            e.preventDefault();
            Alert.alert(
                'Discard Changes?',
                'You have unsaved changes. Are you sure you want to go back?',
                [
                    { text: 'Cancel', style: 'cancel' },
                    {
                        text: 'Discard',
                        style: 'destructive',
                        onPress: () => {
                            unsubscribe();
                            setHasUnsavedChanges(false);
                            navigation.dispatch(e.data.action);
                        },
                    },
                ]
            );
        });
        unsubscribeRef.current = unsubscribe;
        return unsubscribe;
    }, [navigation]);

    const handleDateTimeChange = (date: Date) => {
        setSelectedDateTime(date);
        setDatePickerVisible(false);
    }

    const validateForm = () => {
        const errors: FormErrors = {};
        let isValid = true;

        if (!partyInfo.name?.trim()) {
            errors.name = 'Party name is required';
            isValid = false;
        }

        if (!selectedDateTime) {
            errors.dateTime = 'Party time is required';
            isValid = false;
        }

        setFormErrors(errors);
        return isValid;
    };

    const handleSubmit = async () => {
        if (!validateForm()) {
            return;
        }

        setIsCreating(true);

        try {
            if (effectivePartyId) {
                // Update existing party
                await handlePartyUpdate();

                // Handle invitation update if new template is selected
                if (selectedTemplateInfo.mediaUrl &&
                    selectedTemplateInfo.mediaUrl !== partyInfo.invitation?.media?.[0]?.url) {
                    try {
                        await handleMediaUpdate(
                            partyInfo.invitation?._id,
                            selectedTemplateInfo.mediaUrl,
                            selectedTemplateInfo.name
                        );
                    } catch (error) {
                        toast.error('Party updated but failed to update invitation');
                    }
                }

                toast.success('Party updated successfully');
                setIsCreating(false);
                setHasUnsavedChanges(false);
                if (unsubscribeRef.current) {
                    unsubscribeRef.current();
                }
                setTimeout(() => {
                    navigation.goBack();
                }, 0);
                clearVenueDetails()
                return;
            }

            // Create new party
            const partyResult = await handlePartyCreation();
            const partyId = partyResult.data?.createParty?.result?.party?.id;
            const invitationId = partyResult.data?.createParty?.result?.party?.invitation?._id;

            if (!partyId) {
                throw new Error('Failed to create party');
            }

            if (selectedTemplateInfo.templateId || selectedTemplateInfo.mediaUrl) {
                try {
                    await handleMediaUpdate(
                        invitationId,
                        selectedTemplateInfo.mediaUrl,
                        selectedTemplateInfo.name
                    );
                } catch (error) {
                    toast.error('Party created but failed to add invitation');
                }
            }

            setIsCreating(false);
            setHasUnsavedChanges(false);
            if (unsubscribeRef.current) {
                unsubscribeRef.current();
            }
            navigation.replace("PartyDetails", {
                screen: 'NewPartyDetailsScreen',
                params: { partyId: partyId, fromCreate: true, showSuccessAnimation: true }
            });
            clearVenueDetails()
        } catch (error: any) {
            const errorMessage = error?.message || 'Failed to process party. Please try again.';
            toast.error(errorMessage);
            console.error("Error processing party:", error);
            setIsCreating(false);
        }
    };

    const cohostSheetRef = useRef<BottomSheetModal>(null);
    const snapPoints = useMemo(() => ["80%", "80%"], []);

    const handleCohostChange = (cohosts: Array<{
        id: string;
        firstName: string;
        lastName?: string;
        phoneNumber: string;
        email?: string;
    }>) => {
        setPartyInfo((prev: any) => ({
            ...prev,
            cohosts: cohosts
        }));
    };

    const handleBack = () => {
        clearVenueDetails()
        navigation.goBack();
    };
     
    return (
        <GestureHandlerRootView style={{ flex: 1 }}>
            <View style={{ flex: 1 }}>
                <BottomSheetModalProvider>
                    {isLoading ? (
                        <View style={styles.loadingOverlay}>
                            <LoadingIndicator />
                        </View>
                    ) : (
                        <>
                            <Appbar.Header
                                mode="center-aligned"
                                style={styles.appBar}
                            >
                                <Appbar.Action
                                    icon={() => <BackArrow size={Icons.size.xl} color={Colors.secondary} />}
                                    onPress={handleBack}
                                />
                                <Appbar.Content
                                    title={effectivePartyId ? '' : 'Add Party'}
                                    titleStyle={styles.appBarTitle}
                                />
                                <TouchableRipple
                                    onPress={handleSubmit}
                                    disabled={isCreating}
                                    style={styles.saveButton}
                                >
                                    <Appbar.Content
                                        title="Save"
                                        titleStyle={[
                                            styles.saveButtonTitle,
                                            { color: isCreating ? Colors.text.tertiary : Colors.primary }
                                        ]}
                                        style={styles.saveButtonContent}
                                    />
                                </TouchableRipple>
                            </Appbar.Header>
                            <KeyboardAwareScrollView
                                keyboardShouldPersistTaps='handled'
                                contentContainerStyle={{
                                    flexGrow: 1,
                                    backgroundColor: Colors.background.primary,
                                }}
                            >
                                <View style={styles.container}>
                                    <ScrollView
                                        style={styles.scrollView}
                                        contentContainerStyle={styles.contentContainer}
                                        showsVerticalScrollIndicator={false}
                                    >
                                        <ImageTemplate
                                            partyInfo={{
                                                ...partyInfo,
                                                partyType: { portraitImage: '' },
                                                invitation: selectedTemplateInfo.mediaUrl ? {
                                                    media: [{ url: selectedTemplateInfo.mediaUrl }]
                                                } : { media: [] }
                                            }}
                                            onImagePress={() => {
                                                if(effectivePartyId === undefined) {
                                                    navigation.navigate('AddPartyScreen', {
                                                        screen: 'PartyInviteTemplate'})
                                                } else {
                                                    navigation.navigate('PartyDetails', {
                                                        screen: 'PartyInviteTemplate',
                                                        params: { partyId: effectivePartyId ?? undefined }
                                                    });
                                                }
                                            }}
                                        />
                                        <AddPartyForm
                                            isCohost={isCohost ?? false}
                                            partyInfo={partyInfo}
                                            dateTime={selectedDateTime}
                                            area={addressBook?.label}
                                            onDateTimePress={() => {
                                                Keyboard.dismiss();
                                                setDatePickerVisible(true);

                                            }}
                                            isEditMode={!!effectivePartyId}
                                            cohostSheetRef={cohostSheetRef}
                                            onCohostChange={handleCohostChange}
                                            onNameChange={(name) => setPartyInfo((prev: any) => ({ ...prev, name }))}
                                            formErrors={formErrors}
                                            isLoading={addressBookLoading}
                                            onPressLocation={() => { 
                                                navigation.navigate("AddPartyScreen", {
                                                    screen: "AddLocation", 
                                                    params: {
                                                        partyId: effectivePartyId,
                                                        eventGroupId: eventGroupId
                                                    }
                                                })
                                            }}
                                        />
                                    </ScrollView>
                                </View>
                            </KeyboardAwareScrollView>
                            <DateTimePickerModal
                                visible={isDatePickerVisible}
                                onDismiss={() => setDatePickerVisible(false)}
                                value={selectedDateTime || new Date()}
                                onChange={handleDateTimeChange}
                                title="Select Date and Time"
                                minimumDate={new Date()}
                            />
                            <BottomSheetModal
                                ref={cohostSheetRef}
                                index={1}
                                snapPoints={snapPoints}
                                enablePanDownToClose
                                handleIndicatorStyle={{ backgroundColor: Colors.mediumGray }}
                                backgroundStyle={{ backgroundColor: Colors.background.primary }}
                                backdropComponent={(props) => (
                                    <BottomSheetBackdrop
                                        {...props}
                                        appearsOnIndex={1}
                                        disappearsOnIndex={0}
                                        opacity={0.5}
                                    />
                                )}
                            >
                                <BottomSheetScrollView
                                    keyboardShouldPersistTaps='handled'
                                    showsVerticalScrollIndicator={false}
                                >
                                    <BottomSheetView
                                        style={{
                                            flex: 1,
                                            marginBottom: Spacing.xxl,
                                            paddingBottom: Spacing.xxl,
                                            marginHorizontal: Spacing.md,
                                        }}
                                    >
                                        <AddCohostsForm
                                            existingCohosts={partyInfo?.cohosts || []}
                                            onCohostAdd={(newCohost) => {
                                                setPartyInfo((prev: any) => ({
                                                    ...prev,
                                                    cohosts: [...prev.cohosts, newCohost]
                                                }));
                                                cohostSheetRef.current?.dismiss();
                                            }}
                                        />
                                    </BottomSheetView>
                                </BottomSheetScrollView>
                            </BottomSheetModal>
                            {isCreating && (
                                <Portal>
                                    <View style={styles.loadingOverlay}>
                                        <LoadingIndicator />
                                    </View>
                                </Portal>
                            )}
                        </>
                    )}
                </BottomSheetModalProvider>
            </View>
        </GestureHandlerRootView>
    )
}

export default AddParty

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: "flex-start",
        alignItems: "center",
        backgroundColor: Colors.background.primary,
    },
    partyImage: {
        width: 150,
        height: 200,
        marginBottom: Spacing.md,
        borderRadius: Borders.radius.md,
        alignSelf: 'center',
        ...Shadows.sm,
    },
    scrollView: {
        width: '100%',
    },
    contentContainer: {
        padding: Spacing.md,
        height: '100%',
    },
    loadingOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: Colors.overlay,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 9999,
    },
    appBar: {
        ...Shadows.sm,
        backgroundColor: Colors.background.primary,
        zIndex: 1,
        borderBottomWidth: Borders.width.thin,
        borderBottomColor: Colors.border.light,
    },
    appBarTitle: {
        color: Colors.secondary,
        fontSize: Typography.fontSize.lg,
        fontWeight: '500',
    },
    saveButton: { 
        paddingHorizontal: Spacing.md,
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%'
    },
    saveButtonContent: {
        alignItems: 'center',
        justifyContent: 'center'
    },
    saveButtonTitle: {
        fontSize: Typography.fontSize.md,
        fontWeight: '500',
        textAlign: 'center',
        marginRight: Spacing.md
    }
})