import React from 'react';
import { StyleSheet, TouchableOpacity, ViewStyle, StyleProp } from 'react-native';
import { ThemedText } from '@/components/UI/ThemedText';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '@/constants/Colors';

interface CreateTaskButtonProps {
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
}

export const CreateTaskButton: React.FC<CreateTaskButtonProps> = ({ 
  onPress,
  style
}) => {
  return (
    <TouchableOpacity 
      style={[styles.createTaskButton, style]}
      onPress={onPress}
    >
      <Ionicons name="add" size={24} color={Colors.custom.createEventButtonText} />
      <ThemedText style={styles.createTaskText}>Create Task</ThemedText>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  createTaskButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    backgroundColor: Colors.custom.createEventButtonBackground,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    shadowColor: Colors.custom.searchBarActive,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  createTaskText: {
    color: Colors.custom.createEventButtonText,
    marginLeft: 8,
    fontWeight: '600',
  },
}); 