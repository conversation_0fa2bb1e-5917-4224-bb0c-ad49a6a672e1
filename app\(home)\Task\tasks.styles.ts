import { StyleSheet } from 'react-native';
import { Colors, Typography, Spacing, Borders, Shadows } from '@/constants/DesignSystem';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  title: {
    color: Colors.text.secondary,
    fontFamily: Typography.fontFamily.primary,
    fontWeight: Typography.fontWeight.semibold,
  },
  searchBar: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
    backgroundColor: Colors.background.secondary,
    borderRadius: Borders.radius.md,
    elevation: 0,
  },
  searchInput: {
    flex: 1,
    fontSize: Typography.fontSize.md,
    color: Colors.text.secondary,
    marginLeft: Spacing.sm,
    padding: 0,
    height: 40,
  },
  taskList: {
    paddingHorizontal: Spacing.xs,
    backgroundColor: Colors.background.primary,
    borderRadius: Borders.radius.md,
    marginBottom: 0,
  },
  taskItem: {
    flexDirection: 'row',
    backgroundColor: Colors.background.primary,
    borderRadius: Borders.radius.md,
    elevation: 0,
    overflow: 'hidden',
    marginBottom: 1,
    borderBottomWidth: Borders.width.thin,
    borderBottomColor: Colors.border.medium,
  },
  dateContainer: {
    position: 'absolute',
    right: Spacing.lg,
    top: Spacing.lg,
    minWidth: 85,
    flexShrink: 0,
    alignItems: 'flex-end',
    height: 25
  },
  dateTag: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.inverse,
    backgroundColor: Colors.error,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: Borders.radius.sm,
    overflow: 'hidden',
    fontFamily: Typography.fontFamily.primary,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.lineHeight.tight,
    textAlign: 'center',
  },
  closedDateTag: {
    color: Colors.text.tertiary,
  },
  taskMainContent: {
    flex: 1,
    flexDirection: 'row',
    padding: Spacing.lg,
    paddingRight: 100,
  },
  checkboxContainer: {
    marginRight: Spacing.md,
    justifyContent: 'center',
    width: 24,
    height: 24,
  },
  checkedCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: Borders.width.thin,
    alignItems: 'center',
    justifyContent: 'center',
  },
  taskContent: {
    flex: 1,
    paddingRight: 0,
    marginRight: 0,
  },
  taskTitle: {
    fontSize: Typography.fontSize.lg,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
    lineHeight: Typography.lineHeight.normal,
    flexShrink: 1,
    fontFamily: Typography.fontFamily.primary,
    fontWeight: Typography.fontWeight.semibold,
    textAlign: 'left',
    textDecorationLine: 'none',
    width: '97%'
  },
  closedTaskTitle: {
    color: Colors.text.tertiary,
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.lineHeight.normal,
    textAlign: 'left',
    textDecorationLine: 'none',
  },
  taskMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  assigneeText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    fontWeight: Typography.fontWeight.semibold,
  },
  closedAssigneeText: {
    color: Colors.text.tertiary,
    fontWeight: Typography.fontWeight.semibold,
  },
  calendarIcon: {
    marginRight: 0,
    left: 0,
  },
  headerContainer: {
    backgroundColor: Colors.background.primary,
    paddingTop: Spacing.md,
    paddingHorizontal: Spacing.lg,
    zIndex: 2,
  },
  filterContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
    alignItems: 'center',
    backgroundColor: Colors.background.primary,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.primary,
    padding: Spacing.sm,
    paddingHorizontal: 60,
    borderRadius: Borders.radius.md,
    minWidth: 150,
    justifyContent: 'center',
  },
  filterButtonText: {
    fontSize: Typography.fontSize.lg,
    color: Colors.text.secondary,
    marginRight: Spacing.sm,
    textAlign: 'center',
    fontWeight: Typography.fontWeight.bold,
  },
  searchContainer: {
    paddingHorizontal: Spacing.sm,
    paddingLeft: Spacing.md,
    marginBottom: Spacing.sm,
    backgroundColor: Colors.background.primary,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 1,
  },
  searchWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
    borderRadius: Borders.radius.pill,
    height: 42,
    paddingLeft: Spacing.md,
  },
  filterIconButton: {
    backgroundColor: Colors.background.primary,
    borderRadius: Borders.radius.circle,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  menuContent: {
    marginTop: Spacing.xs,
    minWidth: 150,
  },
  menuItemText: {
    fontSize: Typography.fontSize.md,
    color: Colors.text.secondary,
  },
  checkMark: {
    width: 12,
    height: 12,
    flexShrink: 0,
  },
  checkboxPressable: {
    opacity: 1,
  },
  disabledCheckbox: {
    opacity: 0.7,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  taskCountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
  },
  taskCount: {
    fontSize: Typography.fontSize.md,
    color: Colors.primary,
  },
  separator: {
    borderTopWidth: Borders.width.thin,
    borderColor: Colors.border.medium,
    marginHorizontal: Spacing.sm,
    marginBottom: 0,
  },
  deleteDialog: {
    backgroundColor: Colors.background.primary,
    height: 162,
    borderRadius: Borders.radius.lg,
  },
  deleteDialogContent: {
    paddingHorizontal: Spacing.xl,
  },
  deleteDialogActions: {
    paddingHorizontal: Spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  deleteDialogText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.medium,
    lineHeight: Typography.lineHeight.tight,
    letterSpacing: 0.5,
    color: Colors.text.tertiary,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: Spacing.lg,
    backgroundColor: Colors.background.primary,
    borderTopWidth: Borders.width.thin,
    borderTopColor: Colors.border.medium,
  },
  bodyContainer: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  noTasksText: {
    fontSize: 24,
    color: Colors.text.secondary,
    fontFamily: Typography.fontFamily.primary,
    fontWeight: Typography.fontWeight.semibold,
    textAlign: 'center',
  },
  noTasksSubText: {
    fontSize: 18,
    color: Colors.text.secondary,
    fontFamily: Typography.fontFamily.primary,
    fontWeight: Typography.fontWeight.semibold,
    textAlign: 'center',
  },
});