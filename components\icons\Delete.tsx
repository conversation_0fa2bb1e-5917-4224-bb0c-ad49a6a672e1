import * as React from "react";
import Svg, { <PERSON>, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Delete = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Delete-1_svg__a)">
      <Path
        fill="url(#Delete-1_svg__b)"
        fillRule="evenodd"
        d="M5.028 2.081a1.375 1.375 0 0 1 2.29.58H4.682c.065-.217.183-.417.346-.58m-1.551.58a2.554 2.554 0 0 1 5.046 0h2.191a.59.59 0 0 1 0 1.178H9.93v6.482A1.18 1.18 0 0 1 8.75 11.5h-5.5a1.18 1.18 0 0 1-1.179-1.179V3.84h-.785a.59.59 0 1 1 0-1.178zm.952 2.456c.27 0 .49.22.49.491v3.144a.491.491 0 0 1-.982 0V5.608c0-.27.22-.49.492-.49m3.633.491a.491.491 0 0 0-.982 0v3.144a.491.491 0 0 0 .982 0z"
        clipRule="evenodd"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Delete-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.828}
        y2={14.029}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Delete-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Delete_svg__a)"
    >
      <Path d="M1 3.083h10M2.25 3.083h7.5v7.5a.834.834 0 0 1-.833.834H3.083a.834.834 0 0 1-.833-.834zM3.917 3.083v-.416a2.083 2.083 0 1 1 4.166 0v.416M4.75 5.585v3.334M7.25 5.585v3.334" />
    </G>
    <Defs>
      <ClipPath id="Delete_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Delete;
