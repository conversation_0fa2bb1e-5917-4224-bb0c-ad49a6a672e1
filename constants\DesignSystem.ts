/**
* Design System built by <PERSON><PERSON> for Fast Party Mobile
* This file contains the core design tokens and styles for the application
*/

import { DimensionValue } from 'react-native';

const primaryColor = '#F4862B';
const secondaryColor = '#34434D';
const tertiaryColor = '#FDEDDF';

interface ColorPalette {
  primary: string;
  secondary: string;
  tertiary: string;
  accent: string;
  selection: {
    selected: string;
    unselected: string;
  }
  gradient: {
    primary: string;
    blue: string;
    purple: string;
    darkPurple: string;
    magenta: string;
    red: string;
    orange: string;
    yellow: string;
  };
  black: string;
  mediumGray: string;
  lightGray: string;
  white: string;
  success: string;
  error: string;
  warning: string;
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
    transparent: string;
    orange: string;
  };
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
    inverse: string;
  };
  border: {
    light: string;
    medium: string;
    dark: string;
    orange: string;
  };
  buttonBorder: {
    light: string;
    medium: string;
    dark: string;
    orange: string;
  }
  button: {
    primary: string;
    secondary: string;
    tertiary: string;
    disabled: string;
  };
  overlay: string;
}

export const Colors: ColorPalette = {
  // Primary palette
  primary: primaryColor, // FastParty blue
  secondary: secondaryColor, // FastParty purple
  tertiary: tertiaryColor, // FastParty darker purple
  accent:'linear-gradient(180deg, #6495ED -12.07%, #D55B2E 122.99%)',
  selection: {
    selected:  tertiaryColor,
    unselected: '#FFFFFF', //whit
  },
  // FastParty gradient colors
  gradient: {
    primary: 'linear-gradient(180deg, #F4862B -12.07%, #D55B2E 122.99%)',
    blue: '#405DE6',
    purple: '#5851DB',
    darkPurple: '#833AB4',
    magenta: '#C13584',
    red: '#E1306C',
    orange: '#D55B2E',
    yellow: '#F56040',
  },
  // Neutrals
  black: '#000000',
  mediumGray: '#90A0AB', // FastParty medium gray
  lightGray: '#f4f6f7', // FastParty light gray

  white: '#FFFFFF',
  // Semantic colors
  success: '#1FC16B', // Green
  error: '#FB3748', // FastParty red/error
  warning: '#FFDB43', // Yellow
  // Background colors
  background: {
    primary: '#FFFFFF',
    secondary: '#FDEDDF', // FastParty light background
    tertiary: '#F4F6F7', // FastParty secondary background
    transparent: '#FFFFFF',
    orange: '#F4862B',
  },
  // Text colors
  text: {
    primary: primaryColor, // FastParty primary text
    secondary: '#34434D', // FastParty secondary text
    tertiary: '#000000', // FastParty tertiary text
    inverse: '#FFFFFF',
  },
  // Border colors
  border: {
    light: '#EEF1F2', // FastParty light border
    medium: '#DBDBDB', // FastParty medium border
    dark: '#8E8E8E',
    orange: '#F4862B', // FastParty dark border
  },
  buttonBorder: {
    light: '#EEF1F2',
    medium: '#90A0AB',
    dark: '#8E8E8E',
    orange: '#F4862B',
  },
  // Component specific
  button: {
    primary: primaryColor, // FastParty blue button
    secondary: secondaryColor,
    tertiary: tertiaryColor,
    disabled: '#C7C7C7',
  },
  // Overlay colors
  overlay: 'rgba(0, 0, 0, 0.5)',
};
// TYPOGRAPHY
// Define the allowed font weight types that match React Native's TextStyle
type FontWeightType = 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900';

// Define the Typography interface with proper types
interface TypographyInterface {
  fontFamily: {
    primary: string;
    secondary: string;
  };
  fontSize: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
    xxxl: number;
  };
  fontWeight: {
    light: FontWeightType;
    regular: FontWeightType;
    medium: FontWeightType;
    semibold: FontWeightType;
    bold: FontWeightType;
  };
  lineHeight: {
    tight: number;
    normal: number;
    loose: number;
  };
}

export const Typography: TypographyInterface = {
  fontFamily: {
    primary: 'Plus Jakarta Sans', // System font - FastParty uses SF Pro on iOS, Roboto on Android
    secondary: 'Plus Jakarta Sans', // System font
  },
  fontSize: {
    xs: 8, // FastParty uses slightly smaller text for captions
    sm: 12,
    md: 14, // FastParty's primary text size
    lg: 18,
    xl: 24,
    xxl: 32,
    xxxl: 48,
  },
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500', // FastParty uses medium weight for most UI elements
    semibold: '600', // FastParty uses semibold for emphasis
    bold: '700',
  },
  lineHeight: {
    tight: 1.2,
    normal: 1.4, // FastParty uses slightly tighter line height
    loose: 1.6,
  },
};
// SPACING
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 12, // FastParty uses slightly tighter spacing
  lg: 16,
  xl: 24,
  xxl: 32,
  xxxl: 48,
  ml: 68,
};
// BORDERS
export const Borders = {
  radius: {
    sm: 6, // FastParty uses slightly smaller border radius
    md: 8,
    lg: 10,
    xl: 14,
    pill: 24, // FastParty uses pill-shaped buttons
    circle: 9999,
  },
  width: {
    thin: 1,
    medium: 1.5, // FastParty uses thinner borders
    thick: 3,
  },
};
// SHADOWS
interface ShadowStyle {
  backgroundColor: string;
  shadowColor: string;
  shadowOffset: {
    width: number;
    height: number;
  };
  shadowOpacity: number;
  shadowRadius: number;
  elevation: number;
}

interface ShadowPalette {
  sm: ShadowStyle;
  md: ShadowStyle;
  lg: ShadowStyle;
}

export const Shadows: ShadowPalette = {
  sm: {
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {
      width: 2,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 2,
  },
  md: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000000',
    shadowOffset: {
      width: 4,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 4,
  },
  lg: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000000',
    shadowOffset: {
      width: 6,
      height: 6,
    },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 6,
  },
};
// COMPONENT STYLES
interface ButtonStyle {
  backgroundColor?: string;
  color: string;
  borderRadius?: number;
  paddingVertical: number;
  paddingHorizontal: number;
  fontWeight: string;
  borderColor?: string;
  style: string;
  height?: number;
  width?: DimensionValue;
  alignItems?: 'center' | 'flex-start' | 'flex-end' | 'stretch' | 'baseline';
  justifyContent?: 'center' | 'flex-start' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  flexDirection?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  gap?: number;
}

interface ComponentStylePalette {
  button: {
    primary: ButtonStyle;
    secondary: ButtonStyle;
    text: ButtonStyle;
    bottomButton: ButtonStyle;
  };
  input: {
    backgroundColor: string;
    borderColor: string;
    borderWidth: number;
    borderRadius: number;
    paddingVertical: number;
    paddingHorizontal: number;
    fontSize: number;
    color: string;
  };
  card: {
    backgroundColor: string;
    borderRadius: number;
    borderColor: string;
    borderWidth: number;
    padding: number;
  };
  header: {
    backgroundColor: string;
    borderBottomColor: string;
    borderBottomWidth: number;
    paddingVertical: number;
    paddingHorizontal: number;
  };
  modal: {
    backgroundColor: string;
    borderRadius: number;
    padding: number;
  };
  avatar: {
    borderWidth: number;
    borderColor: string;
    borderRadius: number;
  };
}

export const ComponentStyles: ComponentStylePalette = {
  button: {
    primary: {
      height: 40,
      backgroundColor: Colors.primary,
      color: Colors.mediumGray,
      borderRadius: Borders.radius.sm,
      paddingVertical: Spacing.sm,
      paddingHorizontal: Spacing.md,
      fontWeight: Typography.fontWeight.medium,
      alignItems: 'center',
      justifyContent: 'center',
      style: `background-color: ${Colors.primary}; color: ${Colors.black}; border-radius: ${Borders.radius.circle}px; padding: ${Spacing.sm}px ${Spacing.md}px; font-weight: ${Typography.fontWeight.medium}; text-align: center;`
    },
    secondary: {
      backgroundColor: Colors.white,
      color: Colors.text.secondary,
      borderColor: Colors.mediumGray,
      borderRadius: Borders.radius.pill,
      paddingVertical: Spacing.sm,
      paddingHorizontal: Spacing.md,
      fontWeight: Typography.fontWeight.medium,
      alignItems: 'center',
      justifyContent: 'center',
      style: `background-color: ${Colors.white}; color: ${Colors.text.secondary}; border: 1px solid ${Colors.mediumGray}; border-radius: ${Borders.radius.pill}px; padding: ${Spacing.sm}px ${Spacing.md}px; font-weight: ${Typography.fontWeight.medium}; text-align: center;`
    },
    text: {
      color: Colors.secondary,
      paddingVertical: Spacing.sm,
      paddingHorizontal: Spacing.md,
      fontWeight: Typography.fontWeight.medium,
      alignItems: 'center',
      justifyContent: 'center',
      style: `color: ${Colors.secondary}; padding: ${Spacing.sm}px ${Spacing.md}px; font-weight: ${Typography.fontWeight.medium}; text-align: center;`
    },
    bottomButton: {
      width: '100%',
      backgroundColor: Colors.primary,
      color: Colors.text.tertiary,
      borderRadius: Borders.radius.md,
      paddingVertical: Spacing.md,
      paddingHorizontal: Spacing.md,
      fontWeight: Typography.fontWeight.medium,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      gap: Spacing.md,
      style: `background-color: ${Colors.primary}; color: ${Colors.text.tertiary}; border-radius: ${Borders.radius.md}px; padding: ${Spacing.md}px ${Spacing.md}px; font-weight: ${Typography.fontWeight.medium}; text-align: center;`
    },
  },
  input: {
    backgroundColor: Colors.background.tertiary,
    borderColor: Colors.border.light,
    borderWidth: Borders.width.thin,
    borderRadius: Borders.radius.sm,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    fontSize: Typography.fontSize.md,
    color: Colors.text.primary,
  },
  card: {
    backgroundColor: Colors.white,
    borderRadius: Borders.radius.sm,
    borderColor: Colors.border.light,
    borderWidth: Borders.width.thin,
    padding: Spacing.md,
  },
  header: {
    backgroundColor: Colors.white,
    borderBottomColor: Colors.border.light,
    borderBottomWidth: Borders.width.thin,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
  },
  modal: {
    backgroundColor: Colors.white,
    borderRadius: Borders.radius.lg,
    padding: Spacing.md,
  },
  avatar: {
    borderWidth: Borders.width.medium,
    borderColor: Colors.gradient.purple,
    borderRadius: Borders.radius.circle,
  },
};

export const Icons = {
  size: {
    sm: 12,
    md: 16,
    lg: 20,
    xl: 24,
    xxl: 32,
  },
};

export function getButtonStyle(type: 'primary' | 'secondary' | 'text' | 'bottomButton'): ButtonStyle {
  const style = ComponentStyles.button[type];
  return {
    backgroundColor: style.backgroundColor,
    color: style.color,
    borderRadius: style.borderRadius,
    paddingVertical: style.paddingVertical,
    paddingHorizontal: style.paddingHorizontal,
    fontWeight: style.fontWeight,
    width: style.width,
    height: style.height,
    alignItems: style.alignItems,
    justifyContent: style.justifyContent,
    flexDirection: style.flexDirection,
    gap: style.gap,
    ...(type === 'secondary' && { borderColor: style.borderColor }),
    style: style.style,
  };
}

