import { DocumentPickerAsset } from 'expo-document-picker';

export interface TaskAttachment {
  __typename?: "Document";
  description?: string | null;
  documentType?: string;
  documentUrl: string;
  name: string;
  id?: string;
  size?: number;
  mimeType?: string;
  uri?: string;
  owner?: {
    firstName: string;
    lastName: string;
  };
}

export interface FileWithDocId extends DocumentPickerAsset {
  documentId?: string;
  mimeType: string;
  size: number;
  name: string;
  uri: string;
}

export type AttachmentFile = TaskAttachment | (DocumentPickerAsset & { id?: string; documentId?: string });

export interface FileTypeIcon {
  icon: string;
  color: string;
}

export interface AttachmentsSectionProps {
  taskId: string;
  uploadingFiles: DocumentPickerAsset[];
  onAttachmentPress: () => void;
  onFileRemove: (file: FileWithDocId) => void;
  getFileTypeIcon: (file: AttachmentFile) => FileTypeIcon;
  isLoading?: boolean;
  documentIds?: Map<number, string>;
  existingAttachments?: TaskAttachment[];
  uploadedAttachments?: DocumentPickerAsset[];
  allAttachments?: string[];
  onAttachmentsChange?: (changes: { 
    existing: TaskAttachment[]; 
    uploading: DocumentPickerAsset[] 
  }) => void;
}

export interface FilePreviewItemProps {
  file: AttachmentFile;
  index: number;
  onFileRemove: (file: DocumentPickerAsset & { id?: string; documentId?: string }) => void;
  getFileTypeIcon: (file: AttachmentFile) => FileTypeIcon;
  documentId?: string;
  taskId?: string;
} 