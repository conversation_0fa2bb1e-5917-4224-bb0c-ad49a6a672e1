import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Edit = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Edit-1_svg__a)">
      <Path
        fill="url(#Edit-1_svg__b)"
        d="M8.919.5a1.18 1.18 0 0 0-.84.352l-6.473 6.44a.4.4 0 0 0-.101.173l-.99 3.536a.393.393 0 0 0 .484.484l3.535-.99a.4.4 0 0 0 .173-.101l6.44-6.472.002-.001a1.18 1.18 0 0 0 0-1.672h-.001L9.76.852a1.18 1.18 0 0 0-.841-.354"
      />
      <Path
        stroke="url(#Edit-1_svg__c)"
        strokeLinecap="round"
        strokeWidth={1.286}
        d="M6 11.143h4.286"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Edit-1_svg__b"
        x1={5.998}
        x2={5.998}
        y1={-0.828}
        y2={14.028}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Edit-1_svg__c"
        x1={8.143}
        x2={8.143}
        y1={11.022}
        y2={12.373}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Edit-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G stroke={color} strokeLinecap="round" clipPath="url(#Edit_svg__a)">
      <Path
        strokeLinejoin="round"
        d="m4.46 10.032-3.462.97.97-3.463 6.34-6.31a.77.77 0 0 1 1.1 0l1.363 1.37a.77.77 0 0 1 0 1.093z"
      />
      <Path d="M5.571 11.571h5.572" />
    </G>
    <Defs>
      <ClipPath id="Edit_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Edit;
