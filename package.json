{"name": "mobile-client", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "andoid-build": "eas build --platform android", "ios-build": "eas build --platform ios"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@apollo/client": "^3.13.0", "@clerk/clerk-expo": "^2.3.5", "@clerk/types": "^4.49.0", "@expo/react-native-action-sheet": "^4.1.1", "@expo/vector-icons": "^14.0.2", "@gorhom/bottom-sheet": "^5.0.6", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-seoul/masonry-list": "^1.4.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@react-navigation/stack": "^7.1.1", "axios": "^1.9.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "event-source-polyfill": "^1.0.31", "expo": "~52.0.46", "expo-auth-session": "^6.0.3", "expo-av": "^15.1.6", "expo-blur": "~14.0.3", "expo-clipboard": "~7.0.0", "expo-constants": "~17.0.8", "expo-contacts": "~14.0.5", "expo-dev-client": "~5.0.20", "expo-device": "~7.0.3", "expo-document-picker": "~13.0.3", "expo-file-system": "~18.0.11", "expo-font": "~13.0.3", "expo-haptics": "~14.0.0", "expo-image": "~2.0.6", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.6", "expo-intent-launcher": "~12.0.1", "expo-linear-gradient": "~14.0.1", "expo-linking": "~7.0.3", "expo-location": "~18.0.10", "expo-media-library": "~17.0.6", "expo-notifications": "~0.29.14", "expo-router": "~4.0.21", "expo-secure-store": "~14.0.0", "expo-sharing": "~13.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.0", "expo-updates": "~0.27.4", "expo-video": "~2.0.6", "expo-web-browser": "~14.0.1", "graphql": "^15.10.1", "graphql-tag": "^2.12.6", "lottie-react-native": "7.1.0", "lucide-react-native": "^0.451.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-animated-dots-carousel": "^2.0.0", "react-native-country-picker-modal": "^2.0.0", "react-native-event-source": "^1.1.0", "react-native-file-viewer": "^2.1.5", "react-native-gesture-handler": "~2.20.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-maps": "1.18.0", "react-native-markdown-display": "^7.0.2", "react-native-modal": "^14.0.0-rc.1", "react-native-pager-view": "6.5.1", "react-native-paper": "^5.12.5", "react-native-popover-view": "^6.1.0", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-size-matters": "^0.4.2", "react-native-svg": "^15.8.0", "react-native-tab-view": "^3.5.2", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^10.2.0", "react-native-view-shot": "^4.0.3", "react-native-web": "~0.19.13", "react-native-webview": "^13.12.5", "rn-emoji-picker": "^1.1.6", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@expo/webpack-config": "18.0.1", "@svgr/cli": "^8.1.0", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.17", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.0.7", "babel-plugin-module-resolver": "^5.0.2", "eas-cli": "^16.1.0", "eslint": "^8.57.0", "eslint-config-expo": "~8.0.1", "expo-module-scripts": "^4.0.4", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-test-renderer": "18.2.0", "typescript": "~5.3.3"}, "private": true, "resolutions": {"expo-modules-autolinking": "~2.0.0", "@expo/config-plugins": "~9.0.0", "@expo/prebuild-config": "~8.0.0", "@expo/metro-config": "~0.19.0"}}