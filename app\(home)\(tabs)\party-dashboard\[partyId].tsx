import React, { useState } from 'react';
import { StyleSheet, SafeAreaView, View, Platform, Pressable, Text } from 'react-native';
import { ThemedView } from '@/components/UI/ThemedView';
import { Colors } from '@/constants/Colors';
import { VendorTypes } from '@/constants/vendorTypes';
import { useLocalSearchParams } from 'expo-router';
import { router } from 'expo-router';
import { PartyHeader } from '@/components/party-dashboard/partyHeaderSection/PartyHeader';
import Animated, { useSharedValue, useAnimatedScrollHandler } from 'react-native-reanimated';
import { useStatusBarStyle } from '@/hooks/useStatusBarStyle';
import { DarkStatusBar } from '@/components/shared/GlobalStatusBar';
import NewPartyDetailsScreen from '@/components/create-event/NewPartyDetailsScreen';
import { BackArrow } from '@/components/icons';
import { Icons } from '@/constants/DesignSystem';

import { useRoute } from '@react-navigation/native';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '@/navigation/types';

type PartyDashboardRouteProp = RouteProp<RootStackParamList, 'PartyDashboard'>;

export default function PartyDashboardScreen() {
  const route = useRoute<PartyDashboardRouteProp>();
  // const params = useLocalSearchParams();
  const [selectedVendor, setSelectedVendor]  = useState<string>(VendorTypes.OVERVIEW);
  const [activeIndex, setActiveIndex] = useState(0);
  const scrollY = useSharedValue(0);
  const partyId = route.params.partyId;

  const handleBackPress = () => {
    setActiveIndex(() => 0);
    setSelectedVendor(() => VendorTypes.OVERVIEW);
    router.back();
  };

  return (
    <ThemedView style={styles.container}>
      <DarkStatusBar />
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <Pressable onPress={handleBackPress} style={styles.backButton}>
            <BackArrow size={Icons.size.md} />
          </Pressable>
        </View>
        <NewPartyDetailsScreen route={{ params: { partyId } }} />
      </SafeAreaView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.custom.black,
  },
  safeArea: {
    flex: 1,
    backgroundColor: Colors.custom.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: Colors.custom.white, 
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButtonText: {
    color: 'white',
    marginLeft: 8,
    fontSize: 16,
  },
});