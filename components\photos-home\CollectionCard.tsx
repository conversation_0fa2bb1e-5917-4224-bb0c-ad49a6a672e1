import { StyleSheet, View, Pressable } from 'react-native'
import { Text } from 'react-native-paper'
import { Image } from 'expo-image'
import Animated, { FadeIn } from 'react-native-reanimated'
import { Colors, Typography, Spacing, Borders, Shadows } from '@/constants/DesignSystem'

interface CollectionCardProps {
    name: string
    media: string[]
    onPress: () => void
    ownerId: { id: string }[]
    loggedInUserId?: string
}

export function CollectionCard({  name, media, onPress, ownerId, loggedInUserId }: CollectionCardProps) {
    const isHost = loggedInUserId && ownerId.some(owner => owner.id === loggedInUserId)
    const overlayColor = isHost ? Colors.primary : Colors.secondary

    return (
        <Pressable onPress={onPress} style={styles.container}>
            <Animated.View entering={FadeIn} style={styles.cardContainer}>
                {/* Background Image */}
                <Image
                    source={media[1]}
                    style={[styles.image, styles.backgroundImage]}
                    contentFit="cover"
                />
                {/* Foreground Image with Title */}
                <View style={styles.foregroundContainer}>
                    <Image
                        source={media[0]}
                        style={styles.image}
                        contentFit="cover"
                    />
                    {/* Color Overlay */}
                    <View style={[
                        styles.overlay,
                        { backgroundColor: overlayColor }
                    ]} />
                    <View style={styles.titleContainer}>
                        <Text variant="titleMedium" style={styles.title}>
                            {name}
                        </Text>
                    </View>
                </View>
            </Animated.View>
        </Pressable>
    )
}

const styles = StyleSheet.create({
    container: {
        width: 120,
        height: 200,
        marginRight: Spacing.md,
        marginVertical: Spacing.md,
        paddingBottom: 36,
        paddingHorizontal: Spacing.md,
    },
    cardContainer: {
        flex: 1,
        position: 'relative',
        ...Shadows.md,
    },
    image: {
        width: '100%',
        height: '100%',
        borderRadius: Borders.radius.lg,
        backgroundColor: Colors.background.secondary,
    },
    backgroundImage: {
        position: 'absolute',
        opacity: 0.9,
        transform: [{ scale: 0.95 }],
        width: '100%',
        height: '100%',
        borderRadius: Borders.radius.lg,
        overflow: 'hidden',
    },
    foregroundContainer: {
        flex: 1,
        position: 'relative',
        transform: [
            { rotate: '5deg' },
            { translateX: 12 },
        ],
        overflow: 'hidden',
        borderRadius: Borders.radius.lg,
        ...Shadows.md,
    },
    titleContainer: {
        position: 'absolute',
        left: 10,
        right: 0,
        top: 20,
        paddingHorizontal: Spacing.md,
        paddingVertical: Spacing.md,
        overflow: 'hidden',
        zIndex: 2,
        transform: [
            { rotate: '-5deg' }, // Counter-rotate to keep text straight
            { translateX: -12 }, // Counter-translate to maintain position
        ],
    },
    title: {
        color: Colors.white,
        textAlign: 'left',
        textShadowColor: 'rgba(0, 0, 0, 0.75)',
        textShadowOffset: { width: 0, height: 1 },
        textShadowRadius: 4,
        fontWeight: Typography.fontWeight.bold,
        fontFamily: Typography.fontFamily.primary,
    },
    subtitle: {
        color: Colors.white,
        textAlign: 'left',
        marginTop: Spacing.xs,
        textShadowColor: 'rgba(0, 0, 0, 0.75)',
        textShadowOffset: { width: 0, height: 1 },
        textShadowRadius: 4,
        fontFamily: Typography.fontFamily.primary,
    },
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        opacity: 0.3,
        borderRadius: Borders.radius.lg,
        overflow: 'hidden',
    },
})