import React from 'react';
import { StyleSheet, ScrollView, View } from 'react-native';
import { VendorRating } from './VendorRating';
import { ReviewsList } from './ReviewList';
import { Colors } from '@/constants/Colors';


interface ReviewModalContentProps {
    reviews: {
        rating: number;
        comment: string;
        userId: string;
        userName: string;
        userImage: string;
        createdAt: string;
    }[];
    ratingAggregate: {
        rating_avg: number;
    };
}

export function ReviewModalContent({ reviews, ratingAggregate }: ReviewModalContentProps) {
    return (
        <ScrollView style={styles.container}>
            <VendorRating reviews={reviews} ratingAggregate={ratingAggregate} />
            <View style={{ borderTopWidth: 0.5, borderColor: Colors.light.textSecondary }} />
            <ReviewsList reviews={reviews} />
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: 'white',
    },
});