import React from 'react';
import { StyleSheet, Text, View, useWindowDimensions } from 'react-native';
import { Colors } from '@/constants/Colors';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Animated, { interpolate, useAnimatedStyle } from 'react-native-reanimated';
import AppHeaderIcons from '@/components/AppHeaderIcons';

interface EventHeaderProps {
  title: string;
  funFact: string;
  headerImage: any;
  notificationCount: number;
  scrollY: Animated.SharedValue<number>;
}

export function EventHeader({
  title,
  funFact,
  headerImage,
  notificationCount,
  scrollY
}: EventHeaderProps) {
  const { width, height } = useWindowDimensions();
  const insets = useSafeAreaInsets();

  const imageAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{
      translateY: interpolate(
        scrollY.value,
        [0, height * 0.35],
        [0, height * 0.175],
        'clamp'
      )
    }],
  }));

  return (
    <View style={[styles.headerContainer, { height: height * 0.35 }]}>
      <Animated.Image
        source={{ uri: headerImage }}
        style={[styles.headerImage, { width, height: height * 0.35 }, imageAnimatedStyle]}
        resizeMode="cover"
      />
      <View style={[styles.overlay, { paddingTop: insets.top }]}>
        <View style={[
          styles.topIcons
        ]}>
         <AppHeaderIcons iconColor={Colors.custom.white} />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.title}>{title}</Text>
          {funFact.trim() && <Text style={styles.funFact}>"{funFact}"</Text>}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    overflow: 'hidden',
    position: 'relative',
  },
  headerImage: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: Colors.light.eventHeaderOverlay,
    justifyContent: 'space-between',
    padding: 16,
  },
  topIcons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 20,
  },
  iconButton: {
    padding: 8,
  },
  badge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: Colors.light.notificationBadge,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  textContainer: {
    alignItems: 'center',
    paddingHorizontal: '10%',
    marginBottom: '20%',
  },
  title: {
    color: Colors.dark.text,
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 12,
  },
  funFact: {
    color: Colors.dark.text,
    fontSize: 16,
    textAlign: 'center',
  },

});
