import * as React from "react";
import Svg, {
  G,
  <PERSON>,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Rect,
} from "react-native-svg";
import { CommonProps } from ".";

const Alert = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Alert-1_svg__a)">
            <Path 
                fillRule="evenodd" 
                clipRule="evenodd" 
                d="M5.37707 0.606346C5.56463 0.491696 5.78019 0.43103 6.00001 0.43103C6.21984 0.43103 6.43539 0.491696 6.62295 0.606346C6.81051 0.720997 6.96279 0.885183 7.06302 1.08083L7.0648 1.0843L11.4436 9.84208C11.535 10.0237 11.5789 10.2264 11.5704 10.4296C11.5619 10.633 11.5015 10.8309 11.395 11.0044C11.2884 11.1779 11.1394 11.3213 10.9618 11.421C10.7843 11.5207 10.5842 11.5733 10.3806 11.5739H10.3794H1.62058H1.61936C1.41577 11.5733 1.2157 11.5207 1.03818 11.421C0.860649 11.3213 0.711552 11.1779 0.605045 11.0044C0.498538 10.8309 0.438157 10.633 0.429636 10.4296C0.421124 10.2264 0.464634 10.0243 0.556036 9.84272L4.93524 1.08431L4.93698 1.08082C5.03721 0.885175 5.18952 0.720997 5.37707 0.606346ZM5.99999 3.71094C6.32981 3.71094 6.59719 3.97832 6.59719 4.30814V6.89599C6.59719 7.22581 6.32981 7.49319 5.99999 7.49319C5.67017 7.49319 5.40279 7.22581 5.40279 6.89599V4.30814C5.40279 3.97832 5.67017 3.71094 5.99999 3.71094ZM6.79625 9.08571C6.79625 9.52549 6.43975 9.88197 5.99999 9.88197C5.56023 9.88197 5.20373 9.52549 5.20373 9.08571C5.20373 8.64594 5.56023 8.28945 5.99999 8.28945C6.43975 8.28945 6.79625 8.64594 6.79625 9.08571Z" 
                fill="url(#Alert-1_svg__b)"
            />
          </G>
          <Defs>
            <LinearGradient id="Alert-1_svg__b" x1="6.00002" y1="-0.913805" x2="6.00002" y2="14.1355" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <ClipPath id="Alert-1_svg__a">
              <Rect width="12" height="12" fill="white"/>
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G clipPath="url(#Alert_svg__a)">
            <Path d="M6.70455 1.29021C6.63812 1.16055 6.53719 1.05173 6.41289 0.975748C6.28859 0.899764 6.14572 0.859558 6.00004 0.859558C5.85435 0.859558 5.71149 0.899764 5.58719 0.975748C5.46289 1.05173 5.36196 1.16055 5.29554 1.29021L0.941856 9.99757C0.881134 10.118 0.852224 10.2521 0.857871 10.3869C0.863518 10.5217 0.903536 10.6528 0.974123 10.7678C1.04471 10.8829 1.14352 10.9779 1.26118 11.044C1.37883 11.11 1.51142 11.145 1.64636 11.1454H10.3537C10.4887 11.145 10.6212 11.11 10.7389 11.044C10.8566 10.9779 10.9554 10.8829 11.026 10.7678C11.0965 10.6528 11.1366 10.5217 11.1422 10.3869C11.1479 10.2521 11.1189 10.118 11.0582 9.99757L6.70455 1.29021Z" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
            <Path d="M6 4.41705V6.98968" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
            <Path d="M6.00002 9.16648C5.89073 9.16648 5.80212 9.0779 5.80212 8.96859C5.80212 8.85927 5.89073 8.77069 6.00002 8.77069" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
            <Path d="M6 9.16648C6.10929 9.16648 6.19789 9.0779 6.19789 8.96859C6.19789 8.85927 6.10929 8.77069 6 8.77069" 
                stroke={color} 
                strokeWidth={strokeWidth}
                strokeLinecap="round" 
                strokeLinejoin="round"
            />
            </G>
            <Defs>
            <ClipPath id="Alert_svg__a">
                <Rect width="12" height="12" fill="white"/>
            </ClipPath>
            </Defs>
        </>
      )}
    </Svg>
  );
};
export default Alert;
