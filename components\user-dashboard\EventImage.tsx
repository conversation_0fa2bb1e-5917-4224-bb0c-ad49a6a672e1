import React from 'react';
import { StyleSheet, Image, View, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '@/constants/Colors';

interface EventImageProps {
  imageUrl: string;
  isFavorite?: boolean;
  onFavorite?: () => void;
}

export const EventImage: React.FC<EventImageProps> = ({
  imageUrl,
  isFavorite,
  onFavorite,
}) => (
  <View style={styles.imageContainer}>
    <Image source={{ uri: imageUrl }} style={styles.image} />
    <TouchableOpacity style={styles.heartButton} onPress={onFavorite}>
      <Ionicons 
        name={isFavorite ? "heart" : "heart-outline"} 
        size={24} 
        color={isFavorite ? Colors.custom.searchBarActive : Colors.light.text} 
      />
    </TouchableOpacity>
  </View>
);

const styles = StyleSheet.create({
  imageContainer: {
    position: 'relative',
    width: 100,
    height: 100,
    alignSelf: 'stretch',
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  heartButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 12,
    padding: 4,
  },
});