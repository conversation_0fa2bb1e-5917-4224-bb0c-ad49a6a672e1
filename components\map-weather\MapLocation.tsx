import React from 'react';
import { View, StyleSheet, TouchableOpacity, Text, Platform } from 'react-native';
import MapView, { Marker } from 'react-native-maps';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { PartyDetailsRootList } from '../create-event/Navigation/PartyDetailsRootList';
import { Colors, Icons } from '@/constants/DesignSystem';
import { Location as LocationIcon } from '@/components/icons';

export interface MapLocationProps {
  party: {
    latitude: number;
    longitude: number;
    location: string,
    partyId: string;
    partyTime: string;
    userRole: string
  };
}

const FullScreenMapView: React.FC<MapLocationProps> = ({ party }) => {
  const locationCoordinates = {
    latitude: party.latitude,
    longitude: party.longitude,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
    partyTime: party.partyTime
  };
  const locationName = party.location;
  const navigation = useNavigation<NavigationProp<PartyDetailsRootList>>();

  const handleMapTap = () => {
    navigation.navigate('FullScreenMapView', { mapProps: { party: party } });
  };

  return (
    <TouchableOpacity
      style={styles.mapContainer}
      activeOpacity={1}
      onPress={handleMapTap}
    >
      <MapView
        style={styles.map}
        initialRegion={locationCoordinates}
        scrollEnabled={false}
        zoomEnabled={false}
        rotateEnabled={false}
        pitchEnabled={false}
        mapType="standard"
      >
        {Platform.OS === 'ios' ? (
          <Marker
          coordinate={locationCoordinates}
        >
          <View style={styles.calloutView}>
            <LocationIcon size={Icons.size.md} color={Colors.primary} style={styles.pinIcon} />
            <Text style={styles.calloutText}>{locationName}</Text>
            </View>
          </Marker>
        ):
        <Marker
          coordinate={locationCoordinates}
          title={locationName}
          pinColor="orange"
        >
        </Marker>
        }
      </MapView>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  mapContainer: {
    flex: 1,
    borderRadius: 8,
    overflow: 'hidden',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  calloutView: {
    flexDirection: 'column',
    alignSelf: 'center',
    borderRadius: 6,
    padding: 5,
    alignItems: 'center',
  },
  calloutText: {
    fontFamily: 'Plus Jakarta Sans',
    fontSize: 11,
    fontWeight: '500',
    marginLeft: 5,
    color: 'orange',
  },
  pinIcon: {
    marginRight: 5,
  },
});

export default FullScreenMapView;
