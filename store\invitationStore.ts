import { create } from 'zustand';

export interface Invitation {
  id: string;
  title: string;
  date: string;
  location: string;
  hostedBy: string;
  image: any;
  type: 'upcoming' | 'host' | 'guest' | 'past';
}

interface InvitationStore {
  invitations: Invitation[];
  setInvitations: (invitations: Invitation[]) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filteredInvitations: () => Invitation[];
}

export const useInvitationStore = create<InvitationStore>((set, get) => ({
  invitations: [],
  setInvitations: (invitations) => set({ invitations }),
  searchQuery: '',
  setSearchQuery: (query) => set({ searchQuery: query }),
  filteredInvitations: () => {
    const { invitations, searchQuery } = get();
    if (!searchQuery.trim()) return invitations;
    
    return invitations.filter(invitation => 
      invitation.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invitation.hostedBy.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invitation.location.toLowerCase().includes(searchQuery.toLowerCase())
    );
  },
})); 