// Base interfaces
interface ServiceLocation {
  __typename: 'MdServiceLocation';
  name: string;
  city: string;
}

interface VendorType {
  name: string;
}
 
export interface CoHost {
  userId: {
    email: string | null;
    firstName?: string | null;
    lastName?: string | null;
    phone?: string | null;
  };
}

interface Party {
  id: string;
  name: string;
  serviceLocation: ServiceLocation;
  vendorTypes: VendorType[];
  coHosts: CoHost[];
  totalBudget: number;
  time: string;
  expectedGuestCount: number;
}

// Response type matching the exact structure
export interface PartyByIdResponse {
  getPartyById: {
    result: {
      party: Party;
    };
  };
}
