import { View, StyleSheet } from 'react-native';
import { useWindowDimensions } from 'react-native';
import { ImageCarousel } from '@/components/ImageCarousel';

interface MediaGalleryProps {
  images: {
    id: string;
    image_src: string;
  }[];
}

export default function MediaGallery({ 
  images, 
}: MediaGalleryProps) {
  const { width: deviceWidth } = useWindowDimensions();

  return (
    <View style={[styles.container, { width: deviceWidth }]}>
      <ImageCarousel 
        images={images} 
        showDotPagination={false}
        showNumberPagination={true}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
});
