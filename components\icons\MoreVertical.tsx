import * as React from "react";
import Svg, {
  Circle,
  Defs,
  LinearGradient,
  Stop,
} from "react-native-svg";
import { CommonProps } from ".";

const MoreVertical = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Circle cx="6" cy="10" r="1" transform="rotate(-90 6 10)" fill="url(#MoreVertical-1_svg__a)" stroke="url(#MoreVertical-1_svg__b)" strokeWidth="0.892857"/>
          <Circle cx="6" cy="6" r="1" transform="rotate(-90 6 6)" fill="url(#MoreVertical-1_svg__c)" stroke="url(#MoreVertical-1_svg__d)" strokeWidth="0.892857"/>
          <Circle cx="6" cy="1.99994" r="1" transform="rotate(-90 6 1.99994)" fill="url(#MoreVertical-1_svg__e)" stroke="url(#MoreVertical-1_svg__f)" strokeWidth="0.892857"/>
          <Defs>
            <LinearGradient id="MoreVertical-1_svg__a" x1="6" y1="8.75862" x2="6" y2="11.4598" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="MoreVertical-1_svg__b" x1="6" y1="8.75862" x2="6" y2="11.4598" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="MoreVertical-1_svg__c" x1="6" y1="4.75862" x2="6" y2="7.45977" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="MoreVertical-1_svg__d" x1="6" y1="4.75862" x2="6" y2="7.45977" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="MoreVertical-1_svg__e" x1="6" y1="0.758559" x2="6" y2="3.45971" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="MoreVertical-1_svg__f" x1="6" y1="0.758559" x2="6" y2="3.45971" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
          </Defs>
        </>
      ) : (
        <>
            <Circle cx="5.99995" cy="10.2" r="1.05" transform="rotate(-90 5.99995 10.2)" stroke={color}/>
            <Circle cx="5.99995" cy="5.99999" r="1.05" transform="rotate(-90 5.99995 5.99999)" stroke={color}/>
            <Circle cx="5.99995" cy="1.79998" r="1.05" transform="rotate(-90 5.99995 1.79998)" stroke={color}/>
        </>
      )}
    </Svg>
  );
};
export default MoreVertical;
