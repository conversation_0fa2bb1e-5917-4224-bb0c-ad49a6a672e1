import React from 'react';
import { View } from 'react-native';
import { Text } from 'react-native-paper';
import { Button } from '../Button';
import { MULTI_DAY_EVENT_TITLE } from '@/constants/createEvent.constants';

interface MultiDayScreenProps {
  value: boolean | null;
  onChange: (isMultiDay: boolean) => void;
}

export function MultiDayScreen({ value, onChange }: MultiDayScreenProps) {
  return (
    <View style={{ padding: 16 }}>
      <Text variant="headlineMedium" style={{ marginBottom: 24 }}>
        {MULTI_DAY_EVENT_TITLE}
      </Text>
      
      <View style={{ 
        flexDirection: 'row', 
        gap: 16, 
        justifyContent: 'center' 
      }}>
        <Button
          title="Yes"
          mode={value === true ? "contained" : "outlined"}
          onPress={() => onChange(true)}
          containerStyle={{ flex: 1 }}
        />
        <Button
          title="No"
          mode={value === false ? "contained" : "outlined"}
          onPress={() => onChange(false)}
          containerStyle={{ flex: 1 }}
        />
      </View>
    </View>
  );
}