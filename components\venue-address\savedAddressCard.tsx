import { View, Text, StyleSheet } from 'react-native';
import { Colors, Borders, Spacing, Typography, Icons } from '@/constants/DesignSystem';
import { useState, useRef, useMemo, useCallback } from 'react';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { BottomSheetModal, BottomSheetBackdrop, BottomSheetView } from '@gorhom/bottom-sheet';
import { AddressBook } from '@/app/(home)/EventsDashboard/AddParty';
import { MoreVertical, Home, Edit, Delete } from '../icons';   

interface SavedAddressCardProps {
    address: AddressBook;
    ellipsis?: boolean;
    onEdit?: () => void;
    onDelete?: () => void;
    onPress: () => void;
}

export default function SavedAddressCard({ address, ellipsis = true, onEdit, onDelete, onPress }: SavedAddressCardProps) {

    const bottomSheetModalRef = useRef<BottomSheetModal>(null);
    const snapPoints = useMemo(() => ['35%'], []);

    const handlePresentModalPress = useCallback(() => {
        bottomSheetModalRef.current?.present();
    }, []);

    const handleSheetChanges = useCallback((index: number) => {
    }, []);

    const handleEditOption = () => {
        onEdit!();
        bottomSheetModalRef.current?.dismiss();
    };

    const handleDeleteOption = () => {
        onDelete!();
        bottomSheetModalRef.current?.dismiss();
    };

    return (
        <>
            <TouchableOpacity onPress={onPress}>
            <View style={styles.cardContainer}>
                <View style={styles.contentRow}>
                    <Home size={Icons.size.md} color={Colors.secondary} style={styles.icon} />
                    <View style={styles.textContainer}>
                        <Text style={styles.titleText}>{address.label}</Text>
                        <View style={styles.addressContainer}>
                            <Text style={styles.addressText}>{address.venueAddress.address}</Text>
                        </View>
                    </View>
                    {ellipsis && (
                        <TouchableOpacity onPress={handlePresentModalPress}>
                            <MoreVertical size={Icons.size.md} color={Colors.text.secondary} />
                        </TouchableOpacity>
                    )}
                </View>
            </View>
            </TouchableOpacity>

            <BottomSheetModal
                ref={bottomSheetModalRef}
                index={0}
                snapPoints={snapPoints}
                onChange={handleSheetChanges}
                backdropComponent={props => (
                    <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} />
                )}
            >
                <BottomSheetView style={[styles.bottomSheetContentContainer]}>
                    <TouchableOpacity style={styles.optionButton} onPress={handleEditOption}>
                        <Edit size={Icons.size.md} color={Colors.text.primary} style={styles.icon} />
                        <Text style={{ color: Colors.text.primary, fontWeight: 'bold'}}>Edit</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.optionButton} onPress={handleDeleteOption}>
                        <Delete size={Icons.size.md} color={Colors.error} style={styles.icon} />
                        <Text style={{ color: Colors.error, fontWeight: 'bold'}}>Delete</Text>
                    </TouchableOpacity>
                    <View style={{ height: 50 }} />
                </BottomSheetView>
            </BottomSheetModal>
        </>
    );
}

const styles = StyleSheet.create({
    cardContainer: {
        backgroundColor: Colors.background.tertiary,
        borderRadius: Borders.radius.md,
        padding: Spacing.lg,
        marginVertical: Spacing.sm,
        marginHorizontal: Spacing.md,
    },
    contentRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    icon: {
        marginRight: Spacing.md,
    },
    textContainer: {
        flex: 1, // Allows text to take available space and wrap
        flexDirection: 'column',
        marginRight: Spacing.sm, // Space before the chevron
    },
    titleText: {
        fontSize: Typography.fontSize.md, // Fallback style
        fontWeight: 'bold',
        color: Colors.text.secondary,
        marginBottom: Spacing.xs,
    },
    addressText: {
        fontSize: Typography.fontSize.sm, // Fallback style
        color: Colors.text.secondary,
    },
    addressContainer: {
        marginTop: Spacing.xs,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    // Styles for BottomSheet content
    bottomSheetContentContainer: {
        paddingHorizontal: Spacing.lg,
        paddingVertical: Spacing.md,
        paddingBottom: Spacing.xl,
    },
    optionButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: Spacing.md,
        borderBottomWidth: 1,
        borderBottomColor: Colors.border.light,
        padding: 10,
        margin: 5
    },
    optionText: {
        fontSize: 16, // Fallback style
        color: Colors.text.primary,
    }
});