import { useEffect, useState } from 'react';
import { userStorage } from '@/app/auth/userStorage';
import { useUserStore } from '@/app/auth/userStore';
import { useAuth } from '@clerk/clerk-expo';

export function useInitializeUser() {
  const [isInitialized, setIsInitialized] = useState(false);
  const { isLoaded, isSignedIn } = useAuth();
  const setUserData = useUserStore((state) => state.setUserData);

  useEffect(() => {
    async function initializeUserData() {
      try {
        console.log('Initializing user data...', { isLoaded, isSignedIn });
        
        if (isLoaded) {
          // Only proceed with user data and token registration if user is signed in
          if (isSignedIn) {
            console.log('User is signed in, retrieving stored user data...');
            const storedUser = await userStorage.getUser();
            
            if (storedUser) {
              console.log('Setting user data in store...');
              setUserData(storedUser);
            } else {
              console.log('No stored user data found');
            }
          } else {
            console.log('User is not signed in, skipping token registration');
          }
        } else {
          console.log('Auth not loaded yet');
        }
      } catch (error) {
        console.error('Error initializing user data:', error);
      } finally {
        setIsInitialized(true);
        console.log('Initialization completed');
      }
    }

    initializeUserData();
  }, [isLoaded, isSignedIn, setUserData]);

  return isInitialized;
}