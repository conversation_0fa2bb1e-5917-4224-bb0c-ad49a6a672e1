import { View, Platform, KeyboardAvoidingView, ScrollView, RefreshControl, Animated } from 'react-native';
import { Text, Surface, useTheme } from 'react-native-paper';
import { useState, useRef, useEffect, useMemo, forwardRef, useImperativeHandle, useCallback } from 'react';
import * as DocumentPicker from 'expo-document-picker';
import { styles as importedStyles } from '@/components/Task/TaskForm.styles';
import { BottomSheetBackdrop, BottomSheetModal, BottomSheetView } from '@gorhom/bottom-sheet';
import { Assignee, Assignees } from './SharedInterfaces';
import { useRoute, RouteProp } from '@react-navigation/native';
import { AssigneeField } from './Form/Fields/AssigneeField/AssigneeField';
import { PartyField } from './Form/Fields/PartyField/PartyField';
import { DueDateField } from './Form/Fields/DueDateField/DueDateField';
import { DescriptionField } from './Form/Fields/DescriptionField/DescriptionField';
import { formatDateAndTime } from '@/app/(home)/utils/reusableFunctions';
import { useSubmitHandler } from './Form/Handlers/UseSubmitHandler';
import { TaskNameField } from './Form/Fields/TaskNameField/TaskNameField';
import CollaboratorSearch from './Comments/CollaboratorsModal';
import { DateTimePickerAndroid } from '@react-native-community/datetimepicker';
import DisplayComments, { CommentInterface } from './Comments/DisplayComments';
import AddComments from './Comments/AddComments';
import { TaskNames, TaskFormLayout } from '@/constants/Task.constants';
import { StatusField } from './Form/Fields/StatusField/StatusField';
import { useQuery } from '@apollo/client';
import { GET_TASK_COLLABORATORS } from './Comments/TaskComments.data';
import { BottomDrawer } from './BottomDrawer';
import { AttachmentsSection } from './Attachments/AttachmentsSection';
import { AttachmentOptions } from './Attachments/AttachmentOptions';
import { getFileTypeIcon } from './Attachments/Attachments.utils';
import { NormalizedCacheObject, ApolloClient } from '@apollo/client';
import { useApolloClient } from '@apollo/client';
import { useAttachmentHandler } from './Attachments/Attachments.hooks';
import { useAttachmentPreviewStore } from './Attachments/attachmentPreview.store';
import { KeyboardProvider } from '@/commons/KeyboardContext';
import { ActionButton } from '@/components/UI/ReusableComponents/ActionButton';

const MAX_FILES_LIMIT = 10;
const MAX_TOTAL_FILES_LIMIT = 25;
interface Party {
  id: string;
  name: string;
}

interface TaskAttachment {
  __typename?: "Document";
  description?: string | null;
  documentType?: string;
  documentUrl: string;
  name: string;
  id?: string;
}

interface TaskFormProps {
  onSubmit: (formData: {
    title: string;
    description: string;
    dueDate: string;
    assignedTo: string;
    partyId: string;
    attachments: string[];
  }) => void;
  isLoading: boolean;
  navigation: any;
  parties: Party[];
  isPartiesLoading: boolean;
  hasPartiesError?: boolean;
  assignees: Assignees;
  isAssigneesLoading: boolean;
  taskId?: string;
  editMode: boolean;
  errorMessage?: string | null;
  initialData?: {
    title: string;
    description: string;
    dueDate: Date | null;
    partyId: string | null;
    assignedTo?: Assignee | null;
    attachments: TaskAttachment[];
  };
  onMenuPress: () => void;
  onTitleChange?: (title: string) => Promise<void>;
  onAssigneeChange?: (assigneeId: string | null) => Promise<void>;
  onDeselectAssignee?: () => void;
  onDueDateChange?: (date: string | null) => Promise<void>;
  onPartyChange?: (partyId: string | null) => Promise<void>;
  onDescriptionChange?: (description: string | null) => Promise<void>;
  initialStatus?: string;
  status?: string;
  onStatusChange?: (status: string) => Promise<void>;
  taskRefetch?: () => void;
  refetchComments?: () => void;
  commentsList?: CommentInterface[];
  onAttachmentsChange?: (attachments: string[]) => Promise<void>;
}

interface TaskFormRef {
  handleSubmit: () => void;
}

type TaskFormRouteParams = {
  selectedAssignee?: string;
};

type TaskFormScreenRouteProp = RouteProp<Record<string, TaskFormRouteParams>, string>;

interface UploadStatus {
  [key: string]: {
    status: 'success' | 'error';
    response?: unknown;
  };
}

export const TaskForm = forwardRef<TaskFormRef, TaskFormProps>((props, ref) => {
  const theme = useTheme();
  const { createTemporaryTaskId, clearTemporaryTask, setCombinedAttachmentsForTask } = useAttachmentPreviewStore();
  const [temporaryTaskId] = useState(() => createTemporaryTaskId());
  
  // Use temporaryTaskId when no real taskId exists
  const effectiveTaskId = props.taskId || temporaryTaskId;

  const [isDatePickerVisible, setDatePickerVisible] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(props.initialData?.dueDate || null);
  const [taskTitle, setTaskTitle] = useState(props.initialData?.title || '');
  const [description, setDescription] = useState(props.initialData?.description || '');
  const [attachments, setAttachments] = useState<string[]>(
    props.initialData?.attachments?.map(a => a.id || '').filter(Boolean) || []
  );
  const [selectedParty, setSelectedParty] = useState<Party | null>(props.initialData?.partyId ? props.parties.find(p => p.id === props.initialData?.partyId) || null : null);
  const [selectedAssignee, setSelectedAssignee] = useState<Assignee | null>(() => {
    if (props.initialData?.assignedTo) {
      return props.initialData.assignedTo;
    }
    return null;
  });
  const [selectedStatus, setSelectedStatus] = useState(props.status || props.initialStatus || 'In Progress');
  const collaboratorsBottomSheetRef = useRef<BottomSheetModal>(null);
  const collaboratorsSnapPoints = useMemo(() => TaskFormLayout.BOTTOM_SHEET_SNAP_POINTS, []);
  const route = useRoute<TaskFormScreenRouteProp>();
  const params = route.params || {};

  const { data: taskCollaborators, refetch: refetchTaskCollaborators } = useQuery(GET_TASK_COLLABORATORS, {
    variables: {
      filter: {
        taskId: props.taskId
      }
    },
    skip: !props.taskId
  });

  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  const [slideAnim] = useState(new Animated.Value(0));
  const [overlayAnim] = useState(new Animated.Value(0));
  const [isPickerActive, setIsPickerActive] = useState(false);
  const [isDocumentPickerOpen, setIsDocumentPickerOpen] = useState(false);
  const documentPickerTimeout = useRef<NodeJS.Timeout | null>(null);
  const [uploadingFiles, setUploadingFiles] = useState<DocumentPicker.DocumentPickerAsset[]>([]);
  const [isCameraSessionActive, setIsCameraSessionActive] = useState(false);
  const [bottomDrawerFiles, setBottomDrawerFiles] = useState<DocumentPicker.DocumentPickerAsset[]>([]);
  const [uploadStatuses, setUploadStatuses] = useState<UploadStatus>({});
  const apolloClient = useApolloClient() as ApolloClient<NormalizedCacheObject>;
  const [uploadedKeysMap, setUploadedKeysMap] = useState<Map<number, string>>(new Map());

  const [combinedAttachments, setCombinedAttachments] = useState<{
    existing: TaskAttachment[];
    uploading: DocumentPicker.DocumentPickerAsset[];
  }>({
    existing: props.initialData?.attachments || [],
    uploading: []
  });


  const {
    handleAttachmentPress,
    handleCloseBottomSheet,
    handleDocumentPick,
    handlePhotoGallery,
    handleCamera,
    handleFileRemove,
    handleUploadComplete
  } = useAttachmentHandler({
    setIsBottomSheetOpen,
    slideAnim,
    overlayAnim,
    bottomDrawerFiles,
    setBottomDrawerFiles,
    setUploadingFiles,
    uploadingFiles,
    isDocumentPickerOpen,
    setIsDocumentPickerOpen,
    setIsPickerActive,
    documentPickerTimeout,
    MAX_FILES_LIMIT,
    MAX_TOTAL_FILES_LIMIT,
    setIsCameraSessionActive,
    isCameraSessionActive,
    apolloClient,
    uploadedKeysMap,
    setUploadStatuses,
  });

  const [taskNameError, setTaskNameError] = useState('');
  const [partyError, setPartyError] = useState('');
  const handleFileRetry = (index: number) => {
    // Implement retry logic here
    console.log('Retrying upload for file at index:', index);
  };

  // Update effect for uploadingFiles
  useEffect(() => {
    const newCombined = {
      ...combinedAttachments,
      uploading: uploadingFiles
    };
    setCombinedAttachments(newCombined);
    
    // Always use effectiveTaskId
    setCombinedAttachmentsForTask(effectiveTaskId, newCombined);
  }, [uploadingFiles, effectiveTaskId]);

  const handleAttachmentsChange = useCallback(({
    existing,
    uploading
  }: {
    existing: TaskAttachment[];
    uploading: DocumentPicker.DocumentPickerAsset[];
  }) => {
    const newCombined = { existing, uploading };
    setCombinedAttachments(newCombined);
    
    // Always use effectiveTaskId
    setCombinedAttachmentsForTask(effectiveTaskId, newCombined);

    const newAttachments = [
      ...existing.map(a => a.id || '').filter(Boolean),
      ...Array.from(uploadedKeysMap.values())
    ];
    setAttachments(newAttachments);
    
    props.onAttachmentsChange?.(newAttachments);
  }, [uploadedKeysMap, effectiveTaskId, setCombinedAttachmentsForTask]);

  const { handleSubmit } = useSubmitHandler({
    onSubmit: (formData) => {
      let isValid = true;

      setTaskNameError('');
      setPartyError('');

      if (!taskTitle.trim()) {
        setTaskNameError(TaskNames.TASK_NAME_REQUIRED);
        isValid = false;
      }

      if (!selectedParty) {
        setPartyError(TaskNames.PARTY_REQUIRED);
        isValid = false;
      }

      if (!isValid) return;


      props.onSubmit({
        title: taskTitle,
        description: description,
        dueDate: selectedDate?.toISOString() || '',
        assignedTo: selectedAssignee ? selectedAssignee.id : '',
        partyId: selectedParty?.id || '',
        attachments: attachments,
      });
    },
    title: taskTitle,
    description: description,
    dueDate: selectedDate?.toISOString(),
    assignedTo: selectedAssignee ? selectedAssignee.id : '',
    partyId: selectedParty?.id,
  });

  useEffect(() => {
    return () => {
      if (documentPickerTimeout.current) {
        clearTimeout(documentPickerTimeout.current);
        documentPickerTimeout.current = null;
      }
      setIsDocumentPickerOpen(false);
      setIsPickerActive(false);
    };
  }, []);

  const handleShowCollaboratorsModal = () => {
    collaboratorsBottomSheetRef.current?.present();
  };

  useEffect(() => {
    if (Platform.OS === 'android' && isDatePickerVisible) {
      DateTimePickerAndroid.open({
        value: selectedDate || new Date(),
        mode: 'date',
        is24Hour: true,
        onChange: (event: any, date?: Date) => {
          setDatePickerVisible(false);
          if (date) {
            setSelectedDate(date);
          }
        },
      });
    }
  }, [isDatePickerVisible]);

  useEffect(() => {
    if (params.selectedAssignee) {
      setSelectedAssignee(JSON.parse(params.selectedAssignee));
    }
  }, [params.selectedAssignee]);

  useEffect(() => {
    if (props.initialData?.dueDate) {
      setSelectedDate(props.initialData.dueDate);
    }
  }, [props.initialData?.dueDate]);

  useEffect(() => {
    if (props.initialData?.assignedTo && props.assignees) {
      const foundAssignee = props.assignees.find(a => a.id === props.initialData?.assignedTo?.id);
      if (foundAssignee) {
        setSelectedAssignee(foundAssignee);
      }
    }
  }, [props.initialData?.assignedTo, props.assignees]);

  useEffect(() => {
    if (props.status) {
      setSelectedStatus(props.status);
    }
  }, [props.status]);

  useImperativeHandle(ref, () => ({
    handleSubmit: () => {
      handleSubmit();
    }
  }));

  const scrollViewRef = useRef<ScrollView>(null);

  const handleCommentSent = () => {
    // Wait for the next frame to ensure the new comment is rendered
    requestAnimationFrame(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    });
  };

  // Clean up temporary task when component unmounts
  useEffect(() => {
    return () => {
      if (!props.taskId) {
        clearTemporaryTask(temporaryTaskId);
      }
    };
  }, [props.taskId, temporaryTaskId]);

  return (
    <KeyboardProvider>
    <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} style={importedStyles.safeArea} >
      {props.errorMessage && (
        <View style={importedStyles.errorContainer}>
          <Text style={importedStyles.taskTitleErrorText}>{props.errorMessage}</Text>
        </View>
      )}
      <Surface mode="flat" style={[importedStyles.surface, { backgroundColor: '#FFFFFF' }]}>
        <View style={importedStyles.contentWrapper}>
          <ScrollView
            ref={scrollViewRef}
            style={[importedStyles.scrollView, {paddingBottom : props.editMode ? '24%' : 0,
              marginBottom : props.editMode ? Platform.OS === "android" ? '6%' : '10%' : 0,
              height: '100%'
            }]}
            contentContainerStyle={[importedStyles.contentWrapper]}
            keyboardShouldPersistTaps="handled"
            nestedScrollEnabled={true}
            overScrollMode='always'
            showsVerticalScrollIndicator={true}
            maintainVisibleContentPosition={{
              minIndexForVisible: 0,
              autoscrollToTopThreshold: 100,
            }}
            refreshControl={<RefreshControl refreshing={props.isLoading} onRefresh={props.refetchComments} />}
          >
            <View style={[importedStyles.contentWrapper]}>
              <View style={importedStyles.bottomFoamSection}>
                <TaskNameField
                  value={taskTitle}
                  onChangeText={setTaskTitle}
                  inputTheme={theme}
                  onSaveTitle={props.onTitleChange}
                  taskId={props.taskId}
                />
                {taskNameError && !taskTitle.trim() ? (
                  <Text style={importedStyles.taskTitleErrorText}>{taskNameError}</Text>
                ) : null}

                {/* Assigned To and Due Date Row */}
                <View style={[importedStyles.rowContainer]}>
                  <AssigneeField
                    selectedAssignee={selectedAssignee}
                    onAssigneeSelect={(assignee) => {
                      setSelectedAssignee(assignee);
                    }}
                    onDeselectAssignee={() => {
                      setSelectedAssignee(null);
                    }}
                    onSaveAssignee={props.onAssigneeChange}
                    taskId={props.taskId}
                  />
                  <View style={[importedStyles.saparator]}></View>
                  <DueDateField
                    selectedDate={selectedDate}
                    formatDateAndTime={formatDateAndTime}
                    onDateChange={(date) => {
                      setSelectedDate(date);
                    }}
                    onSaveDueDate={props.onDueDateChange}
                    taskId={props.taskId}
                    isEditMode={props.editMode}
                    isInitialLoad={!props.editMode || !!props.initialData?.dueDate}
                  />
                </View>

                {/* Party Dropdown */}
                <View style={[importedStyles.rowContainer]}>
                  <PartyField
                    selectedParty={selectedParty}
                    onPartySelect={(party) => {
                      setSelectedParty(party);
                    }}
                    inputTheme={theme}
                    isLoading={props.isPartiesLoading}
                    parties={props.parties}
                    onSaveParty={props.onPartyChange}
                    taskId={props.taskId}
                    hasError={!!partyError}
                  />

                  {partyError && !selectedParty ? (
                    <Text style={importedStyles.partyFieldErrorText}>{partyError}</Text>
                  ) : null}

                  <View style={[
                    importedStyles.saparator, 
                    { paddingLeft: props.editMode ? 0 : 160 }
                  ]}>
                  </View>
                  
                  {props.editMode && (
                  <StatusField
                    selectedStatus={selectedStatus}
                    onStatusSelect={setSelectedStatus}
                    onSaveStatus={props.onStatusChange}
                    taskId={props.taskId}
                    inputTheme={theme}
                    isLoading={props.isLoading}
                    dueDate={selectedDate}
                  />
                )}
                </View>

                {/* Description */}
                <DescriptionField
                  value={description}
                  onChangeText={setDescription}
                  onSaveDescription={props.onDescriptionChange}
                  taskId={props.taskId}
                />

                {/* Attachments Section */}
                <AttachmentsSection
                  taskId={effectiveTaskId}
                  uploadingFiles={uploadingFiles}
                  existingAttachments={combinedAttachments.existing}
                  uploadedAttachments={combinedAttachments.uploading}
                  onAttachmentsChange={handleAttachmentsChange}
                  onAttachmentPress={handleAttachmentPress}
                  onFileRemove={handleFileRemove}
                  getFileTypeIcon={getFileTypeIcon}
                  documentIds={uploadedKeysMap}
                  allAttachments={attachments}
                />

                <BottomDrawer
                  isVisible={isBottomSheetOpen}
                  onClose={handleCloseBottomSheet}
                  slideAnim={slideAnim}
                  overlayAnim={overlayAnim}
                  title="Attachments"
                  selectedFiles={bottomDrawerFiles}
                  onFileRemove={(index, documentId) => {
                    setBottomDrawerFiles(prev => prev.filter((_, i) => i !== index));
                    setAttachments(prev => prev.filter(id => id !== documentId));
                  }}
                  onFileRetry={handleFileRetry}
                  hasUploadingFiles={bottomDrawerFiles.length > 0}
                  isLoading={isPickerActive}
                  onUploadComplete={(fileIndex, status, response) => {
                    if (status === 'success' && response?.documentId) {
                      // Get current timestamp as unique key instead of using fileIndex
                      const uniqueKey = Date.now();
                      
                      setUploadedKeysMap(prev => {
                        const newMap = new Map(prev);
                        newMap.set(uniqueKey, response.documentId);
                        return newMap;
                      });
                      setAttachments(prev => {
                        const newAttachments = [...prev, response.documentId];
                        return newAttachments;
                      });
                      setCombinedAttachments(prev => {
                        const newCombined = {
                          ...prev,
                          uploading: [...prev.uploading, response.documentId]
                        };
                        return newCombined;
                      });

                      // Log state checks after updates
                     
                    }
                  }}
                >
                  <AttachmentOptions
                    isPickerActive={isPickerActive}
                    isCameraSessionActive={isCameraSessionActive}
                    handleDocumentPick={handleDocumentPick}
                    handlePhotoGallery={handlePhotoGallery}
                    handleCamera={handleCamera}
                  />
                </BottomDrawer>
              </View>
              {props.editMode && props.commentsList?.length ? (
                <DisplayComments
                  comments={props.commentsList} taskId={props.taskId || ''}
                  refetchComments={props.refetchComments || (() => { })}
                />
              ) : null}
            </View>
            <View style={{ height: Platform.OS === "android" ? 70 : 100 }}></View>
          </ScrollView>
        </View>
      </Surface>
      {!props.editMode && (
      <View style={importedStyles.footer}>
        <View style={importedStyles.bottomButton}>
          <ActionButton
            onPress={handleSubmit}
            label={TaskNames.SAVE_BUTTON_LABEL}
            disabled={props.isLoading}
            style={importedStyles.saveButton}
            labelStyle={importedStyles.saveButtonLabel}
          />
        </View>
      </View>
      )}
      {props.editMode && (
        <View style={{ flex: 1, zIndex: 800 }}>
          <AddComments
            handleShowCollaboratorsModal={handleShowCollaboratorsModal}
            taskId={props.taskId}
            refetchComments={props.refetchComments}
            taskCollaborators={taskCollaborators?.getTaskCollaborators?.result?.taskCollaborators}
            onCommentSent={handleCommentSent}
          />
        </View>
      )}

      <BottomSheetModal
        ref={collaboratorsBottomSheetRef}
        index={1}
        snapPoints={collaboratorsSnapPoints as any}
        enablePanDownToClose
        backdropComponent={(props) => (
          <BottomSheetBackdrop
            {...props}
            appearsOnIndex={1}
            disappearsOnIndex={0}
          />
        )}
      >
        <BottomSheetView
          style={{
            flex: 1,
            marginBottom: "10%",
            paddingBottom: "10%",
          }}
        >
          <CollaboratorSearch
            taskId={props.taskId || ''}
            taskCollaborators={taskCollaborators?.getTaskCollaborators.result.taskCollaborators?.length ? taskCollaborators?.getTaskCollaborators.result.taskCollaborators : []}
            refetchTaskCollaborators={refetchTaskCollaborators}
            collaboratorsBottomSheetRef={collaboratorsBottomSheetRef}
          />
        </BottomSheetView>
      </BottomSheetModal>
    </KeyboardAvoidingView>
    </KeyboardProvider>
  );
});

TaskForm.displayName = 'TaskForm';

export default TaskForm;

