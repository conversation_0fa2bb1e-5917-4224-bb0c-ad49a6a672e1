import { StyleSheet } from 'react-native';
import { Colors, Typography } from '@/constants/DesignSystem';
export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    marginBottom: 0,
  },
  welcomeText: {
    marginTop: 0,
    marginBottom: 10,
    marginHorizontal: 16,
    fontWeight: Typography.fontWeight.bold,
    fontSize: Typography.fontSize.xl,
    color: Colors.text.secondary,
  },
  headerLeft: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'center',
    paddingLeft: 16,
  },
  headerLeftText: {
    fontWeight: 'bold',
    color: '#000000',
    fontSize: 20,
    fontFamily: 'Skia',
    lineHeight: 22,
  },
  headerRight: {
    flex: 1,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'flex-end',
    flexDirection: 'row',
  },
  subText: {
    marginBottom: 16,
    opacity: 0.7,
    marginHorizontal: 16,
    color: '#000000',
  },
  partyAntText: {
    fontWeight: 'bold',
    color: '#000000',
  },
  // Filter styles moved to Filters.tsx component
  eventsContainer: {
    backgroundColor: 'white',
    paddingVertical: 8,
  },
  eventCard: {
    width: 150,
    marginHorizontal: 8,
    backgroundColor: 'white',
  },
  newPartyCardImageContainer: {
    borderWidth: 1,
    borderColor: Colors.border.orange,
    borderRadius: 8,
    overflow: 'hidden',
    height: 220,
  },
  cardImageContainer: {
    borderWidth: 1,
    borderColor: Colors.border.light,
    borderRadius: 8,
    overflow: 'hidden',
    height: 220,
  },
  cardImage: {
    width: '100%',
    height: '100%',
  },
  cardContent: {
    marginTop: 8,
  },
  cardTitle: {
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: '600',
    fontSize: 16,
    marginBottom: 4,
    color: Colors.black,
  },
  cardSubtitle: {
    fontFamily: 'Plus Jakarta Sans',
    fontSize: 12,
    color: Colors.black,
  },
  cardButton: {
    marginTop: 8,
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: Colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardButtonText: {
    fontFamily: Typography.fontFamily.primary,
    fontWeight: Typography.fontWeight.medium,
    fontSize: Typography.fontSize.md,
    color: Colors.text.tertiary,
    marginLeft: 4,
  },
  dateChip: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'white',
    padding: 4,
    borderRadius: 999,
    borderWidth: 1,
    borderColor: '#DBDBDB',
  },
  dateChipText: {
    fontSize: 10,
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: '600',
    color: '#000000',
    paddingVertical: 2,
  },
  actionButton: {
    position: 'absolute',
    bottom: 16,
    left: '50%',
    transform: [{ translateX: -40 }],
    backgroundColor: 'white',
    paddingVertical: 8,
    paddingHorizontal: 24,
    borderRadius: 24,
    zIndex: 10,
  },
  actionButtonText: {
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: '600',
    fontSize: 12,
    color: '#000000',
  },
  optionsButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 4,
    borderRadius: 999,
  },
  inboxContainer: {
    marginTop: 8,
    backgroundColor: 'white',
  },
  inboxDescription: {
    marginLeft: 50,
  },
  // chipLabel styles moved to Filters.tsx component
  headerLogo: {
    height: 15,
    width: 130,
  },
  circlesContainer: {
    marginBottom: 16,
    backgroundColor: 'white',
    paddingVertical: 8,
    margin: 10,
  }

});