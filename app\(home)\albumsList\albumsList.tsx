import { StyleSheet, View, Dimensions, Pressable } from "react-native";
import { Text, Searchbar } from "react-native-paper";
import { router, Stack, useLocalSearchParams } from "expo-router";
import { FastPartyActivityIndicator } from "@/components/FastPartyActivityIndicator";
import { useQuery } from "@apollo/client";
import { GET_ALBUMS } from "./albumList.data";
import { useUserStore } from "@/app/auth/userStore";
import MasonryList from "@react-native-seoul/masonry-list";
import { useState, useCallback, useEffect, useMemo } from 'react';
import { Image } from 'expo-image';
import { SafeAreaView } from 'react-native-safe-area-context';
import debounce from 'lodash/debounce';
import { Cross, MultipleUsers, Photos } from '@/components/icons';
import { Colors } from "@/constants/DesignSystem";
import { Icons } from "@/constants/DesignSystem";

interface Album {
    id: string;
    name: string;
    albumCover?: string[];
    publish?: boolean;
}

const { width } = Dimensions.get("window");

const blurhash =
  '|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[';

export default function AlbumsList() {
    const { albumType } = useLocalSearchParams<{ albumType: string }>();
    const userData = useUserStore((state) => state.userData);
    const [refreshing, setRefreshing] = useState(false);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [allAlbums, setAllAlbums] = useState<Album[]>([]);
    const [searchQuery, setSearchQuery] = useState('');
    const limit = 20; // Items per page
    const albumTypeUpper = albumType.toUpperCase();

    const { data, loading, error, fetchMore, refetch } = useQuery(GET_ALBUMS, {
        variables: {
            filter: {
                userType: albumTypeUpper,
                userId: userData?.id,
                name: searchQuery
            },
            pagination: {
                limit: limit,
                skip: 0
            }
        },
        fetchPolicy: "network-only"
    });

    const debouncedSearch = useMemo(
        () => debounce(async (query: string) => {
            console.log('Search triggered with query:', query);
            
            if (query.length < 3) {
                console.log('Query too short, resetting to initial state');
                setPage(1);
                setHasMore(true);
                await refetch({
                    filter: {
                        userType: albumTypeUpper,
                        userId: userData?.id,
                        name: ''
                    },
                    pagination: {
                        limit: limit,
                        skip: 0
                    }
                });
                return;
            }

            console.log('Executing search with query:', query);
            setPage(1);
            setHasMore(true);
            try {
                const result = await refetch({
                    filter: {
                        userType: albumTypeUpper,
                        userId: userData?.id,
                        name: query
                    },
                    pagination: {
                        limit: limit,
                        skip: 0
                    }
                });
                console.log('Search completed:', result);
            } catch (err) {
                console.error('Error searching albums:', err);
            }
        }, 500),
        [refetch, albumTypeUpper, userData?.id]
    );

    // Trigger search when query changes
    useEffect(() => {
        console.log('Search query changed:', searchQuery);
        debouncedSearch(searchQuery);
        return () => {
            debouncedSearch.cancel();
        };
    }, [searchQuery]);

    useEffect(() => {
        if (data?.getMediaFolders?.result?.mediaFolders) {
            const newAlbums = data.getMediaFolders.result.mediaFolders;
            console.log('Received new albums:', newAlbums.length);
            
            // Only update albums if it's a new search or first page
            if (page === 1) {
                setAllAlbums(newAlbums);
            } else {
                setAllAlbums(prevAlbums => {
                    const existingIds = new Set(prevAlbums.map((album: Album) => album.id));
                    const uniqueNewAlbums = newAlbums.filter((album: Album) => !existingIds.has(album.id));
                    return [...prevAlbums, ...uniqueNewAlbums];
                });
            }

            // Update hasMore based on pagination info
            const paginationInfo = data.getMediaFolders.pagination;
            setHasMore(paginationInfo.currentPage < paginationInfo.totalPages);
        }
    }, [data, page]);

    const handleEndReached = async () => {
        if (!hasMore || loading) return;

        const nextPage = page + 1;
        console.log('Loading more albums... Page:', nextPage);

        try {
            await fetchMore({
                variables: {
                    filter: {
                        userType: albumTypeUpper,
                        userId: userData?.id,
                        name: searchQuery
                    },
                    pagination: {
                        limit: limit,
                        skip: (nextPage - 1) * limit
                    }
                }
            });
            setPage(nextPage);
        } catch (err) {
            console.error('Error loading more albums:', err);
        }
    };

    const redirectToAlbumView = (albumId: string) => {
        router.push({
          pathname: '/(home)/photosAlbumView/photosAlbumView',
          params: {
            albumId: albumId,
            albumType: albumType
          }
        });
      }
    const onRefresh = useCallback(async () => {
        setRefreshing(true);
        try {
            setPage(1);
            setHasMore(true);
            await refetch({
                filter: {
                    userType: albumTypeUpper,
                    userId: userData?.id,
                    name: searchQuery
                },
                pagination: {
                    limit: limit,
                    skip: 0
                }
            });
        } catch (err) {
            console.error('Error refreshing albums:', err);
        } finally {
            setRefreshing(false);
        }
    }, [refetch, albumTypeUpper, userData?.id, searchQuery]);

    // Only show loading indicator on initial load, not during refresh
    if (loading && !refreshing && allAlbums.length === 0) {
        return <FastPartyActivityIndicator size="large" />;
    }

    if (error) {
        return (
            <View style={styles.centerContainer}>
                <Text>Error loading albums: {error.message}</Text>
            </View>
        );
    }

    const albumCovers = allAlbums.map((album: Album) => ({
        uri: album?.albumCover?.[0],
        dimensions: { width: width / 3, height: 150 },
        id: album.id,
        title: album.name,
        isPublished: album.publish
    }));

    const renderItem = ({ item }: { item: any }) => (
        <Pressable
            style={styles.imageWrapper}
            onPress={() => {
                console.log("item.id", item)
                redirectToAlbumView(item.id)}}
        >
            {item.uri ? (
                <Image
                    source={item.uri}
                    style={[styles.image, item.dimensions]}
                    placeholder={blurhash}
                    contentFit="cover"
                    transition={200}
                    cachePolicy="memory-disk"
                />
            ) : (
                <View style={[styles.image, item.dimensions, styles.placeholderContainer]}>
                    <Photos size={Icons.size.md} color={Colors.primary} />
                </View>
            )}
            {item.isPublished && (
                <View style={styles.publishedIcon}>
                    <MultipleUsers size={Icons.size.md} color={Colors.primary} />
                </View>
            )}
            <View style={styles.titleOverlay}>
                <Text numberOfLines={1} style={styles.imageTitle}>
                    {item.title}
                </Text>
            </View>
        </Pressable>
    );

    return (
        <SafeAreaView style={styles.container} edges={['top']}>
            <Stack.Screen
                options={{
                    headerShown: false
                }}
            />
            <View style={styles.header}>
                <Text style={styles.headerTitle}>{albumType === 'host' ? 'Host' : 'Guest'} Albums</Text>
                <Pressable onPress={() => router.back()}>
                    <Cross size={Icons.size.md} color={Colors.black} />
                </Pressable>
            </View>
            <View style={styles.searchContainer}>
                <Searchbar
                    placeholder="Search albums (min. 3 characters)..."
                    onChangeText={setSearchQuery}
                    value={searchQuery}
                    style={styles.searchBar}
                    iconColor="#6B7280"
                    inputStyle={styles.searchInput}
                />
            </View>
            <View style={styles.content}>
                {albumCovers.length > 0 ? (
                    <MasonryList
                        style={{paddingBottom: 100}}
                        data={albumCovers}
                        renderItem={renderItem}
                        numColumns={3}
                        onEndReached={handleEndReached}
                        onEndReachedThreshold={0.1}
                        keyExtractor={(item) => item.id}
                        contentContainerStyle={styles.listContainer}
                        showsVerticalScrollIndicator={false}
                        onRefresh={onRefresh}
                        refreshing={refreshing}
                    />
                ) : !loading && (
                    <Text style={styles.noAlbums}>No albums found</Text>
                )}
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    content: {
        flex: 1,
        padding: 8,
    },
    centerContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    imageWrapper: {
        margin: 4,
        borderRadius: 12,
        overflow: 'hidden',
        position: 'relative',
        backgroundColor: '#fff',
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    image: {
        flex: 1,
        width: '100%',
        height: '100%',
        backgroundColor: '#f5f5f5',
    },
    publishedIcon: {
        position: 'absolute',
        top: 8,
        right: 8,
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        borderRadius: 20,
        padding: 4,
        zIndex: 1,
    },
    titleOverlay: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        padding: 8,
    },
    imageTitle: {
        color: '#fff',
        fontSize: 14,
        fontWeight: '500',
    },
    listContainer: {
        paddingHorizontal: 4,
    },
    noAlbums: {
        textAlign: 'center',
        marginTop: 20,
    },
    placeholderContainer: {
        backgroundColor: '#f5f5f5',
        justifyContent: 'center',
        alignItems: 'center',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 8,
    },
    headerTitle: {
        fontSize: 16,
        fontWeight: '500',
    },
    searchContainer: {
        paddingHorizontal: 16,
        paddingVertical: 8,
    },
    searchBar: {
        elevation: 0,
        backgroundColor: '#f5f5f5',
        borderRadius: 8,
    },
    searchInput: {
        fontSize: 14,
    },
});
