import React from 'react';
import { View } from 'react-native';
import { Text, ActivityIndicator, Button } from 'react-native-paper';
import { PartyTypesAccordion } from '../event-dashboard/AddParty/PartyTypesAccordion';
import { EventType } from '../../app/(home)/CreateEvent/CreateEvent.models';
import { useQuery } from '@apollo/client';
import { GET_EVENT_EVENTS } from '../user-events/user-events.data';
import { GetEventTypesData } from '../user-events/user-events.model';
import { useRouter } from 'expo-router';
import { useToast } from '../Toast/useToast';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';

interface EventTypeScreenProps {
  value: EventType | null;
  onChange: (eventType: EventType) => void;
}

export function EventTypeScreen({ value, onChange }: EventTypeScreenProps) {
  const router = useRouter();
  const toast = useToast();
  const { data, loading, error, refetch } = useQuery<GetEventTypesData>(GET_EVENT_EVENTS, {
    fetchPolicy: 'network-only',
    onError: () => {
      toast.error('Error loading event types');
    }
  });

  if (loading) {
    return <FastPartyActivityIndicator />;
  }

  const eventTypes = data?.getMdEventTypes?.result?.mdEventTypes || [];

  if (error || eventTypes.length === 0) {
    return (
      <View style={{ padding: 16, justifyContent: 'center', alignItems: 'center' }}>
        <Text variant="bodyLarge" style={{ color: 'red', marginBottom: 16, textAlign: 'center' }}>
          {error ? 'Error loading event types' : 'No event types available'}
        </Text>
        <View style={{ flexDirection: 'row', gap: 16 }}>
          <Button mode="contained" onPress={() => refetch()}>
            Retry
          </Button>
          <Button mode="outlined" onPress={() => router.back()}>
            Go Back
          </Button>
        </View>
      </View>
    );
  }

  return (
    <View style={{ padding: 16 }}>
      <Text variant="headlineMedium" style={{ marginBottom: 16 }}>
        Select Event Type
      </Text>
      <PartyTypesAccordion<EventType>
        label="Event Type"
        value={value?.name || ''}
        options={eventTypes}
        onSelect={onChange}
      />
    </View>
  );
}