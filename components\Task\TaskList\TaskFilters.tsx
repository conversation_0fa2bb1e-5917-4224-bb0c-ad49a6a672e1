import { View, Pressable, Keyboard } from 'react-native';
import { Text } from 'react-native-paper';
import { styles } from '@/app/(home)/Task/tasks.styles';
import { TaskFilter } from './types';
import { BottomSheetModal, BottomSheetBackdrop, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useRef, useMemo, useState } from 'react';
import { TextInput } from 'react-native-paper';
import { Icons, Colors } from '@/constants/DesignSystem';
import { Check, DropDown } from '@/components/icons';

// Custom radio button component
const CustomRadioButton = ({ isSelected, onPress }: { isSelected: boolean; onPress: () => void }) => (
  <Pressable 
    onPress={onPress}
    style={{
      width: 27,
      height: 27,
      borderRadius: 20,
      borderWidth: 2,
      borderColor: isSelected ? '#768DEC' : '#CFCECE',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: isSelected ? '#BEC9F6' : 'transparent',
      marginLeft: 16,
    }}
  >
    {isSelected && (
      <Check 
        size={Icons.size.md} 
        color={Colors.primary}
      />
    )}
  </Pressable>
);

interface TaskFiltersProps {
  isFilterVisible: boolean;
  setFilterVisible: (visible: boolean) => void;
  selectedFilter: TaskFilter;
  partyFilters: TaskFilter[];
  onFilterSelect: (filter: TaskFilter) => void;
}

export function TaskFilters({
  isFilterVisible,
  setFilterVisible,
  selectedFilter,
  partyFilters,
  onFilterSelect,
}: TaskFiltersProps) {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['80%'], []);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredParties = partyFilters.filter((filter) =>
    filter.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handlePress = () => {
    Keyboard.dismiss();
    bottomSheetRef.current?.present();
  };

  const handleFilterSelect = (filter: TaskFilter) => {
    onFilterSelect(filter);
    bottomSheetRef.current?.dismiss();
  };

  return (
    <View style={styles.filterContainer}>
      <Pressable 
        style={styles.filterButton}
        onPress={handlePress}
      >
        <Text 
          style={styles.filterButtonText} 
          numberOfLines={1} 
          ellipsizeMode="tail"
        >
          {selectedFilter.name}
        </Text>
        <DropDown size={Icons.size.md} color={Colors.primary} />
      </Pressable>

      <BottomSheetModal
        ref={bottomSheetRef}
        index={1}
        snapPoints={snapPoints}
        enablePanDownToClose
        onAnimate={(fromIndex, toIndex) => {
          if (toIndex !== -1) {
            Keyboard.dismiss();
          }
        }}
        style={{
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
          elevation: 5,
        }}
        backdropComponent={(props) => (
          <BottomSheetBackdrop
            {...props}
            appearsOnIndex={0}
            disappearsOnIndex={-1}
            pressBehavior="close"
          />
        )}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'white',
        }}>
          <View style={{
            padding: 16,
            borderBottomWidth: 1,
            borderBottomColor: '#E5E5E5',
            alignItems: 'center',
          }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#344054',
            }}>
              Select Party
            </Text>
          </View>

          <TextInput
            placeholder="Search parties..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={{
              backgroundColor: '#ffffff',
              height: 50,
              borderBottomWidth: 1,
              borderColor: '#E5E5E5',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 1,
              elevation: 2,
              paddingHorizontal: 16,
            }}
            mode="flat"
            underlineColor="transparent"
            activeUnderlineColor="transparent"
            cursorColor="#000000"
            selectionColor="#000000"
          />

          <BottomSheetScrollView contentContainerStyle={{
            paddingHorizontal: 16,
          }}>
            {filteredParties.length === 0 ? (
              <Text style={{
                padding: 16,
                textAlign: 'center',
                color: '#666',
              }}>
                No parties found
              </Text>
            ) : (
              filteredParties.map((filter) => (
                <Pressable
                  key={filter.id}
                  onPress={() => handleFilterSelect(filter)}
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingVertical: 16,
                    borderBottomWidth: 1,
                    borderBottomColor: '#E5E5E5',
                  }}
                >
                  <Text style={{
                    fontSize: 16,
                    color: '#344054',
                    flex: 1,
                  }}>
                    {filter.name}
                  </Text>
                  <CustomRadioButton
                    isSelected={selectedFilter.id === filter.id}
                    onPress={() => handleFilterSelect(filter)}
                  />
                </Pressable>
              ))
            )}
          </BottomSheetScrollView>
        </View>
      </BottomSheetModal>
    </View>
  );
} 