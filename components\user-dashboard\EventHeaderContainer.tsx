import React from 'react';
import { StyleSheet, View } from 'react-native';
import { HeaderComponent } from '../UI/ReusableComponents/HeaderComponent';
import { EventTabs } from './EventTabs';
import { EventTabType } from '@/constants/eventTabTypes';
import { Colors } from '@/constants/Colors';

interface EventHeaderContainerProps {
  activeTab: EventTabType;
  onTabChange: (tab: EventTabType) => void;
  notificationCount?: number;
}

export const EventHeaderContainer: React.FC<EventHeaderContainerProps> = ({
  activeTab,
  onTabChange,
  notificationCount,
}) => {
  return (
    <View style={styles.headerContainer}>
      <HeaderComponent 
        notificationCount={notificationCount}
        isTaskScreen={false}
      />
      <EventTabs 
        activeTab={activeTab}
        onTabChange={onTabChange}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    backgroundColor: Colors.light.background,
    paddingTop: 44,
    paddingHorizontal: 16,
    zIndex: 2,
  },
});