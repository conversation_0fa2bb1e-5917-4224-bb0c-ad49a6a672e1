import { useQuery } from '@apollo/client';
import { GET_PARTY_BY_ID } from './party-dashboard.data';
import type { PartyByIdResponse } from './party-dashboard.model';

export function usePartyDetails(partyId: string) {
  console.log('Querying with partyId:', partyId);

  return useQuery<PartyByIdResponse>(GET_PARTY_BY_ID, {
    variables: { getPartyByIdId: partyId },
    onError: (error) => {
      console.error('GraphQL Error:', error);
    }
  });
}   