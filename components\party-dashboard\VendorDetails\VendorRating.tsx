import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Star } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';

interface VendorRatingProps {
  reviews: { rating: number;
    comment: string;
    userId: string;
    userName: string;
    userImage: string;
    createdAt: string;
  }[];
  ratingAggregate: {
    rating_avg: number;
  };
}

interface RatingBarProps {
  percentage: number;
  starCount: number;
}

function RatingBar({ percentage, starCount }: RatingBarProps) {
  return (
    <View style={styles.ratingBarContainer}>
      <Text style={styles.starText}>{starCount}</Text>
      <View style={styles.barContainer}>
        <View style={[styles.bar, { width: `${percentage}%` }]} />
      </View>
    </View>
  );
}

export function VendorRating({ reviews, ratingAggregate }: VendorRatingProps) {
  const calculateStarCounts = () => {
    const counts = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    reviews.forEach(review => {
      counts[review.rating as keyof typeof counts]++;
    });
    return counts;
  };

  const starCounts = calculateStarCounts();
  const totalReviews = reviews.length;

  return (
    <View style={styles.container}>
      <View style={styles.overallRating}>
        <Text style={styles.ratingNumber}>{ratingAggregate.rating_avg.toFixed(1)}</Text>
        <View style={styles.starsContainer}>
          {[1, 2, 3, 4, 5].map((star) => (
            <Star
              key={star}
              size={24}
              color={Colors.light.text}
              fill={star <= ratingAggregate.rating_avg ? Colors.light.text : 'transparent'}
            />
          ))}
        </View>
      </View>

      <View style={styles.ratingBars}>
        {[5, 4, 3, 2, 1].map((star) => (
          <RatingBar
            key={star}
            starCount={star}
            percentage={(starCounts[star as keyof typeof starCounts] / totalReviews) * 100}
          />
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    backgroundColor: 'white',
  },
  overallRating: {
    alignItems: 'center',
    marginBottom: 24,
  },
  ratingNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  starsContainer: {
    flexDirection: 'row',
    marginVertical: 8,
  },
  totalReviews: {
    fontSize: 16,
    color: Colors.light.text,
  },
  ratingBars: {
    gap: 8,
  },
  ratingBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    width: '90%',
  },
  starText: {
    width: 24,
    textAlign: 'right',
  },
  barContainer: {
    flex: 1,
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
  },
  bar: {
    height: '100%',
    backgroundColor: Colors.light.text,
    borderRadius: 4,
  },
  reviewCount: {
    width: 40,
    fontSize: 12,
  },
});