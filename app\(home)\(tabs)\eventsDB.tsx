import React, { useEffect } from "react";
import {
  StyleSheet,
} from "react-native";
import { ThemedView } from "@/components/UI/ThemedView";
import { FastPartyActivityIndicator } from "@/components/FastPartyActivityIndicator";
import { PartyCard } from "@/components/event-dashboard/PartyCard";
import { Colors } from "@/constants/Colors";
import {
  getFormattedDate,
  getFormattedTime,
  getLongDayFromDate,
} from "@/app/(home)/utils/reusableFunctions";
import { EventHeader } from "@/components/event-dashboard/EventHeader";
import Animated, {
  useAnimatedScrollHandler,
  useSharedValue,
} from "react-native-reanimated";
import { ThemeProvider, DefaultTheme } from "@react-navigation/native";
import { DarkStatusBar } from "@/components/shared/GlobalStatusBar";
import { GET_EVENT_FOR_EVENT_DASHBOARD, GET_EVENT_FUN_FACT_FOR_EVENT_DASHBOARD } from "@/app/(home)/EventsDashboard/AddParty.data";
import { useQuery } from "@apollo/client";
import { GET_PARTIES_BY_EVENT_ID } from "@/app/(home)/EventsDashboard/AddParty.data";
import { ThemedText } from "@/components/UI/ThemedText";
import { useEventStore } from '@/store/eventStore';
import { useFocusEffect } from '@react-navigation/native';
import { usePagination } from '@/hooks/usePagination';
import { AddButton } from "@/components/UI/ReusableComponents/AddButton";

interface Party {
  id: string;
  name: string;
  time: string;
  serviceLocation: {
    city: string;
    name?: string;
  };
  partyType: {
    portraitImage: string;
  };
  day?: string;
}

export default function EventsDashboard() {
  const scrollY = useSharedValue(0);
  const selectedEventId = useEventStore((state) => state.selectedEventId);
  const shouldRefreshParties = useEventStore((state) => state.shouldRefreshParties);
  const setShouldRefreshParties = useEventStore((state) => state.setShouldRefreshParties);
  const setEventName = useEventStore((state) => state.setEventName);
  const {
    page,
    setPage,
    isLoadingMore,
    setIsLoadingMore,
    hasMoreData,
    getPaginationVariables,
    updateHasMore,
    resetPagination
  } = usePagination({ pageSize: 10, totalItems: 0 });

  const { data: eventResult, loading: eventLoading } = useQuery(GET_EVENT_FOR_EVENT_DASHBOARD, {
    variables: {
      getEventByIdId: selectedEventId,
    },
    skip: !selectedEventId,
    fetchPolicy: 'network-only',
  });

  const eventDetails = eventResult?.getEventById?.result?.event;

  const { data: funFactResult, loading: funFactLoading } = useQuery(GET_EVENT_FUN_FACT_FOR_EVENT_DASHBOARD, {
    variables: {
      filter: {
        eventTypes: {
          id: eventDetails?.eventType?.id,
        },
      },
    },
    skip: !eventDetails?.eventType?.id,
    fetchPolicy: 'network-only',
  });

  const funFact = funFactResult?.getMdFunFacts?.result?.mdFunFacts[0]?.funFact || '';

  const { data, loading, refetch, fetchMore } = useQuery(GET_PARTIES_BY_EVENT_ID, {
    variables: {
      filter: {
        eventId: selectedEventId,
      },
      pagination: {
        skip: 0,
        limit: 100,
      },
    },
    skip: !selectedEventId,
    fetchPolicy: 'network-only',
    onCompleted: (data) => {
      if (!isLoadingMore && data?.getParties?.result?.parties) {
        setLocalParties(data.getParties.result.parties);

        const totalItems = data?.getParties?.pagination?.totalItems || 0;
        const currentItemsCount = data.getParties.result.parties.length;
        updateHasMore(totalItems, currentItemsCount);
      }
    },
  });

  const handleLoadMore = async () => {

    if (isLoadingMore || !hasMoreData) return;

    setIsLoadingMore(true);
    try {
      const nextPage = page + 1;

      await fetchMore({
        variables: { ...getPaginationVariables(nextPage) },
        updateQuery: (prev, { fetchMoreResult }) => {
          const newParties = fetchMoreResult?.getParties?.result?.parties || [];

          // Deduplicate parties based on `id`
          setLocalParties((current) => {
            const seen = new Set(current.map((item) => item.id));
            return [
              ...current,
              ...newParties.filter((item: Party) => !seen.has(item.id)),
            ];
          });

          return prev;
        },
      });

      setPage(nextPage);
    } catch (error) {
      console.error("Error loading more parties:", error);
    } finally {
      setIsLoadingMore(false);
    }
  };



  const refetchAllData = React.useCallback(async () => {
    try {
      resetPagination();

      const { data: freshData } = await refetch({
        filter: {
          eventId: selectedEventId,
        },
        ...getPaginationVariables(1),
      });
      // Only clear and update local parties if we actually got new data
      if (freshData?.getParties?.result?.parties) {
        setLocalParties(freshData.getParties.result.parties);
      }
    } catch (error) {
      console.error('Error refetching data:', error);
    }
  }, [refetch, selectedEventId, resetPagination, getPaginationVariables]);

  useFocusEffect(
    React.useCallback(() => {
      refetchAllData();

      return () => {
        resetPagination();
      };
    }, [refetchAllData, resetPagination]),
  );


  const mapPartiesToDisplayFormat = (parties: Party[]) => {
    return parties?.map((party: Party) => ({
      id: party.id,
      title: party.name,
      date: getFormattedDate(party.time),
      time: getFormattedTime(party.time),
      day: getLongDayFromDate(party.time),
      location: party.serviceLocation?.name || '',
      imageSource: party.partyType?.portraitImage || "https://mypartystore.blob.core.windows.net/party-images/party_temp.jpg",
    })) || [];
  };

  const [localParties, setLocalParties] = React.useState<Party[]>([]);

  useEffect(() => {
    if (data?.getParties?.result?.parties) {
      setLocalParties((current) => {
        const newParties = data.getParties.result.parties;
        const seen = new Set(current.map((item) => item.id));
        return [
          ...current,
          ...newParties.filter((item: Party) => !seen.has(item.id)),
        ];
      });
    }
  }, [data]);


  const mappedParties = mapPartiesToDisplayFormat(localParties);

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  const renderItem = ({ item }: { item: any }) => (
    <ThemedView style={{ paddingHorizontal: "4%", paddingBottom: 8, }}>
      <PartyCard
        partyInfo={item}
        onDeleteSuccess={handlePartyDeleted}
      />
    </ThemedView>
  );

  const handlePartyUpdated = React.useCallback(() => {
    // Reset local state
    setLocalParties([]);
    // Refetch data with reset pagination    refetchAllData();
  }, [refetchAllData]);

  const handlePartyDeleted = React.useCallback(() => {
    handlePartyUpdated();
  }, [handlePartyUpdated]);

  useEffect(() => {
    if (shouldRefreshParties) {
      refetchAllData().then(() => {
        setShouldRefreshParties(false);
      });
    }
  }, [shouldRefreshParties, setShouldRefreshParties, refetchAllData]);

  const renderHeader = () => (
    <ThemeProvider value={DefaultTheme}>
      <ThemedView style={{ paddingBottom: 8 }}>
        <EventHeader
          title={eventDetails?.name || `Event #${selectedEventId}`}
          funFact={funFact}
          headerImage={eventDetails?.eventType?.bannerImage}
          notificationCount={eventDetails?.notificationCount || 0}
          scrollY={scrollY}
        />
      </ThemedView>
    </ThemeProvider>
  );

  const renderFooter = () => {
    if (!isLoadingMore) return null;
    return (
      <ThemedView style={additionalStyles.footerLoader}>
        <FastPartyActivityIndicator size="small" fullScreen={false} />
      </ThemedView>
    );
  };

  const renderContent = () => {
    try {
      if (eventLoading || funFactLoading || loading) {
        return <FastPartyActivityIndicator size="large" color={Colors.light.tint} />;
      }

    return (
      <Animated.FlatList
      data={mappedParties}
      renderItem={renderItem}
      keyExtractor={(item) => {
          return item.id;
        }}
        contentContainerStyle={[styles.flatListContainer, { flexGrow: 1 }]}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={() => (
          <ThemedView style={[styles.centerContent, { flex: 1 }]}>
            <ThemedText type="subtitle" style={styles.emptyText}>
              No parties available
            </ThemedText>
          </ThemedView>
        )}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
      />
    );
  } catch (error) {
    console.error("Error rendering content:", error);
    return (
      <ThemedView style={[styles.container, styles.centerContent]}>
        <ThemedText style={styles.emptyText}>Something went wrong"</ThemedText>
      </ThemedView>
    );
  }
};

  const additionalStyles = StyleSheet.create({
    footerLoader: {
      paddingVertical: 16,
      alignItems: 'center',
    },
  });

  useEffect(() => {
    if (eventDetails?.name) {
      setEventName(eventDetails.name);
    }
  }, [eventDetails?.name, setEventName]);

  return (
    <ThemedView style={styles.container}>
      <DarkStatusBar />
      {renderContent()}
      <AddButton
        name="Add Party"
        route="/EventsDashboard/AddPartyClone"
        params={{ eventId: eventDetails?.id }}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  flatListContainer: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    textAlign: 'center',
    color: Colors.light.text,
    fontSize: 16,
    marginTop: 20,
  },
});
