import { Platform, ScrollView, StyleSheet, View } from 'react-native';
import { Chip, Text } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { Colors, Typography, Spacing, Borders, Icons } from '@/constants/DesignSystem';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { HomeRootStackList } from '@/app/Home/HomeNavigation';
import { Search, UpcomingEvents, PastEvents, Hosting, Guests } from '@/components/icons';

const chipHeight = 32;

interface FiltersProps {
  filters: Array<{ label: string; type: string }>;
  onFilterSelect: (type: 'next' | 'host' | 'guest' | 'past') => void;
  selectedFilter: string;
}

const styles = StyleSheet.create({
  filterContainer: {
    flexGrow: 1,
    marginVertical: Spacing.sm,
    marginBottom: Platform.OS === 'ios' ? Spacing.md - 2 : Spacing.lg,
  },
  contentContainer: {
    paddingHorizontal: Spacing.lg
  },
  chip: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderWidth: Borders.width.thin,
    borderColor: Colors.buttonBorder.medium,
    borderRadius: Borders.radius.circle,
    marginRight: Spacing.sm,
    height: chipHeight,
  },
  chipLabel: {
    color: Colors.text.secondary,
    textAlign: 'center',
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.sm
  },
  searchChip: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderWidth: Borders.width.thin,
    borderColor: Colors.buttonBorder.medium,
    borderRadius: Borders.radius.circle,
    marginRight: Spacing.sm,
    height: chipHeight
  }
});

type NavigationProp = NativeStackNavigationProp<HomeRootStackList>;

export function Filters({ filters, onFilterSelect, selectedFilter }: FiltersProps) {
  const navigation = useNavigation<NavigationProp>();

  const iconComponents = {
    next: UpcomingEvents,
    past: PastEvents,
    host: Hosting,
    guest: Guests,
  };

  const handleSearchPress = () => {
    // Navigate to SearchInvitations using React Navigation
    navigation.navigate('SearchInvitations');
  };

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.filterContainer}
      contentContainerStyle={styles.contentContainer}
    >
      <Chip
        style={styles.searchChip}
        onPress={handleSearchPress}
      >
        <Search
          size={Typography.fontSize.md}
          color={Colors.text.secondary}
        />
      </Chip>
      {filters.map((filter, index) => {
        const isSelected = selectedFilter === filter.type;
        const IconComponent = iconComponents[filter.type as keyof typeof iconComponents];

        return (
          <Chip
            key={index}
            mode="outlined"
            selected={isSelected}
            showSelectedCheck={false}
            style={[
              styles.chip,
              {
                borderColor: isSelected ? Colors.primary : Colors.buttonBorder.medium,
                backgroundColor: isSelected ? Colors.background.secondary : Colors.background.transparent,
              },
            ]}
            textStyle={[styles.chipLabel]}
            onPress={() => onFilterSelect(filter.type as 'next' | 'host' | 'guest' | 'past')}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', gap: 4 }}>
              {IconComponent && (
                <IconComponent
                  size={Icons.size.md}
                  variant={isSelected ? 'filled' : 'outline'}
                  {...(isSelected
                    ? {
                        gradientStartColor: Colors.primary,
                        gradientEndColor: Colors.gradient.orange,
                      }
                    : { color: Colors.secondary })}
                />
              )}
              <Text style={{ color: isSelected ? Colors.text.primary : Colors.text.secondary, fontWeight: isSelected ? Typography.fontWeight.semibold : Typography.fontWeight.regular }}>
                {filter.label}
              </Text>
            </View>
          </Chip>
        );
      })}
    </ScrollView>
  );
}