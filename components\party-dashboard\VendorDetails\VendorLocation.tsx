import React from 'react';
import { StyleSheet, TouchableOpacity, Platform, Linking } from 'react-native';
import { ThemedText } from '@/components/UI/ThemedText';
import { ThemedView } from '@/components/UI/ThemedView';
import MapView, { Marker } from 'react-native-maps';
import { VendorDetails } from '@/constants/displayNames';

interface VendorLocationProps {
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
}

export function VendorLocation({ address, coordinates }: VendorLocationProps) {
  const openMaps = () => {
    const scheme = Platform.select({
      ios: 'maps:',
      android: 'geo:',
    });
    const url = Platform.select({
      ios: `${scheme}?q=${address}&ll=${coordinates.latitude},${coordinates.longitude}`,
      android: `${scheme}${coordinates.latitude},${coordinates.longitude}?q=${address}`,
    });

    if (url) {
      Linking.openURL(url);
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>{VendorDetails.VENDOR_LOCATION}</ThemedText>
      <ThemedText style={styles.address}>{address}</ThemedText>
      <TouchableOpacity onPress={openMaps} style={styles.mapContainer}>
        <MapView
          style={styles.map}
          initialRegion={{
            latitude: coordinates.latitude,
            longitude: coordinates.longitude,
            latitudeDelta: 0.005,
            longitudeDelta: 0.005,
          }}
          scrollEnabled={false}
          zoomEnabled={false}
        >
          <Marker
            coordinate={{
              latitude: coordinates.latitude,
              longitude: coordinates.longitude,
            }}
          />
        </MapView>
      </TouchableOpacity>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: '8%',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  address: {
    fontSize: 14,
    marginBottom: 16,
    lineHeight: 20,
  },
  mapContainer: {
    borderRadius: 12,
    overflow: 'hidden',
    height: 200,
  },
  map: {
    width: '100%',
    height: '100%',
  },
});