import { Text, View, TouchableOpacity, StyleSheet } from 'react-native'
import React from 'react'
import { router } from 'expo-router';
import { useQuery } from '@apollo/client';
import { GET_IN_APP_NOTIFICATIONS } from '@/graphql/queries/InAppNotificationCount';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { HomeRootStackList } from '@/app/Home/HomeNavigation';
import { User, Notification } from './icons';
import { Icons, Colors } from '@/constants/DesignSystem';

const AppHeaderIcons = () => {
  const navigation = useNavigation<NavigationProp<HomeRootStackList>>();
  const { data } = useQuery(GET_IN_APP_NOTIFICATIONS, {
    variables: {
      filter: {
        read: false,
      },
    },
    fetchPolicy: 'cache-and-network',
  });

  const notificationCount = data?.getInAppNotifications?.pagination?.totalItems ?? 0;

  const onNotificationPress = () => {
    navigation.navigate('InAppNotification');
  };
 
  const onProfilePress = () => {
    router.push('/userProfile');
  };

  return (
    <View style={styles.headerIcons}>
      <TouchableOpacity style={styles.iconButton} onPress={onNotificationPress}>
        <Notification size={Icons.size.lg} color={Colors.secondary}/>
        {notificationCount > 0 && (
          <View style={styles.badge}>
            <Text style={styles.badgeText}>{notificationCount > 99 ? '99+' : notificationCount}</Text>
          </View>
        )}
      </TouchableOpacity>
      <TouchableOpacity style={styles.iconButton} onPress={onProfilePress}>
        <User size={Icons.size.lg} color={Colors.secondary}/>
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  headerIcons: {
    flexDirection: 'row',
  },
  iconButton: {
    padding: 8,
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: "red",
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 9,
    fontWeight: 'bold',
    includeFontPadding: false,
  },
})

export default AppHeaderIcons

