import { 
    NavBars, 
    DashboardTabs, 
    Tab,
    Themes,
    MdAppSettings
  } from './appSetting.model';
  
  export function transformAppSettings(response: any): MdAppSettings {
    if (!response?.getMdAppSettingsByVersion?.result?.mdAppSettings) {
      throw new Error('Invalid response structure');
    }
  
    const { mdAppSettings } = response?.getMdAppSettingsByVersion?.result;
    // Transform host dashboard tabs
    const hostTabs: DashboardTabs = {
      tasks: transformTab(mdAppSettings.navBars.hostEventDashboard.tabs.tasks),
      apps: transformTab(mdAppSettings.navBars.hostEventDashboard.tabs.apps),
      eventsDb: transformTab(mdAppSettings.navBars.hostEventDashboard.tabs.eventsDb),
      guests: transformTab(mdAppSettings.navBars.hostEventDashboard.tabs.guests),
      messages: transformTab(mdAppSettings.navBars.hostEventDashboard.tabs.messages),
    };
  
    // Transform user dashboard tabs
    const userTabs: DashboardTabs = {
      home: transformTab(mdAppSettings.navBars.userDashboard.tabs.home),
      myEvents: transformTab(mdAppSettings.navBars.userDashboard.tabs.myEvents),
      publicEvents: transformTab(mdAppSettings.navBars.userDashboard.tabs.publicEvents),
    };
  
    // Transform nav bars
    const navBars: NavBars = {
      hostEventDashboard: {
        tabs: hostTabs
      },
      userDashboard: {
        tabs: userTabs
      }
    };
  
    // Transform themes
    const themes: Themes = {
      dark: {
        backgroundColor: mdAppSettings.themes.dark.backgroundColor,
        primaryColor: mdAppSettings.themes.dark.primaryColor,
        secondaryColor: mdAppSettings.themes.dark.secondaryColor,
        textColor: mdAppSettings.themes.dark.textColor,
      },
      light: {
        backgroundColor: mdAppSettings.themes.light.backgroundColor,
        primaryColor: mdAppSettings.themes.light.primaryColor,
        secondaryColor: mdAppSettings.themes.light.secondaryColor,
        textColor: mdAppSettings.themes.light.textColor,
      }
    };
    return {
      navBars,
      themes,
      version: mdAppSettings.version
    };
  }
  
  function transformTab(tab: any): Tab | undefined {
    if (!tab) return undefined;
    
    return {
      id: tab.id,
      iconSrc: tab.iconSrc,
      name: tab.name
    };
  }