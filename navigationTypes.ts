import { RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

export type RootStackParamList = {
  MapLocation: undefined;
  FullScreenMap: { latitude: number; longitude: number };
  EditPartyDetails: { 
    party: { id: string; name: string; };
  };
};

export type EditPartyDetailsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'EditPartyDetails'>;
export type EditPartyDetailsScreenRouteProp = RouteProp<RootStackParamList, 'EditPartyDetails'>;

