import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { View, TouchableOpacity, ScrollView, RefreshControl } from 'react-native';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ScrollView as GestureScrollView } from 'react-native-gesture-handler';
import { useMutation, useQuery } from '@apollo/client';
import { HeaderComponent } from '@/components/UI/ReusableComponents/HeaderComponent';
import { DELETE_TASK, GET_EVENT_TASKS } from '@/app/(home)/Task/Task.data';
import { styles } from '@/app/(home)/Task/tasks.styles';
import { TaskItem } from '@/components/Task/TaskList/TaskItem';
import { TaskFilters } from '@/components/Task/TaskList/TaskFilters';
import { SearchBar } from '@/components/Task/TaskList/SearchBar';
import { Task, TaskFilter } from '@/components/Task/TaskList/types';
import { useEventStore } from '@/store/eventStore';
import { useFocusEffect } from '@react-navigation/native';
import { LoadingIndicator } from '@/components/UI/ReusableComponents/LoadingIndicator';
import { useFilterStore } from '@/services/filterService';
import { formatDate } from 'date-fns';
import { Button } from 'react-native-paper';
import { Cross } from '@/components/icons';
import { Icons, Colors } from '@/constants/DesignSystem';
import NoTasks from '@/components/Illustrations/NoTasks';

interface TaskFilterVariables {
  filter: {
    partyId?: string | null;
    task: {
      status?: string[] | null;
      dueDate?: {
        start: string;
        end: string;
      } | null;
      assignedTo?: Array<{ id: string }> | null;
      title?: string | null;
    };
  };
  getEventByIdId: string;
  eventId: string;
}

const FilterPill = ({ onPress, children }: { onPress: () => void; children: React.ReactNode }) => (
  <TouchableOpacity 
    onPress={onPress}
    style={{
      backgroundColor: '#768DEC',
      borderRadius: 20,
      paddingHorizontal: 12,
      paddingVertical: 6,
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 8,
      minWidth: 90,
      minHeight: 20,
      justifyContent: 'center',
    }}
  >
    <Cross size={Icons.size.sm} color={Colors.white} />
    {typeof children === 'string' ? (
      <Text 
        style={{ 
          color: 'white',
          fontFamily: 'PlusJakartaSans-SemiBold',
          fontSize: 6,
          fontWeight: '600',
          lineHeight: 16,
          letterSpacing: 0.5,
          textAlign: 'center',
        }}
      >
        {children}
      </Text>
    ) : children}
  </TouchableOpacity>
);

export default function TasksScreen() {
  const navigation = useNavigation<NavigationProp<any>>();
  const selectedEventId = useEventStore((state) => state.selectedEventId);
  const filters = useFilterStore((state) => state.filters);
  
  //state management
  const [searchQuery, setSearchQuery] = useState('');
  const [isFilterVisible, setFilterVisible] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<TaskFilter>({ id: 'all', name: 'All Tasks' });
  const [dateRange, setDateRange] = useState({
    startDate: new Date(),
    endDate: new Date()
  });
  const [isFilterLoading, setIsFilterLoading] = useState(false);
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Query setup
  const getDefaultQueryVariables = (): TaskFilterVariables => ({
    eventId: selectedEventId || '',
    filter: {
      task: {
        status: null,
        dueDate: null,
        assignedTo: null,
        title: null
      }
    },
    getEventByIdId: selectedEventId || ''
  });

  const getFilteredQueryVariables = (
    partyId?: string | null,
    searchText?: string | null,
    dateFilter?: { start: string; end: string } | null,
    statusFilter?: string[] | null,
    assigneeFilter?: Array<{ id: string }> | null
  ): TaskFilterVariables => ({
    eventId: selectedEventId || '',
    filter: {
      partyId: partyId === 'all' ? null : partyId,
      task: {
        title: searchText || null,
        status: statusFilter || null,
        dueDate: dateFilter || null,
        assignedTo: assigneeFilter || null
      }
    },
    getEventByIdId: selectedEventId || ''
  });

  const { loading, error, data, refetch } = useQuery(GET_EVENT_TASKS, {
    variables: getDefaultQueryVariables(),
    skip: !selectedEventId,
    fetchPolicy: 'network-only'
  });

  const [deleteTask, { loading: isDeleting }] = useMutation(DELETE_TASK, {
    refetchQueries: [
      {
        query: GET_EVENT_TASKS,
        variables: getFilteredQueryVariables(
          selectedFilter.id,
          searchQuery
        )
      }
    ]
  });

  // Effect handlers
  useFocusEffect(
    React.useCallback(() => {
      if (selectedEventId) {
        setSelectedFilter({ id: 'all', name: 'All Tasks' });
        setSearchQuery('');
        refetch(getDefaultQueryVariables());
      }
    }, [selectedEventId, refetch])
  );

  useEffect(() => {
    if (selectedEventId) {
      setIsFilterLoading(true);
      const filterVariables = getFilteredQueryVariables(
        selectedFilter.id,
        debouncedSearchQuery,
        filters?.date ? {
          start: filters.date.startDate.toISOString(),
          end: filters.date.endDate.toISOString()
        } : null,
        filters?.status?.length ? filters.status : null,
        filters?.assignee?.length ? filters.assignee.map(assignee => ({ id: assignee.id })) : null
      );
      
      const timeoutId = setTimeout(() => {
        refetch(filterVariables)
          .catch(error => {
            console.error('Error refetching tasks:', error);
          })
          .finally(() => {
            setIsFilterLoading(false);
          });
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [filters, selectedEventId, selectedFilter.id, debouncedSearchQuery, refetch]);

  // Debounced search effect
  useEffect(() => {
    if (selectedEventId) {
      setIsSearching(true);
      const timeoutId = setTimeout(() => {
        const filterVariables = getFilteredQueryVariables(
          selectedFilter.id,
          searchQuery,
          filters?.date ? {
            start: filters.date.startDate.toISOString(),
            end: filters.date.endDate.toISOString()
          } : null,
          filters?.status?.length ? filters.status : null,
          filters?.assignee?.length ? filters.assignee.map(assignee => ({ id: assignee.id })) : null
        );

        refetch(filterVariables)
          .finally(() => {
            setIsSearching(false);
          });
      }, 300); // Debounce delay of 300ms

      return () => clearTimeout(timeoutId);
    }
  }, [searchQuery, selectedEventId, selectedFilter.id, filters]);

  // Event handlers
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleSearchSubmit = useCallback((query: string) => {
    const trimmedQuery = query.trim();
    setDebouncedSearchQuery(trimmedQuery);
    refetch(getFilteredQueryVariables(
      selectedFilter.id,
      trimmedQuery || null,
      filters?.date ? {
        start: filters.date.startDate.toISOString(),
        end: filters.date.endDate.toISOString()
      } : null,
      filters?.status?.length ? filters.status : null,
      filters?.assignee?.length ? filters.assignee.map(assignee => ({ id: assignee.id })) : null
    ));
  }, [selectedFilter.id, refetch, filters]);

  const handleFilterSelect = useCallback((filter: TaskFilter) => {
    setSelectedFilter(filter);
    setFilterVisible(false);
    refetch(getFilteredQueryVariables(filter.id, searchQuery));
  }, [searchQuery, refetch]);

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await refetch(getFilteredQueryVariables(
        selectedFilter.id,
        searchQuery,
        filters?.date ? {
          start: filters.date.startDate.toISOString(),
          end: filters.date.endDate.toISOString()
        } : null,
        filters?.status?.length ? filters.status : null,
        filters?.assignee?.length ? filters.assignee.map(assignee => ({ id: assignee.id })) : null
      ));
    } catch (error) {
      console.error('Error refreshing tasks:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [selectedFilter.id, searchQuery, filters, refetch]);

  // Check for API success status
  const hasError = 
    data?.getEventTasks?.status !== 'SUCCESS' || 
    data?.getEventById?.status !== 'SUCCESS';

  const errorMessage = 'Something went wrong';

  // Extract parties from getEventById result
  const partyFilters = useMemo(() => {
    const parties = data?.getEventById?.result?.event?.parties || [];
    
    return [
      { id: 'all', name: 'All Tasks' },
      ...parties.map((party: { id: string; name: string }) => ({
        id: party.id,
        name: party.name
      }))
    ];
  }, [data]);

  const tasks = data?.getEventTasks?.result?.partyTasks?.flatMap(
    (partyTask: { tasks: Task[] }) => partyTask.tasks
  ) || [];

  const getTaskCount = () => {
    if (!data?.getEventTasks?.result) return 0;

    // If no filters are applied and viewing all tasks
    if (selectedFilter.id === 'all' && !hasActiveFilters()) {
      return data.getEventTasks.result.totalTasksCount || 0;
    }

    // If party filter is applied
    if (selectedFilter.id !== 'all') {
      const partyTasks = data.getEventTasks.result.partyTasks.find(
        (partyTask: { tasks: Task[] }) => 
          partyTask.tasks.some(task => task.party?.id === selectedFilter.id)
      );
      return partyTasks?.tasksCount || 0;
    }

    // If any filters are applied, return the filtered count
    return tasks.length;
  };

  // Add a helper function to check if any filters are active
  const hasActiveFilters = () => {
    return !!(
      (filters?.status && filters.status.length > 0) ||
      filters?.date ||
      (filters?.assignee && filters.assignee.length > 0) ||
      searchQuery.trim()
    );
  };

  const getFilteredTaskCount = () => {
    return tasks.length;
  };

  const taskCount = searchQuery ? getFilteredTaskCount() : getTaskCount();

  const totalTasksCount = data?.getEventTasks?.result?.totalTasksCount || 0;

  const handleTaskPress = (taskId: string) => {
    navigation.navigate('UpdateTask', { taskId }); 
  };

  const handleDeleteTask = (taskId: string) => {
    deleteTask({ variables: { deleteTaskId: taskId } });
    refetch();
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.headerContainer}>
        <HeaderComponent
          notificationCount={6}
          isTaskScreen={true}
        />
      </View>

      {!selectedEventId ? (
        <View style={styles.centerContent}>
          <Text>No event selected</Text>
        </View>
      ) : loading || isDeleting || isFilterLoading ? (
        <LoadingIndicator />
      ) : error || hasError ? (
        <View style={styles.centerContent}>
          <Text>
            {error 
              ? 'Something went wrong'
              : errorMessage
            }
          </Text>
        </View>
      ) : (
        <View style={[styles.bodyContainer]}>
          <TaskFilters
            isFilterVisible={isFilterVisible}
            setFilterVisible={setFilterVisible}
            selectedFilter={selectedFilter}
            partyFilters={partyFilters}
            onFilterSelect={handleFilterSelect}
          />

          <SearchBar
            searchQuery={searchQuery}
            onSearchChange={handleSearch}
            onSubmit={handleSearchSubmit}
            isSearching={isSearching}
          />

          <View style={[styles.taskCountContainer, { flexDirection: 'row', alignItems: 'center' }]}>
            <View style={{ flex: 1 }}>
              <ScrollView 
                horizontal 
                showsHorizontalScrollIndicator={false}
                style={{ marginLeft: 0 }}
                contentContainerStyle={{ alignItems: 'center' }}
              >
                <View style={{ flexDirection: 'row' }}>
                  {filters?.status?.length && filters?.status?.length > 0 && (
                    <FilterPill onPress={() => useFilterStore.getState().clearFilters()}>
                      <Text style={{ color: 'white' }}>
                        Status: {filters?.status?.join(', ')}
                      </Text>
                    </FilterPill>
                  )}

                  {filters?.date && (
                    <FilterPill onPress={() => useFilterStore.getState().clearFilters()}>
                      <Text style={{ color: 'white' }}>
                        Date: {formatDate(new Date(filters.date.startDate), 'MMM dd, yy')} to{' '}
                        {formatDate(new Date(filters.date.endDate), 'MMM dd, yy')}
                      </Text>
                    </FilterPill>
                  )}

                  {filters?.assignee?.length && filters?.assignee?.length > 0 && (
                    <FilterPill onPress={() => useFilterStore.getState().clearFilters()}>
                      <Text style={{ color: 'white' }}>
                        Assignee:{' '}
                        {filters?.assignee
                          ?.map(assignee => `${assignee.firstName} ${assignee.lastName}`.trim())
                          .join(', ')}
                      </Text>
                    </FilterPill>
                  )}
                </View>
              </ScrollView>
              
            </View>
            
            <Text style={styles.taskCount}>
              {hasActiveFilters() 
                ? `(${tasks.length} / ${data?.getEventTasks?.result?.totalTasksCount || 0})`
                : selectedFilter.id === 'all'
                  ? `(${data?.getEventTasks?.result?.totalTasksCount || 0})`
                  : `(${getTaskCount()} / ${data?.getEventTasks?.result?.totalTasksCount || 0})`
              }
            </Text>
          </View>

          <View style={styles.separator} />

          {tasks.length === 0 ? (
            <View style={styles.centerContent}>
              <NoTasks />
              <Text style={styles.noTasksText}>No tasks yet. What’s on your party checklist?</Text>
              <Text style={styles.noTasksSubText}>👉 Button: "Create Task"</Text>
            </View>
          ) : (
            <GestureScrollView 
              style={styles.taskList} 
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ paddingBottom: 80 }}
              refreshControl={
                <RefreshControl
                  refreshing={isRefreshing}
                  onRefresh={handleRefresh}
                  tintColor="#768DEC"
                  colors={['#768DEC']}
                />
              }
            >
              {tasks.map((task: Task) => (
                <TaskItem 
                  key={task.id} 
                  task={task} 
                  onPress={() => handleTaskPress(task.id)}
                  onDelete={handleDeleteTask}
                  refetchTask={async () => {
                    await refetch({
                      eventId: selectedEventId,
                      filter: {
                        partyId: selectedFilter.id === 'all' ? null : selectedFilter.id,
                        task: {
                          title: searchQuery || null
                        }
                      },
                      getEventByIdId: selectedEventId
                    });
                  }}
                />
              ))}
            </GestureScrollView>
          )}

          <View style={styles.buttonContainer}>
            <Button
              mode="elevated"
              onPress={() => navigation.navigate('CreateTask')}
              icon="plus"
              contentStyle={{ backgroundColor: Colors.white }}
            >
              Create Task
            </Button>
          </View>

        </View>
      )}
    </SafeAreaView>
  );
}