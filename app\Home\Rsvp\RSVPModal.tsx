import React, { useState, useEffect } from 'react';
import {
  View, Text, TextInput, TouchableOpacity, StyleSheet, KeyboardAvoidingView,
  ScrollView, Platform, ActivityIndicator,
  NativeSyntheticEvent, TextInputContentSizeChangeEventData, Keyboard
} from 'react-native';
import { useMutation } from '@apollo/client';
import { UPDATE_INVITATION_RSVP } from './Rsvp.data';
import { useNavigation } from 'expo-router';
import { HomeRootStackList } from '../HomeNavigation';
import { NavigationProp } from '@react-navigation/native';
import { Alert } from 'react-native';
import { getRandomDefaultImage } from '@/constants/HomePageDefaultImages';
import { Cross, Check, MayBe, Add, Minus } from '@/components/icons';
import { Borders, Colors, Icons, Spacing, Typography } from '@/constants/DesignSystem';

const RSVPModal = ({ selectedResponse, onClose, rsvpId, partyId, fullName, navigation }: {
  selectedResponse: 'yes' | 'maybe' | 'no' | null,
  onClose: () => void,
  rsvpId: string | null,
  partyId: string,
  fullName: string;
  navigation: any;
}) => {


  const [count, setCount] = useState(0);
  const [firstName, setFirstName] = useState('');
  const [comment, setComment] = useState('');
  const [status, setSatus] = useState('PENDING');
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [errorMessage, setErrorMessage] = useState('');
  const [isLoading, setisLoading] = useState(false);
  const [isFirstNameFocused, setIsFirstNameFocused] = useState(false);
  const [isCommentFocused, setIsCommentFocused] = useState(false);
  const [height, setHeight] = useState(40);


  const handleFirstNameFocus = () => {
    setIsFirstNameFocused(true);
  };

  const handleFirstNameBlur = () => {
    setIsFirstNameFocused(false);
  };

  const handleCommentFocus = () => {
    setIsCommentFocused(true);
  };

  const handleCommentBlur = () => {
    setIsCommentFocused(false);
  };
  useEffect(() => {
    setFirstName(fullName);
    if (selectedResponse === 'maybe') {
      setSatus('MAYBE');
    } else if (selectedResponse === 'yes') {
      setSatus('ACCEPTED');
    } else {
      setSatus('NO');
    }
  }, [selectedResponse]);

  // Define mutation hook
  const [updateInvitationRSVP] = useMutation(UPDATE_INVITATION_RSVP);


  const handleIncrement = () => {
    if (count < 10) {
      setCount(count + 1);
    }
    if (count === 9) {
      setErrorMessage('Guest limit reached. You Cannot add more guests.');
    }
  };

  const handleDecrement = () => {
    if (count > 0) {
      setCount(count - 1);
    }

    if (count <= 10) {
      setErrorMessage('');
    }
  };

  const handleFirstNameChange = (text: string) => {
    setFirstName(text);
    setFormErrors({});
  };

  const navigateToPartyDeatilsPage = () => {
    navigation.replace('PartyDetails', {
      screen: 'NewPartyDetailsScreen',
      params: { 
        partyId: partyId,
        isHosting: false,
        rsvp: status
      }
    });
  };

  const handleSubmit = async () => {
    Keyboard.dismiss();
    const firstnamePart = firstName.split(' ');
    let lastName = '';
    if (firstnamePart.length > 1) {
      lastName = firstnamePart.slice(1).join(' ');
    } else {
      lastName = "";
    }
    try {
      setFormErrors({});
      setisLoading(true)
      const variables = {
        input: {
          additionalGuestsCount: count,
          status: status,
          message: comment,
        },
        updateInvitationRsvpId: rsvpId,
        updateUserInput2: {
          firstName: firstnamePart[0],
          lastName: lastName,
        },
      };
      const response = await updateInvitationRSVP({ variables });
      onClose();
      navigateToPartyDeatilsPage();
    } catch (error: any) {
      if (error.message === "Network request failed") {
        Alert.alert(
          'No Network',
          'No internet connection. Please check and try again..',
          [{ text: 'OK' }]
        );
      } else {
        console.error('Unable to save rsvp: ', error);
      }

      setFormErrors({ general: 'An unexpected error occurred. Please try again later.' });
    } finally {
      setFormErrors({});
      setisLoading(false)
    }
  };

  const isSaveButtonDisabled = !firstName.trim();
  const handleContentSizeChange = (e: NativeSyntheticEvent<TextInputContentSizeChangeEventData>) => {
    const { contentHeight } = e.nativeEvent as unknown as { contentHeight: number };
    setHeight(contentHeight);
  };

  return (

    <KeyboardAvoidingView
      style={styles.modalContent}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>

      <View style={styles.loadView}>
        {isLoading && <ActivityIndicator size="large" />}
      </View>

      <ScrollView keyboardShouldPersistTaps="handled" contentContainerStyle={styles.scrollViewContent}>
        <TouchableOpacity onPress={onClose} style={styles.cancelButton}>
          <Cross size={Icons.size.lg} color={Colors.black} />
        </TouchableOpacity>

        <View style={styles.rsvpContainer}>

          <View
            style={[styles.rsvpButton, selectedResponse === 'yes' && styles.selectedButton]}
          >
            <Check size={Icons.size.md} color={Colors.black} />
            <Text style={styles.rsvpText}>YES</Text>
          </View>

          <View
            style={[styles.rsvpButton, selectedResponse === 'maybe' && styles.selectedButton]}
          >
            <MayBe size={Icons.size.md} color={Colors.black} />
            <Text style={styles.rsvpText}>MAYBE</Text>
          </View>

          <View
            style={[styles.rsvpButton, selectedResponse === 'no' && styles.selectedButton]}
          >
            <Cross size={Icons.size.md} color={Colors.black} />
            <Text style={styles.rsvpText}>NO</Text>
          </View>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.label}>
            Name<Text style={styles.starMark}>*</Text>
          </Text>

          <TextInput
            style={[
              styles.input,
              isFirstNameFocused && { borderColor: Colors.gradient.orange },
              formErrors.firstName && { borderColor: Colors.error },
            ]}
            placeholder=""
            value={firstName}
            onChangeText={handleFirstNameChange}
            onFocus={handleFirstNameFocus}
            onBlur={handleFirstNameBlur}
          />

          {formErrors.firstName && (
            <Text style={styles.errorText}>{formErrors.firstName}</Text>
          )}

          <Text style={[styles.label, { marginTop: 10 }]}>Along with</Text>

          <View>
            <View style={styles.checkboxRow}>
              <TouchableOpacity onPress={handleDecrement}>
                <Minus size={Icons.size.md} color={Colors.primary} />
              </TouchableOpacity>
              <View style={styles.checkbox}>
                <Text style={styles.countText}>{count}</Text>
              </View>
              <TouchableOpacity onPress={handleIncrement}>
                <Add size={Icons.size.md} color={Colors.primary} />
              </TouchableOpacity>
            </View>


            {errorMessage !== '' && (
              <Text style={styles.errorText}>{errorMessage}</Text>
            )}
          </View>

          <Text style={[styles.label, { marginTop: 10 }]}>Comment</Text>

          <TextInput
            style={[
              styles.input,
              isCommentFocused && { borderColor: Colors.gradient.orange },
              { height },
            ]}
            value={comment}
            onChangeText={(text) => setComment(text)}
            onFocus={handleCommentFocus}
            onBlur={handleCommentBlur}
            multiline={true}
            onContentSizeChange={handleContentSizeChange}
          />

        </View>

        <TouchableOpacity
          style={[styles.saveButton, isSaveButtonDisabled && { backgroundColor: Colors.button.disabled }]}
          onPress={handleSubmit}
          disabled={isSaveButtonDisabled}
        >
          <Text style={styles.saveButtonText}>Submit RSVP</Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  modalContent: {
    borderRadius: Borders.radius.lg,
    backgroundColor: Colors.white,
    padding: Spacing.md,
    marginTop: Spacing.xl,
    flex: 0.9,
    width: '90%',
    margin: Spacing.sm,
    justifyContent: 'space-between',
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: 'space-between',
  },
  cancelButton: {
    alignSelf: 'flex-end',
    marginBottom: Spacing.sm,
    padding: Spacing.xs,
    paddingTop: Spacing.xl,
  },
  cancelText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.medium,
    marginLeft: Spacing.lg,
    marginTop: Spacing.xl,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
  },
  rsvpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: Spacing.lg,
    marginTop: Spacing.sm,
  },
  rsvpButton: {
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: Borders.width.thin,
    borderColor: Colors.border.medium,
    borderRadius: Borders.radius.circle,
    width: 78,
    height: 78,
    marginHorizontal: Spacing.sm,
  },
  rsvpText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.black,
    marginTop: Spacing.xs,
    fontFamily: Typography.fontFamily.primary,
    fontWeight: Typography.fontWeight.semibold,
  },
  formContainer: {
    marginBottom: Spacing.lg,
    padding: Spacing.lg,
  },
  label: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.medium,
    marginBottom: Spacing.xs,
    color: Colors.text.secondary,
    fontFamily: Typography.fontFamily.primary,
  },
  input: {
    borderWidth: Borders.width.thin,
    borderColor: Colors.border.dark,
    borderRadius: Borders.radius.sm,
    padding: Spacing.sm,
    marginTop: Spacing.sm,
    height: 40,
    marginBottom: Spacing.lg,
    fontSize: Typography.fontSize.md,
    color: Colors.text.secondary,
    fontFamily: Typography.fontFamily.primary,
  },
  commentInput: {
    borderWidth: Borders.width.thin,
    borderColor: Colors.border.dark,
    borderRadius: Borders.radius.sm,
    padding: Spacing.sm,
    marginTop: Spacing.sm,
    height: 80,
    alignItems: 'flex-start',
    marginBottom: Spacing.lg,
    textAlign: 'left',
    textAlignVertical: 'top',
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  checkbox: {
    width: 40,
    height: 40,
    borderWidth: Borders.width.thin,
    borderColor: Colors.border.light,
    marginRight: Spacing.lg,
    marginLeft: Spacing.lg,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Borders.radius.sm,
  },
  plusMinusIcon: {
    fontSize: Typography.fontSize.xxl,
    color: Colors.gradient.orange,
    fontWeight: Typography.fontWeight.bold,
  },
  countText: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.black,
    textAlign: 'center',
    fontFamily: Typography.fontFamily.primary,
  },
  saveButton: {
    backgroundColor: Colors.gradient.orange,
    paddingVertical: Spacing.md,
    borderRadius: Borders.radius.sm,
    alignItems: 'center',
    marginTop: 'auto',
    marginBottom: Spacing.xxxl,
    marginLeft: Spacing.lg,
    marginRight: Spacing.lg,
  },
  saveButtonText: {
    fontSize: Typography.fontSize.lg,
    color: Colors.white,
    fontWeight: Typography.fontWeight.semibold,
    fontFamily: Typography.fontFamily.primary,
  },
  starMark: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.gradient.orange,
  },
  errorText: {
    color: Colors.error,
    fontSize: Typography.fontSize.sm,
    marginTop: -Spacing.md,
    marginLeft: Spacing.sm,
    fontFamily: Typography.fontFamily.primary,
  },
  selectedButton: {
    backgroundColor: Colors.gradient.orange,
    borderWidth: Borders.width.thin,
    borderColor: Colors.gradient.orange,
  },
  loadView: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
  }
});

export default RSVPModal;
