import { gql } from '@apollo/client'

export const GET_ALBUMS = gql`
query GetMediaFolders($filter: MediaFolderFilterInput) {
  getMediaFolders(filter: $filter) {
    ... on MediaFoldersResponse {
      result {
        mediaFolders {
          id
          albumCover
          category
          name
        }
      }
    }
  }
}
`

export const GET_COLLECTIONS = gql`
query GetMediaFolders($filter: MediaFolderFilterInput) {
  getMediaFolders(filter: $filter) {
    ... on MediaFoldersResponse {
      result {
        mediaFolders {
          id
          albumCover
          name
          owner {
            id
          }
        }
      }
    }
  }
}
`
