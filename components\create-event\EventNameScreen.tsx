import React from 'react';
import { View } from 'react-native';
import { Text, TextInput } from 'react-native-paper';

interface EventNameScreenProps {
  value: string;
  onChange: (value: string) => void;
}

export function EventNameScreen({ value, onChange }: EventNameScreenProps) {
  return (
    <View style={{ padding: 16 }}>
      <Text variant="headlineMedium" style={{ marginBottom: 24 }}>
        What's your event called?
      </Text>
      
      <TextInput
        label="Event Name"
        value={value}
        onChangeText={onChange}
        mode="outlined"
        placeholder="Enter event name"
        spellCheck={true}
        autoFocus
        style={{ backgroundColor: 'transparent' }}
      />
      
      <Text variant="bodySmall" style={{ marginTop: 8, color: 'gray' }}>
        This name will be visible to all your guests and vendors
      </Text>
    </View>
  );
}