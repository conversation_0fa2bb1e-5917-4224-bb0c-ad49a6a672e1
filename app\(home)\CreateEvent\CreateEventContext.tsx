import React, { createContext, useContext, useState } from 'react';
import { CreateEventData, EventType, Party } from './CreateEvent.models';

interface CreateEventContextType {
  eventData: CreateEventData;
  setEventData: React.Dispatch<React.SetStateAction<CreateEventData>>;
  selectedEventType: EventType | null;
  availablePartyTypes: Party[];
}

const CreateEventContext = createContext<CreateEventContextType | undefined>(undefined);

export function CreateEventProvider({ children }: { children: React.ReactNode }) {
  const [eventData, setEventData] = useState<CreateEventData>({
    name: '',
    eventType: null,
    isMultiDay: null,
    date: null,
    locationId: '',
    locationName: '',
    area: '',
    cohosts: [],
    parties: []
  });

  const selectedEventType = eventData.eventType;
  const availablePartyTypes = selectedEventType?.partyTypes || [];

  return (
    <CreateEventContext.Provider 
      value={{ 
        eventData, 
        setEventData,
        selectedEventType,
        availablePartyTypes
      }}
    >
      {children}
    </CreateEventContext.Provider>
  );
}

export function useCreateEvent() {
  const context = useContext(CreateEventContext);
  if (context === undefined) {
    throw new Error('useCreateEvent must be used within a CreateEventProvider');
  }
  return context;
}