import React from 'react';
import { StyleSheet } from 'react-native';
import { Colors } from '@/constants/Colors';
import { Linking } from 'react-native';
import { Redirect } from 'expo-router';

async function openPromotionLink(ctaLink?: string) {
  if (!ctaLink) return;
  
  try {
    const canOpen = await Linking.canOpenURL(ctaLink);
    if (canOpen) {
      await Linking.openURL(ctaLink);
    } else {
      console.log('Cannot open URL:', ctaLink);
    }
  } catch (error) {
    console.error('Error opening URL:', error);
  }
}

export default function Index() {
  return <Redirect href="/homePage" />;
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 50,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
    paddingTop: 20,
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: 16,
  },
  welcomeText: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    color: Colors.light.text,
  },
  subText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 8,
  },
  quickActionsContainer: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 8,
    paddingHorizontal: 8,
  },
  link: {
    marginBottom: 12,
  },
  linkContainer: {
    padding: 16,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  linkText: {
    color: Colors.light.background,
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 12,
    flex: 1,
  },
  chevron: {
    marginLeft: 'auto',
  },
  featuredSection: {
    marginBottom: 32,
  },
  featuredCard: {
    padding: 20,
    borderRadius: 12,
    backgroundColor: Colors.light.secondaryBackground,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  featuredTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    color: Colors.light.text,
  },
  featuredDescription: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    marginBottom: 16,
  },
  featuredButton: {
    backgroundColor: Colors.light.buttonBackground,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  featuredButtonText: {
    color: Colors.light.background,
    fontSize: 16,
    fontWeight: '600',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  eventsSection: {
    marginBottom: 24,
    marginTop: 24,
  },
  horizontalScroll: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  promotionsSection: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  promotionItem: {
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 8,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  promotionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  loader: {
    marginTop: 24,
  },
  promotionsContainer: {
    marginTop: 0,
  },
  featuredPromotionsWrapper: {
    marginTop: 32,
    paddingHorizontal: 16,
  },
  sectionLoader: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.light.background,
  },
  emptyStateContainer: {
    marginHorizontal: 16,
    borderRadius: 12,
    overflow: 'hidden',
    height: 250, // Adjust this value based on your design needs
  },
});
