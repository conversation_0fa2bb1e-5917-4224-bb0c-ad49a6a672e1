import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Download = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      clipPath="url(#Download-1_svg__a)"
    >
      <Path
        stroke="url(#Download-1_svg__b)"
        d="M10.529 6.703V9.84a.697.697 0 0 1-.697.697H2.168a.697.697 0 0 1-.697-.697V6.703"
      />
      <Path
        stroke="url(#Download-1_svg__c)"
        d="M4.258 6.007 6 7.749l1.742-1.742"
      />
      <Path stroke="url(#Download-1_svg__d)" d="M6 7.226v-5.69" />
    </G>
    <Defs>
      <LinearGradient
        id="Download-1_svg__b"
        x1={6}
        x2={6}
        y1={6.241}
        y2={11.417}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Download-1_svg__c"
        x1={6}
        x2={6}
        y1={7.959}
        y2={5.606}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Download-1_svg__d"
        x1={6.5}
        x2={6.5}
        y1={0.849}
        y2={8.534}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Download-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Download_svg__a)"
    >
      <Path d="M11 6.773v3.461a.77.77 0 0 1-.77.77H1.77a.77.77 0 0 1-.77-.77V6.773" />
      <Path d="M4.077 6.004 6 7.927l1.923-1.923M6 7.35V1.068" />
    </G>
    <Defs>
      <ClipPath id="Download_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Download;
