import React from 'react';
import { View, Platform } from 'react-native';
import { Modal, Text, Button, useTheme } from 'react-native-paper';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import { Colors, Spacing, Shadows, Typography, Borders } from '@/constants/DesignSystem';

interface DateTimeModalProps {
  visible: boolean;
  onDismiss: () => void;
  value: Date;
  onChange: (date: Date) => void;
  title: string;
  minimumDate: Date;
  maximumDate?: Date;
}

export function DateTimePickerModal({
  visible,
  onDismiss,
  value,
  onChange,
  title,
  minimumDate,
  maximumDate,
}: DateTimeModalProps) {
  const theme = useTheme();
  const [mode, setMode] = React.useState<'date' | 'time'>('date');
  const [tempDate, setTempDate] = React.useState(value);
  const [show, setShow] = React.useState(false);
  const [isModalVisible, setIsModalVisible] = React.useState(visible);

  // Handle visibility changes
  React.useEffect(() => {
    if (visible) {
      setIsModalVisible(true);
      setMode('date');
      setTempDate(value);
      if (Platform.OS === 'android') {
        setShow(true);
      }
    } else {
      setIsModalVisible(false);
      setShow(false);
    }
  }, [visible, value]);

  const isValidTime = (date: Date) => {
    const now = new Date();
    const selectedDate = new Date(date);

    // If selected date is future date, any time is valid
    if (selectedDate.getDate() !== now.getDate() ||
        selectedDate.getMonth() !== now.getMonth() ||
        selectedDate.getFullYear() !== now.getFullYear()) {
      return true;
    }

    // If same day, time must be in future
    return selectedDate.getTime() > now.getTime();
  };

  const handleChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    if (event.type === 'dismissed') {
      setShow(false);
      onDismiss();
      return;
    }

    if (!selectedDate) return;

    if (Platform.OS === 'android') {
      if (mode === 'date') {
        const newDate = new Date(selectedDate);
        newDate.setHours(
          tempDate.getHours(),
          tempDate.getMinutes(),
          tempDate.getSeconds(),
          tempDate.getMilliseconds()
        );
        setTempDate(newDate);

        // Small delay before showing time picker
        setTimeout(() => {
          setMode('time');
          setShow(true);
        }, 100);
      } else {
        const finalDateTime = new Date(tempDate);
        finalDateTime.setHours(
          selectedDate.getHours(),
          selectedDate.getMinutes(),
          selectedDate.getSeconds(),
          selectedDate.getMilliseconds()
        );

        if (!isValidTime(finalDateTime)) {
          const nextValidTime = new Date();
          nextValidTime.setMinutes(nextValidTime.getMinutes() + 1);
          nextValidTime.setSeconds(0);
          nextValidTime.setMilliseconds(0);

          finalDateTime.setHours(
            nextValidTime.getHours(),
            nextValidTime.getMinutes(),
            nextValidTime.getSeconds(),
            nextValidTime.getMilliseconds()
          );
        }

        setShow(false);
        onDismiss();
        onChange(finalDateTime);
      }
    } else {
      // For iOS
      if (mode === 'date') {
        const newDate = new Date(selectedDate);
        newDate.setHours(
          tempDate.getHours(),
          tempDate.getMinutes(),
          tempDate.getSeconds(),
          tempDate.getMilliseconds()
        );
        setTempDate(newDate);
      } else {
        const newDate = new Date(tempDate);
        newDate.setHours(
          selectedDate.getHours(),
          selectedDate.getMinutes(),
          selectedDate.getSeconds(),
          selectedDate.getMilliseconds()
        );

        if (!isValidTime(newDate)) {
          const nextValidTime = new Date();
          nextValidTime.setMinutes(nextValidTime.getMinutes() + 1);
          nextValidTime.setSeconds(0);
          nextValidTime.setMilliseconds(0);
          setTempDate(nextValidTime);
        } else {
          setTempDate(newDate);
        }
      }
    }
  };

  const handleModalDismiss = React.useCallback(() => {
    setIsModalVisible(false);
    setShow(false);
    onDismiss();
  }, [onDismiss]);

  const handleDone = React.useCallback(() => {
    // Handle done button press
    if (!isValidTime(tempDate)) {
      const nextValidTime = new Date();
      nextValidTime.setMinutes(nextValidTime.getMinutes() + 1);
      nextValidTime.setSeconds(0);
      nextValidTime.setMilliseconds(0);
      onChange(nextValidTime);
    } else {
      onChange(tempDate);
    }
    handleModalDismiss();
  }, [tempDate, onChange, handleModalDismiss]);

  if (Platform.OS === 'android') {
    return show ? (
      <DateTimePicker
        value={tempDate}
        mode={mode}
        is24Hour={false}
        display="default"
        onChange={handleChange}
        minimumDate={mode === 'date' ? minimumDate : undefined}
        maximumDate={maximumDate}
        themeVariant={theme.dark ? 'dark' : 'light'}
        accentColor={theme.colors.primary}
      />
    ) : null;
  }

  return (
    <Modal
      visible={isModalVisible}
      onDismiss={handleModalDismiss}
      dismissable={false}
      contentContainerStyle={{
        backgroundColor: Colors.background.primary,
        padding: Spacing.lg,
        margin: Spacing.lg,
        borderRadius: Borders.radius.lg,
        ...Shadows.lg
      }}
    >
      <View>
        <Text
          variant="titleMedium"
          style={{
            marginBottom: Spacing.md,
            color: Colors.text.primary,
            fontSize: Typography.fontSize.lg,
            fontWeight: Typography.fontWeight.semibold
          }}
        >
          {title}
        </Text>
        <DateTimePicker
          value={tempDate}
          mode={mode}
          display={mode === 'date' ? 'inline' : 'spinner'}
          onChange={handleChange}
          minimumDate={mode === 'date' ? minimumDate : undefined}
          maximumDate={maximumDate}
          textColor={Colors.text.primary}
          accentColor={Colors.primary}
          themeVariant={theme.dark ? 'dark' : 'light'}
          style={mode === 'date' ? { height: 300 } : undefined}
        />
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginTop: Spacing.md,
          gap: Spacing.sm
        }}>
          {mode === 'date' ? (
            <>
              <Button
                mode="outlined"
                onPress={handleModalDismiss}
                style={{ flex: 1 }}
                buttonColor={Colors.background.primary}
                textColor={Colors.text.primary}
                rippleColor={Colors.background.secondary}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={() => setMode('time')}
                style={{ flex: 1 }}
                buttonColor={Colors.primary}
                textColor={Colors.white}
                rippleColor="rgba(255,255,255,0.2)"
              >
                Next
              </Button>
            </>
          ) : (
            <>
              <Button
                mode="outlined"
                onPress={() => setMode('date')}
                style={{ flex: 1 }}
                buttonColor={Colors.background.primary}
                textColor={Colors.text.primary}
                rippleColor={Colors.background.secondary}
              >
                Back
              </Button>
              <Button
                mode="contained"
                onPress={handleDone}
                style={{ flex: 1 }}
                buttonColor={Colors.primary}
                textColor={Colors.white}
                rippleColor="rgba(255,255,255,0.2)"
              >
                Done
              </Button>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
}