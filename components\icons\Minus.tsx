import * as React from "react";
import Svg, {
  Path,
  Defs,
  LinearGradient,
  Stop,
} from "react-native-svg";
import { CommonProps } from ".";

const Minus = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path d="M1.5 5.97229H10.5" stroke="url(#Minus-1_svg__a)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <Defs>
            <LinearGradient id="Minus-1_svg__a" x1="6" y1="5.8516" x2="6" y2="7.20218" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
          </Defs>
        </>
      ) : (
        <Path d="M0.75 5.96765H11.25" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
      )}
    </Svg>
  );
};
export default Minus;
