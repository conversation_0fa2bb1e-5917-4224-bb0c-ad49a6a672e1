import gql from 'graphql-tag';

export const UPDATE_LIVE_LOCATIONS = gql`
    mutation UpdateLiveLocation($guestId: ID!, $input: UpdateLiveLocationInput!) {
  updateLiveLocation(guestId: $guestId, input: $input) {
    ... on LiveLocationResponse {
      message
    }
    ... on LiveLocationErrorResponse {
      message
    }
  }
}
`

export const SEND_REMINDERS = gql`
  mutation SendLiveLocationReminder($partyId: ID!) {
  sendLiveLocationReminder(partyId: $partyId) {
    ... on SendLiveLocationReminderResponse {
      result
    }
    ... on LiveLocationErrorResponse {
      message
    }
  }
}
`

export const GET_INVITEES = gql`
  query GetPartyById($getPartyByIdId: ID!) {
  getPartyById(id: $getPartyByIdId) {
    ... on PartyResponse {
      result {
        party {
          rsvps {
            status
            guest {
              user {
                firstName
                id
                lastName
                phone
                profilePicture
              }
            }
          }
        }
      }
    }
  }
}
`