import React, { useState } from 'react';
import { View, FlatList, StyleSheet, useWindowDimensions, ScrollView, RefreshControl, Platform, ActivityIndicator, TouchableOpacity } from 'react-native';
import { Card, Searchbar, FAB, Portal, Dialog, Button, Text } from 'react-native-paper';
import { GET_PARTY_INVITE_TEMPLATES, GET_TEMPLATE_TAGS, CREATE_MEDIA } from './partyInviteTemplate.data';
import { useQuery, useMutation } from '@apollo/client';
import { Stack, router } from 'expo-router';
import Animated, { FadeIn } from 'react-native-reanimated';
import * as ImagePicker from 'expo-image-picker';
import { useUserStore } from '@/app/auth/userStore';
import { useTemplateStore } from './templateStore';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { PartyDetailsRootList } from '@/components/create-event/Navigation/PartyDetailsRootList';
import { Colors, Typography, Spacing, Borders, Shadows, Icons } from '@/constants/DesignSystem';
import { createUploadFileObject } from './fileUtils';
import { uploadFiles } from '@/app/(home)/Task/Task.utils';
import { Cross } from '@/components/icons';

interface Template {
  id: string;
  imageUrl: string;
  name: string;
  tags: Array<Tag>;
}

interface Tag {
  id: string;
  name: string;
  slug: string;
}

const TagSkeleton = () => (
  <ScrollView
    horizontal
    showsHorizontalScrollIndicator={false}
    style={styles.chipContainer}
    contentContainerStyle={styles.chipContent}
  >
    {[1, 2, 3, 4, 5].map((key) => (
      <View key={key} style={[styles.chip, styles.skeletonChip]} />
    ))}
  </ScrollView>
);

const PhotoSkeleton = () => {
  const { width } = useWindowDimensions();
  const numColumns = 2;
  const gap = 8;
  const itemWidth = (width - (numColumns + 1) * gap) / numColumns;
  const itemHeight = (itemWidth * 4) / 3;

  return (
    <View style={styles.skeletonContainer}>
      <View style={styles.skeletonRow}>
        <View style={[styles.skeletonCard, { width: itemWidth, height: itemHeight }]} />
        <View style={[styles.skeletonCard, { width: itemWidth, height: itemHeight }]} />
      </View>
      <View style={styles.skeletonRow}>
        <View style={[styles.skeletonCard, { width: itemWidth, height: itemHeight }]} />
        <View style={[styles.skeletonCard, { width: itemWidth, height: itemHeight }]} />
      </View>
    </View>
  );
};

export default function PartyInviteTemplates() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const { width } = useWindowDimensions();
  const navigation = useNavigation<NavigationProp<PartyDetailsRootList>>();
  const numColumns = 2;
  const gap = 8;
  const itemWidth = (width - (numColumns + 1) * gap) / numColumns;
  const itemHeight = (itemWidth * 4) / 3;
  const userData = useUserStore((state) => state.userData);
  const { data: tagsData, loading: tagsLoading, refetch: refetchTags } = useQuery(GET_TEMPLATE_TAGS);
  const tags: Tag[] = tagsData?.getAllMdInvitationTemplateTags?.result?.tags || [];

  const { data, loading: templatesLoading, refetch: refetchTemplates, fetchMore } = useQuery(GET_PARTY_INVITE_TEMPLATES, {
    variables: {
      filter: {
        id: null,
        name: !searchQuery?.trim() ? null : searchQuery,
        tags: [
          ...(selectedTag ? [{ id: selectedTag }] : []),
          ...(searchQuery?.trim() ? [{ name: searchQuery }] : [])
        ]
      },
      pagination: {
        limit: 10,
        skip: 0
      }
    }
  });
  const templates: Template[] = data?.getMdInvitationTemplates?.result?.mdInvitationTemplates || [];
  const totalCount = data?.getMdInvitationTemplates?.result?.totalCount || 0;

  const [createMedia] = useMutation(CREATE_MEDIA);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [selectedTemplateForDialog, setSelectedTemplateForDialog] = useState<Template | null>(null);
  const setSelectedTemplate = useTemplateStore((state) => state.setSelectedTemplate);
  const setSelectedMedia = useTemplateStore((state) => state.setSelectedMedia);

  const onRefresh = async () => {
    setRefreshing(true);
    setPage(0);
    setHasMore(true);
    try {
      await Promise.all([refetchTags(), refetchTemplates()]);
    } finally {
      setRefreshing(false);
    }
  };

  const loadMoreTemplates = async () => {
    if (!hasMore || isLoadingMore || templatesLoading) return;

    setIsLoadingMore(true);
    try {
      const nextPage = page + 1;
      const { data: newData } = await fetchMore({
        variables: {
          filter: {
            id: null,
            name: !searchQuery?.trim() ? null : searchQuery,
            tags: [
              ...(selectedTag ? [{ id: selectedTag }] : []),
              ...(searchQuery?.trim() ? [{ name: searchQuery }] : [])
            ]
          },
          pagination: {
            limit: 10,
            skip: nextPage * 10
          }
        },
        updateQuery: (prev, { fetchMoreResult }) => {
          if (!fetchMoreResult) return prev;
          
          return {
            getMdInvitationTemplates: {
              ...prev.getMdInvitationTemplates,
              result: {
                ...prev.getMdInvitationTemplates.result,
                mdInvitationTemplates: [
                  ...prev.getMdInvitationTemplates.result.mdInvitationTemplates,
                  ...fetchMoreResult.getMdInvitationTemplates.result.mdInvitationTemplates
                ],
                totalCount: fetchMoreResult.getMdInvitationTemplates.result.totalCount
              }
            }
          };
        }
      });

      const newTemplates = newData?.getMdInvitationTemplates?.result?.mdInvitationTemplates || [];
      setHasMore(newTemplates.length > 0);
      setPage(nextPage);
    } catch (error) {
      console.error('Error loading more templates:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplateForDialog(template);
    setDialogVisible(true);
  };

  const handleConfirmTemplate = async () => {
    if (selectedTemplateForDialog) {
      // Store the template information without creating media
      setSelectedTemplate(selectedTemplateForDialog);
      setDialogVisible(false);
      navigation.goBack();
    }
  };

  const handleConfirmUpload = (mediaId: string, mediaUrl: string) => {
    setSelectedMedia(
      mediaId,
      mediaUrl
    );
    setDialogVisible(false);
    router.back();
  };

  const uploadFile = async (imagePickerResult: ImagePicker.ImagePickerResult) => {
    if (!userData?.id) {
      throw new Error('User ID is required');
    }

    const imageAsset = imagePickerResult.assets?.[0];

    if (!imageAsset) {
      throw new Error('No image asset selected');
    }

    try {
      console.log('Starting file upload process');
      console.log('Platform:', Platform.OS);

      // Log the image asset details for debugging
      console.log('Image asset details:', {
        uri: imageAsset.uri,
        type: imageAsset.type,
        width: imageAsset.width,
        height: imageAsset.height,
        fileSize: imageAsset.fileSize
      });

      // Create file object exactly like in ProfilePhotoSection
      const file = {
        name: imageAsset.uri.split('/').pop() || `image_${Date.now()}.jpg`,
        type: 'image/jpeg',
        uri: imageAsset.uri,
        size: imageAsset.fileSize || 0
      };

      console.log('Prepared file object:', file);

      // Upload the file to blob storage
      console.log('Uploading file to blob storage...');
      const uploadResult = await uploadFiles([file], 'media');
      console.log('Upload result:', uploadResult);

      if (uploadResult.failures && uploadResult.failures.length > 0) {
        console.error('Upload failures:', uploadResult.failures);
        throw new Error(`Failed to upload file: ${uploadResult.failures[0]?.message || 'Unknown error'}`);
      }

      if (!uploadResult.successful || uploadResult.successful.length === 0) {
        throw new Error('No successful uploads returned from server');
      }

      const uploadedFile = uploadResult.successful[0];
      const blobUrl = uploadedFile.url;
      console.log('File uploaded successfully, blob URL:', blobUrl);

      // Now create a media entry with the blob URL
      console.log('Creating media entry...');
      const response = await createMedia({
        variables: {
          input: {
            url: blobUrl,
            title: file.name
          }
        }
      });

      console.log('Create media response:', response.data);

      if (!response.data?.createMedia?.result?.media) {
        console.error('Failed to create media, response:', response);
        throw new Error('Failed to create media record in database');
      }

      const { id, url } = response.data.createMedia.result.media;
      console.log('Media created successfully with ID:', id);
      return { id, url };

    } catch (error) {
      console.error('Upload error details:', error);

      // Provide more specific error message based on the error
      if (error instanceof Error) {
        // Keep the original error message if it's detailed enough
        throw error;
      } else {
        throw new Error('Failed to upload file: Unknown error');
      }
    }
  };

  const handleUpload = async () => {
    try {
      setIsUploading(true);
      console.log('Starting upload process, checking permissions...');

      // Check for permissions
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      console.log('Permission result:', permissionResult);

      if (!permissionResult.granted) {
        console.warn('Gallery permission denied');
        alert('Permission to access gallery is required!');
        setIsUploading(false);
        return;
      }

      console.log('Launching image picker...');
      // Configure image picker options with platform-specific settings
      const pickerOptions: ImagePicker.ImagePickerOptions = {
        mediaTypes: "images", // Use string value instead of deprecated enum
        allowsMultipleSelection: false,
        quality: Platform.OS === 'android' ? 0.7 : 0.8, // Lower quality on Android to reduce file size
        allowsEditing: true, // Allow user to crop/edit the image
        exif: false, // Don't include EXIF data to reduce file size
      };

      if (Platform.OS === 'android') {
        // Android-specific options
        pickerOptions.base64 = false; // Don't include base64 data
      }

      console.log('Image picker options:', pickerOptions);
      const result = await ImagePicker.launchImageLibraryAsync(pickerOptions);

      console.log('Image picker result:', result.canceled ? 'Canceled' : `Selected ${result.assets?.length} assets`);

      if (!result.canceled && result.assets.length > 0) {
        try {
          console.log('Starting file upload...');
          // Upload the file to blob storage
          const { id, url } = await uploadFile(result);
          console.log('Upload completed successfully, media ID:', id);

          // Set the media with the ID and URL from the server
          setSelectedMedia(id, url);
          console.log('Navigating back...');
          router.back();
        } catch (error) {
          console.error('Upload error in handleUpload:', error);

          // Provide more specific error message to the user
          let errorMessage = 'Failed to upload image. Please try again.';
          if (error instanceof Error) {
            // Add more specific error messages for common issues
            if (error.message.includes('Network request failed')) {
              errorMessage = 'Network connection issue. Please check your internet connection and try again.';
            } else if (error.message.includes('timeout')) {
              errorMessage = 'Upload timed out. Please try again with a smaller image or better connection.';
            } else if (error.message.includes('storage')) {
              errorMessage = 'Failed to store the image. Please try again.';
            } else {
              // Include the actual error message for other cases
              errorMessage = `Upload failed: ${error.message}`;
            }
          }

          alert(errorMessage);
        }
      } else {
        console.log('Image selection canceled or no image selected');
      }
    } catch (error) {
      console.error('Image picker error:', error);

      let errorMessage = 'Failed to open image picker';
      if (error instanceof Error) {
        errorMessage = `Image picker error: ${error.message}`;
      }

      alert(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const renderItem = ({ item }: { item: Template }) => (
    <Animated.View entering={FadeIn}>
      <Card
        style={[styles.card, { width: itemWidth, height: itemHeight }]}
        mode="contained"
        onPress={() => handleTemplateSelect(item)}
        elevation={1 as any}
      >
        <Card.Cover
          source={{ uri: item.imageUrl }}
          style={styles.image}
        />
      </Card>
    </Animated.View>
  );

  return (
    <View style={styles.container}>
      <Stack.Screen options={{
        title: "Templates",
        headerBackVisible: false,
        headerRight: () => (
          <TouchableOpacity onPress={() => navigation.goBack()} style={{ marginRight: Spacing.md }}>
            <Cross size={Icons.size.md} color={Colors.text.primary} />
          </TouchableOpacity>
        ),
        headerStyle: {
          backgroundColor: Colors.background.primary,
        },
        headerTintColor: Colors.text.primary,
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: Typography.fontSize.lg,
        },
      }} />

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Find an image..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          iconColor={Colors.text.secondary}
          placeholderTextColor={Colors.mediumGray}
        />
      </View>

      {tagsLoading ? (
        <TagSkeleton />
      ) : (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.chipContainer}
          contentContainerStyle={styles.chipContent}
        >
          {tags.map((tag) => {
            const isSelected = selectedTag === tag.id;
            return (
              <TouchableOpacity
                key={tag.id}
                activeOpacity={1}
                onPress={() => setSelectedTag(isSelected ? null : tag.id)}
                style={[
                  styles.chip,
                  {
                    backgroundColor: isSelected
                      ? Colors.background.secondary
                      : Colors.white,
                    borderColor: isSelected
                      ? Colors.primary
                      : Colors.border.medium,
                    borderWidth: 1,
                  },
                ]}
              >
                <Text
                  style={[
                    styles.chipText,
                    { color: isSelected ? Colors.primary : Colors.text.secondary },
                  ]}
                >
                  {tag.name}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      )}

      {templatesLoading && page === 0 ? (
        <PhotoSkeleton />
      ) : (
        <>
          <FlatList
            data={templates}
            renderItem={renderItem}
            keyExtractor={(item) => item.id}
            numColumns={numColumns}
            contentContainerStyle={styles.listContentContainer}
            style={styles.list}
            columnWrapperStyle={styles.row}
            ItemSeparatorComponent={() => <View style={{ height: gap }} />}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                tintColor={Colors.primary}
                colors={[Colors.primary, Colors.secondary]}
              />
            }
            onEndReached={loadMoreTemplates}
            onEndReachedThreshold={0.5}
            ListFooterComponent={
              isLoadingMore ? (
                <View style={styles.loadingMoreContainer}>
                  <ActivityIndicator color={Colors.primary} />
                </View>
              ) : null
            }
          />
          <Portal>
            <FAB
              icon="upload"
              label={isUploading ? "Uploading..." : "Upload Template"}
              style={styles.fab}
              mode="elevated"
              onPress={handleUpload}
              disabled={isUploading}
              loading={isUploading}
              color={Colors.black}
            />
          </Portal>
        </>
      )}

      <Portal>
        <Dialog
          visible={dialogVisible}
          onDismiss={() => setDialogVisible(false)}
          style={{
            backgroundColor: Colors.background.primary,
            borderRadius: Borders.radius.lg,
          }}
        >
          <Dialog.Title style={{
            color: Colors.text.primary,
            fontSize: Typography.fontSize.lg,
            fontWeight: Typography.fontWeight.semibold,
          }}>
            Select Template
          </Dialog.Title>
          <Dialog.Content>
            <Text style={{
              color: Colors.text.primary,
              fontSize: Typography.fontSize.md
            }}>
              Do you want to use this template for your invitation?
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button
              onPress={() => setDialogVisible(false)}
              disabled={isUploading}
              textColor={Colors.primary}
              style={{
                borderRadius: Borders.radius.pill,
              }}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleConfirmTemplate}
              loading={isUploading}
              disabled={isUploading}
              buttonColor={Colors.button.primary}
              textColor={Colors.white}
              style={{
                borderRadius: Borders.radius.pill,
              }}
            >
              OK
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  searchContainer: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    marginTop: Spacing.sm,
    marginBottom: Spacing.md,
  },
  searchBar: {
    backgroundColor: Colors.background.tertiary,
    elevation: 0,
    borderRadius: Borders.radius.sm,
    height: 40,
  },
  searchInput: {
    fontSize: Typography.fontSize.sm,
    minHeight: 40,
    color: Colors.text.primary,
  },
  chipContainer: {
    height: 40,
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.md,
  },
  chipContent: {
    gap: Spacing.sm,
  },
  chip: {
    borderRadius: Borders.radius.pill,
    height: 32,
    paddingHorizontal: Spacing.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  chipText: {
    fontSize: Typography.fontSize.sm,
  },
  list: {
    paddingBottom: Spacing.xxxl * 2,
    marginBottom: Spacing.xxxl * 2,
  },
  listContentContainer: {
    paddingBottom: Spacing.xxxl * 2,
    marginBottom: Spacing.xxxl * 2,
  },
  row: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  card: {
    flex: 1,
    margin: 0,
    overflow: 'hidden',
    borderRadius: Borders.radius.sm,
    ...Shadows.sm,
  },
  image: {
    height: '100%',
    width: '100%',
    borderRadius: Borders.radius.sm,
  },
  skeletonContainer: {
    paddingHorizontal: Spacing.md,
    gap: Spacing.sm,
  },
  skeletonRow: {
    flexDirection: 'row',
    gap: Spacing.sm,
    justifyContent: 'space-between',
  },
  skeletonCard: {
    backgroundColor: Colors.background.tertiary,
    borderRadius: Borders.radius.sm,
  },
  skeletonChip: {
    width: 80,
    backgroundColor: Colors.background.tertiary,
  },
  fab: {
    position: 'absolute',
    margin: Spacing.md,
    left: Spacing.md,
    right: Spacing.md,
    bottom: Spacing.md,
    borderRadius: Borders.radius.md,
    backgroundColor: Colors.button.primary,
  },
  loadingMoreContainer: {
    paddingVertical: Spacing.md,
    alignItems: 'center',
  },
});