import { useState, useCallback } from 'react';
import { useUserStore } from '@/app/auth/userStore';
import { useApolloClient } from '@apollo/client';

export interface UseUserDataReturn {
  userData: any;
  isRefreshing: boolean;
  refreshUserData: () => Promise<void>;
  refreshError: string | null;
}

/**
 * Custom hook for managing user data with refresh capabilities
 * 
 * This hook provides:
 * - Current user data from the store
 * - Ability to refresh user data from the server
 * - Loading states and error handling
 * 
 * Usage:
 * ```tsx
 * const { userData, isRefreshing, refreshUserData, refreshError } = useUserData();
 * 
 * // To refresh user data
 * await refreshUserData();
 * ```
 */
export const useUserData = (): UseUserDataReturn => {
  const apolloClient = useApolloClient();
  const userData = useUserStore((state) => state.userData);
  const refreshUserDataFromServer = useUserStore((state) => state.refreshUserDataFromServer);
  const isStoreRefreshing = useUserStore((state) => state.isRefreshing);
  
  const [localRefreshing, setLocalRefreshing] = useState(false);
  const [refreshError, setRefreshError] = useState<string | null>(null);

  const refreshUserData = useCallback(async () => {
    if (!userData?.id) {
      setRefreshError('No user ID available for refresh');
      return;
    }

    setLocalRefreshing(true);
    setRefreshError(null);

    try {
      await refreshUserDataFromServer(apolloClient, userData.id);
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to refresh user data';
      setRefreshError(errorMessage);
      throw error;
    } finally {
      setLocalRefreshing(false);
    }
  }, [userData?.id, refreshUserDataFromServer, apolloClient]);

  return {
    userData,
    isRefreshing: isStoreRefreshing || localRefreshing,
    refreshUserData,
    refreshError,
  };
};

/**
 * Hook for automatic user data refresh on focus
 * 
 * This hook automatically refreshes user data when the screen comes into focus.
 * Useful for screens that should always show the latest user information.
 * 
 * @param enabled - Whether auto-refresh is enabled (default: true)
 * @param refreshInterval - Minimum time between refreshes in milliseconds (default: 30000 = 30 seconds)
 */
export const useAutoRefreshUserData = (enabled: boolean = true, refreshInterval: number = 30000) => {
  const { userData, isRefreshing, refreshUserData, refreshError } = useUserData();
  const [lastRefreshTime, setLastRefreshTime] = useState<number>(0);

  const autoRefresh = useCallback(async () => {
    if (!enabled || isRefreshing) return;
    
    const now = Date.now();
    if (now - lastRefreshTime < refreshInterval) return;

    try {
      await refreshUserData();
      setLastRefreshTime(now);
    } catch (error) {
      // Error is already handled by useUserData hook
    }
  }, [enabled, isRefreshing, refreshUserData, lastRefreshTime, refreshInterval]);

  return {
    userData,
    isRefreshing,
    refreshUserData,
    refreshError,
    autoRefresh,
  };
};
