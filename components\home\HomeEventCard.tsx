import React from 'react';
import { StyleSheet, View, Image, TouchableOpacity } from 'react-native';
import { Text } from 'react-native-paper';
import { Colors } from '@/constants/Colors';

interface HomeEventCardProps {
  id: string;
  title: string;
  date: string;
  responses: {
    yes: number;
    no: number;
    noReply: number;
  };
  imageUrl: string;
  onPress?: () => void;
}

export function HomeEventCard({ title, date, responses, imageUrl, onPress }: HomeEventCardProps) {
  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <View style={styles.cardContent}>
        <View style={styles.imageContainer}>
          <Image 
            source={{ uri: imageUrl }} 
            style={styles.image} 
            resizeMode="cover"
          />
        </View>
        <View style={styles.content}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.date}>{date}</Text>
          </View>
          <View style={styles.responsesContainer}>
            <View style={styles.responseRow}>
              <Text style={[styles.responseCount, styles.yesResponse]}>{responses.yes}</Text>
              <Text style={styles.responseText}>YES</Text>
            </View>
            <View style={styles.responseRow}>
              <Text style={[styles.responseCount, styles.noResponse]}>{responses.no}</Text>
              <Text style={styles.responseText}>NO</Text>
            </View>
            <View style={styles.responseRow}>
              <Text style={[styles.responseCount, styles.noReplyResponse]}>{responses.noReply}</Text>
              <Text style={styles.responseText}>NO REPLY</Text>
            </View>
          </View>
        </View>
      </View>
      <TouchableOpacity style={styles.addGuestButton}>
        <Text style={styles.addGuestText}>+ ADD GUEST</Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    width: 260,
    aspectRatio: 1.2,
    backgroundColor: 'white',
    borderRadius: 8,
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  cardContent: {
    flex: 1,
    flexDirection: 'row',
    padding: 16,
    gap: 16,
  },
  imageContainer: {
    flex: 1,
    height: '100%',
    borderRadius: 4,
    overflow: 'hidden',
    maxWidth: '45%',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  content: {
    flex: 1,
  },
  titleContainer: {
    marginBottom: 12,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  date: {
    fontSize: 12,
    color: Colors.light.textSecondary,
  },
  responsesContainer: {
    gap: 8,
  },
  responseRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  responseCount: {
    fontSize: 12,
    fontWeight: '500',
    width: 30,
    textAlign: 'right',
  },
  responseText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.light.text,
  },
  yesResponse: {
    color: '#4CAF50',
  },
  noResponse: {
    color: '#F44336',
  },
  noReplyResponse: {
    color: '#FFA726',
  },
  addGuestButton: {
    borderTopWidth: 0.5,
    borderTopColor: Colors.light.textSecondary,
    padding: 16,
    alignItems: 'center',
  },
  addGuestText: {
    color: '#2196F3',
    fontSize: 12,
    fontWeight: '500',
  },
}); 