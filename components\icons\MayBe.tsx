import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const MayBe = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#May_be-1_svg__a)">
      <Path
        fill="url(#May_be-1_svg__b)"
        fillRule="evenodd"
        d="M11.5 6a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0M5.018 4.625A.982.982 0 1 1 6 5.607a.59.59 0 0 0-.59.59v.507a.59.59 0 0 0 1.18 0 2.162 2.162 0 1 0-2.75-*********** 0 1 0 1.178 0M6.786 8.75a.786.786 0 1 1-1.572 0 .786.786 0 0 1 1.572 0"
        clipRule="evenodd"
      />
    </G>
    <Defs>
      <LinearGradient
        id="May_be-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.828}
        y2={14.029}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="May_be-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#May_be_svg__a)"
    >
      <Path d="M6 11A5 5 0 1 0 6 1a5 5 0 0 0 0 10" />
      <Path d="M4.654 4.654a1.345 1.345 0 0 1 1.61-1.32 1.348 1.348 0 0 1 .982 1.835 1.35 1.35 0 0 1-1.245.83v.898M6 8.66a.192.192 0 1 1 0-.384M6 8.66a.192.192 0 1 0 0-.384" />
    </G>
    <Defs>
      <ClipPath id="May_be_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default MayBe;
