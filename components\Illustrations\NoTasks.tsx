import * as React from "react";
import Svg, { <PERSON>, <PERSON>, De<PERSON>, <PERSON>arGradient, Stop } from "react-native-svg";
/* SVGR has dropped some elements not supported by react-native-svg: filter */
import type { SvgProps } from "react-native-svg";
const SvgNoTasks = (props: SvgProps) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={300}
    height={300}
    fill="none"
    viewBox="0 0 300 300"
    {...props}
  >
    <Path
      fill="url(#No_tasks_svg__a)"
      d="M52.754 97.162a118.7 118.7 0 0 1-40.078 10.74c1.876 4.361 15.208 26.195 54.784 44.184 1.07.165 25.783-11.332 39.346-19.216-.461.659-42.128-14.673-54.052-35.708"
    />
    <Path
      fill="#FCD9BD"
      d="M53.73 108.017a2.27 2.27 0 0 0-1.989-.294l-22.088 7.146a1.175 1.175 0 0 0-.541 1.87c.429.515 1.13.711 1.764.494l22.652-7.759a.824.824 0 0 0 .203-1.457M53.348 116.819a2.09 2.09 0 0 0-2.093-.456l-15.292 5.146a1.016 1.016 0 0 0-.46 1.608c.37.449.978.622 1.528.435l16.042-5.462a.76.76 0 0 0 .275-1.271M61.556 121.177a2.15 2.15 0 0 0-2.075-.342l-17.614 6.402a1.024 1.024 0 0 0-.395 1.664c.386.409.98.549 1.507.355l18.358-6.737a.78.78 0 0 0 .218-1.342M67.714 134.391a1.96 1.96 0 0 0-2.15-.515l-10.066 3.841a1 1 0 0 0-.448 1.529 1.376 1.376 0 0 0 1.603.465l10.79-4.179a.712.712 0 0 0 .27-1.141M233.5 264.73c12.426 0 22.5-.672 22.5-1.5 0-.829-10.074-1.5-22.5-1.5s-22.5.671-22.5 1.5c0 .828 10.074 1.5 22.5 1.5"
    />
    <Path
      fill="url(#No_tasks_svg__b)"
      d="M143.905 152.602c5.043 34.486 8.445 57.729 24.872 66.579 6.549 3.524 16.137 5.295 25.287 4.146 2.94-.367 8.985-1.131 14.786-4.697 8.35-5.136 12.007-13.505 16.861-28.728 7.064-22.177 10.596-39.853 11.609-45.03 4.101-20.945 6.092-36.896 6.91-43.921a558 558 0 0 0 3.04-36.464h-98.944c-2.489 19.15-4.972 38.294-7.461 57.444a329 329 0 0 0 3.04 30.683z"
    />
    <Path
      fill="url(#No_tasks_svg__c)"
      d="M157.683 123.973c4.909.449 8.78 4.477 12.551 10.193 2.992 4.536 3.718 8.865 4.402 13.224 2.49 15.833 3.729 23.747 4.131 28.093 2.094 22.962 1.74 28.242 5.777 36.634a51.4 51.4 0 0 0 7.429 11.292c-37.701.366-75.408.732-113.108 1.105a1608 1608 0 0 1 23.391-64.728c3.782-9.804 8.155-20.724 19.266-28.242 8.675-5.87 17.898-7.264 23.946-7.565h12.221z"
    />
    <Path
      fill="url(#No_tasks_svg__d)"
      d="M16.865 249.643h94.018a59.2 59.2 0 0 0 12.921-11.577c3.979-4.804 9.09-12.563 13.198-28.661 3.396-13.313 3.908-23.732 4.704-39.711.896-18.065-1.297-25.995 3.572-36.662 1.857-4.066 5.429-8.681 12.344-9.059h-84.44c-3.42 0-8.153.827-10.552 3.274-2.747 2.801-5.216 6.595-6.725 11.612-4.457 14.773-3.024 23.171-5.771 47.955-1.592 14.366-3.325 28.601-12.373 42.441-6.761 10.342-15.173 16.742-20.89 20.394z"
    />
    <Path
      fill="#63B3ED"
      d="M138.04 152.602c5.043 34.486 8.445 57.729 24.873 66.579 6.548 3.524 16.137 5.295 25.287 4.146 2.939-.367 8.984-1.131 14.786-4.697 8.35-5.136 12.006-13.505 16.86-28.728 7.064-22.177 10.596-39.853 11.609-45.03 4.101-20.945 6.092-36.896 6.91-43.921a558 558 0 0 0 3.04-36.464h-98.944c-2.489 19.15-4.972 38.294-7.461 57.444a329 329 0 0 0 3.04 30.683z"
    />
    <Path
      fill="#fff"
      d="M151.818 123.973c4.91.449 8.781 4.477 12.551 10.193 2.992 4.536 3.718 8.865 4.402 13.224 2.49 15.833 3.73 23.747 4.131 28.093 2.095 22.962 1.741 28.242 5.777 36.634a51.4 51.4 0 0 0 7.429 11.292c-37.701.366-75.407.732-113.108 1.105a1608 1608 0 0 1 23.391-64.728c3.783-9.804 8.155-20.724 19.266-28.242 8.675-5.87 17.898-7.264 23.946-7.565h12.221z"
    />
    <Path
      fill="url(#No_tasks_svg__e)"
      d="M151.818 123.973c4.91.449 8.781 4.477 12.551 10.193 2.992 4.536 3.718 8.865 4.402 13.224 2.49 15.833 3.73 23.747 4.131 28.093 2.095 22.962 1.741 28.242 5.777 36.634a51.4 51.4 0 0 0 7.429 11.292c-37.701.366-75.407.732-113.108 1.105a1608 1608 0 0 1 23.391-64.728c3.783-9.804 8.155-20.724 19.266-28.242 8.675-5.87 17.898-7.264 23.946-7.565h12.221z"
    />
    <Path
      fill="#fff"
      d="M11 249.643h94.019a59.2 59.2 0 0 0 12.921-11.577c3.978-4.804 9.089-12.563 13.198-28.661 3.395-13.313 3.908-23.732 4.703-39.711.896-18.065-1.296-25.995 3.573-36.662 1.856-4.066 5.428-8.681 12.343-9.059h-84.44c-3.419 0-8.152.827-10.551 3.274-2.747 2.801-5.217 6.595-6.726 11.612-4.456 14.773-3.024 23.171-5.771 47.955-1.591 14.366-3.325 28.601-12.373 42.441-6.76 10.342-15.172 16.742-20.89 20.394z"
    />
    <Path
      fill="url(#No_tasks_svg__f)"
      d="M11 249.643h94.019a59.2 59.2 0 0 0 12.921-11.577c3.978-4.804 9.089-12.563 13.198-28.661 3.395-13.313 3.908-23.732 4.703-39.711.896-18.065-1.296-25.995 3.573-36.662 1.856-4.066 5.428-8.681 12.343-9.059h-84.44c-3.419 0-8.152.827-10.551 3.274-2.747 2.801-5.217 6.595-6.726 11.612-4.456 14.773-3.024 23.171-5.771 47.955-1.591 14.366-3.325 28.601-12.373 42.441-6.76 10.342-15.172 16.742-20.89 20.394z"
    />
    <Path fill="#fff" d="m163 75.73-10.677-10L147 74.762z" />
    <Path fill="url(#No_tasks_svg__g)" d="m163 75.73-10.677-10L147 74.762z" />
    <Path
      fill="#F4862B"
      d="M151.532 69.98c5.703-10.78 5.227-22.217-1.063-25.545-6.291-3.327-16.013 2.714-21.716 13.494s-5.227 22.218 1.063 25.545c6.291 3.328 16.013-2.713 21.716-13.494"
    />
    <Path
      fill="url(#No_tasks_svg__h)"
      d="M151.532 69.98c5.703-10.78 5.227-22.217-1.063-25.545-6.291-3.327-16.013 2.714-21.716 13.494s-5.227 22.218 1.063 25.545c6.291 3.328 16.013-2.713 21.716-13.494"
    />
    <Path
      fill="#fff"
      d="M127.921 57.276c3.844-7.265 3.524-14.973-.715-17.216-4.239-2.242-10.791 1.83-14.635 9.096s-3.523 14.973.715 17.216c4.239 2.242 10.791-1.83 14.635-9.096"
    />
    <Path
      fill="#fff"
      d="m113.777 65.377 12.969-24.514c.295-.559.99-.773 1.549-.477l10.282 5.44c6.908 3.653 9.548 12.225 5.894 19.132-4.141 7.827-13.858 10.824-21.689 6.681l-8.454-4.472a1.32 1.32 0 0 1-.551-1.79"
    />
    <Path
      fill="url(#No_tasks_svg__i)"
      d="M127.921 57.276c3.844-7.265 3.524-14.973-.715-17.216-4.239-2.242-10.791 1.83-14.635 9.096s-3.523 14.973.715 17.216c4.239 2.242 10.791-1.83 14.635-9.096"
    />
    <Path
      fill="url(#No_tasks_svg__j)"
      d="m113.777 65.377 12.969-24.514c.295-.559.99-.773 1.549-.477l10.282 5.44c6.908 3.653 9.548 12.225 5.894 19.132-4.141 7.827-13.858 10.824-21.689 6.681l-8.454-4.472a1.32 1.32 0 0 1-.551-1.79"
    />
    <G stroke="#fff" strokeMiterlimit={10} strokeWidth={2} opacity={0.89}>
      <Path d="M224.565 78.722h-7.666a8.984 8.984 0 0 0-8.984 8.985v1.88a8.984 8.984 0 0 0 8.984 8.983h7.666a8.984 8.984 0 0 0 8.984-8.984v-1.88a8.984 8.984 0 0 0-8.984-8.984ZM218.276 111.095h-7.666a8.985 8.985 0 0 0-8.985 8.985v1.879a8.985 8.985 0 0 0 8.985 8.985h7.666a8.985 8.985 0 0 0 8.984-8.985v-1.879a8.985 8.985 0 0 0-8.984-8.985ZM209.907 143.463h-7.666a8.985 8.985 0 0 0-8.985 8.984v1.88a8.985 8.985 0 0 0 8.985 8.984h7.666a8.984 8.984 0 0 0 8.984-8.984v-1.88a8.984 8.984 0 0 0-8.984-8.984ZM197.086 177.308h-7.666a8.984 8.984 0 0 0-8.984 8.984v1.88a8.984 8.984 0 0 0 8.984 8.984h7.666a8.985 8.985 0 0 0 8.985-8.984v-1.88a8.985 8.985 0 0 0-8.985-8.984ZM117.161 138.545h-7.666a8.985 8.985 0 0 0-8.985 8.984v1.88a8.985 8.985 0 0 0 8.985 8.984h7.666a8.984 8.984 0 0 0 8.984-8.984v-1.88a8.984 8.984 0 0 0-8.984-8.984ZM111.279 169.594h-7.666a8.984 8.984 0 0 0-8.984 8.984v1.88a8.984 8.984 0 0 0 8.984 8.984h7.666a8.985 8.985 0 0 0 8.985-8.984v-1.88a8.985 8.985 0 0 0-8.985-8.984ZM201.626 88.646h-48.291M194.131 121.019h-25.086M189.721 153.387h-18.193M95.995 146.536H54.92M87.176 180.996H76.702M69.532 180.996H50.233" />
    </G>
    <Path
      stroke="url(#No_tasks_svg__k)"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="M222.889 77.884h-7.666a8.984 8.984 0 0 0-8.984 8.985v1.88a8.984 8.984 0 0 0 8.984 8.984h7.666a8.985 8.985 0 0 0 8.985-8.985v-1.88a8.985 8.985 0 0 0-8.985-8.984Z"
    />
    <Path
      stroke="url(#No_tasks_svg__l)"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="M216.6 110.258h-7.666a8.984 8.984 0 0 0-8.984 8.984v1.879a8.985 8.985 0 0 0 8.984 8.985h7.666a8.985 8.985 0 0 0 8.985-8.985v-1.879a8.985 8.985 0 0 0-8.985-8.984Z"
    />
    <Path
      stroke="url(#No_tasks_svg__m)"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="M208.231 142.625h-7.666a8.984 8.984 0 0 0-8.984 8.984v1.88a8.984 8.984 0 0 0 8.984 8.984h7.666a8.985 8.985 0 0 0 8.985-8.984v-1.88a8.985 8.985 0 0 0-8.985-8.984Z"
    />
    <Path
      stroke="url(#No_tasks_svg__n)"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="M195.41 176.47h-7.666a8.984 8.984 0 0 0-8.984 8.984v1.88a8.984 8.984 0 0 0 8.984 8.984h7.666a8.985 8.985 0 0 0 8.985-8.984v-1.88a8.985 8.985 0 0 0-8.985-8.984Z"
    />
    <Path
      stroke="url(#No_tasks_svg__o)"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="M115.485 137.707h-7.666a8.984 8.984 0 0 0-8.984 8.984v1.88a8.984 8.984 0 0 0 8.984 8.984h7.666a8.984 8.984 0 0 0 8.984-8.984v-1.88a8.984 8.984 0 0 0-8.984-8.984Z"
    />
    <Path
      stroke="url(#No_tasks_svg__p)"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="M109.604 168.756h-7.667a8.985 8.985 0 0 0-8.984 8.985v1.879a8.984 8.984 0 0 0 8.984 8.984h7.667a8.984 8.984 0 0 0 8.984-8.984v-1.879a8.985 8.985 0 0 0-8.984-8.985Z"
    />
    <Path
      stroke="url(#No_tasks_svg__q)"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="M199.95 87.809h-48.291"
    />
    <Path
      stroke="url(#No_tasks_svg__r)"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="M192.455 120.182H167.37"
    />
    <Path
      stroke="url(#No_tasks_svg__s)"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="M188.046 152.549h-18.193"
    />
    <Path
      stroke="url(#No_tasks_svg__t)"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="M94.319 145.698H53.245"
    />
    <Path
      stroke="url(#No_tasks_svg__u)"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="M85.5 180.158H75.026"
    />
    <Path
      stroke="url(#No_tasks_svg__v)"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="M67.856 180.158H48.558"
    />
    <Path
      fill="#fff"
      d="M242.055 262.724s-5.89-5.473-3.473-10.201 5.406 5.437 5.406 5.437-1.114-11.607 2.771-12.21c3.891-.609.53 12.648.53 12.648s1.009-3.056 3.084-1.608-1.663 5.715-1.663 5.715l-6.661.225zM144.055 261.723s-5.89-5.794-3.473-10.8 5.406 5.757 5.406 5.757-1.114-12.29 2.771-12.929c3.891-.644.53 13.392.53 13.392s1.009-3.235 3.084-1.702-1.663 6.051-1.663 6.051l-6.661.238z"
    />
    <Path
      fill="url(#No_tasks_svg__w)"
      d="M242.055 262.724s-5.89-5.473-3.473-10.201 5.406 5.437 5.406 5.437-1.114-11.607 2.771-12.21c3.891-.609.53 12.648.53 12.648s1.009-3.056 3.084-1.608-1.663 5.715-1.663 5.715l-6.661.225z"
    />
    <Path
      fill="url(#No_tasks_svg__x)"
      d="M144.055 261.723s-5.89-5.794-3.473-10.8 5.406 5.757 5.406 5.757-1.114-12.29 2.771-12.929c3.891-.644.53 13.392.53 13.392s1.009-3.235 3.084-1.702-1.663 6.051-1.663 6.051l-6.661.238z"
    />
    <Path
      fill="#34434D"
      d="M246.409 218.304c1.388-5.262-2.379-10.818-8.414-12.41s-12.053 1.382-13.441 6.644c-1.389 5.261 2.378 10.818 8.413 12.41s12.053-1.382 13.442-6.644"
    />
    <Path
      fill="#34434D"
      d="M225.132 223.89c-1.892 3.192-.237 7.672 3.7 10.007 3.936 2.335 8.659 1.643 10.55-1.548 1.106-1.862.686-4.02.556-4.64-.727-3.411-3.665-5.054-4.25-5.367-3.18-1.679-8.559-1.809-10.551 1.548z"
    />
    <Path
      fill="#34434D"
      d="M237.328 234.879c.222-1.613-2.092-3.264-5.167-3.687-3.076-.422-5.749.544-5.971 2.158-.221 1.613 2.092 3.264 5.168 3.687 3.076.422 5.748-.544 5.97-2.158"
    />
    <Path
      fill="#34434D"
      d="M226.743 234.568s4.116-2.651 4.589-2.728c2.394-.378 8.429.059 11.863 5.61 2.814 4.545 2.063 8.316 3.18 10.804-2.352.089-7.962 5.911-16.219-.372-4.279-3.257-6.147-8.405-4.149-12.235zM239.726 207.541c2.518-5.828 8.416-7.489 14.174-5.556l-.479 1.082c-4.882-2.542-11.55-1.07-13.181 4.663l-.514-.195zM235.476 208.723c-1.017-6.2 3.57-10.787 9.398-11.863l.124 1.177c-5.615.331-10.373 4.746-9.221 10.639l-.307.053z"
    />
    <Path
      stroke="#34434D"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="m230.584 234.73-.682 13.742L232 261.73h-3M234 234.73l1.004 13.5 4.996 13.5h-4.101"
    />
    <Path fill="#F4862B" d="M227 224.73h10.34l4.66 13.289-10.543.711z" />
    <Path
      stroke="#34434D"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="m236 233.73 9.668-.215c.562-.135.381-.602-.321-.817l-7.68-9.968M226 223.73l-6 7.276 5.007 6.724"
    />
    <Path
      fill="#34434D"
      stroke="#34434D"
      strokeMiterlimit={10}
      d="M224.569 240.659c1.052-1.196 1.534-2.493 1.075-2.896-.458-.403-1.683.24-2.735 1.436s-1.534 2.492-1.075 2.896c.458.403 1.683-.24 2.735-1.436ZM236.235 233.623c.252-.672-.752-1.671-2.244-2.231-1.491-.56-2.905-.469-3.157.203s.752 1.671 2.243 2.231c1.492.56 2.905.469 3.158-.203Z"
    />
    <Path
      fill="#FCD9BD"
      d="M139.5 264.73c38.384 0 69.5-.672 69.5-1.5 0-.829-31.116-1.5-69.5-1.5s-69.5.671-69.5 1.5c0 .828 31.116 1.5 69.5 1.5"
    />
    <Path
      fill="#34434D"
      d="M80.668 87.192c5.81-1.533 9.437-6.885 8.1-11.954-1.338-5.07-7.133-7.936-12.944-6.403-5.81 1.533-9.437 6.885-8.1 11.955 1.338 5.069 7.133 7.935 12.944 6.402"
    />
    <Path
      fill="#34434D"
      d="M88.228 86.158c1.82 3.073.23 7.388-3.565 9.634-3.788 2.246-8.34 1.579-10.166-1.495-1.064-1.797-.662-3.872-.532-4.469.703-3.286 3.529-4.87 4.096-5.166 3.062-1.613 8.245-1.743 10.167 1.496"
    />
    <Path
      fill="#34434D"
      d="M82.22 98.828c2.962-.407 5.19-1.998 4.977-3.554s-2.789-2.487-5.751-2.08-5.19 1.998-4.977 3.554 2.788 2.486 5.75 2.08"
    />
    <Path
      fill="#34434D"
      d="M87.382 97.489s-4.675-3.6-5.13-3.67c-2.306-.367-8.116.058-11.426 5.402-2.707 4.379-1.986 8.015-3.062 10.408 2.264.083 7.666 5.698 15.622-.36 4.12-3.139 5.917-8.098 3.996-11.786zM73.663 70.595c-1.56-5.527-7.991-6.934-12.678-4.48l-.48-1.082c3.86-1.283 8.495-1.023 11.42 2.039.952.952 1.685 2.092 2.252 3.328zM77.96 71.5c1.118-5.67-3.475-9.925-8.877-10.227l.124-1.176c3.96.769 7.914 3.358 8.884 7.46.336 1.3.36 2.665.177 3.99l-.308-.054z"
    />
    <Path
      stroke="#34434D"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="m82.884 96.73.91 13.228L81 122.73h4M80 96.73l-1.006 12.997L74 122.73h4.101"
    />
    <Path
      stroke="#34434D"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="m76 87.73-6 7.504 5.455 3.496M86 86.73l6 7.65-4.382 6.35"
    />
    <Path
      fill="#34434D"
      stroke="#34434D"
      strokeMiterlimit={10}
      d="M91.408 103.675c.441-.388-.023-1.636-1.036-2.788s-2.193-1.771-2.634-1.383.023 1.636 1.036 2.788 2.193 1.772 2.634 1.383Z"
    />
    <G filter="url(#No_tasks_svg__y)">
      <Path
        fill="#fff"
        d="M252.544 47.73A110.9 110.9 0 0 0 290 57.767c-1.753 4.076-14.213 24.481-51.201 41.294-.999.154-24.096-10.59-36.772-17.959.431.615 39.372-13.713 50.517-33.372"
      />
    </G>
    <Path
      fill="url(#No_tasks_svg__z)"
      d="M252.544 47.73A110.9 110.9 0 0 0 290 57.767c-1.753 4.076-14.213 24.481-51.201 41.294-.999.154-24.096-10.59-36.772-17.959.431.615 39.372-13.713 50.517-33.372"
    />
    <Path
      fill="#FCD9BD"
      d="M251.631 57.875a2.12 2.12 0 0 1 1.859-.275l20.644 6.679c.74.24 1.003 1.15.505 1.747-.4.481-1.056.665-1.648.462l-21.171-7.251a.77.77 0 0 1-.189-1.362M245.85 63.373a1.95 1.95 0 0 1 1.956-.426l14.292 4.81a.95.95 0 0 1 .43 1.502c-.345.42-.914.581-1.428.406l-14.993-5.104a.71.71 0 0 1-.257-1.188M239.911 67.937a2 2 0 0 1 1.939-.32l16.462 5.983a.956.956 0 0 1 .369 1.555 1.31 1.31 0 0 1-1.409.332l-17.156-6.296a.73.73 0 0 1-.205-1.254M226.545 77.068a1.83 1.83 0 0 1 2.008-.482l9.408 3.59c.578.22.786.931.419 1.429a1.285 1.285 0 0 1-1.498.435l-10.084-3.906a.665.665 0 0 1-.253-1.066M232.686 72.192a2.13 2.13 0 0 1 1.861-.196l21.678 7.926a.97.97 0 0 1 .305 1.641 1.34 1.34 0 0 1-1.339.248l-22.361-8.238a.774.774 0 0 1-.144-1.381"
    />
    <Defs>
      <LinearGradient
        id="No_tasks_svg__a"
        x1={59.741}
        x2={59.9}
        y1={152.088}
        y2={86.814}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__b"
        x1={194.067}
        x2={192.886}
        y1={223.676}
        y2={34.5}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__c"
        x1={135.419}
        x2={134.975}
        y1={224.514}
        y2={105.031}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__d"
        x1={87.243}
        x2={86.686}
        y1={249.649}
        y2={100.296}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__e"
        x1={129.554}
        x2={129.111}
        y1={224.514}
        y2={105.031}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__f"
        x1={81.378}
        x2={80.822}
        y1={249.649}
        y2={94.831}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__g"
        x1={155}
        x2={154.969}
        y1={75.73}
        y2={63.846}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__h"
        x1={151.532}
        x2={124.426}
        y1={69.98}
        y2={55.725}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__i"
        x1={127.921}
        x2={109.655}
        y1={57.276}
        y2={47.67}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__j"
        x1={144.029}
        x2={115.678}
        y1={65.794}
        y2={50.931}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__k"
        x1={219.056}
        x2={218.98}
        y1={97.733}
        y2={73.282}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__l"
        x1={212.767}
        x2={212.691}
        y1={130.106}
        y2={105.655}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__m"
        x1={204.398}
        x2={204.322}
        y1={162.473}
        y2={138.022}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__n"
        x1={191.577}
        x2={191.501}
        y1={196.318}
        y2={171.867}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__o"
        x1={111.652}
        x2={111.576}
        y1={157.555}
        y2={133.104}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__p"
        x1={105.771}
        x2={105.694}
        y1={188.604}
        y2={164.154}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__q"
        x1={175.805}
        x2={175.805}
        y1={88.808}
        y2={87.577}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__r"
        x1={179.913}
        x2={179.913}
        y1={121.182}
        y2={119.95}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__s"
        x1={178.949}
        x2={178.949}
        y1={153.549}
        y2={152.317}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__t"
        x1={73.782}
        x2={73.782}
        y1={146.698}
        y2={145.466}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__u"
        x1={80.263}
        x2={80.262}
        y1={181.158}
        y2={179.926}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__v"
        x1={58.207}
        x2={58.207}
        y1={181.158}
        y2={179.926}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__w"
        x1={244.5}
        x2={244.5}
        y1={241.295}
        y2={262.73}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#fff" />
        <Stop offset={0.671} stopColor="#80D6AF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#00AC5E" stopOpacity={0.7} />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__x"
        x1={146.5}
        x2={146.5}
        y1={239.034}
        y2={261.73}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#fff" />
        <Stop offset={0.671} stopColor="#80D6AF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#00AC5E" stopOpacity={0.7} />
      </LinearGradient>
      <LinearGradient
        id="No_tasks_svg__z"
        x1={246.014}
        x2={245.865}
        y1={99.063}
        y2={38.058}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default SvgNoTasks;
