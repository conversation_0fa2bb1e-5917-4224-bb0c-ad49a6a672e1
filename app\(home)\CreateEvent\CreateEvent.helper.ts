
import { CreateEventData, CreateEventInput } from './CreateEvent.models';




export function transformEventDataToMutationInput(eventData: CreateEventData): CreateEventInput {
  return {
    input: {
      coHosts: eventData.cohosts.length > 0
        ? eventData.cohosts.map(cohost => ({
            firstName: cohost?.firstName ,
            lastName: cohost?.lastName || '',
            email: cohost.email || '',
            phone: cohost.phoneNumber || ''
          }))
        : [],
      description: eventData.name || "", 
      eventType: eventData.eventType?.id || "",
      location: eventData.locationId,
      name: eventData.name || "",
      // date: eventData.date ? eventData.date.toISOString() : ""
    }
  };
} 
