import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '@react-navigation/native';
import { Share2, Heart } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';

interface VendorHeaderProps {
    vendorName: string;
}

export function VendorHeader({ vendorName }: VendorHeaderProps) {
    const { colors } = useTheme();

    return (
        <View style={[styles.container]}>
            <Text style={[styles.vendorName, { color: colors.text }]}>{vendorName? vendorName : ''}</Text>
            <View style={[styles.iconContainer]}>
                <Share2 style={styles.icon}/>
                <Heart style={styles.icon}/>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        paddingBottom: 4,
        backgroundColor: 'transparent',
    },
    vendorName: {
        fontSize: 28,
        fontWeight: 'bold',
        flex: 1,
    },
    iconContainer: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'flex-start',
        paddingTop: 8,
        marginLeft: 4,
        marginRight: -16,
    },
    icon: {
        marginRight: 16,
        color: Colors.light.text,
    },
});