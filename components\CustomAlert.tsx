import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal } from 'react-native';

type CustomAlertProps = {
    visible: boolean;
    onClose: () => void;
    onConfirm: () => void;
    message: string;
};

const CustomAlert: React.FC<CustomAlertProps> = ({ visible, onClose, onConfirm, message }) => {
    return (
        <Modal
            visible={visible}
            transparent
            animationType="fade"
            statusBarTranslucent
        >
            <View style={styles.overlay}>
                <View style={styles.container}>
                    <Text style={styles.message}>
                        {message}
                    </Text>

                    <View style={styles.buttonRow}>
                        <TouchableOpacity onPress={onConfirm} style={styles.yesButton}>
                            <Text style={styles.yesText}>YES</Text>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={onClose} style={styles.noButton}>
                            <Text style={styles.noText}>NO</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
};

export default CustomAlert;

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.3)',
    },
    container: {
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 24,
        marginHorizontal: 20,
        alignItems: 'center',
    },
    message: {
        fontSize: 16,
        color: '#000',
        textAlign: 'center',
        marginBottom: 24,
    },
    buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: 16,
    },
    yesButton: {
        borderWidth: 1,
        borderColor: '#F44336',
        paddingVertical: 10,
        paddingHorizontal: 34,
        borderRadius: 8,
    },
    yesText: {
        color: '#F44336',
        fontWeight: '600',
    },
    noButton: {
        backgroundColor: '#F0F0F0',
        paddingVertical: 10,
        paddingHorizontal: 34,
        borderRadius: 8,
    },
    noText: {
        color: '#000',
        fontWeight: '600',
    },
});

