import { gql } from "@apollo/client";


export const GET_EVENT_EVENTS = gql`
query GetMdEventTypes($pagination: PaginationInput) {
  getMdEventTypes(pagination: $pagination) {
    ... on MdEventTypesResponse {
      result {
        mdEventTypes {
          name
          id
          partyTypes {
            id
            name
          }
        }
      }
    }
  }
}
`;

export const CREATE_EVENT = gql`
mutation CreateEvent($input: EventInput!) {
  createEvent(input: $input) {
    ... on EventResponse {
      result {
        event {
          id
          name
        }
      }
    }
  }
}
`;
export const GET_EVENT_TYPES_BY_ID = gql`
query GetMdEventTypeById($getMdEventTypeByIdId: ID!) {
  getMdEventTypeById(id: $getMdEventTypeByIdId) {
    ... on MdEventTypeResponse {
      result {
        mdEventType {
          partyTypes {
            id
            name
            vendorTypes {
              id
              name
            }
          }
        }
      }
    }
  }
}
`;
