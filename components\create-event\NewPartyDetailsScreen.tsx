import React, { useState, useEffect, useMemo, useRef } from 'react';
import { View, Text, StyleSheet, SafeAreaView, ActivityIndicator, RefreshControl, TouchableOpacity, Alert, Switch, Pressable, Platform, Modal } from 'react-native';
import * as Location from 'expo-location';
import * as Clipboard from 'expo-clipboard';
import { useQuery, useMutation } from '@apollo/client';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useNavigation, NavigationProp, useFocusEffect } from '@react-navigation/native';
import { Card } from 'react-native-paper';
import MapWeather from '../map-weather/MapWeather';
import ActivityFeed from './ActivityFeed/ActivityFeed';
import { GET_PARTY_DETAILS, GET_UPDATED_MESSAGES, GET_MEDIA_FOLDERS_FOR_EVENT } from '../../app/(home)/(tabs)/party-dashboard/partydetails.data';
import { Activity, NewPartyDetailsResult, Rsvp } from '@/app/(home)/PartyDetails/NewPartyDetailsResponse.model';
import { formatDateString } from '@/app/(home)/utils/reusableFunctions';
import { StackScreenProps } from '@react-navigation/stack';
import { PartyDetailsRootList } from './Navigation/PartyDetailsRootList';
import { useUserStore } from '@/app/auth/userStore'
import { UPDATE_LIVE_LOCATIONS } from '../map-weather/MapData/LiveLocation/LiveLocation.data';
import { HOME_PAGE_DEFAULT_IMAGES } from '@/constants/HomePageDefaultImages';
import { router } from 'expo-router';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';
import { isHost } from '@/app/(home)/utils/reusableFunctions';
import { HomeRootStackList } from '@/app/Home/HomeNavigation';
import { ADDRESS_BOOK_QUERY } from '@/graphql/queries/addressBook';
import { AddressBook } from '@/app/(home)/EventsDashboard/AddParty';
import { DueDate, Location as LocationIcon, Hosts, Invite, Photos, Tasks, Edit, Calender, CircleCheck, MoreVertical, BackArrow } from '../icons';
import { Borders, Colors, Icons, Typography, Shadows, Spacing } from '@/constants/DesignSystem';
import SomethingWentWrong from '@/components/Illustrations/SomethingWentWrong';
import { BottomSheetModal, BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { PartyOptionsBottomSheet } from '@/components/UI/ReusableComponents/PartyOptionsBottomSheet';
import { MUTE_PARTY, UNMUTE_PARTY } from '@/graphql/mutations/muteEvent';
import { useToast } from '@/components/Toast/useToast';
import { Dialog, Portal, Button, Snackbar } from 'react-native-paper';
import { DELETE_EVENT, RSVP_EVENT } from '@/components/HomePage/HomePage.data';
import { SuccessModal } from '../shared/SuccessModal';

type NewPartyDetailsScreenProps = StackScreenProps<PartyDetailsRootList, 'NewPartyDetailsScreen'> ;

const NewPartyDetailsScreen: React.FC<NewPartyDetailsScreenProps> = ({ route }) => {
    const [refreshing, setRefreshing] = useState(false);
    const [isLiveTrackingEnabled, setIsLiveTrackingEnabled] = useState(false);
    const [locationSubscription, setLocationSubscription] = useState<Location.LocationSubscription | null>(null);
    const [activities, setActivities] = useState<Activity[]>([]);
    const [isNetworkError, setIsNetworkError] = useState(false);
    const [retryCount, setRetryCount] = useState(0);
    const navigation = useNavigation<NavigationProp<HomeRootStackList>>();
    const { partyId, isHosting, rsvp, fromSearch, fromCreate } = route.params;
    const [sendLocation] = useMutation(UPDATE_LIVE_LOCATIONS);
    const optionsSheetRef = useRef<BottomSheetModal>(null);
    const [isMuted, setIsMuted] = useState(false);
    const [isMuteDialogVisible, setIsMuteDialogVisible] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const toast = useToast();
    const [deleteEvent] = useMutation(DELETE_EVENT);
    const [rsvpEvent] = useMutation(RSVP_EVENT);
    const [isDeleteOrNotGoingDialogVisible, setIsDeleteOrNotGoingDialogVisible] = useState(false);
    const [deleteOrNotGoingLoading, setDeleteOrNotGoingLoading] = useState(false);
    const [deleteOrNotGoingType, setDeleteOrNotGoingType] = useState<'delete' | 'notgoing'>('delete');
    const [isCopyingLink, setIsCopyingLink] = useState(false);
    const [snackbarVisible, setSnackbarVisible] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');
    const [isSuccessModalVisible, setIsSuccessModalVisible] = useState(false);

    const [muteParty] = useMutation(MUTE_PARTY, {
        variables: { input: { partyId } },
        onCompleted: async () => {
            setIsMuted(true);
            await refetch();
        },
        onError: (error) => {
            setIsMuted(false);
            Alert.alert('Error', error.message || 'Failed to mute party');
        }
    });

    const [unmuteParty] = useMutation(UNMUTE_PARTY, {
        variables: { input: { partyId } },
        onCompleted: async () => {
            setIsMuted(false);
            await refetch();
        },
        onError: (error) => {
            setIsMuted(true);
            Alert.alert('Error', error.message || 'Failed to unmute party');
        }
    });
    const [isLocationModalVisible, setLocationModalVisible] = useState(false);

    const handleBack = () => {
        if (fromCreate) {
            navigation.navigate('HomePage');
        } else {
            navigation.goBack();
        }
    };

    const checkInternetConnection = async () => {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch('https://www.google.com/generate_204', {
                method: 'HEAD',
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            return response.status === 204;
        } catch (error) {
            return false;
        }
    };
    const [addressBook, setAddressBook] = useState<AddressBook | undefined>(undefined);

    const { loading, error, data, refetch } = useQuery(GET_PARTY_DETAILS, {
        variables: { getPartyByIdId: partyId, },
        fetchPolicy: 'network-only',
        skip: !partyId,
        onError: async (error) => {
            if (error.networkError) {
                const isConnected = await checkInternetConnection();
                setIsNetworkError(!isConnected);
            }
        },
        onCompleted: () => {
            setIsNetworkError(false);
            setRetryCount(0);
        }
    });

    useEffect(() => {
       if (!isHosting && !fromCreate && rsvp?.toLowerCase() !== "accepted" && rsvp?.toLowerCase() !== "maybe") {
        navigation.navigate('PartyDetails', {
            screen: 'RsvpScreen',
            params: { partyId: partyId, fromSearch: fromSearch }
          });
       } else {
         refetch();
       }
    }, [isHosting, rsvp, partyId, navigation, refetch]);
    
   

    const { data: addressBookData, loading: addressBookLoading } = useQuery(ADDRESS_BOOK_QUERY, {
        variables: {
            filter: {
                venueAddressId: data?.getPartyById?.result?.party?.venueAddress?.id
            }
        },
        skip: !data?.getPartyById?.result?.party?.venueAddress?.id
    });

    useEffect(() => {
        if (addressBookData?.getAddressBooks?.result?.addressBooks) {
            setAddressBook(addressBookData.getAddressBooks.result.addressBooks[0]);
        }
    }, [addressBookData]);

    const { loading: updateLoading, error: updateError, data: updatedData, refetch: refetchUpdatedMessages } = useQuery(GET_UPDATED_MESSAGES, {
        variables: { getPartyByIdId: partyId },
        fetchPolicy: 'network-only',
        skip: !partyId,
    });



    const eventId = data?.getPartyById?.result?.party?.event?.id;

    const { data: mediaFoldersData } = useQuery(GET_MEDIA_FOLDERS_FOR_EVENT, {
        variables: {
            filter: {
                eventId: eventId
            }
        },
    });

    const refreshMessages = async (messageId: string, commentText: string) => {
        try {
            console.log('Refreshing:')
            const response = await refetchUpdatedMessages();
            console.log('Refreshing:', response)
            setActivities((prevActivities) =>
                prevActivities.map((item) =>
                    item.id === messageId
                        ? { ...item, text: commentText }
                        : item
                )
            );

            const updatedPartyDetails = response?.data?.getPartyById?.result;
            if (updatedPartyDetails?.party?.activity) {
                console.log('Updated messages fetched successfully');
                setActivities(updatedPartyDetails.party.activity);
            } else {
                console.warn('No activity data found in the updated response');
            }
            console.log('Refreshing: Success')
        } catch (error) {
            console.error('Error fetching updated messages:', error);
            console.log('Refreshing: Failure')
        }
    }

    const handleEventReceived = (data: any) => {
        console.log('Real-time update received:', data);

        // Check if the data contains activity updates
        if (data && data.type === 'activity_added' && data.activity) {
            console.log('New activity received:', data.activity);
            setActivities(prevActivities => [data.activity, ...prevActivities]);
        } else if (data && data.type === 'activity_updated' && data.activity) {
            console.log('Activity updated:', data.activity);
            setActivities(prevActivities =>
                prevActivities.map(item =>
                    item.id === data.activity.id ? data.activity : item
                )
            );
        } else if (data && data.type === 'activity_deleted' && data.activityId) {
            console.log('Activity deleted:', data.activityId);
            setActivities(prevActivities =>
                prevActivities.filter(item => item.id !== data.activityId)
            );
        } else if (data && data.type === 'party_updated') {
            console.log('Party updated, refreshing data');
            refetch();
        } else if (data && data.type === 'rsvp_updated') {
            console.log('RSVP updated, refreshing data');
            refetch();
        } else {
            // For any other updates, just refresh the data
            refetch();
        }
    };

    // NOTE: Server-Sent Events (SSE) implementation was causing 404 errors
    // The server doesn't have an endpoint at /events/{resourceId}
    // Instead, we're using a polling mechanism to periodically refresh data
    // To re-enable SSE in the future:
    // 1. Import useEventStream from '@/services/eventStreamService'
    // 2. Replace this polling implementation with:
    //    const { isConnected: isStreamConnected } = useEventStream(
    //        partyId ?? '',
    //        handleEventReceived,
    //        {
    //            eventTypes: [...],
    //            autoReconnect: true,
    //            reconnectDelay: 3000,
    //            maxReconnectAttempts: 10
    //        }
    //    );
    const [isStreamConnected, setIsStreamConnected] = useState(false);

    useEffect(() => {
        if (!partyId) return;

        console.log('Setting up polling for party updates');
        let pollingInterval: NodeJS.Timeout;
        let retryTimeout: NodeJS.Timeout;

        const startPolling = () => {
            pollingInterval = setInterval(async () => {
                try {
                    const response = await refetch();
                    const updatedPartyDetails = response?.data?.getPartyById?.result;
                    if (updatedPartyDetails?.party?.activity) {
                        const newActivities = updatedPartyDetails.party.activity;
                        const existingActivityIds = new Set(activities.map(a => a.id));
                        const brandNewActivities = newActivities.filter((activity: Activity) => !existingActivityIds.has(activity.id));

                        if (brandNewActivities.length > 0) {
                            console.log('New activities detected:', brandNewActivities.length);
                            setActivities(newActivities);
                        }
                    }
                    setIsNetworkError(false);
                    setRetryCount(0);
                } catch (err) {
                    const isConnected = await checkInternetConnection();
                    setIsNetworkError(!isConnected);
                    
                    // Implement exponential backoff for retries
                    if (retryCount < 3) {
                        const backoffTime = Math.min(1000 * Math.pow(2, retryCount), 15000);
                        retryTimeout = setTimeout(() => {
                            setRetryCount(prev => prev + 1);
                            startPolling();
                        }, backoffTime);
                    }
                }
            }, 15000);
        };

        startPolling();

        return () => {
            clearInterval(pollingInterval);
            clearTimeout(retryTimeout);
        };
    }, [partyId, activities.length, retryCount]);

    useFocusEffect(
        React.useCallback(() => {
            refetch();
        }, [refetch])
    );
    let lastSentTimestamp = 0;

    const getGuestId = () => {
        if (!partyDetails?.party?.rsvps || !userData?.id) return null;
        const rsvp = partyDetails.party.rsvps.find(
            (rsvp: Rsvp) => rsvp.guest?.user?.id === userData.id
        );
        return rsvp?.guest?.id || null;
    };

    const onLocation = async (isEnabled: boolean) => {
        setIsLiveTrackingEnabled(isEnabled);

        if (isEnabled) {
            const { status } = await Location.requestForegroundPermissionsAsync();
            if (status !== 'granted') {
                Alert.alert('Permission Denied', 'Location permission is required for live tracking.');
                setIsLiveTrackingEnabled(false);
                return;
            }

            const subscription = await Location.watchPositionAsync(
                {
                    accuracy: Location.Accuracy.High,
                    timeInterval: 5000,
                    distanceInterval: 1,
                },
                (location) => {
                    const currentTimestamp = Date.now();
                    if (currentTimestamp - lastSentTimestamp > 5000) {
                        lastSentTimestamp = currentTimestamp;
                        console.log('Location updated:', location.coords);
                        sendCoordinatesToBackend(location.coords);
                    } else {
                        console.log('Update ignored to throttle excessive calls');
                    }
                }
            );

            setLocationSubscription(subscription);
        } else {
            console.log('Live tracking disabled');
            if (locationSubscription) {
                locationSubscription.remove();
                setLocationSubscription(null);
            }
        }
    };

    const sendCoordinatesToBackend = async (coords: { latitude: number; longitude: number }) => {
        try {
            console.log('UserID backend:', getGuestId());
            const { data } = await sendLocation({
                variables: {
                    guestId: getGuestId() ?? '',
                    input: {
                        allowLiveTracking: 'ALLOW',
                        coordinates: {
                            latitude: coords.latitude,
                            longitude: coords.longitude
                        }
                    },
                },
            });

            if (data.updateLiveLocation.__typename === 'LiveLocationResponse') {
                console.log('Coordinates sent successfully:', data.updateLiveLocation.message);
            } else {
                console.error('Error sending coordinates:', data.updateLiveLocation.message);
            }
        } catch (error) {
            console.error('Failed to send coordinates to backend:', error);
        }
    };

    useEffect(() => {
        return () => {
            if (locationSubscription) {
                console.log('Cleaning up location subscription');
                locationSubscription.remove();
            }
        };
    }, [locationSubscription]);

    const onRefresh = async () => {
        setRefreshing(true);
        try {
            const response = await refetch();
            const updatedPartyDetails = response?.data?.getPartyById?.result;
            if (updatedPartyDetails?.party?.activity) {
                console.log('Pull-to-refresh: Updated activities fetched successfully');
                setActivities(updatedPartyDetails.party.activity);
            } else {
                console.warn('Pull-to-refresh: No activity data found in the updated response');
            }
        } catch (error) {
            console.error('Pull-to-refresh: Error fetching updated messages:', error);
        } finally {
            setRefreshing(false);
        }
    };

    const onInvite = () => {
        console.log('Invite button pressed');
        navigation.navigate('PartyDetails', {
            screen: 'InvitesView',
            params: { partyDetails: partyDetails?.party ?? undefined }
        });
    }

    const onEdit = () => {
        console.log("Tapped on edit button");
        if (!partyDetails?.party.id) {
            console.error('Party ID is undefined');
            return;
        }
      
        const isCohost = partyDetails?.party.coHosts.some((cohost: { userId: { id: string } }) => cohost.userId.id === userData?.id);
        navigation.navigate('AddPartyScreen', {
            screen: 'EditParty',
            params: { partyId: partyDetails?.party.id , isCohost: isCohost }
        });
    }

    const onTasks = () => {
        navigation.navigate('PartyDetails', {
            screen: 'TasksScreen'
        });
    }

    const onPhotos = () => {
        navigation.navigate('PartyDetails', {
            screen: 'PhotosAlbumView',
            params: {
                albumId: mediaFoldersData?.getMediaFolders?.result?.mediaFolders[0]?.id,
                albumType: 'ALBUM'
            }
        });
    };
    

    const partyDetails = useMemo(() => {
        const partyDataWrapper = data?.getPartyById?.result;
        if (!partyDataWrapper || !partyDataWrapper.party) {
            return partyDataWrapper || undefined;
        }

        const augmentedParty = {
            ...partyDataWrapper.party,
            addressBook: addressBook, 
        };

        return {
            ...partyDataWrapper,
            party: augmentedParty,
        } as NewPartyDetailsResult;
    }, [data, addressBook]);

    useEffect(() => {
        if (partyDetails?.party?.activity) {
            setActivities(partyDetails.party.activity);
        }
    }, [partyDetails]);

    const [rsvpStatus, setrsvpStatus] = useState("RSVP");
    const userData = useUserStore((state) => state.userData);
    const userId = userData?.id;
    const mainHostId = partyDetails?.party?.event?.mainHost?.userId?.id;
    const coHostIds = partyDetails?.party?.coHosts?.map((co: { userId: { id: string } }) => co.userId.id) || [];
    const isHostOrCoHost = userId === mainHostId || coHostIds.includes(userId);

    useEffect(() => {
        if (!partyDetails?.party) return;
        if (partyDetails != null && !loading) {
            const rsvp = partyDetails.party.rsvps.find(
                (rsvp: Rsvp) => rsvp.guest?.user.id === userId
            );
            if (rsvp) {
                setrsvpStatus(rsvp.status === 'ACCEPTED' ? 'Going' : 'Maybe');
            }
        }
    }, [partyDetails, loading, userId]);

    const handleMakeFlyer = () => {
        optionsSheetRef.current?.dismiss();
        navigation.navigate('PartyDetails', {
            screen: 'FlyerScreen',
            params: {
                image: partyDetails.party.invitation.media[0]?.url || '',
                date: partyDetails.party.time || '',
                venue: partyDetails?.party?.addressBook?.label || '',
                name: partyDetails.party.name || '',
                address: partyDetails.party.venueAddress?.address || '',
                partyId: partyDetails.party.id || '',
            }
        });
    };

    useEffect(() => {
        if (partyDetails?.party) {
            setIsMuted(partyDetails.party.muted || false);
        }
    }, [partyDetails]);

    const handleMuteEvent = () => {
        if (!partyId) return;
        optionsSheetRef.current?.dismiss();
        setIsMuteDialogVisible(true);
    };


    const confirmMuteAction = async () => {
        try {
            setIsLoading(true);
            hideMuteDialog();

            if (isMuted) {
                await unmuteParty();
                Alert.alert('Success', 'Party unmuted successfully');
            } else {
                await muteParty();
                Alert.alert('Success', 'Party muted successfully');
            }
        } catch (err: any) {
            Alert.alert('Error', err.message || 'An error occurred while updating notification preferences');
        } finally {
            setIsLoading(false);
        }
    };

    const hideMuteDialog = () => {
        setIsMuteDialogVisible(false);
    };

    const isCurrentUserHost = isHost(partyDetails?.party?.userRole);
    const currentUserRsvp = partyDetails?.party?.rsvps?.find((rsvp: Rsvp) => rsvp.guest?.user?.id === userId);

    function handleDeleteOrNotGoing() {
        if (isCurrentUserHost) setDeleteOrNotGoingType('delete');
        else setDeleteOrNotGoingType('notgoing');
        optionsSheetRef.current?.dismiss();
        setIsDeleteOrNotGoingDialogVisible(true);
    }

    async function confirmDeleteOrNotGoing() {
        setDeleteOrNotGoingLoading(true);
        try {
            if (deleteOrNotGoingType === 'delete') {
                await deleteEvent({ variables: { deleteEventId: partyDetails?.party?.event?.id } });
                toast.success('Event deleted successfully');
                navigation.goBack();
            } else {
                if (!currentUserRsvp) throw new Error('RSVP not found');
                const response = await rsvpEvent({ 
                    variables: { 
                        updateInvitationRsvpId: currentUserRsvp.id, 
                        input: { status: 'REJECTED' } 
                    } 
                });
                
                if (response.data?.updateInvitationRsvp?.message) {
                    toast.success(response.data.updateInvitationRsvp.message);
                } else {
                    toast.success('Successfully marked as not going');
                }
                
                navigation.goBack();
            }
            setIsDeleteOrNotGoingDialogVisible(false);
        } catch (err: any) {
            toast.error(err.message || 'An error occurred');
        } finally {
            setDeleteOrNotGoingLoading(false);
        }
    }

    function hideDeleteOrNotGoingDialog() {
        setIsDeleteOrNotGoingDialogVisible(false);
    }

    const handleCopyLink = async () => {
        if (!partyId) return;
        
        setIsCopyingLink(true);
        try {
            const partyLink = `fastparty://party/${partyId}`;
            await Clipboard.setStringAsync(partyLink);
            setSnackbarMessage('Party link copied to clipboard');
            setSnackbarVisible(true);
            optionsSheetRef.current?.dismiss();
        } catch (error) {
            setSnackbarMessage('Failed to copy link');
            setSnackbarVisible(true);
        } finally {
            setIsCopyingLink(false);
        }
    };

    const onDismissSnackbar = () => setSnackbarVisible(false);

    useEffect(() => {
        if (route.params?.showSuccessAnimation) {
            setIsSuccessModalVisible(true);
            setTimeout(() => setIsSuccessModalVisible(false), 5000);
        }
    }, [route.params?.showSuccessAnimation]);

    if (loading) {
        return (
            <View style={styles.container}>
                <FastPartyActivityIndicator />
            </View>
        );
    }

    if (error) {
        if (error.networkError || isNetworkError) {
            return (
                <View style={styles.somethingWentWrongContainer}>
                    <SomethingWentWrong />
                    <Text style={styles.somethingWentWrongText}>Oops! Something went wrong.</Text>
                    <TouchableOpacity 
                        style={styles.retryButton}
                        onPress={async () => {
                            const isConnected = await checkInternetConnection();
                            if (isConnected) {
                                setRetryCount(0);
                                refetch();
                            }
                        }}
                    >
                        <Text style={styles.retryButtonText}>Retry</Text>
                    </TouchableOpacity>
                </View>
            );
        }
        return (
            <View style={styles.container}>
                <Text>Error: {error.message}</Text>
            </View>
        );
    }

    // Ensure data is valid
    if (!data || !data.getPartyById || data.getPartyById.__typename !== 'PartyResponse') {
        return (
            <View style={styles.container}>
                <Text>No data found</Text>
            </View>
        );
    }

    if (!partyDetails || !partyDetails.party) {
        return (
            <View style={styles.container}>
                <Text>No party data found</Text>
            </View>
        );
    }

    const onGuests = () => {
        console.log("Tapped on Save to Button")
    }

    const onRSVP = () => {
        console.log("Tapped on RSVP Button")
        console.log('Navigating to rsvp with partyId:', partyId);
        navigation.navigate('PartyDetails', {
            screen: 'RsvpScreen',
            params: { partyId }
        });
    }
    const imageUrl = partyDetails.party.invitation.media[0]?.url;
    const dateString = String(partyDetails.party.time);

    return (
        <BottomSheetModalProvider>
            <SafeAreaView style={{ flex: 1 }}>
                <View style={styles.customHeader}>
                    <Pressable onPress={handleBack} style={styles.headerButton}>
                        <BackArrow size={Icons.size.md} color={Colors.primary} />
                    </Pressable>
                    <TouchableOpacity onPress={() => optionsSheetRef.current?.present()} accessibilityRole="button" accessibilityLabel="Show options" style={styles.headerButton}>
                        <MoreVertical size={Icons.size.md} color={Colors.primary} />
                    </TouchableOpacity>
                </View>
                <KeyboardAwareScrollView
                    style={styles.container}
                    contentContainerStyle={{ paddingBottom: 70 }}
                    refreshControl={
                        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
                    }
                >
                    <View style={styles.container}>
                        <View style={styles.row}>
                            <Card.Cover source={imageUrl ? { uri: imageUrl } : HOME_PAGE_DEFAULT_IMAGES[0]} style={styles.image} />
                            <View style={styles.textContainer}>
                                <View style={styles.subContainer}>
                                    <Text style={styles.name}>
                                        {partyDetails.party.name}
                                    </Text>
                                    <View style={[styles.row]}>
                                        <DueDate size={Icons.size.md} color={Colors.text.tertiary} />
                                        <Text style={styles.date}>{formatDateString(dateString, 'd MMM, h:mm a')}</Text>
                                    </View>
                                </View>
                                <View style={styles.subContainer}>
                                    <View style={styles.innerContainer}>
                                        <LocationIcon size={Icons.size.md} color={Colors.text.tertiary} />
                                        <Text style={styles.locationText} numberOfLines={1}>{partyDetails?.party?.addressBook?.label || 'Location TBD'}</Text>
                                        <TouchableOpacity onPress={() => setLocationModalVisible(true)} style={styles.infoButton}>
                                            <View style={styles.infoIconContainer}>
                                                <Text style={styles.infoIconText}>i</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View style={styles.innerContainer}>
                                        <Hosts size={Icons.size.md} color={Colors.text.tertiary} />
                                        <Text style={styles.hostedByText}>Hosted by {partyDetails.party.event.mainHost.userId.firstName}</Text>
                                    </View>
                                </View>
                                <View style={styles.buttonContainer}>
                                    {partyDetails.party.userRole === 'GUEST' ? (
                                        <View style={styles.row}>
                                            <TouchableOpacity
                                                style={[
                                                    styles.roundedButton,
                                                    (rsvpStatus === "Going" || rsvpStatus === "Maybe") && { backgroundColor: 'orange' }
                                                ]}
                                                onPress={onRSVP}
                                            >
                                                {rsvpStatus === "Going" ? (
                                                    <CircleCheck size={Icons.size.md} color={Colors.black} />
                                                ) : rsvpStatus === "Maybe" ? (
                                                    <CircleCheck size={Icons.size.md} color={Colors.black} />
                                                ) : null}
                                                <Text style={[styles.buttonText, styles.horizontalPadding]}>{rsvpStatus}</Text>
                                            </TouchableOpacity>

                                            <TouchableOpacity style={styles.roundedButton} onPress={onGuests}>
                                                <Calender size={Icons.size.md} color={Colors.text.tertiary} />
                                                <Text style={[styles.buttonText, styles.horizontalPadding]}>Add To</Text>
                                            </TouchableOpacity>
                                        </View>
                                    ) : (
                                        <>
                                            <TouchableOpacity style={styles.roundedButton} onPress={onEdit}>
                                                <Edit size={Icons.size.md} color={Colors.text.tertiary} />
                                                <Text style={[styles.buttonText, styles.horizontalPadding]}>Edit</Text>
                                            </TouchableOpacity>

                                            <TouchableOpacity style={styles.roundedButton} onPress={onGuests}>
                                                <Calender size={Icons.size.md} color={Colors.text.tertiary} />
                                                <Text style={[styles.buttonText, styles.horizontalPadding]}>Add To</Text>
                                            </TouchableOpacity>
                                        </>
                                    )}
                                </View>
                            </View>
                        </View>

                        <MapWeather party={partyDetails} />
                        {updateLoading ? 
                        <FastPartyActivityIndicator/> : 
                        <ActivityFeed
                            activities={activities}
                            partyId={partyDetails.party.id}
                            userRole={partyDetails.party.userRole}
                            onLocation={onLocation}
                            refreshMessages={refreshMessages}
                            venueAddress={partyDetails.party.venueAddress}
                        />}
                    </View>
                </KeyboardAwareScrollView>

                <View style={styles.floatingBar}>
                    <TouchableOpacity style={styles.button} onPress={onInvite}>
                        <Invite size={Icons.size.md} color={Colors.primary} />
                        <Text style={styles.buttonText}>{isHost(partyDetails.party.userRole) ? 'Invite' : 'Guests'}</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.button} onPress={onPhotos}>
                        <Photos size={Icons.size.md} color={Colors.primary} />
                        <Text style={styles.buttonText}>Photos</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.button} onPress={onTasks}>
                        <Tasks size={Icons.size.md} color={Colors.primary} />
                        <Text style={styles.buttonText}>Tasks</Text>
                    </TouchableOpacity>
                </View>
                <PartyOptionsBottomSheet
                    ref={optionsSheetRef}
                    onClose={() => {}}
                    onMakeFlyer={handleMakeFlyer}
                    canMakeFlyer={isHostOrCoHost}
                    onMuteEvent={handleMuteEvent}
                    isMuted={isMuted}
                    isHost={isCurrentUserHost}
                    onDelete={handleDeleteOrNotGoing}
                    onNotGoing={handleDeleteOrNotGoing}
                    deleteOrNotGoingLoading={deleteOrNotGoingLoading}
                    deleteOrNotGoingDisabled={deleteOrNotGoingLoading}
                    onCopyLink={handleCopyLink}
                    copyLinkLoading={isCopyingLink}
                />
                <Portal>
                    <Dialog visible={isMuteDialogVisible} onDismiss={hideMuteDialog}>
                        <Dialog.Title>{isMuted ? 'Unmute Event' : 'Mute Event'}</Dialog.Title>
                        <Dialog.Content>
                            <Text>
                                {isMuted 
                                    ? 'Are you sure you want to unmute this event? You will receive notifications for this event.'
                                    : 'Are you sure you want to mute this event? You will not receive notifications for this event.'}
                            </Text>
                        </Dialog.Content>
                        <Dialog.Actions>
                            <Button onPress={hideMuteDialog}>Cancel</Button>
                            <Button 
                                onPress={confirmMuteAction}
                                loading={isLoading}
                                disabled={isLoading}
                                textColor={isMuted ? Colors.primary : Colors.error}
                            >
                                {isMuted ? 'Unmute' : 'Mute'}
                            </Button>
                        </Dialog.Actions>
                    </Dialog>
                </Portal>
                <Portal>
                    <Dialog visible={isDeleteOrNotGoingDialogVisible} onDismiss={hideDeleteOrNotGoingDialog}>
                        <Dialog.Title>{isCurrentUserHost ? 'Delete Event' : 'Not Going'}</Dialog.Title>
                        <Dialog.Content>
                            <Text>
                                {isCurrentUserHost
                                    ? 'Are you sure you want to delete this event? This action cannot be undone.'
                                    : 'Are you sure you want to mark yourself as not going to this event?'}
                            </Text>
                        </Dialog.Content>
                        <Dialog.Actions>
                            <Button onPress={hideDeleteOrNotGoingDialog}>Cancel</Button>
                            <Button
                                onPress={confirmDeleteOrNotGoing}
                                loading={deleteOrNotGoingLoading}
                                disabled={deleteOrNotGoingLoading}
                                textColor={Colors.error}
                            >
                                {isCurrentUserHost ? 'Delete' : 'Confirm'}
                            </Button>
                        </Dialog.Actions>
                    </Dialog>
                </Portal>
                <Snackbar
                    visible={snackbarVisible}
                    onDismiss={onDismissSnackbar}
                    duration={3000}
                    style={styles.snackbar}
                >
                    {snackbarMessage}
                </Snackbar>
            </SafeAreaView>
            <Modal
                animationType="slide"
                transparent={true}
                visible={isLocationModalVisible}
                onRequestClose={() => {
                    setLocationModalVisible(!isLocationModalVisible);
                }}
            >
                <View style={styles.centeredView}>
                    <View style={styles.modalView}>
                        <Text style={styles.modalTitle}>Location Details</Text>
                        <Text style={styles.modalText}>
                            {partyDetails?.party?.venueAddress?.address || 'No address available'}
                        </Text>
                        <Text style={styles.modalText}>
                           Additional Details: {partyDetails?.party?.venueAddress?.directions || 'N/A'}
                        </Text>
                        <Pressable
                            style={[styles.buttonModal, styles.buttonClose]}
                            onPress={() => setLocationModalVisible(!isLocationModalVisible)}
                        >
                            <Text style={styles.textStyle}>Close</Text>
                        </Pressable>
                    </View>
                </View>
            </Modal>
            <SuccessModal
                visible={isSuccessModalVisible}
                onDismiss={() => setIsSuccessModalVisible(false)}
                message="Party created successfully!"
                videoSource={require('../../assets/videos/Success.mp4')}
            />
        </BottomSheetModalProvider>
    );
};


const styles = StyleSheet.create({
    customHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: Spacing.lg,
        paddingVertical: Spacing.md,
        borderBottomWidth: Borders.width.thin,
        borderColor: Colors.border.medium,
        backgroundColor: Colors.white,
        zIndex: 1,
        marginTop: Platform.OS === 'android' ? 30 : 0,
    },
    headerButton: {
        padding: Spacing.sm,
    },
    somethingWentWrongContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    container: {
        flex: 1,
        padding: 16,
        backgroundColor: 'white',
        gap: 10
    },
    subContainer: {
        flexDirection: 'column',
        justifyContent: 'center',
        gap: 4,
        alignItems: 'flex-start'
    },
    innerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4
    },
    locationText: {
        fontSize: 14,
        flexShrink: 1,
    },
    hostedByText: {
        fontSize: 14
    },
    streamIndicatorContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        alignSelf: 'flex-end',
    },
    streamIndicator: {
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: '#4CAF50',
        marginRight: 4,
    },
    streamIndicatorText: {
        fontSize: 12,
        color: '#4CAF50',
        fontFamily: 'Plus Jakarta Sans',
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    horizontalPadding: {
        marginHorizontal: 10,
    },
    bottomPadding: {
        marginBottom: 10,
        marginVertical: 5
    },
    bottomPadding2Px: {
        marginBottom: 5,
    },
    verticalPadding: {
        paddingVertical: 20,
    },
    verticalPaddingSmall: {
        paddingVertical: 5,
    },
    image: {
        resizeMode: 'cover',
        borderRadius: 10,
        flex: 0.4,
        backgroundColor: "white",
    },
    textContainer: {
        flex: 0.6,
        marginLeft: 10,
        flexDirection: 'column',
        alignItems: 'flex-start',
        justifyContent: 'center',
        gap: 15
    },
    somethingWentWrongText: {
        paddingTop: 10,
        paddingBottom: 10,
        fontFamily: 'Plus Jakarta Sans',
        fontSize: 24,
        fontWeight: '500',
        color: 'black',
    },
    name: {
        fontFamily: 'Plus Jakarta Sans',
        fontSize: 22,
        fontWeight: Typography.fontWeight.bold,
    },
    subheading: {
        fontFamily: 'Plus Jakarta Sans',
        fontSize: 18,
        fontWeight: '500',
    },
    date: {
        fontFamily: 'Plus Jakarta Sans',
        fontSize: 16,
        color: 'black',
        marginLeft: 5,
        fontWeight: Typography.fontWeight.bold
    },
    floatingBar: {
        position: 'absolute',
        bottom: 16,
        left: 16,
        right: 16,
        height: 70,
        backgroundColor: 'white',
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
        borderRadius: 35,
        paddingHorizontal: 30,
        shadowColor: 'black',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
        elevation: 2,
    },
    button: {
        alignItems: 'center',
        fontWeight: Typography.fontWeight.semibold,
    },
    roundedButton: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: 'black',
        paddingVertical: 4,
        paddingHorizontal: 8,
        borderRadius: 20,
        marginHorizontal: 5,
    },
    buttonContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    buttonText: {
        color: 'black',
        fontSize: 12,
        fontFamily: 'Plus Jakarta Sans',
    },
    icon: {
        marginRight: 8,
    },
    retryButton: {
        marginTop: 10,
        padding: 10,
        backgroundColor: '#43A5BE',
        borderRadius: 5,
        alignItems: 'center',
    },
    retryButtonText: {
        color: 'white',
        fontWeight: '600',
    },
    snackbar: {
        marginBottom: Platform.OS === 'ios' ? 40 : 20,
    },
    centeredView: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
    modalView: {
        margin: 20,
        backgroundColor: "white",
        borderRadius: 20,
        padding: 35,
        alignItems: "center",
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 2
        },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
        width: '80%',
    },
    buttonModal: {
        borderRadius: 20,
        padding: 10,
        elevation: 2,
        marginTop: 15,
    },
    buttonClose: {
        backgroundColor: Colors.primary,
        paddingHorizontal: 20,
    },
    textStyle: {
        color: "white",
        fontWeight: "bold",
        textAlign: "center"
    },
    modalTitle: {
        marginBottom: 15,
        textAlign: "center",
        fontSize: Typography.fontSize.xl,
        fontWeight: Typography.fontWeight.bold
    },
    modalText: {
        marginBottom: 15,
        textAlign: "center",
        fontSize: Typography.fontSize.md,
    },
    infoButton: {
        marginLeft: Spacing.sm,
    },
    infoIconContainer: {
        width: 20,
        height: 20,
        borderRadius: 10,
        backgroundColor: Colors.primary,
        justifyContent: 'center',
        alignItems: 'center',
    },
    infoIconText: {
        color: Colors.white,
        fontWeight: 'bold',
        fontSize: 12,
    }
});

export default NewPartyDetailsScreen;
