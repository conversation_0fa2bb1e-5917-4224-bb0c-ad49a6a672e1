import * as Linking from 'expo-linking';

export const linkingConfig = {
  prefixes: [Linking.createURL('/')],
  config: {
    screens: {
      '(home)': {
        screens: {
          'party-dashboard': {
            path: 'party/:partyId',
            parse: {
              partyId: (id: string) => id,
            },
          },
          'new-party': {
            path: 'new-party',
          },
          'rsvp': {
            path: 'rsvp/:partyId',
            parse: {
              partyId: (id: string) => id,
            },
          },
        },
      },
      'sign-in': 'sign-in',
      'sign-up': 'sign-up',
    },
  },
};

export function useDeepLinking() {
  const handleDeepLink = async (url: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        console.log("Cannot open URL: " + url);
      }
    } catch (error) {
      console.error('Error handling deep link:', error);
    }
  };

  const generateDeepLink = (path: string, params?: Record<string, string>) => {
    const baseUrl = Linking.createURL('/');
    const queryString = params
      ? '?' + Object.entries(params)
          .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
          .join('&')
      : '';
    return `${baseUrl}${path}${queryString}`;
  };

  return {
    handleDeepLink,
    generateDeepLink,
  };
} 