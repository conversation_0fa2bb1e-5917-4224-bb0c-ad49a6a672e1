import { gql } from '@apollo/client';

export interface ArticleSource {
  link: string;
  mainUrl: string;
}

export interface Article {
  id: string;
  headline: string;
  description: string | null;
  image: string;
  source: ArticleSource;
  date: string; 
  userArticleActivity: {
    liked: boolean | null;
    saved: boolean | null;
    shared: boolean | null;
    articleId: { id: string };
  }
}

export interface PaginationInput {
  limit: number | null;
  skip: number | null;
}

export interface ArticleFilter {
  search: string | null;
}

// Define the possible response types for getArticles query
interface ArticlesResponse {
  __typename: 'ArticlesResponse';
  status: string;
  message: string;
  result: {
    __typename: 'ArticlesResult'; // Add typename for nested objects too
    articles: Article[];
  };
  pagination: {
    __typename: 'PaginationInfo';
    totalItems: number;
    totalPages: number;
    pageSize: number;
    currentPage: number;
  };
}

interface ArticleErrorResponse {
  __typename: 'ArticleErrorResponse';
  status: string;
  message: string;
}

// Update GetArticlesData to reflect the union
export interface GetArticlesData {
  getArticles: ArticlesResponse | ArticleErrorResponse; // Union type
}

export const GET_ARTICLES = gql`
  query GetArticles($pagination: PaginationInput, $filter: ArticleFilter) {
    getArticles(pagination: $pagination, filter: $filter) {
      __typename
      ... on ArticlesResponse {
        status
        message
        result {
          articles {
            id
            headline
            description
            image
            source {
              link
              mainUrl
            }
            date
            userArticleActivity {
              liked
              saved
              shared
              articleId {
                id
              }
            }
          }
        }
        pagination {
          totalItems
          totalPages
          pageSize
          currentPage
        }
      }
      ... on ArticleErrorResponse {
        status
        message
      }
    }
  }
`; 

export const UPDATE_USER_ARTICLE_ACTIVITY = gql`
  mutation UpdateUserArticleActivity($input: UserArticleActivityInput!) {
    updateUserArticleActivity(input: $input) {
      status
      message
      result {
        activity {
          id
          liked
          saved
          shared
        }
      }
    }
  }
`;

export interface UserArticleActivityInput {
  articleId: string;
  liked?: boolean | null;
  saved?: boolean | null;
  shared?: boolean | null;
}

export interface ArticleSearchFilter {
  search: string | null;
}

export interface UserArticleActivityFilter {
  article: ArticleSearchFilter | null;
}

export interface UserArticleActivity {
  id: string;
  liked: boolean;
  saved: boolean;
  shared: boolean;
  userId: string;
  articleId: Article;
}

// Define the possible response types for getUserArticleActivities query
interface UserArticleActivitiesResponse {
    __typename: 'UserArticleActivitiesResponse';
    status: string;
    message: string;
    result: {
        __typename: 'UserArticleActivitiesResult';
        activities: UserArticleActivity[];
        totalCount: number;
    };
}

// Assuming a possible error response type
interface UserArticleActivitiesErrorResponse {
    __typename: 'UserArticleActivitiesErrorResponse';
    status: string;
    message: string;
}

// Update GetUserArticleActivitiesData to reflect the union
export interface GetUserArticleActivitiesData {
  getUserArticleActivities: UserArticleActivitiesResponse | UserArticleActivitiesErrorResponse; // Union type
}

export const GET_USER_ARTICLE_ACTIVITIES = gql`
  query GetUserArticleActivities($pagination: PaginationInput, $filter: UserArticleActivityFilter) {
    getUserArticleActivities(pagination: $pagination, filter: $filter) {
      __typename # Request type name
       ... on UserArticleActivitiesResponse { # Success case
          status
          message
          result {
            activities {
              articleId {
                id
                headline
                description
                image
                source {
                  link
                  mainUrl
                }
                date
                # Need userArticleActivity here if NewsCard expects it for saved items?
                # Let's assume not for now based on previous code.
              }
              userId
              liked
              saved
              shared
              id
            }
            totalCount
          }
       }
    }
  }
`;