import * as React from "react";
import Svg, {
  G,
  <PERSON>,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Rect,
} from "react-native-svg";
import { CommonProps } from ".";

const CheckBox = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#CheckBox-1_svg__a)">
            <Path d="M10.5 7V9.5C10.5 9.76522 10.3946 10.0196 10.2071 10.2071C10.0196 10.3946 9.76522 10.5 9.5 10.5H2.5C2.23478 10.5 1.98043 10.3946 1.79289 10.2071C1.60536 10.0196 1.5 9.76522 1.5 9.5V2.5C1.5 2.23478 1.60536 1.98043 1.79289 1.79289C1.98043 1.60536 2.23478 1.5 2.5 1.5H7.5" stroke="url(#CheckBox-1_svg__b)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M4.5 5.5L6 7L11 2" stroke="url(#CheckBox-1_svg__c)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </G>
          <Defs>
            <LinearGradient id="CheckBox-1_svg__b" x1="6" y1="0.413793" x2="6" y2="12.569" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="CheckBox-1_svg__c" x1="7.75" y1="1.39655" x2="7.75" y2="8.14943" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <ClipPath id="CheckBox-1_svg__a">
              <Rect width="12" height="12" fill="#fff"/>
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
            <Path d="M10.5 5.328V9.5C10.5 9.76522 10.3946 10.0196 10.2071 10.2071C10.0196 10.3946 9.76522 10.5 9.5 10.5H2.5C2.23478 10.5 1.98043 10.3946 1.79289 10.2071C1.60536 10.0196 1.5 9.76522 1.5 9.5V2.5C1.5 2.23478 1.60536 1.98043 1.79289 1.79289C1.98043 1.60536 2.23478 1.5 2.5 1.5H8.672" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M4.5 5.5L6 7L11 2" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
        </>
      )}
    </Svg>
  );
};
export default CheckBox;
