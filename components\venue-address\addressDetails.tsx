import { View, Text, StyleSheet, TouchableOpacity, Keyboard, TouchableWithoutFeedback, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button } from 'react-native-paper';
import { Colors, Spacing, Typography, Borders } from '@/constants/DesignSystem';
import { useState, useEffect } from 'react';
import { StackScreenProps } from '@react-navigation/stack';
import { AddPartyRootStackList } from '@/app/(home)/EventsDashboard/AddPartyRootStackList';
import { useVenueStore } from '@/store/venueStore';

type AddressDetailsRouteProps = StackScreenProps<AddPartyRootStackList, 'AddressDetails'>;

export default function AddressDetails({route, navigation}: AddressDetailsRouteProps) {
    const { title: maintitle, address, additionalDetails: moreDetails, isEditing, onPressUpdate, partyId, eventGroupId } = route.params;
    const [title, setTitle] = useState(maintitle);
    const [titleError, setTitleError] = useState(false);
    const [additionalDetails, setAdditionalDetails] = useState(moreDetails);
    const setVenueDetails = useVenueStore((state) => state.setVenueDetails);
    const venueDetails = useVenueStore((state) => state.venueDetails);
    const clearVenueDetails = useVenueStore((state) => state.clearVenueDetails);

    useEffect(() => { if (!isEditing) setTitle(maintitle) }, [maintitle, isEditing]);

    const isFormValid = () => {
        if (!title || title.trim() === '') {
            setTitleError(true);
            return false;
        }
        return true;
    }

    const handleConfirm = () => {
        if (!isFormValid()) {
            return;
        }

        setTitleError(false);
        
        setVenueDetails({
            isNew: true,
            placeId: venueDetails?.placeId ?? '',
            name: venueDetails?.name ?? '',
            title: title!.trim(),
            directions: additionalDetails?.trim(),
        });

        if (partyId !== undefined && partyId !== null) {
            navigation.popTo('EditParty', { partyId: partyId, eventGroupId: eventGroupId });
        } else {
            navigation.popTo('AddParty', { partyId: partyId, eventGroupId: eventGroupId });
        }
    }

    const handleUpdate = () => {
        if (!isFormValid()) {
            return;
        }

        setTitleError(false);
        
        onPressUpdate!({placeId: venueDetails?.placeId!, label: title!, name: venueDetails?.name!, directions: additionalDetails!});

        navigation.goBack();
    }

    return (
        <SafeAreaView style={styles.container}>
            <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
                <View style={{ flex: 1, justifyContent: 'space-between' }}>
                    <View>
                        <View style={styles.section}>
                            <Text style={styles.label}>Title<Text style={styles.required}>*</Text></Text>
                            <TextInput
                                placeholder="Enter the title"
                                placeholderTextColor={Colors.mediumGray}
                                style={styles.input}
                                value={title}
                                onChangeText={(text) => setTitle(text)}
                                cursorColor={Colors.primary}
                            />
                            {titleError && <Text style={styles.errorText}>Title is required.</Text>}
                        </View>

                        <View style={styles.section}>
                            <Text style={styles.label}>Location</Text>
                            <View style={styles.locationContainer}>
                                <Text style={styles.locationText}>
                                    {address}
                                </Text>
                                <TouchableOpacity onPress={() => {
                                    clearVenueDetails();
                                    navigation.navigate('AddLocation', { partyId: partyId, eventGroupId: eventGroupId, isEditing: isEditing, onPressUpdate: onPressUpdate });
                                }}>
                                    <Text style={styles.changeButton}>Change</Text>
                                </TouchableOpacity>
                            </View>
                        </View>

                        <View style={styles.section}>
                            <Text style={styles.label}>Additional Details</Text>
                            <TextInput
                                placeholder="Unit no/Landmark"
                                placeholderTextColor={Colors.mediumGray}
                                style={[styles.input, styles.descriptionInput]}
                                multiline
                                numberOfLines={4}
                                value={additionalDetails}
                                onChangeText={setAdditionalDetails}
                                cursorColor={Colors.primary}
                            />
                        </View>
                    </View>

                    <Button mode="contained" onPress={isEditing ? handleUpdate : handleConfirm} style={styles.confirmButton}>
                        <Text style={{color: Colors.black}}>{isEditing ? "Update Address" : "Confirm Address"}</Text>
                    </Button>
                </View>
            </TouchableWithoutFeedback>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: Spacing.lg,
        backgroundColor: Colors.background.primary,
    },
    section: {
        marginBottom: Spacing.lg,
    },
    label: {
        fontSize: Typography.fontSize.md,
        marginBottom: Spacing.sm,
        color: Colors.text.secondary,
        fontWeight: Typography.fontWeight.medium,
    },
    required: {
        color: Colors.error,
        fontSize: Typography.fontSize.sm,
    },
    errorText: {
        color: Colors.error,
        fontSize: Typography.fontSize.sm,
        marginTop: Spacing.xs,
    },
    input: {
        backgroundColor: Colors.background.tertiary,
        borderRadius: Borders.radius.sm,
        paddingHorizontal: Spacing.sm,
        color: Colors.text.secondary,
        fontSize: Typography.fontSize.md,
        height: 50,
    },
    descriptionInput: {
        textAlignVertical: 'top',
        paddingTop: Spacing.sm,
        height: 100,
    },
    locationContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.background.tertiary,
        padding: Spacing.md,
        borderRadius: Borders.radius.sm,
    },
    locationText: {
        flex: 1,
        marginRight: Spacing.sm,
        color: Colors.text.secondary,
        fontSize: Typography.fontSize.md,
    },
    changeButton: {
        color: Colors.primary,
        fontWeight: Typography.fontWeight.bold,
        fontSize: Typography.fontSize.md,
    },
    confirmButton: {
        marginTop: Spacing.lg,
        paddingVertical: Spacing.sm,
        backgroundColor: Colors.primary,
        borderRadius: Borders.radius.md,
    },
});