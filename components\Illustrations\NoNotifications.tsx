import * as React from "react";
import Svg, {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  De<PERSON>,
  <PERSON>arGradient,
  Stop,
} from "react-native-svg";
import type { SvgProps } from "react-native-svg";
const SvgNoNotifications = (props: SvgProps) => (
  <Svg
    width={300}
    height={300}
    fill="none"
    viewBox="0 0 300 307"
    {...props}
  >
    <Path
      fill="url(#No_notifications_svg__a)"
      d="M60.757 106.36a.61.61 0 0 0-.093-.715.6.6 0 0 0-.21-.145c-2.316-.916-10.547-3.663-15.043 1.831-3 3.64-7.239 6.043-11.903 6.748a.59.59 0 0 0-.492.443.59.59 0 0 0 .236.619c3.946 2.857 17.067 10.035 27.505-8.781"
    />
    <Path
      fill="url(#No_notifications_svg__b)"
      d="M270.757 202.36a.612.612 0 0 0-.303-.86c-2.316-.916-10.547-3.663-15.043 1.831a19.13 19.13 0 0 1-11.903 6.748.585.585 0 0 0-.256 1.062c3.946 2.857 17.067 10.035 27.505-8.781"
    />
    <Path
      fill="#fff"
      d="M179.254 227.975c47.124-15.804 80.285-43.647 74.066-62.188s-49.46-20.761-96.584-4.957-80.285 43.647-74.067 62.188 49.461 20.761 96.585 4.957"
    />
    <Path
      fill="url(#No_notifications_svg__c)"
      d="M179.301 227.489c42.983-14.415 73.23-39.811 67.558-56.723s-45.114-18.936-88.097-4.52c-42.983 14.415-73.23 39.811-67.558 56.723s45.115 18.936 88.097 4.52"
    />
    <Path
      fill="#63B3ED"
      d="M178.67 218.895c32.251-10.816 55.854-27.158 52.721-36.502-3.134-9.343-31.818-8.15-64.068 2.666s-55.855 27.158-52.721 36.502c3.134 9.343 31.818 8.15 64.068-2.666"
    />
    <Path
      fill="url(#No_notifications_svg__d)"
      d="M184 210.5c-3.63 2.038-15.088 7.882-30.515 6.2-14.768-1.61-24.309-9.156-27.485-11.909 3.444-2.889 8.148-5.882 14.187-7.528C162.47 191.174 182.37 208.995 184 210.5"
    />
    <Mask
      id="No_notifications_svg__e"
      width={83}
      height={46}
      x={125}
      y={176}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: "luminance",
      }}
    >
      <Path
        fill="#fff"
        d="M144.513 194.285c-8.373 4.111-13.547 6.301-18.903 11.027.84 3.36 2.584 10.585 2.836 12.601s18.797 1.156 26.778 3.781h7.561l22.683-7.561 21.738-15.437v-22.368s-4.41 0-14.492 1.89c-10.081 1.89-22.998 5.986-26.463 7.246-3.466 1.26-13.365 4.71-21.738 8.821"
      />
    </Mask>
    <G mask="url(#No_notifications_svg__e)">
      <Path
        fill="#fff"
        d="M163.831 215.664c22.762 0 41.214-18.452 41.214-41.214s-18.452-41.214-41.214-41.214-41.214 18.452-41.214 41.214 18.452 41.214 41.214 41.214"
      />
      <Path
        fill="url(#No_notifications_svg__f)"
        d="M163.831 215.664c22.762 0 41.214-18.452 41.214-41.214s-18.452-41.214-41.214-41.214-41.214 18.452-41.214 41.214 18.452 41.214 41.214 41.214"
      />
    </G>
    <Path
      fill="#63B3ED"
      d="M97.042 204a142.4 142.4 0 0 1-11.052-28.657c-2.787-10.483-6.591-25.391-4.272-44.444 1.207-9.93 3.16-25.961 15.178-41.371 3.507-4.495 12.392-15.592 28.588-21.636 19.463-7.266 36.55-2.42 41.7-.877 20.721 6.2 32.317 20.714 38.313 28.218 7.078 8.852 11.634 18.056 14.054 22.933 3.507 7.078 5.972 13.492 10.06 24.125A596 596 0 0 1 236 159.707a161.6 161.6 0 0 0-31.09-1.9c-6.086.214-26.781 1.298-51.843 10.451-8.429 3.079-20.979 7.762-35.242 17.761A135.2 135.2 0 0 0 97.048 204z"
    />
    <Path
      fill="url(#No_notifications_svg__g)"
      d="M92.142 206.185a141.5 141.5 0 0 1-11.121-28.608c-2.805-10.464-6.632-25.347-4.299-44.366 1.24-10.083 3.784-25.585 15.274-41.3 9.137-12.498 21.902-18.938 29.151-21.911-4.509 5.126-12.797 15.94-15.715 31.236-1.259 6.609-4.222 22.143 4.451 36.938 7.452 12.717 19.515 18.099 24.913 20.415 0 0 6.546 4.638 58.204 0 0 0-19.318 2.416-44.511 11.547-8.482 3.073-20.621 9.106-34.973 19.088-9.175 6.384-15.203 13.694-20.03 18.776z"
    />
    <Path
      fill="#FCD9BD"
      d="M181.5 297c48.325 0 87.5-1.343 87.5-3s-39.175-3-87.5-3-87.5 1.343-87.5 3 39.175 3 87.5 3M53 296c11.598 0 21-.672 21-1.5s-9.402-1.5-21-1.5-21 .672-21 1.5 9.402 1.5 21 1.5"
    />
    <Path
      fill="#63B3ED"
      d="M105.01 79c-.711-2.862-1.489-7.3-.638-13.022.553-3.74 1.232-8.327 4.814-12.654 4.356-5.265 10.078-6.615 11.628-6.931 7.404-1.499 13.145 1.705 14.885 2.713 4.522 2.616 7.094 6.297 8.738 8.662 1.651 2.364 2.884 4.819 3.563 6.375-1.226-.064-2.718.272-4.128.323-2.083.084-3.715.033-5.398.323-.095-.349-.495-1.201-.698-1.796-.407-1.188-1.15-3.358-2.591-5.225-2.058-2.662-4.782-3.708-5.551-3.992-.831-.31-4.172-1.44-8.147-.226-2.763.846-4.49 2.41-4.96 2.861-1.575 1.499-2.356 3.056-2.813 3.992a15.7 15.7 0 0 0-1.334 3.992c-.495 2.584-.209 4.645 0 6.098.21 1.44.445 2.538.692 3.34-1.371 1.02-2.857 1.614-4.445 2.583-1.27.97-2.385 1.609-3.617 2.584"
    />
    <Path
      fill="#fff"
      d="M65 199c14.912 0 27-12.088 27-27s-12.088-27-27-27-27 12.088-27 27 12.088 27 27 27"
    />
    <Path
      fill="url(#No_notifications_svg__h)"
      d="M64.5 200c14.083 0 25.5-11.417 25.5-25.5S78.583 149 64.5 149 39 160.417 39 174.5 50.417 200 64.5 200"
    />
    <Path
      stroke="url(#No_notifications_svg__i)"
      strokeMiterlimit={10}
      strokeWidth={8}
      d="M65.503 160h-.006C59.7 160 55 164.93 55 171.012v5.976C55 183.07 59.7 188 65.497 188h.006C71.3 188 76 183.07 76 176.988v-5.976C76 164.93 71.3 160 65.503 160Z"
    />
    <Path
      stroke="#63B3ED"
      strokeMiterlimit={10}
      strokeWidth={8}
      d="M64.504 159h-.008C58.147 159 53 163.93 53 170.012v5.976C53 182.07 58.147 187 64.496 187h.008C70.853 187 76 182.07 76 175.988v-5.976C76 163.93 70.853 159 64.504 159Z"
    />
    <Path
      stroke="#F4862B"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="m48 293 13.755-96.289M59.5 293l12.403-97.498M48.948 284.835h11.005M50.758 271.862h10.57M53.113 257.604H63.4M55.11 244.472h10.016M56.257 231.748h10.597M58.62 218.34h9.96M60.34 204.932h9.967"
    />
    <Path
      fill="#34434D"
      d="M64.308 212.699c4.46-1.793 6.808-6.4 5.245-10.29-1.564-3.891-6.448-5.591-10.908-3.798s-6.808 6.4-5.244 10.291 6.447 5.59 10.907 3.797"
    />
    <Path
      fill="#34434D"
      d="M67.869 215.314c0 2.86-2.86 5.174-6.383 5.174s-6.383-2.314-6.383-5.174c0-1.672 1.126-2.929 1.458-3.288 1.823-1.976 4.414-1.907 4.925-1.886 2.763.138 6.383 2.162 6.383 5.174"
    />
    <Path
      fill="#34434D"
      d="M64.111 221.248c0 1.257-1.934 2.273-4.324 2.273s-4.325-1.016-4.325-2.273 1.935-2.273 4.325-2.273 4.324 1.016 4.324 2.273"
    />
    <Path
      fill="#34434D"
      d="M64.015 223.023s-3.316-3.357-3.669-3.467c-1.782-.539-6.438-.836-9.636 3.039-2.625 3.178-2.446 6.134-3.558 7.91 1.79.31 5.458 5.346 12.414 1.416 3.606-2.038 5.568-5.768 4.448-8.898M56.333 200.372c-3.53-2.901-8.497-.967-10.535 2.743l-.587-.366c2.632-3.703 7.35-5.292 11.308-2.639zM59.572 199.067c-1.775-4.228-6.873-5.022-10.562-2.846l-.325-.608c2.93-1.327 6.66-1.416 9.147.836.8.704 1.436 1.588 1.906 2.549l-.166.076z"
    />
    <Path
      fill="#fff"
      d="M231.699 293.941s7.425-6.451 4.7-11.676c-2.726-5.217-6.84 6.393-6.84 6.393s1.896-13.146-2.811-13.646c-4.707-.499-1.238 14.293-1.238 14.293s-1.087-3.403-3.684-1.669c-2.597 1.735 1.76 6.364 1.76 6.364l8.105-.066z"
    />
    <Path
      fill="url(#No_notifications_svg__j)"
      d="M231.699 293.941s7.425-6.451 4.7-11.676c-2.726-5.217-6.84 6.393-6.84 6.393s1.896-13.146-2.811-13.646c-4.707-.499-1.238 14.293-1.238 14.293s-1.087-3.403-3.684-1.669c-2.597 1.735 1.76 6.364 1.76 6.364l8.105-.066z"
    />
    <Path
      stroke="#34434D"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="m61.945 221 .453 11.667a1 1 0 0 1-.004.081l-1.324 10.691a.5.5 0 0 0 .496.561H63M59.191 221l.495 11.66a.5.5 0 0 1-.005.095l-1.595 10.671a.5.5 0 0 0 .494.574H61"
    />
    <Path
      stroke="#34434D"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="m62 215-2.434-15.96L57 190M66 215l7.709-16.301L75 190"
    />
    <Path
      fill="#34434D"
      stroke="#34434D"
      strokeMiterlimit={10}
      d="M56.953 190.266c.407-.235.24-1.285-.373-2.346-.612-1.06-1.438-1.73-1.844-1.495s-.24 1.284.373 2.345c.612 1.061 1.438 1.73 1.844 1.496ZM76.103 188.862c.618-1.071.8-2.123.407-2.35s-1.213.457-1.83 1.528c-.619 1.07-.801 2.122-.408 2.349s1.213-.457 1.831-1.527Z"
    />
    <Path
      fill="#34434D"
      d="M226.267 258.833c1.563-3.89-.784-8.497-5.245-10.29-4.46-1.793-9.343-.093-10.907 3.798s.784 8.497 5.244 10.29 9.344.093 10.908-3.798"
    />
    <Path
      fill="#34434D"
      d="M211.823 265.286c0 2.86 2.86 5.174 6.383 5.174s6.383-2.314 6.383-5.174c0-1.672-1.126-2.929-1.458-3.288-1.823-1.976-4.414-1.907-4.925-1.886-2.763.138-6.383 2.162-6.383 5.174"
    />
    <Path
      fill="#34434D"
      d="M219.912 273.499c2.389 0 4.325-1.017 4.325-2.272 0-1.256-1.936-2.273-4.325-2.273-2.388 0-4.324 1.017-4.324 2.273 0 1.255 1.936 2.272 4.324 2.272"
    />
    <Path
      fill="#34434D"
      d="M215.677 273.002s3.316-3.357 3.668-3.468c1.783-.539 6.439-.836 9.637 3.04 2.625 3.177 2.445 6.134 3.558 7.909-1.79.311-5.458 5.347-12.414 1.416-3.606-2.037-5.568-5.768-4.449-8.897M223.173 250.088c3.958-2.659 8.669-1.063 11.308 2.639l-.587.366c-2.038-3.709-7.005-5.636-10.535-2.742zM219.954 248.969c1.948-4.324 6.997-5.298 11.052-3.378l-.324.608c-3.689-2.176-8.787-1.374-10.563 2.846z"
    />
    <Path
      stroke="#34434D"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="m217.583 272-.678 10.623a.5.5 0 0 0 .009.132l1.964 9.645a.5.5 0 0 1-.49.6H216"
    />
    <Path
      stroke="#34434D"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="m220.407 272-.649 10.621a.5.5 0 0 0 .011.137l2.099 9.636a.5.5 0 0 1-.489.606H218"
    />
    <Path
      stroke="#34434D"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="m221 256-6.104 15.509L210 280"
    />
    <Path
      stroke="#34434D"
      strokeMiterlimit={10}
      strokeWidth={2}
      d="m213 264-6.002-11.05L205 240"
    />
    <Path
      fill="#34434D"
      stroke="#34434D"
      strokeMiterlimit={10}
      d="M209.741 281.787c.808-.919 1.179-1.916.826-2.226-.352-.31-1.293.184-2.102 1.104-.809.919-1.179 1.916-.827 2.226s1.294-.184 2.103-1.104Z"
    />
    <Path
      fill="#fff"
      d="M64.698 293.944s7.427-6.112 4.7-11.061c-2.725-4.943-6.838 6.056-6.838 6.056s1.895-12.454-2.812-12.927c-4.707-.474-1.238 13.54-1.238 13.54s-1.087-3.223-3.684-1.581c-2.597 1.643 1.76 6.029 1.76 6.029l8.105-.063z"
    />
    <Path
      fill="url(#No_notifications_svg__k)"
      d="M64.698 293.944s7.427-6.112 4.7-11.061c-2.725-4.943-6.838 6.056-6.838 6.056s1.895-12.454-2.812-12.927c-4.707-.474-1.238 13.54-1.238 13.54s-1.087-3.223-3.684-1.581c-2.597 1.643 1.76 6.029 1.76 6.029l8.105-.063z"
    />
    <Path
      fill="#34434D"
      d="M129.72 258.988c1.148-4.032-1.668-8.369-6.291-9.686-4.624-1.316-9.303.885-10.451 4.917-1.149 4.033 1.668 8.369 6.291 9.686s9.302-.884 10.451-4.917"
    />
    <Path
      fill="#34434D"
      d="M124.873 266.26c0 2.86-2.86 5.174-6.383 5.174s-6.383-2.314-6.383-5.174c0-1.672 1.126-2.929 1.457-3.288 1.824-1.976 4.415-1.907 4.926-1.886 2.763.138 6.383 2.162 6.383 5.174"
    />
    <Path
      fill="#34434D"
      d="M121.115 272.201c0 1.257-1.934 2.272-4.324 2.272s-4.325-1.015-4.325-2.272c0-1.258 1.935-2.273 4.325-2.273s4.324 1.015 4.324 2.273"
    />
    <Path
      fill="#34434D"
      d="M121.018 273.969s-3.315-3.357-3.668-3.468c-1.782-.539-6.438-.836-9.636 3.04-2.625 3.177-2.446 6.134-3.558 7.909 1.789.311 5.457 5.347 12.414 1.416 3.606-2.037 5.568-5.768 4.448-8.897M122.89 250.289c2.321-4.131 7.323-4.822 11.295-2.68l-.366.587c-3.482-2.411-8.787-1.921-10.652 2.245l-.284-.152zM119.512 250.724c-.186-4.739 3.903-7.861 8.387-7.958l-.021.691c-4.276-.304-8.476 2.687-8.179 7.26h-.18z"
    />
    <Path
      stroke="#34434D"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="m119.412 273 .68 10.62a.5.5 0 0 1-.009.132l-1.961 9.648a.5.5 0 0 0 .49.6H121"
    />
    <Path
      stroke="#34434D"
      strokeLinecap="round"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="m116.588 273 .658 10.617a.5.5 0 0 1-.01.138l-2.104 9.638a.5.5 0 0 0 .489.607H119"
    />
    <Path
      stroke="#34434D"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="m114 254 6.104 15.509L125 278M120 262l5.472 11 6.528-1.034"
    />
    <Path
      fill="#34434D"
      stroke="#34434D"
      strokeMiterlimit={10}
      d="M127.567 280.891c.353-.31-.018-1.307-.826-2.226-.809-.92-1.751-1.414-2.103-1.104s.018 1.307.827 2.226c.809.92 1.75 1.414 2.102 1.104ZM205.42 240.367c.393-.227.211-1.279-.407-2.349s-1.438-1.755-1.831-1.528-.211 1.279.407 2.35c.618 1.07 1.438 1.754 1.831 1.527Z"
    />
    <Path
      fill="url(#No_notifications_svg__l)"
      d="M207.497 176.093a1.5 1.5 0 1 0-2.994-.186zM202.21 237l-.014 1.5 1.423.012.088-1.419zm-2.21-.019.004-1.5a1.5 1.5 0 0 0-.017 3zm4.304 1.513a1.5 1.5 0 1 0 .009-3zM206 176l-1.497-.093-3.791 61 1.498.093 1.497.093 3.79-61zm-3.79 61 .013-1.5-2.21-.019-.013 1.5-.013 1.5 2.209.019zm-2.21-.019-.004 1.5 4.308.013.005-1.5.004-1.5-4.309-.013z"
    />
    <Path
      fill="#F4862B"
      d="M128 276.385c0 .317.292.554.602.49l6.832-1.424a.5.5 0 0 0 .398-.481l.156-9.278a.5.5 0 0 0-.648-.486l-6.989 2.18a.5.5 0 0 0-.351.477z"
    />
    <Defs>
      <LinearGradient
        id="No_notifications_svg__a"
        x1={46.916}
        x2={46.876}
        y1={118.889}
        y2={101.195}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_notifications_svg__b"
        x1={256.916}
        x2={256.876}
        y1={214.889}
        y2={197.195}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_notifications_svg__c"
        x1={179.301}
        x2={154.772}
        y1={227.489}
        y2={154.746}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_notifications_svg__d"
        x1={155}
        x2={154.962}
        y1={217}
        y2={192.043}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_notifications_svg__f"
        x1={163.831}
        x2={163.422}
        y1={215.664}
        y2={117.707}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_notifications_svg__g"
        x1={134.5}
        x2={133.692}
        y1={208}
        y2={38.002}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_notifications_svg__h"
        x1={64.5}
        x2={64.247}
        y1={200}
        y2={139.392}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_notifications_svg__i"
        x1={65.5}
        x2={65.315}
        y1={188}
        y2={153.508}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#069AFF" stopOpacity={0.7} />
        <Stop offset={0.36} stopColor="#83CDFF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
      <LinearGradient
        id="No_notifications_svg__j"
        x1={229}
        x2={229}
        y1={270.043}
        y2={294}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#fff" />
        <Stop offset={0.671} stopColor="#80D6AF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#00AC5E" stopOpacity={0.7} />
      </LinearGradient>
      <LinearGradient
        id="No_notifications_svg__k"
        x1={62}
        x2={62}
        y1={271.304}
        y2={294}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#fff" />
        <Stop offset={0.671} stopColor="#80D6AF" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#00AC5E" stopOpacity={0.7} />
      </LinearGradient>
      <LinearGradient
        id="No_notifications_svg__l"
        x1={203}
        x2={199.928}
        y1={237}
        y2={164.637}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default SvgNoNotifications;
