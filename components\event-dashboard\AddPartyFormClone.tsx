import { View, Platform } from 'react-native'
import { TextInput, useTheme, Text } from 'react-native-paper'
import { PartyTypesAccordion } from '@/components/event-dashboard/AddParty/PartyTypesAccordion'
import { PartyServices } from '@/components/event-dashboard/AddParty/PartyServices'
import {
  PartyType,
  PartyFormData,
  VendorType, FormErrors
} from '../../app/(home)/EventsDashboard/AddParty.models'
import { AddCohosts } from '@/components/event-dashboard/AddParty/AddCohostsParent';
import { numberConstraints, textConstraints } from '@/app/(home)/utils/reusableFunctions';
import { Button } from 'react-native-paper';
import { AddPartyNames } from '@/constants/displayNames';
import { BottomSheetModal } from '@gorhom/bottom-sheet'

  
interface AddPartyFormProps {
  partyId?: string;
  formData: PartyFormData;
  setFormData: React.Dispatch<React.SetStateAction<PartyFormData>>;
  formErrors: FormErrors;
  setFormErrors: React.Dispatch<React.SetStateAction<FormErrors>>;
  partyTypes?: PartyType[];
  availableServices: VendorType[];
  handlePartyTypeChange: (type: PartyType) => void;
  toggleService: (serviceId: string) => void;
  setShowDatePicker: (show: boolean) => void;
  setShowTimePicker: (show: boolean) => void;
  setShowAreaSearch: (show: boolean) => void;
  handlePartySubmission: () => void;
  showSubmitButton?: boolean; // Made optional with default value
  showPartyNameAndType?: boolean;
  cohostSheetRef: React.RefObject<BottomSheetModal>;
}

const AddPartyForm: React.FC<AddPartyFormProps> = ({
  partyId,
  formData,
  setFormData,
  formErrors,
  setFormErrors,
  partyTypes,
  availableServices,
  handlePartyTypeChange,
  toggleService,
  setShowDatePicker,
  setShowTimePicker,
  setShowAreaSearch,
  handlePartySubmission,
  showSubmitButton = true,
  showPartyNameAndType = true,
  cohostSheetRef

}) => {
  const theme = useTheme();
 
  const handleGuestsChange = (value: string) => {
    const sanitizedValue = numberConstraints.sanitizeNumberWithinRange(value, 0, 999999);
    setFormData(prev => ({ ...prev, guests: sanitizedValue }));
    setFormErrors(prev => ({ ...prev, guests: '' }));
  };

  const handleBudgetChange = (value: string) => {
    const sanitizedValue = numberConstraints.sanitizeNumberWithinRange(value, 0, 1000000000000);
    setFormData(prev => ({ ...prev, budget: sanitizedValue }));
    setFormErrors(prev => ({ ...prev, budget: '' }));
  };

  const handlePartyNameChange = (value: string) => {
    const sanitizedValue = textConstraints.sanitizeTextInput(value, 300);
    setFormData(prev => ({ ...prev, partyName: sanitizedValue }));
    setFormErrors(prev => ({ ...prev, partyName: '' }));
  };

  return (
        <View style={{ marginBottom: Platform.OS === 'ios' ? 0 : '20%' }}>
          <View style={{ marginBottom: 16 }}>
            {showPartyNameAndType && (
              <>
                <Text variant="titleMedium" style={{ marginLeft: 4, marginBottom: 8 }}>Select Party</Text>
                <PartyTypesAccordion<PartyType>
                  label="Party Type *"
                  value={partyTypes?.find(
                    (type: PartyType) => type.id === formData.partyType
                  )?.name || ''}
                  options={partyTypes || []}
                  onSelect={handlePartyTypeChange}
                />
                {formErrors.partyType && (
                  <Text style={{ color: theme.colors.error, fontSize: 12, marginLeft: 4 }}>
                    {formErrors.partyType}
                  </Text>
                )}
              </>
            )}
          </View>
          {showPartyNameAndType && (
            <>
              <TextInput
                label="Party Name *"
                value={formData.partyName}
                onChangeText={handlePartyNameChange}
                style={{ marginBottom: formErrors.partyName ? 4 : 16 }}
                error={!!formErrors.partyName}
                spellCheck={true}
              />
              {formErrors.partyName && (
                <Text style={{ color: theme.colors.error, fontSize: 12, marginLeft: 4, marginBottom: 16 }}>
                  {formErrors.partyName}
                </Text>
              )}
            </>
          )}
          <AddCohosts
            cohosts={formData.cohosts}
            onCohostChange={(newCohosts) => {
              setFormData(prev => ({ ...prev, cohosts: newCohosts }));
            }}
            cohostSheetRef={cohostSheetRef}
          />

          <Text variant="titleMedium" style={{ marginTop: 8, marginBottom: 8, marginLeft: 4 }}>
            Party Preferences
          </Text>
          <TextInput
            label="Date *"
            value={formData.date.toLocaleDateString()}
            right={<TextInput.Icon icon="calendar" onPress={() => setShowDatePicker(true)} />}
            editable={false}
            style={{ marginBottom: formErrors.date ? 4 : 16 }}
            error={!!formErrors.date}
          />
          {formErrors.date && (
            <Text style={{ color: theme.colors.error, fontSize: 12, marginLeft: 4, marginBottom: 16 }}>
              {formErrors.date}
            </Text>
          )}
          <TextInput
            label="Time *"
            value={formData.time.toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true
            })}
            right={<TextInput.Icon icon="clock" onPress={() => setShowTimePicker(true)} />}
            editable={false}
            style={{ marginBottom: formErrors.time ? 4 : 16 }}
            error={!!formErrors.time}
          />
          {formErrors.time && (
            <Text style={{ color: theme.colors.error, fontSize: 12, marginLeft: 4, marginBottom: 16 }}>
              {formErrors.time}
            </Text>
          )}

          <TextInput
            label="Area/City *"
            value={formData.area?.title || ''}
            right={<TextInput.Icon icon="map-marker" onPress={() => setShowAreaSearch(true)} />}
            editable={false}
            style={{ marginBottom: formErrors.area ? 4 : 16 }}
            error={!!formErrors.area}
          />
          {formErrors.area && (
            <Text style={{ color: theme.colors.error, fontSize: 12, marginLeft: 4, marginBottom: 16 }}>
              {formErrors.area}
            </Text>
          )}

          <TextInput
            label="No. of Guests *"
            value={formData.guests}
            onChangeText={handleGuestsChange}
            keyboardType="numeric"
            style={{ marginBottom: formErrors.guests ? 4 : 16 }}
            error={!!formErrors.guests}
            inputMode="numeric"
          />
          {formErrors.guests && (
            <Text style={{ color: theme.colors.error, fontSize: 12, marginLeft: 4, marginBottom: 16 }}>
              {formErrors.guests}
            </Text>
          )}

          <TextInput
            label="Budget *"
            value={formData.budget}
            onChangeText={handleBudgetChange}
            keyboardType="numeric"
            style={{ marginBottom: formErrors.budget ? 4 : 16 }}
            error={!!formErrors.budget}
            inputMode="numeric"
          />
          {formErrors.budget && (
            <Text style={{ color: theme.colors.error, fontSize: 12, marginLeft: 4, marginBottom: 16 }}>
              {formErrors.budget}
            </Text>
          )}

          <Text variant="titleMedium" style={{ marginLeft: 4, marginBottom: 8 }}>
            Select Services *
          </Text>
          <PartyServices
            services={availableServices}
            selectedServices={formData.services}
            onToggleService={toggleService}
          />
          {formErrors.services && (
            <Text style={{
              color: theme.colors.error,
              fontSize: 12,
              marginLeft: 4,
              marginTop: 4,
              marginBottom: 16
            }}>
              {formErrors.services}
            </Text>
          )}
          {showSubmitButton && (
            <View style={{ marginTop: 16 }}>
              <Button
                mode="contained"
                onPress={handlePartySubmission}
                style={{ width: '100%' }}
              >
                {partyId ? AddPartyNames.UPDATE : AddPartyNames.SUBMIT}
              </Button>
            </View>
          )}
        </View>
       
      
  )
}

export default AddPartyForm