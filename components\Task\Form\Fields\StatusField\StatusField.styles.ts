import { StyleSheet, Platform, Dimensions } from 'react-native';

const screenHeight = Dimensions.get('window').height;
const screenWidth = Dimensions.get('window').width;

export const statusFieldStyles = StyleSheet.create({
  dropdownContainer: {
    position: 'relative',
    backgroundColor: '#fff',
    marginTop: 25,
    height: 80,
    zIndex: 1,
  },

  menu: {
    marginTop: Platform.OS === 'ios' 
      ? screenHeight * 0.175
      : screenHeight * 0.22,
    borderWidth: 1,
    borderColor: '#5270E7',
    borderRadius: 10,
    width: screenWidth * 0.4,
    alignItems: 'center',
    marginLeft: Platform.OS === 'ios' 
      ? screenWidth * 0.16 
      : screenWidth * 0.15,
    paddingHorizontal: 5,
  },

  pressableArea: {
    flex: 1,
    width: '30%',
    borderColor: '#fff',
    borderWidth: 1,
  },

  inputStyle: {
    backgroundColor: '#fff',
    marginVertical: 0,
    height: 72,
    color: '#000000',
    fontSize: screenHeight * 0.02,
    fontWeight: '500',
    textAlign: 'left',
  },

  selectText: {
    position: 'absolute',
    color: '#655F5F',
    fontSize: screenHeight * 0.02,
    marginTop: 30,
    textAlign: 'left',
    paddingLeft: 10,
    fontWeight: '500',
  },

  valueText: {
    position: 'absolute',
    color: '#768DEC',
    fontSize: screenHeight * 0.02,
    marginTop: 30,
    textAlign: 'left',
    fontWeight: '500',
    width: '300%',
    overflow: 'hidden',
  },

  selectArrow: {
    position: 'absolute',
    left: 70,
    marginTop: 35,
    paddingTop: 20,
  },

  menuItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#000000',
    paddingVertical: 12,
  },

  menuItemText: {
    fontSize: 16,
    color: '#000000',
    fontWeight: '600',
    textAlign: 'center',
  },

  menuItemLast: {
    borderBottomWidth: 0,
  },

  menuScrollContainer: {
    maxHeight: screenHeight * 0.3,
  },

  menuContent: {
    flexGrow: 1,
  },
}); 