import React, { useEffect, useState } from 'react';
import { View, ScrollView, ActivityIndicator, Alert } from 'react-native';
import { Surface, Text } from 'react-native-paper';
import { styles } from '../TaskList/CreateTask.styles';
import { FilePreviewItem } from './FilePreviewItem';
import { 
  AttachmentsSectionProps, 
  TaskAttachment, 
  FileWithDocId, 
  AttachmentFile 
} from './Attachments.types';
import { useKeyboard } from '@/commons/KeyboardContext';
import { Icons, Colors } from '@/constants/DesignSystem';
import { Add } from '@/components/icons';

export function AttachmentsSection({
  taskId,
  uploadingFiles,
  onAttachmentPress,
  onFileRemove,
  getFileTypeIcon,
  isLoading = false,
  documentIds,
  existingAttachments = [],
  uploadedAttachments = [],
  allAttachments = [],
  onAttachmentsChange,
}: AttachmentsSectionProps) {
  const { isKeyboardActive } = useKeyboard();
  const [localExistingAttachments, setLocalExistingAttachments] = useState<TaskAttachment[]>(existingAttachments);

  useEffect(() => {
    setLocalExistingAttachments(existingAttachments);
  }, [existingAttachments]);

  useEffect(() => {
    if (taskId) {
      onAttachmentsChange?.({
        existing: localExistingAttachments,
        uploading: uploadingFiles
      });
    }
  }, [localExistingAttachments, uploadingFiles, taskId, onAttachmentsChange]);

  const handleFileRemove = (file: AttachmentFile) => {
    // First try to get document ID directly
    let documentId = ('id' in file) ? file.id : ('documentId' in file ? file.documentId : undefined);
    
    // If no document ID found, try to find it using file name
    if (!documentId && 'name' in file) {
      
      // First check in uploadedAttachments
      if (uploadedAttachments?.length) {
        const fileIndex = uploadedAttachments.findIndex(
          attachment => attachment.name === file.name
        );
        
        if (fileIndex !== -1 && documentIds) {
          // Try to get document ID from the map using the file index
          documentId = documentIds.get(fileIndex);
        }
      }
      
      // If still no ID found, check in existing attachments
      if (!documentId) {
        const existingFile = existingAttachments?.find(
          attachment => attachment.name === file.name && attachment.id
        );
        if (existingFile?.id) {
          documentId = existingFile.id;
        }
      }
      
      // If still no ID found, try to find in allAttachments array
      if (!documentId && allAttachments?.length) {
        // Since we know the file exists and was uploaded, the last ID in allAttachments
        // might be the one we're looking for
        documentId = allAttachments[allAttachments.length - 1];
      }
    }
    
    if (!documentId) {
      console.error('No document ID found for file:', {
        fileName: 'name' in file ? file.name : 'unknown',
        file,
        uploadedAttachments,
        existingAttachments,
        documentIds: Array.from(documentIds?.entries() || []),
        allAttachments
      });
      Alert.alert(
        'Error',
        'Unable to remove file - Document ID not found',
        [{ text: 'OK' }]
      );
      return;
    }
    
    const fileWithId: FileWithDocId = {
      ...(file as any),
      documentId: documentId
    };
    
    Alert.alert(
      'Remove File',
      'Are you sure you want to remove this file?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          onPress: () => {
            if ('id' in file) {
              setLocalExistingAttachments(prev => prev.filter(attachment => attachment.id !== documentId));
            }
            onFileRemove(fileWithId);
          },
          style: 'destructive',
        },
      ],
      { cancelable: true }
    );
  };

  const handlePress = () => {
    if (isKeyboardActive) return;
    onAttachmentPress();
  };

  return (
    <View style={styles.attachmentsContainer}>
      <Text variant="labelLarge" style={styles.attachmentsLabel}>Attachments</Text>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={[
          styles.attachmentsScrollContent,
          uploadingFiles.length === 0 && localExistingAttachments.length === 0 && { flex: 1 }
        ]}
      >
        {isLoading ? (
          <Surface mode="flat" style={[styles.attachmentBox, styles.loadingBox]}>
            <ActivityIndicator size="small" />
          </Surface>
        ) : (
          <>
            <Surface 
              mode="flat" 
              style={[
                styles.attachmentBox,
                styles.attachmentBoxWithFiles,
                {
                  width: 120
                },
              ]}
              onTouchEnd={handlePress}
              pointerEvents={isKeyboardActive ? 'none' : 'auto'}
            >
                <Add
                size={Icons.size.lg}
                color={Colors.primary}
              />
            </Surface>

            {localExistingAttachments.map((attachment, index) => {
              if (!attachment.documentUrl || !attachment.name) {
                return null;
              }

              return (
                <FilePreviewItem
                  key={`existing-${attachment.documentUrl}-${index}`}
                  file={attachment}
                  index={index}
                  onFileRemove={handleFileRemove}
                  getFileTypeIcon={getFileTypeIcon}
                  taskId={taskId}
                />
              );
            })}

            {uploadingFiles.map((file, index) => {
              const documentId = documentIds?.get(index);
              
              return (
                <FilePreviewItem
                  key={`${file.uri}-${index}`}
                  file={file}
                  index={index}
                  onFileRemove={handleFileRemove}
                  getFileTypeIcon={getFileTypeIcon}
                  documentId={documentId}
                  taskId={taskId}
                />
              );
            })}
          </>
        )}
      </ScrollView>
    </View>
  );
}

