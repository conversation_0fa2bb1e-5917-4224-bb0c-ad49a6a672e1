import React from 'react';
import { View } from 'react-native';
import { Text, TextInput } from 'react-native-paper';
import {
  BottomSheetModal as GorhomBottomSheetModal,
  BottomSheetBackdrop,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import { styles } from './BottomSheetModal.styles';

interface BottomSheetModalProps {
  bottomSheetRef: React.RefObject<GorhomBottomSheetModal>;
  snapPoints: string[];
  title: string;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  searchPlaceholder?: string;
  children: React.ReactNode;
}

export function CustomBottomSheetModal({
  bottomSheetRef,
  snapPoints,
  title,
  searchQuery,
  setSearchQuery,
  searchPlaceholder = "Search...",
  children,
}: BottomSheetModalProps) {
  return (
    <GorhomBottomSheetModal
      ref={bottomSheetRef}
      index={1}
      snapPoints={snapPoints}
      enablePanDownToClose
      style={styles.bottomSheet}
      backdropComponent={(props) => (
        <BottomSheetBackdrop
          {...props}
          appearsOnIndex={0}
          disappearsOnIndex={-1}
          pressBehavior="close"
        />
      )}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>
            {title}
          </Text>
        </View>

        <TextInput
          placeholder={searchPlaceholder}
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={styles.searchInput}
          mode="flat"
          underlineColor="transparent"
          activeUnderlineColor="transparent"
          cursorColor="#000000"
        />

        <BottomSheetScrollView contentContainerStyle={styles.scrollContent}>
          {children}
        </BottomSheetScrollView>
      </View>
    </GorhomBottomSheetModal>
  );
} 