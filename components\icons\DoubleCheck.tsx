import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const DoubleCheck = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      stroke="url(#Double_check-1_svg__a)"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="m1 6.171 1.432 1.841a.525.525 0 0 0 .818.016L7.818 2.5"
    />
    <Path
      stroke="url(#Double_check-1_svg__b)"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="m3.273 5.227 2.038 2.75a.525.525 0 0 0 .818.016L11 2.5"
    />
    <Defs>
      <LinearGradient
        id="Double_check-1_svg__a"
        x1={4.409}
        x2={4.409}
        y1={1.81}
        y2={9.531}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Double_check-1_svg__b"
        x1={7.136}
        x2={7.136}
        y1={1.814}
        y2={9.488}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Double_check_svg__a)"
    >
      <Path d="m1.6 6.667 1.575 2.025a.577.577 0 0 0 .9.018L9.1 2.629" />
      <Path d="m4.1 6.667 1.575 2.025a.577.577 0 0 0 .9.018L11.6 2.629" />
    </G>
    <Defs>
      <ClipPath id="Double_check_svg__a">
        <Path fill="#fff" d="M.6 0h12v12H.6z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default DoubleCheck;
