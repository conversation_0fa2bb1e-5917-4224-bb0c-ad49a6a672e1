import * as React from "react";
import Svg, {
  <PERSON>,
  <PERSON>,
  De<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>arGradient,
  Stop,
} from "react-native-svg";
import type { SvgProps } from "react-native-svg";
const NewComment = ({size, ...props}: {size: number} & SvgProps) => (
  <Svg
    width={size}
    height={size}
    fill="none"
    viewBox="0 0 32 32"
    {...props}
  >
    <G clipPath="url(#NewComment_svg__a)">
      <G clipPath="url(#NewComment_svg__b)">
        <Path
          fill="#FFEDDF"
          d="M16 32c8.837 0 16-7.163 16-16S24.837 0 16 0 0 7.163 0 16s7.163 16 16 16"
        />
        <Path
          fill="#F4862B"
          d="M24.023 7.996H7.977a2.29 2.29 0 0 0-2.29 2.29v10.75a2.29 2.29 0 0 0 2.29 2.29h5.173l2.083 3.611a.884.884 0 0 0 1.532 0l2.083-3.608h5.174a2.29 2.29 0 0 0 2.29-2.29v-10.75a2.29 2.29 0 0 0-2.29-2.293"
        />
        <Path
          fill="url(#NewComment_svg__c)"
          d="M24.023 20.67H7.978a2.29 2.29 0 0 1-2.29-2.29v2.658a2.29 2.29 0 0 0 2.29 2.29h5.173l2.083 3.61a.884.884 0 0 0 1.532 0l2.083-3.61h5.174a2.29 2.29 0 0 0 2.29-2.29v-2.657a2.29 2.29 0 0 1-2.29 2.29"
        />
        <Path
          fill="#fff"
          d="M10.844 16.489a1.357 1.357 0 1 0 0-2.715 1.357 1.357 0 0 0 0 2.715M16 16.489a1.358 1.358 0 1 0 0-2.715 1.358 1.358 0 0 0 0 2.715M21.156 16.489a1.357 1.357 0 1 0 0-2.715 1.357 1.357 0 0 0 0 2.715"
        />
      </G>
    </G>
    <Defs>
      <ClipPath id="NewComment_svg__a">
        <Path fill="#fff" d="M0 0h32v32H0z" />
      </ClipPath>
      <ClipPath id="NewComment_svg__b">
        <Path fill="#fff" d="M0 0h32v32H0z" />
      </ClipPath>
      <LinearGradient
        id="NewComment_svg__c"
        x1={16}
        x2={15.981}
        y1={27.379}
        y2={16.686}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F4862B" stopOpacity={0.85} />
        <Stop offset={0.36} stopColor="#FAC395" stopOpacity={0.4} />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default NewComment;
