import gql from 'graphql-tag';

export const GET_PARTY_TYPES = gql`
query GetMdPartyTypes {
  getMdPartyTypes {
    ... on MdPartyTypesResponse {
      result {
        mdPartyTypes {
          id
          name
          vendorTypes {
            id
            name
          }
        }
      }
    }
  }
}
`
export const GET_EVENT_FOR_EVENT_DASHBOARD = gql`
query GetEventById($getEventByIdId: ID!) {
  getEventById(id: $getEventByIdId) {
    ... on EventResponse {
      status
      message
      result {
        event {
          id
          eventType {
            id
            name
            bannerImage
          }
          name
        }
      }
    }
    ... on EventErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}
`

export const GET_EVENT_FUN_FACT_FOR_EVENT_DASHBOARD = gql`
query GetMdFunFacts($filter: MdFunFactFilterInput) {
  getMdFunFacts(filter: $filter) {
    ... on MdFunFactsResponse {
      status
      message
      pagination {
        totalItems
        totalPages
        pageSize
        currentPage
      }
      result {
        mdFunFacts {
          id
          funFact
        }
      }
    }
    ... on MdFunFactErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}
`

export const UPDATE_PARTY = gql`
mutation UpdateParty($updatePartyId: ID!, $input: PartyUpdateInput!) {
  updateParty(id: $updatePartyId, input: $input) {
    ... on PartyResponse {
      result {
        party {
          id
          name
        }
      }
    }
    ... on PartyErrorResponse {
      errors {
        field
        message
      }
    }
  }
}
`

export const GET_LOCATIONS = gql`
query Result {
  getMdServiceLocations {
    ... on MdServiceLocationsResponse {
      result {
        mdServiceLocations {
          city
          areas
          id
          name
        }
      }
    }
  }
}
`
export const ADD_PARTY = gql`
mutation CreateParty($input: PartyInput!) {
  createParty(input: $input) {
    ... on PartyResponse {
      result {
        party {
          id
        invitation {
            _id
          }
        }
      }
    }
          ... on PartyErrorResponse {
      message
      errors {
        field
        message
      }
    }
  }
}
`

export const GET_PARTIES_BY_EVENT_ID = gql`
query GetPartiesByEventId($filter: PartyFilterInput, $pagination: PaginationInput) {

  getParties(filter: $filter, pagination: $pagination) {
    ... on PartiesResponse {
      status
      message
      pagination {
        totalItems
        totalPages
        pageSize
        currentPage
      }
      result {
        parties {
          id
          name
          time
          serviceLocation {
            city
            name
          }
          partyType {
            portraitImage
          }
        }
      }
    }
    ... on PartyErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}
`
export const GET_PARTY_BY_ID = gql`
query GetPartyById($getPartyByIdId: ID!) {
  getPartyById(id: $getPartyByIdId) {
    ... on PartyResponse {
      result {
        party {
          name
          partyType {
            id
            name
            portraitImage
          }
          time
          vendorTypes {
            id
            name
          }
          serviceLocation {
            id
            name
            city
          }
          expectedGuestCount
          totalBudget
          event {
            id
          }
          coHosts {
            userId {
              id
              email
              firstName
              lastName
              phone
            }
          }
          invitation {
            _id
            media {
              id
              url
              title
            }
          }
          venueAddress {
            id
            address
            name
          }
          invitationSettings {
            additional_guest_allowed
            additional_guest_limit
            allow_guest_to_add_photo
            is_guest_list_public
            send_auto_reminder_to_all_guests
          }
          event {
            mainHost {
              userId {
                firstName
              }
            }
          }
        }
      }
    }
  }
}
`

export const CREATE_MEDIA = gql`
mutation CreateMedia($input: MediaInput!) {
  createMedia(input: $input) {
    ... on MediaResponse {
      result {
        media {
          id
          url
          title
        }
      } 
    }
    ... on MediaErrorResponse {
      errors {
        field
        message
      }
    }
  }
}
`

export const GET_EVENT_ASSIGNEES_BY_EVENT_ID = gql`
query EventAssigneesResponse($eventId: ID!) {
  getEventAssignees(eventId: $eventId) {
    ... on EventAssigneesResponse {
      message
      status
      result {
        assignees {
          id
          firstName
          lastName
        }
      }
    }
    ... on EventErrorResponse {
      errors {
        field
        message
      }
      message
      status
    }
  }
}
`
export const GET_ALL_PARTY_SERVICES = gql`
query Result {
  getMdVendorTypes {
    ... on MdVendorTypesResponse {
      result {
        mdVendorTypes {
          id
          name
        }
      }
    }
    ... on MdVendorTypeErrorResponse {
      message
      status
    }
  }
}
`

export const UPDATE_INVITATION = gql`
mutation UpdateInvitation($invitationId: ID!, $invitation: InvitationInput!) {
  updateInvitation(invitationId: $invitationId, invitation: $invitation) {
    ... on InvitationResponse {
      result {
        invitation {
          media {
            url
          }
        }
      }
    }
    ... on InvitationErrorResponse {
      errors {
        field
        message
      }
    }
  }
}
`

export const CREATE_EVENT = gql`
mutation CreateEvent($input: EventInput!) {
  createEvent(input: $input) {
    ... on EventResponse {
      result {
        event {
          id
          name
        }
      }
    }
    ... on EventErrorResponse {
      errors {
        field
        message
      }
      status
    }
  }
}
`;