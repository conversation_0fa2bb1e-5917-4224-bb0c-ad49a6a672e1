import * as React from "react";
import Svg, { <PERSON>, <PERSON>, Defs, <PERSON>lip<PERSON><PERSON>, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const Cancel = ({
  size,
  color,
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' ? (
        <>
          <G clipPath="url(#Cancel-1_svg__a)">
            <Path
              fill={hasGradient ? "url(#Cancel-1_svg__b)" : color}
              d="M6 .5a5.5 5.5 0 1 1 0 11 5.5 5.5 0 0 1 0-11M3.318 8.033a.458.458 0 0 0 .649.648zm4.715.648a.458.458 0 1 0 .648-.648zm.648-5.363a.46.46 0 0 0-.648 0L6 5.352 3.967 3.318l-.072-.058a.459.459 0 0 0-.635.635l.058.072 2.034 2.032-2.034 2.034.325.323.324.325 2.032-2.034 2.034 2.034.323-.325.325-.323L6.647 6l2.034-2.032a.46.46 0 0 0 0-.649"
            />
          </G>
          <Defs>
            <LinearGradient
              id="Cancel-1_svg__b"
              x1={6}
              x2={6}
              y1={-0.828}
              y2={14.029}
              gradientUnits="userSpaceOnUse"
            >
              <Stop stopColor={gradientStartColor} />
              <Stop offset={1} stopColor={gradientEndColor} />
            </LinearGradient>
            <ClipPath id="Cancel-1_svg__a">
              <Path fill="#fff" d="M0 0h12v12H0z" />
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G stroke={color} strokeLinecap="round" clipPath="url(#Cancel_svg__a)">
            <Path strokeLinejoin="round" d="M6 11A5 5 0 1 0 6 1a5 5 0 0 0 0 10" />
            <Path d="m3.692 3.692 4.616 4.616M8.308 3.692 3.692 8.308" />
          </G>
          <Defs>
            <ClipPath id="Cancel_svg__a">
              <Path fill="#fff" d="M0 0h12v12H0z" />
            </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  );
};

export default Cancel;
