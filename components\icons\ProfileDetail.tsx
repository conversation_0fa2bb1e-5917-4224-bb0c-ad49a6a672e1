import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const ProfileDetail = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Profile_detail_svg__a)">
      <Path
        fill="url(#Profile_detail_svg__b)"
        fillRule="evenodd"
        d="M11.5 6c0 1.475-.58 2.814-1.525 3.801A5.48 5.48 0 0 1 6.016 11.5h-.032a5.48 5.48 0 0 1-3.959-1.699A5.5 5.5 0 1 1 11.5 6M9.334 8.75A4.31 4.31 0 0 0 6 7.179 4.31 4.31 0 0 0 2.666 8.75 4.31 4.31 0 0 0 6 10.321 4.31 4.31 0 0 0 9.334 8.75M6 6.393a1.964 1.964 0 1 0 0-3.929 1.964 1.964 0 0 0 0 3.929"
        clipRule="evenodd"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Profile_detail_svg__b"
        x1={6}
        x2={6}
        y1={-0.828}
        y2={14.029}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Profile_detail_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Profile_detail-1_svg__a)"
    >
      <Path d="M6 7.436A1.923 1.923 0 1 0 6 3.59a1.923 1.923 0 0 0 0 3.846M2.715 10.436a3.845 3.845 0 0 1 6.57 0" />
      <Path d="M6 11.667a5 5 0 1 0 0-10 5 5 0 0 0 0 10" />
    </G>
    <Defs>
      <ClipPath id="Profile_detail-1_svg__a">
        <Path fill="#fff" d="M0 .667h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default ProfileDetail;
