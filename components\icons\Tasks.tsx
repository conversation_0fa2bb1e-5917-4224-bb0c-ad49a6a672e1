import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const Tasks = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      fill="url(#Tasks-1_svg__a)"
      fillRule="evenodd"
      d="M4.821.5a.786.786 0 0 0-.785.786v.393c0 .434.351.785.785.785H7.18a.786.786 0 0 0 .785-.785v-.393A.786.786 0 0 0 7.18.5zm-1.767.786H2.66c-.651 0-1.179.527-1.179 1.178v7.857c0 .651.528 1.179 1.179 1.179h6.678c.651 0 1.179-.528 1.179-1.179V2.464c0-.65-.528-1.178-1.179-1.178h-.393v.393c0 .976-.791 1.767-1.767 1.767H4.82A1.77 1.77 0 0 1 3.054 1.68zm5.264 3.85a.59.59 0 0 1 .118.825L6.079 9.104a.59.59 0 0 1-.799.136l-1.178-.785a.59.59 0 1 1 .653-.981l.715.476 2.023-2.696a.59.59 0 0 1 .825-.118"
      clipRule="evenodd"
    />
    <Defs>
      <LinearGradient
        id="Tasks-1_svg__a"
        x1={6}
        x2={6}
        y1={-0.828}
        y2={14.029}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <Path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M7.154 1H4.846a.77.77 0 0 0-.77.77v.384c0 .425.345.77.77.77h2.308a.77.77 0 0 0 .77-.77v-.385A.77.77 0 0 0 7.153 1"
    />
    <Path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M8.115 1.77H9.27a.77.77 0 0 1 .77.769v7.692a.77.77 0 0 1-.77.77H2.731a.77.77 0 0 1-.77-.77V2.539a.77.77 0 0 1 .77-.77h1.154"
    />
    <Path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="m4.462 7.538 1.153.77L7.923 5.23"
    />
        </>
      )}
    </Svg>
  );
};
export default Tasks;
