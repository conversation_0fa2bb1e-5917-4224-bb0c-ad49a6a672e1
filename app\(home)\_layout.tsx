import { useFonts } from 'expo-font';
import { SignedIn, SignedOut } from '@clerk/clerk-expo'
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect } from 'react';
import { StyleSheet, Platform, Pressable, View } from 'react-native';
import { useRouter } from 'expo-router';
import 'react-native-reanimated';
import { StatusBar } from 'expo-status-bar';
import { ScrollContext } from '@/commons/ScrollContext';
import { useScrollContext } from '@/commons/ScrollContext';
import Animated, {
    interpolate,
    useSharedValue,
    useAnimatedStyle
} from 'react-native-reanimated';
import { Button } from 'react-native-paper';
import WelcomeBanner from '@/components/WelcomeBanner';
import { SignUpNames } from '@/constants/displayNames';
import { useInitializeUser } from '@/app/auth/useInitializerUser';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';
import { Previous } from '@/components/icons';
import { Colors, Spacing, Borders, Icons } from '@/constants/DesignSystem';

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

SplashScreen.preventAutoHideAsync();

const AnimatedBackButton = ({ onPress }: { onPress: () => void }) => {
    const { scrollY } = useScrollContext();
    const animatedStyle = useAnimatedStyle(() => {
        const opacity = interpolate(
            scrollY.value,
            [0, 100],
            [1, 0],
            'clamp'
        );

        const translateY = interpolate(
            scrollY.value,
            [0, 100],
            [0, -20],
            'clamp'
        );

        return {
            opacity,
            transform: [{ translateY }],
        };
    });

    return (
        <AnimatedPressable
            onPress={onPress}
            style={[styles.backButtonContainer, animatedStyle]}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
            <Animated.View style={styles.backButton}>
                <Previous size={Icons.size.md} color={Colors.primary} />
            </Animated.View>
        </AnimatedPressable>
    );
};

function AuthenticatedLayout() {
    const router = useRouter();

    return (
        <View style={{ flex: 1, backgroundColor: 'white' }}>
            {Platform.OS === 'ios' ? (
                <View style={styles.statusBarContainer}>
                    <StatusBar style='dark' translucent />
                </View>
            ) : (
                <StatusBar
                    style="dark"
                    translucent
                    backgroundColor="rgba(255, 255, 255, 0.8)"
                />
            )}
            <Stack>
              
                <Stack.Screen name="(tabs)" options={{
                        headerShown: false,
                        presentation: 'card',
                    }} />
                      <Stack.Screen name="onBoarding" options={{
                        headerShown: false,
                        presentation: 'card',
                    }} />
                <Stack.Screen
                    name="party-dashboard/[partyId]"
                    options={{
                        headerTitle: 'Party Dashboard',
                        headerBackTitle: '',
                        headerTitleAlign: 'left'
                    }}
                />
                <Stack.Screen
                    name="VendorDetails/[vendorId]"
                    options={{
                        headerTitle: '',
                        headerBackTitle: '',
                        headerTransparent: true,
                        headerLeft: ({ canGoBack }) => {
                            if (!canGoBack) return null;
                            return <AnimatedBackButton onPress={() => router.back()} />;
                        }
                    }}
                />
                <Stack.Screen
                    name="EventsDashboard/AddParty"
                    options={{
                        headerShown: false,
                        presentation: 'modal',
                    }}
                />
                <Stack.Screen
                    name="userProfile"
                    options={{
                        headerTitleAlign: 'left',
                        headerBackButtonDisplayMode: 'minimal',
                        headerTitle: 'Profile',
                        headerTitleStyle: {
                            fontSize: 22,
                        },
                        presentation: 'card',
                    }}
                />
                <Stack.Screen
                    name="inAppNotifications"
                    options={{
                        headerTitleAlign: 'left',
                        headerBackButtonDisplayMode: 'minimal',
                        headerTitle: 'Notifications',
                        headerTitleStyle: {
                            fontSize: 22,
                        },
                        presentation: 'card',
                    }}
                />
                <Stack.Screen
                    name="Task/CreateTask"
                    options={{
                        headerShown: true,
                        headerTitle: 'Create Task',
                        headerTitleStyle: {
                            fontSize: 24,
                         },
                        headerBackTitle: '',
                        headerBackButtonDisplayMode: 'minimal',
                        headerShadowVisible: true,
                        headerTitleAlign: 'center',
                    }}
                />

                <Stack.Screen
                    name="Task/UpdateTask"
                    options={{
                        headerShown: false,
                        headerBackButtonDisplayMode: 'minimal',
                    }}
                />

                <Stack.Screen
                    name="SearchInvitations"
                    options={{
                        headerShown: true,
                        headerTitle: 'Search Parties',
                        headerTitleStyle: {
                            fontSize: 24,
                        },
                        headerBackTitle: '',
                        headerBackButtonDisplayMode: 'minimal',
                        headerShadowVisible: true,
                        headerTitleAlign: 'left',
                        presentation: 'card'
                    }}
                />

                <Stack.Screen
                    name="Task/Filter"

                    options={{
                        headerShown: false,
                        headerTitle: 'Filters',
                        headerTitleStyle: {
                            fontSize: 24,
                            },
                        headerBackTitle: '',
                        headerBackButtonDisplayMode: 'minimal',
                        headerShadowVisible: true,
                        headerTitleAlign: 'center',
                    }}
                />

                <Stack.Screen
                    name="Settings/PersonalInformation"
                    options={{
                        headerTitle: 'Personal Information',
                        headerBackButtonDisplayMode: 'minimal',
                        presentation: 'card',
                    }}
                />
                <Stack.Screen
                    name="Settings/Notifications"
                    options={{
                        headerTitle: 'Notifications',
                        headerBackButtonDisplayMode: 'minimal',
                        presentation: 'card',
                    }}
                />
                <Stack.Screen
                    name="Settings/HelpCenter"
                    options={{
                        headerTitle: 'Help Center',
                        headerBackButtonDisplayMode: 'minimal',
                        presentation: 'card',
                    }}
                />
                <Stack.Screen
                    name="Settings/TermsOfService"
                    options={{
                        headerTitle: 'Terms Of Service',
                        headerBackButtonDisplayMode: 'minimal',
                        presentation: 'card',
                    }}
                />
                <Stack.Screen
                    name="Settings/HowFastPartyWorks"
                    options={{
                        headerTitle: 'How Fast Party Works',
                        headerBackButtonDisplayMode: 'minimal',
                        presentation: 'card',
                    }}
                />
                <Stack.Screen
                    name="Settings/Feedback"
                    options={{
                        headerTitle: 'Feedback',
                        headerBackButtonDisplayMode: 'minimal',
                        presentation: 'card',
                    }}
                />
                <Stack.Screen
                    name="Settings/ReportIssue"
                    options={{
                        headerTitle: 'Report Issue',
                        headerBackButtonDisplayMode: 'minimal',
                        presentation: 'card',
                    }}
                />
                <Stack.Screen
                    name="/Settings/HelpCenter"
                    options={{
                        headerTitle: 'Help Center',
                        headerBackButtonDisplayMode: 'minimal',
                        presentation: 'card',
                    }}
                />
                <Stack.Screen
                    name="Settings/PrivacyPolicy"
                    options={{
                        headerTitle: 'Privacy Policy',
                        headerBackButtonDisplayMode: 'minimal',
                        presentation: 'card',
                    }}
                />
                <Stack.Screen
                    name="/Settings/TermsOfService"
                    options={{
                        headerTitle: 'Terms Of Service',
                        headerBackButtonDisplayMode: 'minimal',
                        presentation: 'card',
                     }}
                />

                <Stack.Screen
                    name="Task/attachment-preview"
                    options={{
                        headerShown: false,
                        presentation: 'card',
                    }}
                />
                <Stack.Screen
                    name="albumsList/albumsList"
                    options={{
                        headerShown: false,
                        presentation: 'card',
                    }}
                />
            </Stack>
        </View>
    );
}

export default function HomeLayout() {
    const [loaded] = useFonts({
        SpaceMono: require('../../assets/fonts/SpaceMono-Regular.ttf'),
    });
    const router = useRouter();
    const scrollY = useSharedValue(0);
    const isUserInitialized = useInitializeUser();

    useEffect(() => {
        if (loaded && isUserInitialized) {
            SplashScreen.hideAsync();
        }
    }, [loaded, isUserInitialized]);

    if (!loaded || !isUserInitialized) {
        return <FastPartyActivityIndicator />;
    }

    return (
        <ScrollContext.Provider value={{ scrollY }}>
            <SignedIn>
                <AuthenticatedLayout />
            </SignedIn>
            <SignedOut>
                <View style={styles.signedOutContainer}>
                    <WelcomeBanner />
                    <View style={styles.signUpButtonsContainer}>
                        <Button
                            mode="contained"
                            onPress={() => router.push('/sign-up')}
                            buttonColor={Colors.button.primary}
                            textColor={Colors.white}
                            style={styles.primaryButton}
                        >
                            {SignUpNames.SIGN_UP}
                        </Button>
                        <Button
                            mode="outlined"
                            onPress={() => router.push('/sign-in')}
                            textColor={Colors.text.primary}
                            style={styles.secondaryButton}
                        >
                            {SignUpNames.SIGN_IN}
                        </Button>
                    </View>
                </View>
            </SignedOut>
        </ScrollContext.Provider>
    );
}

const styles = StyleSheet.create({
    backButtonContainer: {
        marginLeft: Platform.OS === 'ios' ? 8 : 0,
        left: Platform.OS === 'ios' ? 8 : 0,
        zIndex: 100,
    },
    backButton: {
        backgroundColor: Colors.white,
        width: 32,
        height: 32,
        borderRadius: Borders.radius.circle,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: Colors.black,
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    statusBarContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 999,
        overflow: 'hidden',
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
    },
    signedOutContainer: {
        flex: 1,
        flexDirection: 'column',
        gap: Spacing.md,
        padding: Spacing.lg,
        backgroundColor: Colors.background.primary,
        justifyContent: 'center',
        alignItems: 'center',
    },
    signUpButtonsContainer: {
        gap: Spacing.md,
        width: '80%',
        maxWidth: 400,
        alignSelf: 'center',
    },
    primaryButton: {
        borderRadius: Borders.radius.pill,
        paddingVertical: Spacing.sm,
    },
    secondaryButton: {
        borderRadius: Borders.radius.pill,
        borderColor: Colors.primary,
        paddingVertical: Spacing.sm,
    },
});