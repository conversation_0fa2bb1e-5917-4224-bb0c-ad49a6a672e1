import { ScrollView, Text, View, Pressable, ImageBackground, StyleSheet } from 'react-native';
import { styles as homepageStyles } from '@/components/HomePage/homepage.styles';
import { Event } from '@/components/HomePage/types';
import { getSmartDateTime } from '@/app/(home)/utils/reusableFunctions';
import LottieView from 'lottie-react-native';
import { useEventStore } from '@/store/eventStore';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { HomeRootStackList } from '@/app/Home/HomeNavigation';
import { Colors, Typography, Spacing, Icons } from '@/constants/DesignSystem';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';
import { useState, useRef, useCallback, useMemo, useEffect, memo } from 'react';
import { Text as PaperText } from 'react-native-paper';
import { useQuery } from '@apollo/client';
import { GET_EVENTS } from '@/components/HomePage/HomePage.data';
import { getRandomDefaultImage } from '@/constants/HomePageDefaultImages';
import { useUserStore } from '@/app/auth/userStore';
import { useInvitationStore } from '@/store/invitationStore';
import { Add } from '@/components/icons';
import { useFocusEffect } from '@react-navigation/native';

interface EventsListProps {
  fromScreen?: string;
  selectedFilter: 'next' | 'host' | 'guest' | 'past';
  circleId?: string;
}

interface ServerEvent {
  id: string;
  parties: Array<{
    id: string;
    name: string;
    time: string;
    muted?: boolean;
    invitation?: {
      media?: Array<{ url: string }>;
    };
    guests?: Array<{
      user: {
        id: string;
      };
    }>;
    rsvps?: Array<{
      id: string;
      status?: string;
      guest?: {
        user?: {
          id: string;
        };
      };
    }>;
    serviceLocation?: {
      city: string;
      state?: string;
    };
  }>;
  mainHost: {
    userId: {
      firstName: string;
      lastName: string;
    };
  };
  name: string;
  startDate: string;
}

interface CacheEventsData {
  getUserEvents: {
    result: {
      events: ServerEvent[];
    };
  };
}

interface EventCardProps {
  event: Event;
  onPress: (event: Event) => void;
}

const EventCard = memo(({ event, onPress }: EventCardProps) => {
  const [imageError, setImageError] = useState(false);
  const imageSource = imageError ? getRandomDefaultImage() : event.image;

  return (
    <View style={homepageStyles.eventCard}>
      <Pressable onPress={() => onPress(event)}>
        <View style={homepageStyles.cardImageContainer}>
          <ImageBackground
            source={imageSource as any}
            style={homepageStyles.cardImage}
            imageStyle={{ width: '100%', height: '100%' }}
            onError={() => setImageError(true)}
          >
          </ImageBackground>
          <View style={homepageStyles.dateChip}>
              <PaperText style={homepageStyles.dateChipText}>
                {getSmartDateTime(event.date)}
              </PaperText>
            </View>
        </View>
      </Pressable>

      <View style={homepageStyles.cardContent}>
        <PaperText numberOfLines={1} style={homepageStyles.cardTitle}>
          {event.title}
        </PaperText>
        <PaperText numberOfLines={1} style={homepageStyles.cardSubtitle}>
          Hosted by <Text style={{ fontWeight: '700' }}>{event.hostedBy}</Text>
        </PaperText>
      </View>
    </View>
  );
});

const PAGE_SIZE = 10;

export function EventsList({ fromScreen = '', selectedFilter, circleId }: EventsListProps) {
  const userData = useUserStore((state: any) => state.userData);
  const setSelectedEventId = useEventStore((state: any) => state.setSelectedEventId);
  const navigation = useNavigation<NavigationProp<HomeRootStackList>>();
  const setInvitations = useInvitationStore((state: any) => state.setInvitations);
  
  const scrollViewRef = useRef<ScrollView>(null);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [dialogConfig, setDialogConfig] = useState<{
    title: string;
    message: string;
    confirmText: string;
    onConfirm: () => void;
    confirmButtonColor?: string;
  } | null>(null);

  // Get filtered events based on selected filter
  const getFilterVariables = useCallback(() => {
    switch (selectedFilter) {
      case 'next':
        return { presentAndUpcoming: true, ...(circleId ? {eventGroupId: circleId} : {}) };
      case 'past':
        return { presentAndUpcoming: false, ...(circleId ? {eventGroupId: circleId} : {}) };
      case 'host':
        return { userType: 'HOST' };
      case 'guest':
        return { userType: 'GUEST' };
      default:
        return { presentAndUpcoming: true, ...(circleId ? {eventGroupId: circleId} : {}) };
    }
  }, [selectedFilter]);

  const {
    data,
    loading,
    error,
    refetch: refetchEvents,
    fetchMore,
    networkStatus,
  } = useQuery(GET_EVENTS, {
    fetchPolicy: 'cache-and-network',
    nextFetchPolicy: 'cache-first',
    notifyOnNetworkStatusChange: true,
    pollInterval: 5000, // Poll every 5 seconds for updates
    variables: {
      filter: getFilterVariables(),
      pagination: {
        skip: 0,
        limit: PAGE_SIZE,
      }
    },
    onCompleted: (data) => {
      const totalItems = data?.getUserEvents?.pagination?.totalItems || 0;
      setHasMore(processedEvents.length < totalItems);
    }
  });

  // Track if we're loading more data
  const isLoadingMore = networkStatus === 3; // NetworkStatus.fetchMore
  const isRefetching = networkStatus === 3; // NetworkStatus.refetch
  const shouldShowLoading = networkStatus === 1 && !data; // NetworkStatus.loading and no data

  // Process events from server
  const processedEvents = useMemo(() => {
    if (!data?.getUserEvents?.result?.events) return [];

    const uniqueEvents = new Map();

    data.getUserEvents.result.events.forEach((event: ServerEvent) => {
      if (!uniqueEvents.has(event.id)) {
        const party = event.parties[0];
        const mediaUrl = party?.invitation?.media?.[0]?.url;
        const isCurrentUserGuest = party?.guests?.some((guest: any) => guest.user.id === userData?.id);
        const currentUserInvitationId = party?.rsvps?.find((rsvp: any) => rsvp?.guest?.user?.id === userData?.id)?.id;
        const rsvpStatus = party?.rsvps?.find((rsvp: any) => rsvp?.guest?.user?.id === userData?.id)?.status?.toLowerCase();
        const serviceLocation = party?.serviceLocation;
        const locationString = serviceLocation
          ? `${serviceLocation.city}${serviceLocation.state ? `, ${serviceLocation.state}` : ''}`
          : 'Location TBD';

        const originalId = party?.id;

        uniqueEvents.set(event.id, {
          id: event.id,
          originalId: originalId,
          title: party?.name || event.name,
          hostedBy: `${event.mainHost.userId.firstName} ${event.mainHost.userId.lastName}`,
          image: mediaUrl ? { uri: mediaUrl } : getRandomDefaultImage(),
          type: selectedFilter,
          date: party?.time || event.startDate,
          location: locationString,
          isCurrentUserHost: !isCurrentUserGuest,
          currentUserInvitationId: currentUserInvitationId,
          currentUserRsvpStatus: rsvpStatus,
        });
      }
    });

    return Array.from(uniqueEvents.values());
  }, [data, selectedFilter, userData]);

  useEffect(() => {
    if (processedEvents.length > 0) {
      setInvitations(processedEvents);
    }
  }, [processedEvents, setInvitations]);

  const handleNavigateToPartyDashboard = useCallback((partyId?: string, isHost?: boolean, rsvpStatus?: string) => {
    if (partyId) {
      
      const selectedEvent = processedEvents.find((event: Event) => event.originalId === partyId);

      if (selectedEvent) {
        setSelectedEventId(selectedEvent.id);
      }

      navigation.navigate('PartyDetails', {
        screen: 'NewPartyDetailsScreen',
        params: { partyId: partyId, isHosting: isHost, rsvp: rsvpStatus }
      });
    } else {
   
      navigation.navigate('AddPartyScreen', {
        screen: 'AddParty',
        params: {}
      });
    }
  }, [navigation, processedEvents, setSelectedEventId]);

  const handleEventPress = useCallback((event: Event) => {
    if (!event.originalId) {
   
      navigation.navigate('AddPartyScreen', {
        screen: 'AddParty',
        params: {}
      });
      return;
    }

    const currentEvent = processedEvents.find((processedEvent: Event) => processedEvent.id === event.id);
    const rsvpStatus = currentEvent?.currentUserRsvpStatus?.toLowerCase();
    const isHost = currentEvent?.isCurrentUserHost;
    handleNavigateToPartyDashboard(event.originalId, isHost, rsvpStatus);
  }, [navigation, handleNavigateToPartyDashboard, processedEvents]);

  useEffect(() => {
    setPage(0);
    setHasMore(true);
    // Scroll to the start when filter changes
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({ x: 0, animated: true });
    }
  }, [selectedFilter]);

  useFocusEffect(
    useCallback(() => {
      refetchEvents();
    }, [refetchEvents])
  );

  if (shouldShowLoading) {
    return (
      <View style={[homepageStyles.eventsContainer, { justifyContent: 'center', alignItems: 'center', minHeight: 260 }]}>
        <FastPartyActivityIndicator />
      </View>
    );
  }

  if (error) {
    return (
      <View style={[homepageStyles.eventsContainer, { justifyContent: 'center', alignItems: 'center', minHeight: 260 }]}>
        <Text style={{
          fontFamily: Typography.fontFamily.primary,
          color: Colors.error
        }}>
          Unable to load events
        </Text>
      </View>
    );
  }

  const EmptySpace = () => (
    <View style={{ width: 16 }} />
  );

  const NewPartyCard = () => (
    <View style={homepageStyles.eventCard}>
      <Pressable>
        <View style={homepageStyles.newPartyCardImageContainer}>
          <View style={{ alignItems: 'center', justifyContent: 'center', height: '100%' }}>
            <LottieView
              source={require('../../../assets/json/Home Page Animation (1).json')}
              autoPlay={true}
              loop={true}
              cacheComposition={true}
              style={{
                width: 130,
                height: 150
              }}
            />
          </View>
        </View>
      </Pressable>
      <Pressable
        style={homepageStyles.cardButton}
        onPress={() => handleNavigateToPartyDashboard()}>
        <Add size={Icons.size.sm} color={Colors.black}/>
        <Text style={homepageStyles.cardButtonText}>
          New Party
        </Text>
      </Pressable>
    </View>
  );

  if (processedEvents.length === 0) {
    return (
      fromScreen === 'circleDetails' ? (
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <Text style={{
            fontFamily: Typography.fontFamily.primary,
            color: Colors.text.secondary,
            fontSize: Typography.fontSize.md
          }}>
            {'There are no events to display'}
          </Text>
        </View>
        ) : (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={homepageStyles.eventsContainer}>
          <NewPartyCard />
        </ScrollView>
      )
    );
  }

  return (
    <>
    <ScrollView
      ref={scrollViewRef}
      horizontal
      showsHorizontalScrollIndicator={false}
      style={homepageStyles.eventsContainer}
      removeClippedSubviews={false}
      maintainVisibleContentPosition={{
        minIndexForVisible: 0,
        autoscrollToTopThreshold: undefined
      }}
    >
      {fromScreen !== 'circleDetails' && <NewPartyCard />}
      {processedEvents.map((event: Event) => (
        <EventCard
          key={event.id}
          event={event}
          onPress={handleEventPress}
        />
      ))}
      {isLoadingMore && (
        <View style={[homepageStyles.eventCard, { justifyContent: 'center', alignItems: 'center' }]}>
          <FastPartyActivityIndicator />
        </View>
      )}
      <EmptySpace />
    </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  bottomSheetContentContainer: {
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.md,
  },
  bottomSheetItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.md,
  },
  bottomSheetText: {
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.lg,
    color: Colors.black,
    marginLeft: Spacing.md,
  },
});