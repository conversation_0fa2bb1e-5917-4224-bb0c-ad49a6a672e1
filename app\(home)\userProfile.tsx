import { StyleSheet, <PERSON>, Sc<PERSON>View } from 'react-native'
import React, { useState } from 'react'
import { ThemedView } from '@/components/UI/ThemedView'
import { Button, Text, Dialog, Portal } from 'react-native-paper'
import { useAuth } from '@clerk/clerk-expo'
import { ExternalPathString, RelativePathString, router } from 'expo-router'
import { useUserStore } from '../auth/userStore'
import { userStorage } from '../auth/userStorage'
import { FastPartyClient } from '@/commons/apollo-client'
import SettingsSection from '@/components/settings/SettingsSection'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { DELETE_USER } from '@/components/settings/Settings.data'
import { useMutation } from '@apollo/client'
import { ProfileDetail, Notification, File, Support, Alert, MayBe, Sheild } from '@/components/icons'
import { Icons, Colors } from '@/constants/DesignSystem' 

export interface SettingsItem {
  id: string
  name: string
  icon: React.ReactNode
  route: string
}

export interface SettingsSection {
  title: string
  items: SettingsItem[]
}
const settingsData: SettingsSection[] = [
  {
    title: 'Settings',
    items: [
      { id: 'personal', name: 'Personal Information', icon: <ProfileDetail size={Icons.size.xl} color={Colors.secondary}/>, route: '/Settings/PersonalInformation' },
      { id: 'notifications', name: 'Notifications', icon: <Notification size={Icons.size.xl} color={Colors.secondary}/>, route: '/Settings/Notifications' },
    ]
  },
  {
    title: 'Legal',
    items: [
      { id: 'terms', name: 'Terms of Service', icon: <File size={Icons.size.xl} color={Colors.secondary}/>, route: '/Settings/TermsOfService' },
      { id: 'privacy', name: 'Privacy Policy', icon: <Sheild size={Icons.size.xl} color={Colors.secondary}/>, route: '/Settings/PrivacyPolicy' },
    ]
  }
]

const UserProfile = () => {
  const { bottom } = useSafeAreaInsets()
  const { signOut } = useAuth()
  const userData = useUserStore((state) => state.userData);
  const [deletUserById, { loading, error }] = useMutation(DELETE_USER)
  const [isDeleteDialogVisible, setIsDeleteDialogVisible] = useState(false)
  const [isLogoutDialogVisible, setIsLogoutDialogVisible] = useState(false)

  const showDeleteDialog = () => setIsDeleteDialogVisible(true)
  const hideDeleteDialog = () => setIsDeleteDialogVisible(false)

  const showLogoutDialog = () => setIsLogoutDialogVisible(true)
  const hideLogoutDialog = () => setIsLogoutDialogVisible(false)

  const confirmLogout = async () => {
    try {
      await signOut()
      await userStorage.clearUser()
      await userStorage.clearToken()
      useUserStore.getState().clearUserData()

      FastPartyClient.clearStore()

      router.replace('/sign-in')
    } catch (error) {
      console.error('Error signing out:', error)
    } finally {
      hideLogoutDialog()
    }
    console.log("build commit")
  }

  const handleDeleteAccount = async () => {
    try {
      if (!userData?.id) {
        console.error('User ID not found')
        return
      }

      const deleteUserResponse = await deletUserById()

      if (deleteUserResponse.data) {
        await signOut()
        await userStorage.clearUser()
        await userStorage.clearToken()
        useUserStore.getState().clearUserData()
        FastPartyClient.clearStore()
        router.replace('/sign-in')
      }
    } catch (error) {
      console.error('Error deleting user:', error)
      // You might want to show an error message to the user here
    } finally {
      hideDeleteDialog()
    }
  }

  return (
    <ThemedView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[
          styles.scrollViewContent,
          { paddingBottom: bottom + 24 }
        ]}
      >
        {settingsData.map((section) => (
          <SettingsSection
            key={section.title}
            section={section}
          />
        ))}
        <Portal>
          <Dialog visible={isDeleteDialogVisible} onDismiss={hideDeleteDialog}>
            <Dialog.Title>Delete Account</Dialog.Title>
            <Dialog.Content>
              <Text>Are you sure you want to delete your account? This action cannot be undone.</Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={hideDeleteDialog}>Cancel</Button>
              <Button 
                onPress={handleDeleteAccount} 
                textColor="red"
                loading={loading}
                disabled={loading}
              >
                Delete
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>

        <Portal>
          <Dialog visible={isLogoutDialogVisible} onDismiss={hideLogoutDialog}>
            <Dialog.Title>Logout</Dialog.Title>
            <Dialog.Content>
              <Text>Are you sure you want to logout?</Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={hideLogoutDialog}>Cancel</Button>
              <Button onPress={confirmLogout}>Logout</Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>

        <View style={styles.buttonContainer}>
          <Button
            mode='text'
            onPress={showLogoutDialog}
            style={styles.logoutButton}
            contentStyle={styles.buttonContent}
          >
            <Text style={styles.logoutText} variant='titleMedium'>Logout</Text>
          </Button>
          <Button
            mode='text'
            onPress={showDeleteDialog}
            style={styles.deleteAccountButton}
            contentStyle={styles.buttonContent}
            disabled={loading}
          >
            <Text style={styles.deleteText}>Delete Account</Text>
          </Button>
        </View>
      </ScrollView>
    </ThemedView>
  )
}

export default UserProfile

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  buttonContainer: {
    width: '100%',
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginLeft: 8,
  },
  buttonContent: {
    justifyContent: 'flex-start',
  },
  logoutButton: {
    marginTop: 8,
    width: '100%',
    alignSelf: 'flex-start',
  },
  deleteAccountButton: {
    width: '100%',
    alignSelf: 'flex-start',
  },
  logoutText: {
    color: 'black',
    textDecorationLine: 'underline',
    textDecorationColor: 'black',
  },
  deleteText: {
    color: 'red',
    textDecorationLine: 'underline',
    textDecorationColor: 'red',
  }
})