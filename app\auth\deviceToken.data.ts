import { gql } from '@apollo/client';

export const UPDATE_DEVICE_TOKEN = gql`
  mutation UpdateDeviceToken($input: DeviceTokenInput!) {
    updateDeviceToken(input: $input) {
      ... on DeviceTokenResponse {
        status
        message
        result {
          deviceToken {
            id
            user {
              id
              firstName
              lastName
            }
            deviceId
            token
            platform
          }
        }
      }
      ... on DeviceTokenErrorResponse {
        status
        message
        errors {
          field
          message
        }
      }
    }
  }
`;

export const DELETE_DEVICE_TOKEN = gql`
  mutation DeleteDeviceToken($token: String!) {
    deleteDeviceToken(token: $token) {
      ... on DeviceTokenResponse {
        status
        message
        result {
          deviceToken {
            id
            user {
              id
              firstName
              lastName
            }
            deviceId
            token
            platform
          }
        }
      }
      ... on DeviceTokenErrorResponse {
        status
        message
        errors {
          field
          message
        }
      }
    }
  }
`; 