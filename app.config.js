const IS_DEV = process.env.APP_VARIANT === 'development';

export default {
  expo: {
    name: IS_DEV ? 'Fast Party (Dev)' : 'Fast Party',
    slug: 'mvp',
    version: '1.0.0',
    orientation: 'portrait',
    icon: './assets/images/icon.png',
    scheme: 'fastparty',
    userInterfaceStyle: 'automatic',
    splash: {
      image: './assets/images/splash.png',
      resizeMode: 'contain',
      backgroundColor: '#ffffff'
    },
    newArchEnabled: true,
    updates: {
      url: 'https://u.expo.dev/52eae7d2-ec4a-4854-8348-d1c03b1aa902'
    },
    runtimeVersion: {
      policy: 'appVersion'
    },
    ios: {
      config: {
        googleMapsApiKey: process.env.EXPO_PUBLIC_GOOGLE_API_KEY
      },
      userInterfaceStyle: 'light',
      supportsTablet: true,
      bundleIdentifier: 'ai.fastparty.mobile',
      usesIcloudStorage: true,
      associatedDomains: [
        `applinks:${process.env.WEB_DOMAIN || 'fastparty.app'}`
      ],
      infoPlist: {
        UIBackgroundModes: ['remote-notification'],
        NSLocationWhenInUseUsageDescription: 'We need your location to provide traffic information and live tracking for the party.',
        NSLocationAlwaysAndWhenInUseUsageDescription: 'We need your location to provide traffic information and live tracking for the party.',
        NSLocationAlwaysUsageDescription: 'We need your location to provide traffic information and live tracking for the party.',
        NSLocationTemporaryUsageDescriptionDictionary: {
          'TrafficInfo': 'We need your precise location to provide accurate traffic information.'
        }
      }
    },
    android: {
      package: 'ai.fastparty.mobile',
      adaptiveIcon: {
        foregroundImage: './assets/images/adaptive-icon.png',
        backgroundColor: '#ffffff'
      },
      softwareKeyboardLayoutMode: 'pan',
      config: {
        googleMaps: {
          apiKey: process.env.EXPO_PUBLIC_GOOGLE_API_KEY
        }
      },
      googleServicesFile: process.env.GOOGLE_SERVICES_JSON ?? './google-services.json',
      intentFilters: [
        {
          action: "VIEW",
          autoVerify: true,
          data: [
            {
              scheme: "https",
              host: process.env.WEB_DOMAIN,
              pathPrefix: "/"
            },
            {
              scheme: "fastparty"
            }
          ],
          category: ["BROWSABLE", "DEFAULT"]
        }
      ]
    },
    web: {
      bundler: 'metro',
      output: 'static',
      favicon: './assets/images/favicon.png'
    },
    plugins: [
      'expo-font',
      'expo-router',
      'expo-secure-store',
      [
        'expo-contacts',
        {
          contactsPermission: 'Allow $(PRODUCT_NAME) to access your contacts.'
        }
      ],
      'expo-font',
      [
        'expo-document-picker',
        {
          iCloudContainerEnvironment: 'Production'
        }
      ],
      'expo-video',
      [
        'expo-notifications',
        {
          icon: './assets/images/icon.png',
          color: '#ffffff',
          mode: 'production',
          androidCollapsedTitle: 'New Notification',
          androidMode: 'default',
          androidImportance: 'max',
          androidShowBadge: true,
          iosDisplayInForeground: true
        }
      ]
    ],
    extra: {
      eas: {
        projectId: '52eae7d2-ec4a-4854-8348-d1c03b1aa902'
      }
    },
    owner: 'fast-party'
  }
};
