import { StyleSheet, View, ActivityIndicator } from 'react-native'
import { TextInput, Text, Button, Chip, Portal, Dialog, Searchbar } from 'react-native-paper'
import React, { useState, useEffect, useRef } from 'react'
import { BottomSheetBackdrop, BottomSheetModal, BottomSheetView } from '@gorhom/bottom-sheet';
import { LocationItem } from '@/app/(home)/EventsDashboard/AddParty.models';
import { Colors, Spacing, Typography, Borders, Icons } from '@/constants/DesignSystem';
import { useVenueStore } from '@/store/venueStore'
import { Calender, DropDown, Location, Next, Add } from '../icons';

interface PartyInfo {
  name?: string;
  dateTime?: string | Date;
  area?: string;
  event: {
    mainHost: {
      userId: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        phone: string;
      }
    }
  }
  cohosts: {
    id: string;
    firstName: string;
    lastName?: string;
    phoneNumber: string;
    email?: string;
  }[];
}

interface AddPartyFormProps {
  isCohost: boolean;
  partyInfo: PartyInfo;
  dateTime: Date | null;
  area?: string | null;
  onDateTimePress: () => void;
  isEditMode: boolean;
  cohostSheetRef: React.RefObject<BottomSheetModal>;
  onCohostChange: (cohosts: {
    id: string;
    firstName: string;
    lastName?: string;
    phoneNumber: string;
    email?: string;
  }[]) => void;
  onNameChange: (name: string) => void;
  formErrors: {
    name?: string;
    dateTime?: string;
  };
  isLoading?: boolean;
  onPressLocation: () => void;
}

const AddPartyForm = ({
  isCohost,
  partyInfo,
  dateTime,
  area,
  onDateTimePress,
  isEditMode,
  cohostSheetRef,
  onCohostChange,
  onNameChange,
  formErrors,
  isLoading = false,
  onPressLocation
}: AddPartyFormProps) => {
  const [partyName, setPartyName] = useState('')
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedCohost, setSelectedCohost] = useState<PartyInfo['cohosts'][0] | null>(null)
  const [searchText, setSearchText] = useState('');
  const locationSearchRef = useRef<BottomSheetModal>(null);
  const [suggestions, setSuggestions] = useState<LocationItem[]>([]);
  const venueDetails = useVenueStore((state) => state.venueDetails);
  const isCohostInForm = isCohost;

  useEffect(() => {
    if (partyInfo?.name) {
      setPartyName(partyInfo.name)
    }
  }, [partyInfo])

  const formatDateTime = (date: Date | null) => {
    if (!date) {
      return isEditMode ? 'No date selected' : 'Select date and time';
    }
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    })
  }

 

  return (
    <View>
      <View>
        <TextInput
          value={partyName}
          onChangeText={(text) => {
            setPartyName(text);
            onNameChange(text);
          }}
          mode="outlined"
          placeholder="Enter party name*"
          placeholderTextColor={Colors.mediumGray}
          style={styles.input}
          outlineColor={Colors.border.medium}
          activeOutlineColor={Colors.primary}
          outlineStyle={{ borderRadius: Borders.radius.md }}
          contentStyle={{ paddingHorizontal: Spacing.sm }}
          textColor={Colors.text.secondary}
          error={!!formErrors.name}
        />
        {formErrors.name && (
          <Text style={styles.errorText}>{formErrors.name}</Text>
        )}
        <TextInput
          value={formatDateTime(dateTime)}
          editable={false}
          left={<TextInput.Icon icon={() => <Calender size={Icons.size.lg} color={Colors.mediumGray} />} onPress={onDateTimePress} />}
          right={<TextInput.Icon icon={() => <DropDown size={Icons.size.md} color={Colors.primary} />} onPress={onDateTimePress} />}
          mode="outlined"
          style={styles.input}
          outlineColor={Colors.border.medium}
          activeOutlineColor={Colors.primary}
          outlineStyle={{ borderRadius: Borders.radius.md }}
          contentStyle={{ paddingHorizontal: Spacing.sm }}
          onPress={onDateTimePress}
          error={!!formErrors.dateTime}
          textColor={Colors.text.secondary}
        />
        {formErrors.dateTime && (
          <Text style={styles.errorText}>{formErrors.dateTime}</Text>
        )}
        <TextInput
          placeholder="Venue"
          placeholderTextColor={Colors.mediumGray}
          editable={false}
          left={<TextInput.Icon icon={() => <Location size={Icons.size.lg} color={Colors.mediumGray} />} onPress={onPressLocation} />}
          right={
            isLoading ? (
              <ActivityIndicator size="small" color={Colors.primary} />
            ) : (
              <TextInput.Icon 
                icon={() => <Next size={Icons.size.md} color={Colors.primary} />} 
                onPress={onPressLocation} 
              />
            )
          }
          mode="outlined"
          style={styles.input}
          outlineColor={Colors.border.medium}
          activeOutlineColor={Colors.primary}
          outlineStyle={{ borderRadius: Borders.radius.md }}
          contentStyle={{ paddingHorizontal: Spacing.sm }}
          value={venueDetails?.title || area || ''}
          textColor={Colors.text.secondary}
        />
        <View style={[styles.cohostContainer]}>
          <View style={styles.cohostHeader}>
            <View style={styles.hostTitleContainer}>
              <Text style={styles.hostLabel} numberOfLines={1} ellipsizeMode="tail">Hosted By{' '}
                <Text style={styles.hostName} numberOfLines={1} ellipsizeMode="tail">
                  {partyInfo?.event?.mainHost?.userId?.firstName}
                </Text>
              </Text>
            </View>
            {!isCohostInForm && (
            <Button
              mode="outlined"
              icon={() => <Add size={Icons.size.md} color={Colors.primary} />}
              onPress={() => cohostSheetRef.current?.present()}
              style={styles.addButton}
              textColor={Colors.primary}
              buttonColor={Colors.background.secondary}
              rippleColor={Colors.background.secondary}
            >
              Add
            </Button>
            )}
          </View>
          <View style={styles.chipContainer}>
            {(partyInfo.cohosts || []).map((cohost) => (
              <Chip
                key={cohost.id}
                onClose={isCohostInForm ? undefined : () => {
                  setSelectedCohost(cohost);
                  setShowDeleteDialog(true);
                }}
                style={styles.chip}
                textStyle={{ color: Colors.text.primary }}
                elevated
              >
                {`${cohost.firstName}` }
              </Chip>
            ))}
          </View>
        </View>

        <Portal>
          <Dialog
            visible={showDeleteDialog}
            onDismiss={() => setShowDeleteDialog(false)}
            style={{ backgroundColor: Colors.background.primary, borderRadius: Borders.radius.lg }}
          >
            <Dialog.Title style={{ color: Colors.text.primary, fontSize: Typography.fontSize.lg }}>
              Remove Co-host
            </Dialog.Title>
            <Dialog.Content>
              <Text style={{ color: Colors.text.primary, fontSize: Typography.fontSize.md }}>
                Do you want to remove {selectedCohost?.firstName} {selectedCohost?.lastName || ''} as a co-host?
              </Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button
                onPress={() => setShowDeleteDialog(false)}
                textColor={Colors.text.primary}
              >
                No
              </Button>
              <Button
                onPress={() => {
                  const updatedCohosts = partyInfo.cohosts.filter(c => c.id !== selectedCohost?.id);
                  onCohostChange(updatedCohosts);
                  setShowDeleteDialog(false);
                  setSelectedCohost(null);
                }}
                textColor={Colors.primary}
              >
                Yes
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>
      </View>
    </View>
  )
}

export default AddPartyForm

const styles = StyleSheet.create({
  input: {
    marginHorizontal: Spacing.md,
    marginVertical: Spacing.sm,
    backgroundColor: Colors.background.primary,
    fontSize: Typography.fontSize.md
  },
  dateTimeContainer: {
    width: '100%',
  },
  cohostContainer: {
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.md,
    marginHorizontal: Spacing.md,
    marginVertical: Spacing.sm,
    backgroundColor: Colors.background.primary,
    borderWidth: Borders.width.thin,
    borderColor: Colors.border.medium,
    borderRadius: Borders.radius.md,
  },
  cohostHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: Spacing.sm,
  },
  addButton: {
    borderRadius: Borders.radius.circle,
    minWidth: 80,
    borderColor: Colors.primary,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  chip: {
    marginBottom: Spacing.xs,
    backgroundColor: Colors.background.secondary,
  },
  hostTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: Spacing.md,
    overflow: 'hidden',
  },
  hostLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: '600',
    flex: 1,
    color: Colors.mediumGray,
  },
  hostName: {
    fontSize: Typography.fontSize.md,
    fontWeight: 'normal',
    color: Colors.text.primary,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: Borders.width.thin,
    borderColor: Colors.border.medium,
    borderRadius: Borders.radius.md,
    paddingHorizontal: Spacing.md,
    height: 40,
    backgroundColor: Colors.background.secondary,
    marginBottom: Spacing.md,
  },
  searchIcon: {
    marginRight: Spacing.sm,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: Typography.fontSize.md,
    color: Colors.text.primary,
  },
  footer: {
    position: 'relative',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.background.primary,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.md,
    borderTopWidth: Borders.width.thin,
    borderColor: Colors.border.light,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  button: {
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    borderRadius: Borders.radius.md,
    alignItems: 'center',
  },
  buttonText: {
    color: Colors.white,
    fontSize: Typography.fontSize.md,
    fontWeight: '500',
  },
  errorText: {
    color: Colors.error,
    fontSize: Typography.fontSize.sm,
    marginLeft: Spacing.md,
    marginBottom: Spacing.sm,
  },
})