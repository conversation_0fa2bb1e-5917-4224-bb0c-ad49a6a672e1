declare module 'react-native-masonry-list' {
    interface MasonryListProps {
        images: Array<{
            uri: string;
            dimensions?: {
                width: number;
                height: number;
            };
            id?: string | number;
            title?: string;
        }>;
        columns?: number;
        spacing?: number;
        imageContainerStyle?: any;
        onPressImage?: (item: any) => void;
        renderIndividualHeader?: (item: any) => React.ReactElement;
        renderIndividualFooter?: (item: any) => React.ReactElement;
    }

    export default function MasonryList(props: MasonryListProps): JSX.Element;
} 