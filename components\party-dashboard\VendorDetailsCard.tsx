import React from 'react';
import { StyleSheet, Dimensions, Pressable } from 'react-native';
import { router } from 'expo-router';
import { Star } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';
import { ImageCarousel } from '@/components/ImageCarousel';
import { ThemedView } from '@/components/UI/ThemedView';
import { ThemedText } from '@/components/UI/ThemedText';
import { Platform } from 'react-native';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
interface MainFeature {
  name: string;
  value: string;
}

interface VendorDetailsCardProps {
  vendorId: string;
  name: string;
  mainFeature: MainFeature;
  rating: number;
  images: { id: string, image_src: string }[];
}

export default function VendorDetailsCard({ vendorId, name, mainFeature, rating, images }: VendorDetailsCardProps) {
  return (
    <ThemedView style={styles.container}>
      <ImageCarousel images={images} />
      <Pressable
        onPress={() => router.push(`/(home)/VendorDetails/${vendorId}`)}
      >
        <ThemedView style={styles.infoContainer}>
          <ThemedView style={styles.nameRatingRow}>
            <ThemedText type="subtitle" style={styles.name}>{name}</ThemedText>
            <ThemedView style={styles.ratingContainer}>
              <Star size={16} color={Colors.light.starColor} />
              <ThemedText type="default" style={styles.rating}>{rating?.toFixed(1)}</ThemedText>
            </ThemedView>
          </ThemedView>
          <ThemedView style={styles.detailsRow}>
            <ThemedText type="default">{mainFeature?.name}: {mainFeature?.value}</ThemedText>
          </ThemedView>
        </ThemedView>
      </Pressable>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    width: SCREEN_WIDTH * 0.9,
    borderRadius: 10,
    overflow: 'hidden',
    backgroundColor: Colors.light.paginationDotBackground,
    ...Platform.select({
      ios: {
        shadowColor: Colors.light.cardShadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  dot: {
    width: 6,
    height: 6,
  },
  infoContainer: {
    padding: 15,
  },
  nameRatingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  name: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    marginLeft: 5,
  },
  price: {
    fontSize: 16,
    color: Colors.light.priceColor,
  },
});