import { useState, useEffect } from 'react';
import { View, Pressable } from 'react-native';
import { taskNameFieldStyles } from './TaskNameField.styles';
import { Text } from 'react-native';
import { TextModal } from '@/components/Task/Form/ReusableComponents/TextModal';
import { useKeyboard } from '@/commons/KeyboardContext';

interface TaskNameFieldProps {
  value: string;
  onChangeText: (text: string) => void;
  inputTheme: any;
  onSaveTitle?: (title: string) => Promise<void>;
  taskId?: string;
}

export function TaskNameField({ value, onChangeText, inputTheme, onSaveTitle, taskId }: TaskNameFieldProps) {
  const { isKeyboardActive } = useKeyboard();
  const [modalVisible, setModalVisible] = useState(false);
  const [tempValue, setTempValue] = useState(value);
  const [localValue, setLocalValue] = useState(value);

  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  const handleSave = async () => {
    setLocalValue(tempValue);
    onChangeText(tempValue);
    setModalVisible(false);

    if (onSaveTitle && taskId && tempValue !== value) {
      try {
        await onSaveTitle(tempValue);
      } catch (error: any) {
        console.log(error);
        setLocalValue(value);
        onChangeText(value);
      }
    }
  };

  const handleCancel = () => {
    setTempValue(localValue);
    setModalVisible(false);
    console.log('cancel');
    console.log('modalVisible', modalVisible);
  };

  const handlePress = () => {
    if (isKeyboardActive) return;
    setModalVisible(true);
  };

  return (
    <>
      <Pressable 
        onTouchEnd={handlePress}
        disabled={isKeyboardActive}
        >
        <View style={[taskNameFieldStyles.containerStyle]}>
          <Text
            numberOfLines={2}
            ellipsizeMode="tail"
            style={[
              taskNameFieldStyles.inputStyle,
              taskNameFieldStyles.TaskNameFieldStyle,
              !localValue && taskNameFieldStyles.placeholderStyle,
            ]}>
            {localValue || "Add Task Name *"}
          </Text>
        </View>
      
      <TextModal
        placeholder="Add Task Name"
        maxLength={150}
        visible={modalVisible}
        value={tempValue}
        onChangeText={setTempValue}
        onCancel={handleCancel}
        onSave={handleSave}
        inputTheme={{
          colors: {
            primary: '#0A0A0B',
            background: '#ffffff',
          },
          zIndex: 1000,
        }}
      />
      </Pressable>
    </>
  );
}
