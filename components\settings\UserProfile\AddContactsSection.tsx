import React, {
    forwardRef,
    useCallback,
    useEffect,
    useImperativeHandle,
    useMemo,
    useRef,
    useState,
} from 'react';
import {
    View,
    Text,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    TouchableWithoutFeedback,
    Keyboard,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';
import { BottomSheetModal, BottomSheetBackdrop, BottomSheetView } from '@gorhom/bottom-sheet';
import * as Contacts from 'expo-contacts';
import Modal from 'react-native-modal';
import { PhoneNumberInput } from '@/components/UI/ReusableComponents/PhoneNumberInput';
import { regexValidators } from '@/app/(home)/utils/reusableFunctions';
import { s, vs } from 'react-native-size-matters';
import { FlatList } from 'react-native-gesture-handler';
import { Colors, Typography, Spacing, Borders, Icons } from '@/constants/DesignSystem';
import { Check, Search } from '@/components/icons';

interface FormErrors {
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
}

const ContactsSheetContent = ({
    contacts,
    selectedContacts,
    onToggleSelect,
    onAddNew,
    onSave,
  }: {
    contacts: any[];
    selectedContacts: any[];
    onToggleSelect: (contact: any) => void;
    onAddNew: () => void;
    onSave: () => void;
  }) => {
    const [searchText, setSearchText] = useState('');
    const [debouncedSearchText, setDebouncedSearchText] = useState('');
  
    useEffect(() => {
      const handler = setTimeout(() => {
        setDebouncedSearchText(searchText);
      }, 500);
  
      return () => {
        clearTimeout(handler);
      };
    }, [searchText]);
  
    const filteredContacts = useMemo(() => {
      return contacts.filter(contact => {
        const name = `${contact.firstName ?? ''} ${contact.lastName ?? ''}`.toLowerCase();
        return name.includes(debouncedSearchText.toLowerCase());
      });
    }, [contacts, debouncedSearchText]);
  
    const renderContactItem = useCallback(({ item }: { item: any }) => {
      const contactNumber = item.phoneNumbers?.[0]?.number ?? item?.phoneNumber ?? item?.phone;
  
      const isSelected = selectedContacts.some(
          c => (c.phoneNumbers?.[0]?.number ?? c?.phoneNumber ?? c?.phone) === contactNumber
      );
      const initials = `${item.firstName?.[0] ?? ''}${item.lastName?.[0] ?? ''}`.toUpperCase();
  
      return (
          <TouchableOpacity style={[styles.contactRow]} onPress={() => onToggleSelect(item)}>
              <View style={styles.contactCircle}>
                  <Text style={styles.monogram}>{initials}</Text>
              </View>
              <View style={styles.nameAndNumber}>
                  <Text style={styles.contactName}>{item.firstName} {item.lastName}</Text>
                  <Text style={styles.contactNumber}>{contactNumber}</Text>
              </View>
              <View style={[styles.checkbox, isSelected && styles.checkedBox]}>
                  {isSelected && <Check size={Icons.size.md} color={Colors.primary} />}
              </View>
          </TouchableOpacity>
      );
    }, [selectedContacts, onToggleSelect]);
  
    return (
      <BottomSheetView style={styles.bottomSheetView}>
        <View style={styles.flexGrow}>
          <Text style={styles.heading}>{'Add Members'}</Text>
          <View style={styles.searchContainer}>
            <Search size={Icons.size.md} color={Colors.mediumGray} />
            <TextInput
              style={styles.searchInput}
              placeholder="Find members..."
              placeholderTextColor={Colors.mediumGray}
              value={searchText}
              onChangeText={setSearchText}
            />
          </View>
          <View>
            <TouchableOpacity onPress={onAddNew} style={styles.addNew}>
              <Text style={styles.addNewText}>{'ADD NEW'}</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.contentContainer}>
            <FlatList
              data={filteredContacts}
              keyExtractor={(item, index) => item.id ?? index.toString()}
              renderItem={renderContactItem}
              contentContainerStyle={{ paddingBottom: Spacing.lg }}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
            />
          </View>
          <View style={[styles.footer, styles.flexFooter]}>
            <Text style={styles.selectedText}>{selectedContacts.length} {'Selected'}</Text>
            <TouchableOpacity onPress={onSave} style={styles.saveBtn}>
              <Text style={styles.saveText}>{'SAVE'}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </BottomSheetView>
    );
  };

const AddContactsSection = React.memo(forwardRef(({ members, setMembers }: { members: any; setMembers: (members: any) => void }, ref) => {
    const bottomSheetRef = useRef<BottomSheetModal>(null);
    const [contacts, setContacts] = useState<any[]>([]);
    const [selectedContacts, setSelectedContacts] = useState<any[]>(members ?? []);
    const [isAddNewVisible, setIsAddNewVisible] = useState(false);
    const [newContact, setNewContact] = useState({ firstName: '', lastName: '', phoneNumber: '', email: '' });
    const [formErrors, setFormErrors] = useState<FormErrors>({});
    const [formattedPhoneNumber, setFormattedPhoneNumber] = useState<string>();

    useImperativeHandle(ref, () => ({
        openBottomSheet,
    }));

    useEffect(() => {
        setSelectedContacts(members ? members.map((m: any) => ({ ...m, imageAvailable: false })) : []);
    }, [members]);

    useEffect(() => {
        const loadContacts = async () => {
            const { status } = await Contacts.requestPermissionsAsync();
            if (status === 'granted') {
                const { data } = await Contacts.getContactsAsync({
                    fields: [
                        Contacts.Fields.FirstName,
                        Contacts.Fields.LastName,
                        Contacts.Fields.Emails,
                        Contacts.Fields.PhoneNumbers,
                    ],
                });

                if (data.length > 0) {
                    const dataWithImageFalse = data.map(contact => ({ ...contact, imageAvailable: false }));
                    const getPhoneNumber = (contact: any) =>
                        contact.phoneNumbers?.[0]?.number ?? contact?.phoneNumber ?? contact?.phone;

                    const validContacts = dataWithImageFalse.filter(
                        contact =>
                            (contact.firstName || contact.lastName) &&
                            getPhoneNumber(contact)
                    );

                    const selectedNumbers = new Set(
                        (selectedContacts || []).map(contact => getPhoneNumber(contact))
                    );

                    const nonSelectedContacts = validContacts.filter(
                        contact => !selectedNumbers.has(getPhoneNumber(contact))
                    );

                    const sortedContacts = [...(selectedContacts || []), ...nonSelectedContacts];
                    setContacts(sortedContacts);
                }
            }
        };


        loadContacts();
    }, [selectedContacts]);


    const openBottomSheet = () => {
        bottomSheetRef.current?.present();
    };

    const toggleSelectContact = (contact: any) => {
        const isSelected = selectedContacts.some((c) => c.id === contact.id);
        if (isSelected) {
            setSelectedContacts((prev) => prev.filter((c) => c.id !== contact.id));
        } else {
            setSelectedContacts((prev) => [...prev, contact]);
        }
    };

    const handleSave = () => {
        setMembers(selectedContacts);
        bottomSheetRef.current?.dismiss();
    };

    const validateForm = () => {
        const errors: FormErrors = {};
        let isValid = true;

        if (!newContact.firstName.trim()) {
            errors.firstName = 'First name is required';
            isValid = false;
        }

        if (!newContact.lastName.trim()) {
            errors.lastName = 'Last name is required';
            isValid = false;
        }

        if (!newContact.phoneNumber || !regexValidators.isValidPhone(newContact.phoneNumber)) {
            errors.phoneNumber = 'Valid phone number is required';
            isValid = false;
        }

        setFormErrors(errors);
        return isValid;
    };

    const handleAddNewContact = () => {
        if (!validateForm()) return;

        const newEntry = { ...newContact, id: new Date().toString(), phoneNumber: formattedPhoneNumber, imageAvailable: false };
        setSelectedContacts((prev) => [newEntry, ...prev]);
        setContacts((prev) => [newEntry, ...prev]);
        setNewContact({ firstName: '', lastName: '', phoneNumber: '', email: '' });
        setIsAddNewVisible(false);
    };

    const handlePressAddNew = () => {
        setFormErrors({});
        setIsAddNewVisible(true)
    }

    return (
        <View>
            <BottomSheetModal
                ref={bottomSheetRef}
                index={0}
                snapPoints={['80%']}
                enableContentPanningGesture={false}
                enableHandlePanningGesture={false}
                backdropComponent={(props) => (
                    <BottomSheetBackdrop {...props} appearsOnIndex={0} disappearsOnIndex={-1} />
                )}
            >
                <ContactsSheetContent 
                    contacts={contacts}
                    selectedContacts={selectedContacts}
                    onToggleSelect={toggleSelectContact}
                    onAddNew={handlePressAddNew}
                    onSave={handleSave}
                />
            </BottomSheetModal>

            <Modal
                isVisible={isAddNewVisible}
                onBackdropPress={() => setIsAddNewVisible(false)}
                style={styles.bottomSheet}
            >
                <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
                    <KeyboardAvoidingView
                        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                    >
                        <View style={styles.modalContainer}>
                            <View style={styles.modalHandle} />
                            <View style={styles.modalContent}>
                                <Text style={styles.heading}>{'New Contact'}</Text>
                                <View style={styles.inputContainer}>
                                    <TextInput
                                        placeholder="First Name"
                                        placeholderTextColor={Colors.mediumGray}
                                        value={newContact.firstName}
                                        onChangeText={(text) => setNewContact((prev) => ({ ...prev, firstName: text }))}
                                        style={styles.input}
                                        maxLength={50}
                                    />
                                </View>
                                {formErrors.firstName && (
                                    <Text style={styles.errorText}>{formErrors.firstName}</Text>
                                )}
                                <View style={styles.inputContainer}>
                                    <TextInput
                                        placeholder="Last Name"
                                        placeholderTextColor={Colors.mediumGray}
                                        value={newContact.lastName}
                                        onChangeText={(text) => setNewContact((prev) => ({ ...prev, lastName: text }))}
                                        style={styles.input}
                                        maxLength={100}
                                    />
                                </View>
                                {formErrors.lastName && (
                                    <Text style={styles.errorText}>{formErrors.lastName}</Text>
                                )}
                                <View style={styles.inputContainer}>
                                    <TextInput
                                        placeholder="Email (optional)"
                                        placeholderTextColor={Colors.mediumGray}
                                        value={newContact.email}
                                        onChangeText={(text) => setNewContact((prev) => ({ ...prev, email: text }))}
                                        style={styles.input}
                                        maxLength={320}
                                    />
                                </View>
                                <View style={styles.paddingVertical}>
                                    <PhoneNumberInput
                                        value={newContact.phoneNumber}
                                        onChangeText={(raw, formatted) => {
                                            setNewContact((prev) => ({ ...prev, phoneNumber: raw }));
                                            setFormattedPhoneNumber(formatted);
                                        }}
                                        error={!!formErrors.phoneNumber}
                                        errorText={formErrors.phoneNumber}
                                        mode="outlined"
                                        placeholder="Phone Number"
                                        style={styles.phoneInputStyle}
                                    />
                                </View>
                            </View>
                            <TouchableOpacity
                                onPress={handleAddNewContact}
                                style={styles.addButton}
                            >
                                <Text style={styles.addButtonText}>ADD</Text>
                            </TouchableOpacity>
                        </View>
                    </KeyboardAvoidingView>
                </TouchableWithoutFeedback>
            </Modal>
        </View>
    );
}));

export default AddContactsSection;

const styles = StyleSheet.create({
    // Layout styles
    flexContainer: {
        flex: 1,
    },
    flexGrow: {
        flex: 20,
    },
    flexFooter: {
        flex: 2,
    },
    bottomSheetView: {
        flex: 1,
        paddingHorizontal: Spacing.lg,
    },
    contentContainer: {
        marginTop: Spacing.md,
        flex: 18
    },

    // Text styles
    heading: {
        fontSize: Typography.fontSize.lg,
        fontWeight: Typography.fontWeight.semibold,
        marginBottom: Spacing.md,
        color: Colors.primary,
        fontFamily: Typography.fontFamily.primary,
    },
    contactName: {
        flex: 1,
        fontSize: Typography.fontSize.md,
        fontWeight: Typography.fontWeight.semibold,
        fontFamily: Typography.fontFamily.primary,
        color: Colors.text.tertiary,
    },
    contactNumber: {
        fontSize: Typography.fontSize.md,
        color: Colors.text.secondary,
        fontWeight: Typography.fontWeight.medium,
        fontFamily: Typography.fontFamily.primary,
    },
    monogram: {
        color: Colors.secondary,
        fontWeight: Typography.fontWeight.bold,
        fontSize: Typography.fontSize.md,
        fontFamily: Typography.fontFamily.primary,
    },
    errorText: {
        color: Colors.error,
        fontSize: Typography.fontSize.sm,
        marginBottom: Spacing.sm,
        fontFamily: Typography.fontFamily.primary,
    },
    topShadow: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: 1, // very thin border line
        shadowColor: Colors.mediumGray,
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.15,
        shadowRadius: 2,
        elevation: 8, // for Android
        backgroundColor: Colors.background.tertiary, // needed to cast the shadow
        zIndex: 1,
      },
    addButtonText: {
        color: Colors.text.tertiary,
        fontSize: Typography.fontSize.md,
        fontWeight: Typography.fontWeight.semibold,
        fontFamily: Typography.fontFamily.primary,
    },
    selectedText: {
        fontWeight: Typography.fontWeight.semibold,
        fontFamily: Typography.fontFamily.primary,
        color: Colors.text.tertiary,
    },
    saveText: {
        color: Colors.text.tertiary,
        fontFamily: Typography.fontFamily.primary,
        fontWeight: Typography.fontWeight.semibold,
    },
    addNewText: {
        color: Colors.primary,
        fontFamily: Typography.fontFamily.primary,
        fontWeight: Typography.fontWeight.medium,
    },
    contactNameSmall: {
        fontSize: Typography.fontSize.md,
        fontFamily: Typography.fontFamily.primary,
    },

    // Input styles
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: Borders.width.thin,
        borderColor: Colors.border.light,
        borderRadius: Borders.radius.md,
        paddingHorizontal: Spacing.sm,
        height: 40,
        backgroundColor: Colors.background.tertiary,
        marginBottom: Spacing.md,
    },
    searchInput: {
        flex: 1,
        height: '100%',
        fontSize: Typography.fontSize.md,
        color: Colors.text.tertiary,
        fontFamily: Typography.fontFamily.primary,
        marginLeft: Spacing.sm,
    },
    input: {
        borderColor: Colors.border.light,
        padding: Spacing.sm,
        fontSize: Typography.fontSize.md,
        fontFamily: Typography.fontFamily.primary,
    },
    inputContainer: {
        borderWidth: Borders.width.thin,
        borderColor: Colors.text.tertiary,
        borderRadius: Borders.radius.sm,
        padding: s(3),
        backgroundColor: Colors.background.tertiary,
        marginVertical: Spacing.sm,
        fontSize: Typography.fontSize.md,
    },
    phoneInputStyle: {
        backgroundColor: Colors.background.tertiary
    },

    // Button styles
    uploadBtn: {
        backgroundColor: Colors.primary,
        paddingVertical: Spacing.sm,
        borderRadius: Borders.radius.md,
        alignItems: 'center',
        marginBottom: Spacing.md,
    },
    saveBtn: {
        backgroundColor: Colors.primary,
        paddingHorizontal: Spacing.lg,
        paddingVertical: Spacing.sm,
        borderRadius: Borders.radius.md,
    },
    addNew: {
        backgroundColor: Colors.background.secondary,
        paddingHorizontal: Spacing.xl,
        paddingVertical: Spacing.sm,
        borderRadius: Borders.radius.md,
        alignItems: 'center',
    },
    addButton: {
        backgroundColor: Colors.primary,
        paddingVertical: s(6),
        paddingHorizontal: Spacing.sm,
        alignSelf: 'center',
        marginTop: s(3),
        marginBottom: s(10),
        minWidth: 80,
        height: 36,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: Borders.radius.sm,
    },
    editContainer: {
        flexDirection: 'row',
        borderColor: Colors.border.light,
        borderWidth: Borders.width.thin,
        borderRadius: Borders.radius.circle,
        paddingHorizontal: Spacing.md,
        paddingVertical: Spacing.xs,
        alignItems: 'center',
        gap: Spacing.sm,
        height: 24,
    },

    // Contact styles
    contactRow: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: Spacing.md,
    },
    contactCircle: {
        width: 54,
        height: 54,
        borderRadius: Borders.radius.circle,
        backgroundColor: Colors.background.tertiary,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: Spacing.md,
    },
    nameAndNumber: {
        flex: 1,
        justifyContent: 'center',
        marginLeft: Spacing.sm,
    },
    checkbox: {
        width: 20,
        height: 20,
        borderWidth: Borders.width.thin,
        borderColor: Colors.border.light,
        borderRadius: Borders.radius.sm,
        backgroundColor: Colors.background.primary,
        justifyContent: 'center',
        alignItems: 'center',
    },
    checkedBox: {
        borderColor: Colors.primary,
    },

    // Container styles
    footer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: Spacing.md,
        marginBottom: vs(130)
    },
    bottomSheet: {
        justifyContent: 'flex-end',
        margin: 0,
    },
    modalContainer: {
        backgroundColor: Colors.background.primary,
        padding: Spacing.xs,
    },
    modalContent: {
        padding: Spacing.md,
        backgroundColor: Colors.background.primary,
    },
    modalHandle: {
        marginHorizontal: '40%',
        backgroundColor: Colors.border.light,
        height: 5,
        width: 50,
        alignItems: 'center',
        borderRadius: Borders.radius.pill
    },
    paddingVertical: {
        paddingVertical: Spacing.xl,
    }
});

