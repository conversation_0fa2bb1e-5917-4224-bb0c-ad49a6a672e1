import gql from 'graphql-tag';

export const CREATE_TASK = gql`
  mutation CreateTask($input: TaskInput!) {
    createTask(input: $input) {
      ... on TaskResponse {
        status
        message
        result {
          task {
            id
            title
            description
            status
            dueDate
            createdBy {
              id
            }
            assignedTo {
              id
            }
            party {
              id
            }
          }
        }
      }
      ... on TaskErrorResponse {
        status
        message
        errors {
          field
          message
        }
      }
    }
  }
`;

export const Get_PARTIES_BY_USER_Id_FOR_DROPDOWN = gql`
  query GetParties($filter: PartyFilterInput, $pagination: PaginationInput) {
    getParties(filter: $filter, pagination: $pagination) {
      ... on PartiesResponse {
        message
        status
        result {
          parties {
            id
            name
          }
        }
        pagination {
          currentPage
          pageSize
          totalItems
          totalPages
        }
      }
      ... on PartyErrorResponse {
        errors {
          field
          message
        }
        message
        status
      }
    }
  }
`;

export const GET_EVENT_TASKS = gql`
query PartyTasksResponse($eventId: ID!, $filter: EventTasksFilterInput, $getEventByIdId: ID!) {
  getEventTasks(eventId: $eventId, filter: $filter) {
    ... on PartyTasksResponse {
      message
      result {
        totalTasksCount
        partyTasks {
          tasks {
            id
            dueDate
            title
            status
            assignedTo {
              id
              firstName
              lastName
              role
            }
            party {
              id
              name
            }
          }
          tasksCount
        }
      }
      status
    }
    ... on EventErrorResponse {
      errors {
        field
        message
      }
      status
      message
    }
  }
  getEventById(id: $getEventByIdId) {
    ... on EventResponse {
      message
      status
      result {
        event {
          id
          name
          parties {
            id
            name
          }
        }
      }
    }
    ... on EventErrorResponse {
      errors {
        field
        message
      }
      message
      status
    }
  }
}`
;

export const UPLOAD_FILES_MUTATION = gql`
  mutation Mutation($containerName: String, $files: [Upload!]!) {
    uploadFiles(containerName: $containerName, files: $files) {
      ... on FileUploadResponse {
        result {
          bulkResult {
            successful {
              url
              originalName
              key
            }
            failures {
              message
              filename
            }
            totalProcessed
            successCount
            failureCount
          }
        }
      }
      ... on FileErrorResponse {
        status
        message
        errors {
          field
          message
        }
      }
    }
  }
`;



export const CREATE_DOCUMENT = gql`
mutation Mutation($input: DocumentInput!) {
  createDocument(input: $input) {
    ... on DocumentResponse {
      result {
        document {
          id
          name
        }
      }
    }
    ... on DocumentErrorResponse {
      errors {
        field
        message
      }
      message
      status
    }
  }
}
`;
export const DELETE_DOCUMENT = gql`
mutation DeleteTaskAttachment($deleteTaskAttachmentId: ID!) {
  deleteTaskAttachment(id: $deleteTaskAttachmentId) {
    ... on DocumentResponse {
      result {
        document {
          id
          name
        }
      }
    }
    ... on DocumentErrorResponse {
      message
      status
      errors {
        field
        message
      }
    }
  }
}
`;





export const GET_TASK_BY_ID = gql`
query GetTaskById($getTaskByIdId: ID!) {
  getTaskById(id: $getTaskByIdId) {
    ... on TaskResponse {
      result {
        task {
          assignedTo {
            firstName
            id
            lastName
          }
          collaborators {
            id
            user {
              firstName
              lastName
            }
          }
          comments {
            id
            content
            createdAt
            createdBy {
              id
              firstName
              lastName
              isActive
            }
            mentions {
              id
              firstName
              isActive
              lastName
            }
            reactions {
              id
              reactionUnicode
            }
            updatedAt
          }
          createdBy {
            id
            lastName
            firstName
          }
          id
          status
          title
          dueDate
          description
          attachments {
            description
            documentType
            documentUrl
            id
            name
          }
        }
      }
    }
    ... on TaskErrorResponse {
      errors {
        field
        message
      }
    }
  }
}
`;

export const GET_TASK_BY_ID_FOR_UPDATE = gql`
query GetTaskById($getTaskByIdId: ID!) {
  getTaskById(id: $getTaskByIdId) {
    ... on TaskErrorResponse {
      errors {
        field
        message
      }
      message
      status
    }
    ... on TaskResponse {
      message
      result {
        task {
          assignedTo {
            id
            firstName
            lastName
          }
          createdBy {
            id
            firstName
            lastName
          }
          attachments {
            description
            documentType
            documentUrl
            name
            id
          }
          description
          dueDate
          id
          title
          status
          party {
            id
            name
          }
        }
      }
      status
    }
  }
}
`;

export const UPDATE_TASK = gql`
mutation UpdateTask($updateTaskId: ID!, $input: TaskUpdateInput!) {
  updateTask(id: $updateTaskId, input: $input) {
    ... on TaskResponse {
      result {
        task {
          id
          dueDate
          description
          party {
            id
            name
          }
          assignedTo {
            id
            firstName
            lastName
          }
          title
        }
      }
    }
  }
}
`;


export const DELETE_TASK = gql`
mutation DeleteTask($deleteTaskId: ID!) {
  deleteTask(id: $deleteTaskId) {
    ... on TaskResponse {
      message
      status
      result {
        task {
          id
          title
        }
      }
    }
    ... on TaskErrorResponse {
      errors {
        field
        message
      }
      message
      status
    }
  }
}
`;

export const GET_DOCUMENT_BY_USER_ID = gql`
query GetDocuments($pagination: PaginationInput, $filter: DocumentFilterInput) {
  getDocuments(pagination: $pagination, filter: $filter) {
    ... on DocumentsResponse {
      result {
        documents {
          documentUrl
          documentType
          id
          name
        }
      }
    }
  }
}
`;

export const GET_DOCUMENT_BY_ID = gql`
query GetDocumentById($getDocumentByIdId: ID!) {
  getDocumentById(id: $getDocumentByIdId) {
    ... on DocumentResponse {
      result {
        document {
          createdBy {
            firstName
            lastName
          }
          id
          name
        }
      }
    }
  }
}
`;
