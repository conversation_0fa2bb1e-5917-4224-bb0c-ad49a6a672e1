import { SafeAreaView, StyleSheet, Text, TouchableOpacity, View, ActivityIndicator, Alert, Pressable } from 'react-native';
import React, { useEffect, useState,} from 'react';
import { StackScreenProps } from '@react-navigation/stack';
import { PartyDetailsRootList } from '@/components/create-event/Navigation/PartyDetailsRootList';
import { Card, Title } from 'react-native-paper';
import { formatDateString } from '@/app/(home)/utils/reusableFunctions';
import RSVPModal from './RSVPModal';
import { NewPartyDetailsResult, Rsvp } from '@/app/(home)/PartyDetails/NewPartyDetailsResponse.model';
import { GET_PARTY_DETAILS } from '@/app/(home)/(tabs)/party-dashboard/partydetails.data';
import { useMutation, useQuery } from '@apollo/client';
import { useUserStore } from '@/app/auth/userStore';
import Modal from 'react-native-modal';
import { getRandomDefaultImage } from '@/constants/HomePageDefaultImages';
import { UPDATE_INVITATION_RSVP } from './Rsvp.data';
import { GET_EVENTS } from '@/components/HomePage/HomePage.data';
import { Check, MayBe, Cross, DueDate, Hosts, Location ,BackArrow } from '@/components/icons';
import { Icons, Colors, Spacing, Typography, Borders } from '@/constants/DesignSystem';
import RsvpCantBeDone from '@/components/Illustrations/RsvpCantBeDone';
import { Platform } from 'react-native';

type NewPartyDetailsScreenProps = StackScreenProps<PartyDetailsRootList, 'RsvpScreen'>;

const RsvpScreen: React.FC<NewPartyDetailsScreenProps> = ({ route, navigation }) => {
  const { partyId, fromSearch } = route.params;


  if (!partyId) {
    return (
      <SafeAreaView style={{flex : 1  }}>
            <View style={{position : "absolute", top : Platform.OS === 'ios' ? 0 : 30, left : 10, zIndex : 1}}>
                    <Pressable onPress={() => navigation.popTo("HomePage")} >
                        <BackArrow size={Icons.size.md} color={Colors.primary} />
                    </Pressable>
                  
                </View>
        <View style={styles.mainContainer}> 
        <Text>Error: No Party ID was provided.</Text>
        </View>
     
      </SafeAreaView>
    );
  }

  const { loading, error, data, refetch } = useQuery(GET_PARTY_DETAILS, {
    variables: { getPartyByIdId: partyId },
  });

  const partyDetails: NewPartyDetailsResult | undefined = data?.getPartyById.result;
  const [isModalVisible, setModalVisible] = useState(false);
  const [selectedResponse, setSelectedResponse] = useState<'yes' | 'maybe' | 'no' | null>(null);
  const [updateInvitationRSVP, { loading: updateRsvpLoading }] = useMutation(UPDATE_INVITATION_RSVP, {
    refetchQueries: [
      GET_EVENTS,
    ],
  });
  const userData = useUserStore((state) => state.userData);
  const [currentUserRsvp, setCurrentUserRsvp] = useState<Rsvp | null>(null);
  const [isPartyCompleted, setIsPartyCompleted] = useState(false);
  const userId = userData?.id;

  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };

  useEffect(() => {
    if (selectedResponse !== null) {
      console.log(`Selected Response: ${selectedResponse}`);
    }
  }, [selectedResponse]);

  useEffect(() => {
    if (partyDetails) {
      const rsvp = partyDetails.party.rsvps.find((r) => r.guest?.user.id === userId);
      if (rsvp) {
        setCurrentUserRsvp(rsvp);
      }
    }
  }, [partyDetails, userId]);

  useEffect(() => {
    if (currentUserRsvp) {
      const statusMap: { [key: string]: 'yes' | 'maybe' | 'no' } = {
        ACCEPTED: 'yes',
        MAYBE: 'maybe',
        REJECTED: 'no',
      };
      const rsvpStatus = currentUserRsvp.status.toUpperCase() as keyof typeof statusMap;
      setSelectedResponse(statusMap[rsvpStatus] || null);
    } else {
      setSelectedResponse(null);
    }
  }, [currentUserRsvp]);

  useEffect(() => {
    if (partyDetails?.party?.time) {
      const partyDate = new Date(String(partyDetails.party.time));
      const now = new Date();
      if (partyDate < now) {
        setIsPartyCompleted(true);
      }
    }
  }, [partyDetails]);

  const handleBackinRsvp = () => {
    if(isPartyCompleted){
      navigation.popTo("HomePage");
    }
    else if (selectedResponse === "no"){
      if(fromSearch){
        navigation.popTo("SearchInvitations");
      }
      else{
        navigation.popTo("HomePage");
      }
     
    }
    else if (selectedResponse === "yes" || selectedResponse === "maybe"){
      navigation.pop();
    }
    else{
      navigation.popTo("HomePage");
    }
  }

  const handleNoRsvp = async () => {
    if (!currentUserRsvp) {
      Alert.alert('Error', 'Could not find your invitation details to update.');
      return;
    }

    const originalResponse = selectedResponse;
    setSelectedResponse('no');

    const variables = {
      input: {
        additionalGuestsCount: 0,
        status: 'REJECTED',
        message: null,
      },
      updateInvitationRsvpId: currentUserRsvp.id,
      updateUserInput2: {
        firstName: currentUserRsvp.guest?.user.firstName || '',
        lastName: currentUserRsvp.guest?.user.lastName || '',
      },
    };

    try {
      await updateInvitationRSVP({ variables });
      Alert.alert('Response Saved', 'The host has been notified.', [
        { text: 'OK', onPress: () => navigation.popTo("HomePage") },
      ]);

    } catch (e: any) {
      setSelectedResponse(originalResponse);
      const errorMessage = e.networkError
        ? 'No internet connection. Please try again.'
        : 'Could not save your RSVP. Please try again.';
      Alert.alert('Error', errorMessage);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={{flex : 1  }}>
 <View style={{position : "absolute", top : Platform.OS === 'ios' ? 0 : 30, left : 10, zIndex : 1}}>
                    <Pressable onPress={() => navigation.goBack()} >
                        <BackArrow size={Icons.size.md} color={Colors.primary} />
                    </Pressable>
                  
                </View>
<View style={styles.mainContainer}>
        <ActivityIndicator size="large" />
      </View>
      </SafeAreaView>
    
    );
  }

  if (error) {
    if (error.networkError) {
      Alert.alert(
        'No Network',
        'No internet connection. Please check and try again.',
        [{ text: 'OK', onPress: () => console.log('OK Pressed') }]
      );
      return null; // Return null instead of void
    }
    return (
      <SafeAreaView style={{flex : 1}}>
        <View style={{position : "absolute", top : Platform.OS === 'ios' ? 0 : 30, left : 10, zIndex : 1}}>
                    <Pressable onPress={() => navigation.popTo("HomePage")} >
                        <BackArrow size={Icons.size.md} color={Colors.primary} />
                    </Pressable>
                  
                </View>
        <View style={styles.mainContainer}>
        <Text>Error: {error.message}</Text>
      </View>
      </SafeAreaView>
    );
  }

  if (!data || !data.getPartyById || data.getPartyById.__typename !== 'PartyResponse') {
    return (
      <SafeAreaView style={{flex : 1 }}>
        <View style={{position : "absolute", top : Platform.OS === 'ios' ? 0 : 30, left : 10, zIndex : 1}}>
                    <Pressable onPress={() => navigation.popTo("HomePage")} >
                        <BackArrow size={Icons.size.md} color={Colors.primary} />
                    </Pressable>
                  
                </View>
        <View style={styles.mainContainer}>
        <Text>No data found</Text>
      </View>
      </SafeAreaView>
    );
  }

  if (!partyDetails || !partyDetails.party) {
    return (
      <SafeAreaView style={{flex : 1}}>
        <View style={{position : "absolute", top : Platform.OS === 'ios' ? 0 : 30, left : 10, zIndex : 1}}>
                    <Pressable onPress={() => navigation.popTo("HomePage")} >
                        <BackArrow size={Icons.size.md} color={Colors.primary} />
                    </Pressable>
                  
                </View>
        <View style={styles.mainContainer}>
        <Text>No party data found</Text>
      </View>
      </SafeAreaView>
    );
  }

  const imageUrl = partyDetails.party.invitation.media[0]?.url || ''
  const dateString = String(partyDetails.party.time);
  const fullName = currentUserRsvp?.guest?.user
    ? `${currentUserRsvp.guest.user.firstName} ${currentUserRsvp.guest.user.lastName}`
    : '';

  return (
    <SafeAreaView style={{flex : 1}}>

        <View style={{marginLeft : 10, marginTop : Platform.OS === 'ios' ? 0 : 50 , zIndex : 1}}>
        <Pressable onPress={handleBackinRsvp} >
                        <BackArrow size={Icons.size.xl} color={Colors.black} />
                     
                    </Pressable>     
        </View>
               

      {isPartyCompleted ? (
        <View style={styles.mainContainer}>
      
          <RsvpCantBeDone/>
          <Text style={styles.cannotRSVPText}>
            Oops, you just missed it
          </Text>
          <Text style={styles.cannotRSVPSubText}>
          The event is over and RSVP is no longer open.
          </Text>
      
        </View>
      ) : (
        <>
      
      
      <View style={styles.mainContainer}>
      <Card.Cover
        source={imageUrl ? { uri: imageUrl } : getRandomDefaultImage()}
        style={styles.image}
      />

      <View style={styles.textContainer}>
        <Text style={styles.eventTitle}>{partyDetails.party.name}</Text>

        <View style={styles.infoContainer}>
          <View style={styles.infoRow}>
            <DueDate size={Icons.size.md} color={Colors.primary} />
            <Text style={styles.infoText}>
              {formatDateString(dateString, 'd MMM, h:mm a')}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Location size={Icons.size.md} color={Colors.primary} />
            <Text style={styles.infoText}>{partyDetails.party.addressBook?.label ?? 'LocationTBD'}</Text>
          </View>
        </View>

        <View style={[styles.infoRow, { marginTop: 10 }]}>
          <Hosts size={Icons.size.md} color={Colors.primary} />
          <Text style={styles.infoText}>
            Hosted by {partyDetails.party.event.mainHost.userId.firstName}
          </Text>
        </View>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, selectedResponse === 'yes' && styles.selectedButton]}
          disabled={updateRsvpLoading}
          onPress={() => {
            setSelectedResponse('yes');
            toggleModal();
          }}
        >
          <View style={styles.buttonTextIcon}>
            <Check size={Icons.size.md} color={Colors.black} />
            <Text style={[styles.buttonText, selectedResponse === 'yes' && styles.selectedButtonText]}>YES</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, selectedResponse === 'maybe' && styles.selectedButton]}
          disabled={updateRsvpLoading}
          onPress={() => {
            setSelectedResponse('maybe');
            toggleModal();
          }}
        >
          <View style={styles.buttonTextIcon}>
            <MayBe size={Icons.size.md} color={Colors.black} />
            <Text style={[styles.buttonText, selectedResponse === 'maybe' && styles.selectedButtonText]}>MAYBE</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.button, selectedResponse === 'no' && styles.selectedButton]} disabled={updateRsvpLoading} onPress={handleNoRsvp}>
          <View style={styles.buttonTextIcon}>
            <Cross size={Icons.size.md} color={Colors.black} />
            <Text style={[styles.buttonText, selectedResponse === 'no' && styles.selectedButtonText]}>NO</Text>
          </View>
        </TouchableOpacity>
      </View>
      </View>
      <Modal isVisible={isModalVisible} onBackdropPress={toggleModal} style={styles.bottomModal}>
        <RSVPModal
          selectedResponse={selectedResponse}
          onClose={toggleModal}
          rsvpId={currentUserRsvp?.id || null}
          partyId={partyId}
          fullName={fullName}
          navigation={navigation}
        />
      </Modal>
      </>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
 
    flex: 1,
    backgroundColor: 'white',
    padding: Spacing.md,
    alignContent: "center",
    alignItems: "center",
    justifyContent: "center"
  },
  image: {
    width: '100%',
    height: '65%',
    resizeMode: 'contain',
    borderRadius: Borders.radius.lg,
    marginBottom: Spacing.md,
    backgroundColor: "white"
  },
  textContainer: {
    width: '80%',
    alignItems: 'flex-start',
  },
  eventTitle: {
    fontSize: Typography.fontSize.xxl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
    marginBottom: Spacing.lg,
  },
  infoContainer: {
    flexDirection: 'row',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  infoText: {
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.md,
    color: Colors.black,
    marginLeft: Spacing.md,
    fontWeight: Typography.fontWeight.semibold,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: Spacing.md,
  },
  buttonTextIcon: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    columnGap: 5,
  },
  button: {
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: 78,
    height: 78,
    backgroundColor: 'white',
    borderRadius: Borders.radius.circle ,
    margin: Spacing.md
  },
  buttonText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.black,
    marginTop: Spacing.xs,
    fontFamily: Typography.fontFamily.primary,
  },
  bottomModal: {
    justifyContent: 'center',
    alignItems: 'center',
    margin: 0,
  },
  selectedButton: {
    backgroundColor: Colors.gradient.orange,
  },
  selectedButtonText: {
    color: 'white',
  },
  completedOverlay: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1,
    backgroundColor: Colors.overlay,
    justifyContent: 'center',
    alignItems: 'center',
  },
  completedOverlayText: {
    color: Colors.white,
    fontSize: Typography.fontSize.lg,
    fontWeight: 'bold',
    textAlign: 'center',
    padding: Spacing.lg,
  },
  cannotRSVPText: {
    fontSize: Typography.fontSize.xl,
    fontFamily: Typography.fontFamily.primary,
    textAlign: 'center',
    paddingTop: Spacing.md,
    color: Colors.text.secondary,
  },
  cannotRSVPSubText: {
    fontSize: Typography.fontSize.lg,
    fontFamily: Typography.fontFamily.primary,
    textAlign: 'center',
    paddingTop: Spacing.sm,
    color: Colors.text.secondary,
  },
});

export default RsvpScreen;