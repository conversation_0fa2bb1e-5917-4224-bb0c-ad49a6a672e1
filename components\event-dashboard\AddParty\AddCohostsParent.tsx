import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, Chip, Text } from 'react-native-paper';
import { Cohost } from '../../../app/(home)/EventsDashboard/AddParty.models';
import { BottomSheetModal } from '@gorhom/bottom-sheet';

interface AddCohostsProps {
  cohosts: Cohost[];
  onCohostChange: (cohosts: Cohost[]) => void;
  cohostSheetRef: React.RefObject<BottomSheetModal>;
}

export function AddCohosts({ cohosts, onCohostChange, cohostSheetRef }: AddCohostsProps) {
  const openCohostSheet = () => {
    cohostSheetRef.current?.present();
  };

  const removeCohost = (cohostId: string) => {
    const updatedCohosts = cohosts.filter(cohost => cohost.id !== cohostId);
    onCohostChange(updatedCohosts);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text variant="titleMedium">Co-hosts</Text>
        <Button
          mode="outlined"
          icon="plus"
          onPress={openCohostSheet}
          style={styles.addButton}
        >
          Add
        </Button>
      </View>
      
      <View style={styles.chipContainer}>
        {cohosts.map((cohost) => (
          <Chip
            key={cohost.id}
            onClose={() => removeCohost(cohost.id)}
            style={styles.chip}
          >
            {`${cohost?.firstName} ${cohost?.lastName ? cohost?.lastName : ''}`}
          </Chip>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  addButton: {
    borderRadius: 20,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    marginBottom: 4,
  },
});