import { gql } from '@apollo/client'

export const CREATE_USER = gql`
 mutation Mutation($input: RegisterInput!) {
  register(input: $input) {
    ... on UserResponse {
      result {
        user {
          email
          emailVerified
          externalId
          firstName
          id
          isActive
          isRegistered
          lastName
          phone
        }
      }
      message
      status
    }
    ... on UserErrorResponse {
      errors {
        field
        message
      }
      message
      status
    }
  }
}
`

export const GET_USER = gql`
 query GetUsers($filters: UserFilterInput) {
  getUsers(filters: $filters) {
    ... on UsersResponse {
      result {
        users {
          email
          emailVerified
          externalId
          firstName
          id
          isActive
          isRegistered
          lastName
          phone
          phoneVerified
          profilePicture
        }
      }
    }
    ... on UserErrorResponse {
      errors {
        field
        message
      }
      message
      status
    }
  }
}
`