import React from 'react';
import { View, Platform } from 'react-native';
import { Modal, Text, Button, useTheme } from 'react-native-paper';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';

interface DateModalProps {
  visible: boolean;
  onDismiss: () => void;
  value: Date;
  onChange: (date: Date) => void;
  title: string;
  minimumDate: Date;
  maximumDate?: Date;
}

export function DatePickerModal({
  visible,
  onDismiss,
  value,
  onChange,
  title,
  minimumDate,
  maximumDate,
}: DateModalProps) {
  const theme = useTheme();

  const handleChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      onDismiss();
    }
    if (selectedDate) {
      const newDate = new Date(selectedDate);
      newDate.setHours(
        value.getHours(),
        value.getMinutes(),
        value.getSeconds(),
        value.getMilliseconds()
      );
      if (newDate.getTime() < minimumDate.getTime()) {
        onChange(minimumDate);
        return;
      }
      onChange(newDate);
    }
  };

  if (Platform.OS === 'android') {
    if (!visible) return null;
    return (
      <DateTimePicker
        value={value}
        mode="date"
        display="default"
        onChange={handleChange}
        minimumDate={minimumDate}
        maximumDate={maximumDate}
        themeVariant={theme.dark ? 'dark' : 'light'}
        accentColor={theme.colors.primary}
      />
    );
  }

  return (
    <Modal
      visible={visible}
      onDismiss={onDismiss}
      contentContainerStyle={{ 
        backgroundColor: theme.colors.background,
        padding: 20,
        margin: 20,
        borderRadius: 8
      }}
    >
      <View>
        <Text variant="titleMedium" style={{ marginBottom: 16, color: theme.colors.onBackground }}>
          {title}
        </Text>
        <DateTimePicker
          value={value}
          mode="date"
          display="inline"
          onChange={handleChange}
          minimumDate={minimumDate}
          maximumDate={maximumDate}
          textColor={theme.colors.onBackground}
          accentColor={theme.colors.primary}
          themeVariant={theme.dark ? 'dark' : 'light'}
        />
        <Button 
          mode="contained" 
          onPress={onDismiss}
          style={{ marginTop: 16 }}
        >
          Done
        </Button>
      </View>
    </Modal>
  );
}