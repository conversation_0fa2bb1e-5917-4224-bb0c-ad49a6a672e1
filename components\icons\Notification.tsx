import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Notification = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      fill="url(#Notification-1_svg__a)"
      fillRule="evenodd"
      d="M3.424 1.786a3.75 3.75 0 0 1 6.402 2.652v2.916a.834.834 0 0 0 .833.833.417.417 0 0 1 0 .834H1.492a.417.417 0 0 1 0-.834.834.834 0 0 0 .834-.833V4.438a3.75 3.75 0 0 1 1.098-2.652m1.193 8.902c0-.346.28-.626.625-.626H6.91a.625.625 0 1 1 0 1.25H5.242a.625.625 0 0 1-.625-.624"
      clipRule="evenodd"
    />
    <Defs>
      <LinearGradient
        id="Notification-1_svg__a"
        x1={6.076}
        x2={6.076}
        y1={-0.595}
        y2={13.755}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Notification_svg__a)"
    >
      <Path d="M5.141 11.334H6.96M9.687 4.516a3.636 3.636 0 1 0-7.273 0v3.182A1.364 1.364 0 0 1 1.05 9.062h10a1.364 1.364 0 0 1-1.363-1.364z" />
    </G>
    <Defs>
      <ClipPath id="Notification_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Notification;
