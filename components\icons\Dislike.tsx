import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const Dislike = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      fill="url(#Dislike-1_svg__a)"
      fillRule="evenodd"
      d="M3.952 1.284c.266-.126.556-.192.85-.192h4.23c.974 0 1.803.71 1.954 1.672l.495 3.167c.15.96-.592 1.827-1.563 1.827H7.32v1.865a1.286 1.286 0 0 1-2.408.628L3.27 7.316a1.2 1.2 0 0 1-.151-.58V2.431c0-.458.263-.874.676-1.071zM1.288 7.171A.79.79 0 0 1 .5 6.383V2.338a.79.79 0 0 1 .788-.788h.393c.218 0 .395.177.395.396v4.83a.395.395 0 0 1-.395.395z"
      clipRule="evenodd"
    />
    <Defs>
      <LinearGradient
        id="Dislike-1_svg__a"
        x1={6}
        x2={6}
        y1={12.093}
        y2={-1.165}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <Path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M1.385 6.869h1.822V2.016H1.385A.384.384 0 0 0 1 2.401v4.083a.384.384 0 0 0 .385.385M3.207 6.87l1.954 3.122a.84.84 0 0 0 .723.4.853.853 0 0 0 .884-.846V7.308h3.376a.885.885 0 0 0 .846-1.016l-.615-3.968a.88.88 0 0 0-.87-.77H4.462c-.24 0-.477.055-.692.162l-.554.277M3.207 6.87V2.015"
    />
        </>
      )}
    </Svg>
  );
};
export default Dislike;
