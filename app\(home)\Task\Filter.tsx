import { Pressable, View, useWindowDimensions } from 'react-native';
import { Text, Appbar } from 'react-native-paper';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useState, useMemo } from 'react';
import { styles } from "@/app/(home)/Task/Filter.styles";
import { TaskStatus } from '@/constants/Task.constants';
import { useAssignees } from './hooks/useTaskData';
import { useEventStore } from '@/store/eventStore';
import { StatusFilter } from '@/app/(home)/Task/components/StatusFilter';
import { ActionButton } from '@/components/UI/ReusableComponents/ActionButton';
import { Assignee } from '@/components/Task/SharedInterfaces';
import { DateFilter } from './components/DateFilter';
import { AssigneeFilter } from './components/AssigneeFilter';
import { formatDateAndTime } from '@/app/(home)/utils/reusableFunctions';
import { formatDate } from 'date-fns';
import { useFilterStore } from '@/services/filterService';
import { Icons, Colors } from '@/constants/DesignSystem';
import { BackArrow } from '@/components/icons';

export default function Filter() {
  const router = useRouter();
  const { initialFilters: initialFiltersString } = useLocalSearchParams();
  const selectedEventId = useEventStore((state) => state.selectedEventId);
  const { assignees } = useAssignees(selectedEventId);
  const setFilters = useFilterStore((state) => state.setFilters);

  // Parse initial filters if they exist
  const initialFilters = useMemo(() => {
    if (initialFiltersString) {
      try {
        const parsed = JSON.parse(initialFiltersString as string);
        // Convert date strings back to Date objects if they exist
        if (parsed.date?.startDate) {
          parsed.date.startDate = new Date(parsed.date.startDate);
          parsed.date.endDate = new Date(parsed.date.endDate);
        }
        return parsed;
      } catch (error) {
        console.error('Error parsing initial filters:', error);
        return null;
      }
    }
    return null;
  }, [initialFiltersString]);

  // Initialize state with initial filters
  const [selectedStatus, setSelectedStatus] = useState<string[]>(
    initialFilters?.status || ['all']
  );
  const [selectedAssignee, setSelectedAssignee] = useState<Assignee | null>(null);
  const [isDateRangeSelectorVisible, setIsDateRangeSelectorVisible] = useState(false);
  const [dateRange, setDateRange] = useState({
    startDate: initialFilters?.date?.startDate || new Date(),
    endDate: initialFilters?.date?.endDate || new Date(),
  });
  const [selectedDateRange, setSelectedDateRange] = useState(
    initialFilters?.date || 'Select Range'
  );
  const [selectedAssignees, setSelectedAssignees] = useState<Assignee[]>(
    initialFilters?.assignee 
      ? assignees?.filter(a => initialFilters.assignee.includes(a.id)) || []
      : []
  );
  const [isAssigneeModalOpen, setIsAssigneeModalOpen] = useState(false);

  const isDateRangeSelected = selectedDateRange !== 'Select Range';

  const statusOptions = [
    { label: 'All', value: 'all' },
    { label: 'In Progress', value: TaskStatus.IN_PROGRESS },
    { label: 'Completed', value: TaskStatus.COMPLETED },
    { label: 'Closed', value: TaskStatus.CLOSED },
  ];

  function handleStatusSelect(status: typeof TaskStatus[keyof typeof TaskStatus] | 'all') {
    if (status === 'all') {
      setSelectedStatus(selectedStatus.includes('all') ? [] : ['all']);
      return;
    }
    
    const newSelection = selectedStatus.filter(s => s !== 'all');
    
    if (selectedStatus.includes(status)) {
      const updatedSelection = newSelection.filter(s => s !== status);
      setSelectedStatus(updatedSelection.length === 0 ? ['all'] : updatedSelection);
    } else {
      setSelectedStatus([...newSelection, status]);
    }
  }

  const handleDateRangeConfirm = (startDate: Date, endDate: Date) => {
    setDateRange({ startDate, endDate });
    setSelectedDateRange(`${formatDate(startDate, 'MMM dd, yy')} - ${formatDate(endDate, 'MMM dd, yy')}`);
  };

  function handleApplyFilters() {
    const store = useFilterStore.getState();
    store.setFilters({
      status: selectedStatus.includes('all') ? [] : selectedStatus,
      assignee: selectedAssignees.map(assignee => ({
        id: assignee.id,
        firstName: assignee.firstName,
        lastName: assignee.lastName
      })),
      date: selectedDateRange !== 'Select Range' ? {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      } : undefined
    });
    router.back();
  }

  function handleResetFilters() {
    setSelectedStatus([]);
    setSelectedDateRange('Select Range');
    setDateRange({
      startDate: new Date(),
      endDate: new Date(),
    });
    setSelectedAssignees([]);
  }

  const getSelectedAssigneesDisplay = () => {
    if (selectedAssignees.length === 0) return 'Select';
    return selectedAssignees.map(a => 
      `${a.firstName} ${a.lastName}`.trim()
    ).join(', ');
  };

  const { width: screenWidth } = useWindowDimensions();

  const handleResetDate = () => {
    setSelectedDateRange('Select Range');
    setDateRange({
      startDate: new Date(),
      endDate: new Date(),
    });
  };

  const clearFilters = useFilterStore((state) => state.clearFilters);

  function handleResetAllFilters() {
    clearFilters();
    setSelectedStatus(['all']);
    setSelectedDateRange('Select Range');
    setDateRange({
      startDate: new Date(),
      endDate: new Date(),
    });
    setSelectedAssignees([]);
  }

  const filters = useFilterStore((state) => state.filters);

  return (
    <View style={{ flex: 1, marginBottom: 16 }}>
      <Appbar.Header style={styles.header}>
        <Pressable onPress={() => router.back()} style={styles.backButton}>
          <BackArrow size={Icons.size.lg} color={Colors.primary} />
        </Pressable>
        <Text variant="titleLarge" style={styles.headerTitle}>Filters</Text>
        <View style={styles.rightPlaceholder} />
      </Appbar.Header>

      <View style={styles.filterPageContainer}>
        <View style={styles.gridContainer}>
          <DateFilter 
            isDateRangeSelected={isDateRangeSelected}
            selectedDateRange={typeof selectedDateRange === 'string' ? selectedDateRange : 'Select Range'}
            isDateRangeSelectorVisible={isDateRangeSelectorVisible}
            setIsDateRangeSelectorVisible={setIsDateRangeSelectorVisible}
            dateRange={dateRange}
            onDateRangeConfirm={handleDateRangeConfirm}
            onResetDate={handleResetDate}
          />

          <View style={[styles.oddGridItem, { width: '30%'}]}>
            <Text style={styles.gridItemTextLabel}>Status</Text>
          </View>
          <View style={[styles.evenGridItem, { width: '70%'}]}>
            <StatusFilter 
              selectedStatus={selectedStatus as any} 
              onStatusSelect={handleStatusSelect} 
            />
          </View>

          <AssigneeFilter 
            selectedAssignees={selectedAssignees}
            setSelectedAssignees={setSelectedAssignees}
            isAssigneeModalOpen={isAssigneeModalOpen}
            setIsAssigneeModalOpen={setIsAssigneeModalOpen}
          />

          <View style={[styles.oddGridItem, { width: '30%', borderBottomWidth: 0}]}>
            <Pressable onPress={handleResetAllFilters}>
              <Text style={styles.gridItemTextLabelReset}>Reset All</Text>
            </Pressable>
          </View>
          <View style={[styles.evenGridItem, { width: '70%', borderBottomWidth: 0}]} />
        </View>

        <View style={styles.buttonContainer}>
          <ActionButton
            onPress={handleApplyFilters}
            label="Apply"
          />
        </View>
      </View>
    </View>
  );
}