import * as React from "react";
import Svg, {
  G,
  <PERSON>,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Rect,
} from "react-native-svg";
import { CommonProps } from ".";

const StarOff = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#StarOff-1_svg__a)">
            <Path fill="url(#StarOff-1_svg__b)" d="M9.6084 11.2539C9.57931 11.2821 9.54864 11.3091 9.51562 11.333C9.38533 11.4273 9.23078 11.483 9.07031 11.4941C8.91011 11.5053 8.7493 11.4709 8.60742 11.3955L6.0293 10.0332C6.02006 10.0292 6.0101 10.0273 6 10.0273C5.98991 10.0274 5.97993 10.0292 5.9707 10.0332L3.39258 11.3955L3.3916 11.3965C3.24984 11.4715 3.08975 11.5052 2.92969 11.4941C2.76921 11.483 2.61463 11.4274 2.48438 11.333C2.35411 11.2385 2.25289 11.1091 2.19238 10.96C2.13197 10.8111 2.11516 10.6476 2.14258 10.4893L2.6416 7.56836V7.56543C2.64231 7.56148 2.64185 7.55752 2.64062 7.55371C2.63934 7.54985 2.63772 7.54576 2.63477 7.54297L2.62988 7.53906L0.535156 5.51758C0.416486 5.40441 0.333141 5.2591 0.294922 5.09961C0.256644 4.93966 0.265376 4.77219 0.320312 4.61719C0.375342 4.46211 0.473891 4.32569 0.604492 4.22559C0.730787 4.12885 0.88192 4.06911 1.04004 4.05371L2.91699 3.77539L9.6084 11.2539ZM6 0.496094C6.16228 0.496094 6.32121 0.542592 6.45801 0.629883C6.59314 0.716219 6.70111 0.839441 6.76953 0.984375L8.06934 3.60742L8.0791 3.62695C8.08012 3.6277 8.08176 3.62772 8.08301 3.62793C8.08799 3.62848 8.09367 3.62915 8.09863 3.62988L10.96 4.05371C11.1181 4.06909 11.2692 4.12883 11.3955 4.22559C11.5262 4.3257 11.6247 4.46209 11.6797 4.61719C11.7347 4.77221 11.7433 4.93965 11.7051 5.09961C11.6668 5.25909 11.5835 5.40441 11.4648 5.51758L9.37012 7.53906L9.36523 7.54297C9.36233 7.54575 9.36059 7.54987 9.35938 7.55371C9.35814 7.55752 9.35768 7.56149 9.3584 7.56543V7.56836L9.76074 9.92383L3.99414 3.47852L5.23047 0.984375C5.29889 0.839436 5.40686 0.716222 5.54199 0.629883C5.67878 0.542576 5.83771 0.4961 6 0.496094Z"/>
            <Path stroke="url(#StarOff-1_svg__c)" strokeLinecap="round" d="M1 2L9.5 11.5"/>
          </G>
          <Defs>
            <LinearGradient id="StarOff-1_svg__b" x1="5.99999" y1="-0.831506" x2="5.99999" y2="14.025" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="StarOff-1_svg__c" x1="5.25" y1="0.853448" x2="5.25" y2="13.6839" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <ClipPath id="StarOff-1_svg__a">
              <Rect width="12" height="12" fill="#fff"/>
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G clipPath="url(#StarOff_svg__a)">
            <Path stroke={color} strokeLinecap="round" strokeLinejoin="round" d="M9 9.5L8.78242 7.57258C8.77054 7.5055 8.77556 7.4365 8.79714 7.37186C8.81866 7.30722 8.85605 7.24899 8.90579 7.20248L10.8565 5.26714C10.9143 5.21318 10.9555 5.14391 10.9752 5.06743C10.995 4.99095 10.9927 4.9104 10.9684 4.83521C10.9442 4.76002 10.899 4.69328 10.8383 4.64279C10.7775 4.5923 10.7036 4.56014 10.6252 4.55007L7.91887 4.14912C7.85056 4.14156 7.78542 4.11621 7.72995 4.07563C7.67448 4.03505 7.63062 3.98063 7.60274 3.91781L6.37677 1.44274C6.34338 1.37066 6.29007 1.30962 6.22313 1.26685C6.15619 1.22408 6.0784 1.20135 5.99895 1.20135C5.91951 1.20135 5.84173 1.22408 5.77479 1.26685C5.70784 1.30962 5.65453 1.37066 5.62114 1.44274L5 3M3.88095 4.14912L1.37266 4.55007C1.29238 4.5563 1.21563 4.58568 1.1517 4.63464C1.08778 4.68361 1.03942 4.75006 1.0125 4.82595C0.985569 4.90184 0.981224 4.98391 0.999988 5.06221C1.01875 5.14051 1.05982 5.2117 1.11822 5.26714L3.0921 7.17163C3.14189 7.21815 3.17926 7.27637 3.2008 7.34102C3.22235 7.40566 3.22739 7.47466 3.21547 7.54174L2.74513 10.2944C2.73151 10.3726 2.74012 10.453 2.76997 10.5266C2.79983 10.6001 2.84973 10.6638 2.914 10.7105C2.97827 10.7571 3.05433 10.7847 3.13353 10.7902C3.21272 10.7957 3.29187 10.7789 3.36197 10.7416L5.79848 9.45391C5.86087 9.4233 5.92945 9.40741 5.99895 9.40741C6.06846 9.40741 6.13704 9.4233 6.19943 9.45391L7.78971 10.2944"/>
            <Path stroke={color} strokeLinecap="round" d="M2 2L10.5 11.5"/>
          </G>
          <Defs>
            <ClipPath id="StarOff_svg__a">
              <Rect width="12" height="12" fill="#fff"/>
            </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  );
};
export default StarOff;
