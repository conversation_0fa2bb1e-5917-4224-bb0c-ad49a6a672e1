import React from 'react';
import { View, Text, TextInput, StyleSheet, ScrollView } from 'react-native';
import { CircleData } from '@/app/(home)/CirclesDashboard/AddCircle';
import { Colors, Typography, Spacing, Borders } from '@/constants/DesignSystem';


type CreateCircleFormProps = {
    circleData: CircleData;
    setCircleData: React.Dispatch<React.SetStateAction<CircleData>>;
    formErrors: { name?: string; description?: string };
};

export const CreateCircleForm: React.FC<CreateCircleFormProps> = ({ circleData, setCircleData, formErrors }) => {


    const handleInputChange = (field: keyof CircleData, value: string) => {
        setCircleData((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    return (
        <View style={{ flex: 1 }}>
            <ScrollView keyboardShouldPersistTaps="handled"
                style={styles.container}>
                <View style={styles.inputContainer}>
                    <Text style={styles.label}> {'Name*'} </Text>
                    <TextInput
                        style={styles.input}
                        value={circleData.name}
                        onChangeText={(text) => handleInputChange('name', text)}
                        maxLength={100}
                        placeholder='Circle name'
                        placeholderTextColor={Colors.mediumGray}
                    />
                    {formErrors.name && <Text style={styles.errorText}>{formErrors.name}</Text>}
                </View>
                <View style={styles.inputContainer}>
                    <Text style={styles.label}>{'Description'}</Text>
                    <TextInput
                        style={[styles.input, styles.descriptionInput]}
                        value={circleData.description}
                        onChangeText={(text) => handleInputChange('description', text)}
                        placeholder='Short description'
                        placeholderTextColor={Colors.mediumGray}
                        maxLength={500}
                        multiline={true}
                    />
                    {formErrors.description && <Text style={styles.errorText}>{formErrors.description}</Text>}
                </View>

            </ScrollView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingHorizontal: Spacing.lg,
        gap: Spacing.sm,
        flexGrow: 1,
    },
    label: {
        fontSize: Typography.fontSize.lg,
        marginBottom: Spacing.xs,
        fontFamily: Typography.fontFamily.primary,
        fontWeight: Typography.fontWeight.semibold,
        color: Colors.text.tertiary,
    },
    input: {
        padding: Spacing.md,
        fontSize: Typography.fontSize.md,
        marginBottom: Spacing.sm,
        borderRadius: Borders.radius.sm,
        backgroundColor: Colors.background.tertiary,
        borderWidth: Borders.width.thin, 
        borderColor: Colors.border.medium
    },
    descriptionInput: {
        height: 100,
        textAlignVertical: 'top',
    },
    inputContainer: {
        position: 'relative',
        marginBottom: Spacing.sm,
    },
    placeholder: {
        position: 'absolute',
        left: Spacing.lg,
        top: 40,
        fontSize: Typography.fontSize.md,
        color: Colors.border.light,
        fontWeight: Typography.fontWeight.regular,
        fontFamily: Typography.fontFamily.primary,
    },

    errorText: {
        color: Colors.error,
        fontSize: Typography.fontSize.md,
        marginTop: Spacing.xs,
        fontFamily: Typography.fontFamily.primary,
    },
});
