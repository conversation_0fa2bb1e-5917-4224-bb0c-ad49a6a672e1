import React, { useRef, useState, useEffect } from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, Image, View } from 'react-native';
import { Colors } from '@/constants/Colors';
import * as Haptics from 'expo-haptics';
import party_overview from '@/data/party_overview.json';
import { ThemedText } from '@/components/UI/ThemedText';
import { ThemedView } from '@/components/UI/ThemedView';
import { GET_ALL_PARTY_SERVICES } from '@/app/(home)/EventsDashboard/AddParty.data';
import { useQuery } from '@apollo/client';
import { VendorTypes } from '@/constants/vendorTypes';

export function EventPreferencesScrollView({ setSelectedVendor, vendor_types, setActiveIndex, activeIndex }: { setSelectedVendor: (vendorName: string) => void, vendor_types: any[], setActiveIndex: (index: number) => void, activeIndex: number }) {

    const scrollRef = useRef<ScrollView>(null);
    const itemsRef = useRef<any[]>([]);
    const [vendorPreferences, setVendorPreferences] = useState<any>([]);

    const { data: servicesData } = useQuery(GET_ALL_PARTY_SERVICES);

    useEffect(() => {
        if (vendor_types?.length >= 0) {
            const overview = party_overview;
            const venue = servicesData?.getMdVendorTypes?.result?.mdVendorTypes?.find((service: any) => service.name.toLowerCase() === VendorTypes.VENUE);
            const venueExists = vendor_types.some(vendor => vendor.name.toLowerCase() === VendorTypes.VENUE);
            if (!venueExists && venue) {
                setVendorPreferences([overview, ...vendor_types, venue]);
            } else {
                setVendorPreferences([overview, ...vendor_types]);
            }
        }
    }, [vendor_types, servicesData]);

    const onCategoryChanged = (vendorName: string) => {
        setSelectedVendor(vendorName);
    };

    const selectCategory = (index: number, item: any) => {
        const selected = itemsRef.current[index];
        setActiveIndex(index);
        selected?.measure((x: any) => {
            scrollRef.current?.scrollTo({ x: x - 16, y: 0, animated: true });
        });
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        onCategoryChanged(item?.name);
    };

    return (
        <ThemedView style={styles.container}>
            <ScrollView
                ref={scrollRef}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.scrollViewContent}>
                {vendorPreferences?.map((item: any, index: number) => (
                    <TouchableOpacity
                        ref={(el) => el && (itemsRef.current[index] = el)}
                        key={index}
                        style={styles.categoriesBtn}
                        onPress={() => selectCategory(index, item)}>
                        <Image
                            source={item?.icon?.iconSrc ? { uri: item?.icon?.iconSrc } : require('@/assets/images/notebook-pen.png')}
                            style={[styles.icon, { tintColor: activeIndex === index ? Colors.light.text : Colors.light.icon }]}
                        />
                        <ThemedText style={activeIndex === index ? styles.categoryTextActive : styles.categoryText}>
                            {item?.name}
                        </ThemedText>
                        {activeIndex === index && <View style={styles.activeIndicator} />}
                    </TouchableOpacity>
                ))}
            </ScrollView>
        </ThemedView>
    )
}

const styles = StyleSheet.create({
    categoryText: {
        fontSize: 14,
        fontFamily: 'mon-sb',
        color: Colors.light.icon,
    },
    categoryTextActive: {
        fontSize: 14,
        fontFamily: 'mon-sb',
        color: '#000',
    },
    categoriesBtn: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingBottom: 8,
        position: 'relative',
    },
    icon: {
        width: 20,  // Icon width for bottom nav
        height: 20, // Icon height for bottom nav
        resizeMode: 'contain', // Ensures the image is not stretched or cropped
    },
    container: {
        height: 55,
    },
    scrollViewContent: {
        height: 55,
        paddingTop: 10,
        paddingHorizontal: 10, // Add horizontal padding
        alignItems: 'center',
        gap: 20,
    },
    activeIndicator: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        height: 3,
        backgroundColor: Colors.light.text,
        borderRadius: 2,
    },
})

export default EventPreferencesScrollView
