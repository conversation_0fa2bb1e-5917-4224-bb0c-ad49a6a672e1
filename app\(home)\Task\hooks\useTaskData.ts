import { useQuery } from '@apollo/client';
import { Get_PARTIES_BY_USER_Id_FOR_DROPDOWN } from '../Task.data';
import { GET_EVENT_ASSIGNEES_BY_EVENT_ID } from '../../EventsDashboard/AddParty.data';
import { TaskNames, TaskPagination } from '@/constants/Task.constants';
import { useToast } from '@/components/Toast/useToast';
import { useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { Assignees } from '@/components/Task/SharedInterfaces';

export function useParties(selectedEventId: string | null) {
  const toast = useToast();

  const { 
    data: partiesData, 
    loading: partiesLoading, 
    refetch: refetchParties 
  } = useQuery(Get_PARTIES_BY_USER_Id_FOR_DROPDOWN, {
    variables: {
      filter: {
        eventId: selectedEventId
      },
      pagination: {
        limit: TaskPagination.DEFAULT_LIMIT,
        skip: TaskPagination.DEFAULT_SKIP
      }
    },
    skip: !selectedEventId,
    fetchPolicy: 'network-only',
    onError: () => {
      toast.error(TaskNames.FETCH_PARTIES_ERROR);
    }
  });

  useFocusEffect(
    useCallback(() => {
      if (selectedEventId) {
        refetchParties({
          filter: {
            eventId: selectedEventId
          },
          pagination: {
            limit: TaskPagination.DEFAULT_LIMIT,
            skip: TaskPagination.DEFAULT_SKIP
          }
        });
      }
    }, [selectedEventId, refetchParties])
  );

  const handleRefreshParties = async () => {
    if (selectedEventId) {
      try {
        await refetchParties({
          filter: {
            eventId: selectedEventId
          },
          pagination: {
            limit: TaskPagination.DEFAULT_LIMIT,
            skip: TaskPagination.DEFAULT_SKIP
          }
        });
      } catch {
        toast.error(TaskNames.FETCH_PARTIES_ERROR);
      }
    }
  };

  const parties = partiesData?.getParties?.result?.parties?.map((party: any) => ({
    id: party.id,
    name: party.name
  })) || [];

  return { parties, partiesLoading, handleRefreshParties };
}

export function useAssignees(selectedEventId: string | null) {
  const toast = useToast();

  const { 
    data: assigneesData, 
    loading: assigneesLoading 
  } = useQuery(GET_EVENT_ASSIGNEES_BY_EVENT_ID, {
    variables: {
      eventId: selectedEventId
    },
    skip: !selectedEventId,
    onError: () => {
      toast.error(TaskNames.FETCH_ASSIGNEES_ERROR);
    }
  });

  const assignees: Assignees = assigneesData?.getEventAssignees?.result?.assignees || [];

  return { assignees, assigneesLoading };
} 