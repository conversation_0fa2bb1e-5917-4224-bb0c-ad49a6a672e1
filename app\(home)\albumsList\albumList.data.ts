import { gql } from "@apollo/client";

export const GET_ALBUMS = gql`
query GetMediaFolders($filter: MediaFolderFilterInput, $pagination: PaginationInput) {
  getMediaFolders(filter: $filter, pagination: $pagination) {
    ... on MediaFoldersResponse {
      result {
        mediaFolders {
          albumCover
          name
          publish
          id
        }
      }
      pagination {
         totalItems
         totalPages
         pageSize
         currentPage
       }
    }
  }
}
`;