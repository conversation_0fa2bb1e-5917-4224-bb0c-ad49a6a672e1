import { useState, useEffect } from 'react';
import {
  Text,
  View,
  FlatList,
  TextInput,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import * as Contacts from 'expo-contacts';
import { StackScreenProps } from '@react-navigation/stack';
import { PartyDetailsRootList } from '@/components/create-event/Navigation/PartyDetailsRootList';
import InitialsAvatar from '@/components/Avatar/InitialsAvatar';
import { Guest, Party, User } from '@/app/(home)/PartyDetails/NewPartyDetailsResponse.model';
import { Linking } from 'react-native';
import { ProfileDetail, Search, CheckBox } from '@/components/icons';
import { Colors, Icons } from '@/constants/DesignSystem';

type SendInviteParams = {
  partyDetails: Party,
  selectedContacts: Contacts.Contact[]
};

type SendNewInvitesProps = StackScreenProps<PartyDetailsRootList, 'SendInvitesView'> & SendInviteParams;

const SendNewInvites: React.FC<SendNewInvitesProps> = ({ route, navigation }) => {
  const [contacts, setContacts] = useState<User[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<User[]>([]);
  const [permission, setPermission] = useState(false);
  const [search, setSearch] = useState('');
  const [selectedCount, setSelectedCount] = useState(0);
  const [selectedContacts, setSelectedContacts] = useState<Record<string, User>>({});
  const { partyDetails } = route.params;

  useEffect(() => {
    (async () => {
      const { status } = await Contacts.requestPermissionsAsync();
      if (status === 'granted') {
        setPermission(true);
        const { data } = await Contacts.getContactsAsync({
          fields: [Contacts.Fields.FirstName, Contacts.Fields.LastName, Contacts.Fields.PhoneNumbers],
        });

        const contactsWithPhoneNumbers = data.filter(
          (contact) => contact.phoneNumbers && contact.phoneNumbers.length > 0
        );

        if (contactsWithPhoneNumbers.length > 0) {
          const sortedContacts = contactsWithPhoneNumbers.sort((a, b) => {
            const nameA = (a.firstName || '').toUpperCase();
            const nameB = (b.firstName || '').toUpperCase();
            if (nameA < nameB) return -1;
            if (nameA > nameB) return 1;
            return (a.lastName || '').toUpperCase().localeCompare((b.lastName || '').toUpperCase());
          });

          const myContacts: User[] = sortedContacts.map((contact) => ({
            id: contact.id ?? '',
            firstName: contact.firstName ?? '',
            lastName: contact.lastName ?? '',
            phone: contact.phoneNumbers?.[0]?.number ?? '',
            profilePicture: '',
            email: '',
            role: [''],
          }));

          setContacts(myContacts);
          setFilteredContacts(myContacts);
          console.log('Contacts:', partyDetails?.invitation.savedGuests);
        }
      }
    })();
  }, []);

  const searchFilter = (text: string) => {
    setSearch(text);
    const filteredData = contacts.filter((item) => {
      const itemData = `${item.firstName} ${item.lastName}`.toUpperCase();
      const textData = text.toUpperCase();
      return itemData.indexOf(textData) > -1;
    });

    setFilteredContacts(filteredData);
  };

  const handleSelectContact = (contact: User) => {
    const updatedSelectedContacts = { ...selectedContacts };

    if (selectedContacts[contact.id ?? '']) {
      delete updatedSelectedContacts[contact.id ?? ''];
    } else {
      updatedSelectedContacts[contact.id ?? ''] = contact;
    }

    setSelectedContacts(updatedSelectedContacts);

    // Update count based on the number of selected contacts
    setSelectedCount(Object.keys(updatedSelectedContacts).length);
  };

  const handleNext = () => {
    // Normalize savedGuests to match the User structure
    const normalizedSavedGuests = savedGuests.map((guest) => ({
      id: guest.user.id,
      firstName: guest.user.firstName,
      lastName: guest.user.lastName,
      phone: guest.user.phone,
      profilePicture: guest.user.profilePicture,
      email: '', // Add default value if email is not available
      role: [''], // Add default value if role is not available
    }));

    // Combine normalized savedGuests with selectedContacts
    const combinedContacts = [
      ...normalizedSavedGuests,
      ...Object.values(selectedContacts),
    ];

    console.log('Combined Contacts:', combinedContacts);

    // Navigate to the next screen with the combined contacts
    navigation.navigate('SaveOrSendInvite', {
      partyDetails,
      selectedContacts: combinedContacts,
    });
  };

  const onRsvp = () => {
    navigation.goBack();
  }

  const renderSavedGuestItem = ({ item }: { item: Guest }) => {
    console.log('Saved Guest Item:', item.user.firstName);
    return (
      <View style={styles.contactItem}>
        <InitialsAvatar firstName={item.user.firstName ?? ''} lastName={item.user.lastName ?? ''} />
        <View style={styles.contactInfo}>
          <Text style={styles.contactName}>{item.user.firstName ?? ""} {item.user.lastName ?? ""}</Text>
          <Text style={styles.contactNumber}>{item.user.phone ?? ''}</Text>
        </View>
      </View>
    );
  };

  const renderItem = ({ item }: { item: User }) => {
    const phoneNumber = item.phone ?? '';
    const isSelected = !!selectedContacts[item.id ?? ''];

    return (
      <TouchableOpacity onPress={() => handleSelectContact(item)} activeOpacity={1}>
        <View style={styles.contactItem}>
          <InitialsAvatar firstName={item.firstName ?? ""} lastName={item.lastName ?? ""} />
          <View style={styles.contactInfo}>
            <Text style={styles.contactName}>{item.firstName} {item.lastName}</Text>
            <Text style={styles.contactNumber}>{phoneNumber}</Text>
          </View>
          <View style={{ flex: 1 }} />
          {isSelected ? (
            <CheckBox size={20} color={Colors.primary} />
          ) : (
            <View style={styles.rectangle} />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  if (!permission) {
    return (
      <View style={styles.permissionDeniedContainer}>
        <Text style={styles.permissionDeniedText}>
          Permission to access contacts is denied. Please enable it in your device settings.
        </Text>
        <TouchableOpacity
          style={styles.settingsButton}
          onPress={() => {
            Linking.openSettings();
          }}
        >
          <Text style={styles.settingsButtonText}>Go to Settings</Text>
        </TouchableOpacity>
      </View>
    );
  }
  const savedGuests = partyDetails?.invitation?.savedGuests ?? [];
  return (
    <View style={styles.container}>
      <TouchableOpacity
        onPress={() => onRsvp()}
        style={styles.floatingActionButton}
        activeOpacity={1}
      >
        <ProfileDetail size={Icons.size.md} color={Colors.primary} />
        <Text style={styles.fabText}>RSVP</Text>
      </TouchableOpacity>
      <View style={styles.searchBarContainer}>
        <Search size={Icons.size.md} color={Colors.mediumGray} />
        <TextInput
          placeholder="Find a guest..."
          placeholderTextColor={Colors.mediumGray}
          style={styles.searchBar}
          value={search}
          onChangeText={searchFilter}
        />
      </View>
      <FlatList
        data={filteredContacts}
        renderItem={renderItem}
        keyExtractor={(item) => item.id ?? ''}
        style={styles.list}
        ListHeaderComponent={
          <>
            {savedGuests.length > 0 && (
              <>
                <Text style={styles.sectionHeader}>Previously Saved Contacts ({savedGuests.length})</Text>
                <FlatList
                  data={partyDetails?.invitation.savedGuests ?? []}
                  renderItem={renderSavedGuestItem}
                  keyExtractor={(savedItem) => savedItem.id ?? Math.random().toString()}
                  style={styles.list}
                />
              </>
            )}
            <Text style={styles.sectionHeader}>Newly Selected Contacts ({filteredContacts.length})</Text>
          </>
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No matching guests found.</Text>
          </View>
        }
      />
      <View style={styles.bottomBar}>
        <Text style={styles.selectedCount}>{selectedCount} Selected</Text>
        <TouchableOpacity
          style={[styles.nextButton, selectedCount === 0 && { backgroundColor: Colors.background.tertiary }]}
          onPress={handleNext}
          disabled={selectedCount === 0}
        >
          <Text style={styles.nextButtonText}>Next</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 40,
    paddingHorizontal: 10,
    backgroundColor: '#FFFFFF'
  },
  description: {
    marginTop: 20,
    textAlign: 'center',
  },
  list: {
    marginTop: 10,
  },
  contactItem: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 10,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  contactInfo: {
    marginLeft: 16,
  },
  contactName: {
    fontSize: 16,
    color: '#333',
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: 'bold'
  },
  contactNumber: {
    fontSize: 14,
    color: '#666',
    fontFamily: 'Plus Jakarta Sans'
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 10,
    paddingHorizontal: 10,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#F4F4F4',
  },
  searchBar: {
    flex: 1,
    paddingHorizontal: 10,
    color: Colors.secondary,
  },
  searchIcon: {
    marginRight: 10,
  },
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 25,
    paddingVertical: 44,
    borderTopWidth: 1,
    borderTopColor: '#ccc',
    backgroundColor: '#fff',
  },
  selectedCount: {
    fontSize: 16,
    fontFamily: 'Plus Jakarta Sans',
    color: '#000000',
    fontWeight: 'bold'
  },
  nextButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginLeft: 10,
    backgroundColor: Colors.background.orange
  },
  nextButtonText: {
    fontSize: 14,
    fontFamily: 'Plus Jakarta Sans',
    color: Colors.text.tertiary,
    fontWeight: '500',
    textAlign: 'center',
  },
  rectangle: {
    width: 16,
    height: 16,
    borderWidth: 1,
    borderColor: '#BBBBBB',
    borderRadius: 4,
  },
  permissionDeniedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    backgroundColor: '#FFFFFF',
  },
  permissionDeniedText: {
    fontSize: 16,
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: 'bold',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 20,
  },
  settingsButton: {
    backgroundColor: '#FFA500',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  settingsButtonText: {
    fontSize: 14,
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: 'bold',
    color: '#000000',
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: 'bold',
    color: '#666',
    textAlign: 'center',
  },
  floatingActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    borderRadius: 8,
    backgroundColor: Colors.background.secondary,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    height: 56,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.border.orange,
  },
  fabText: {
    marginLeft: 10,
    color: 'orange',
    fontFamily: 'Plus Jakarta Sans',
    fontSize: 16,
    fontWeight: 'bold',
  },
  sectionHeader: {
    fontSize: 16,
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: 'bold',
    color: '#333',
    marginVertical: 10,
    marginLeft: 10,
  },
  savedGuestsList: {
    marginBottom: 10,
  },
  rsvpButton: {
    alignSelf: 'flex-end',
    marginRight: 10,
    marginTop: 10,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#FFA500',
    borderRadius: 8,
  },
  rsvpButtonText: {
    fontSize: 14,
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
});

export default SendNewInvites;
