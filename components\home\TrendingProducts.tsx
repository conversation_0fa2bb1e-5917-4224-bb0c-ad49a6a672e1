import React from 'react';
import { View, Text, StyleSheet, ScrollView, Pressable, Image } from 'react-native';
import { Colors } from '@/constants/Colors';
import { AntDesign } from '@expo/vector-icons';
import type { MdPromotion } from '@/app/(home)/UserDashboard/promotions';
import { useRouter } from 'expo-router';

interface TrendingProductsProps {
  promotions: MdPromotion[];
  onPromotionPress: (promotion: MdPromotion) => void;
  tagSlug: string;
}

export function TrendingProducts({ promotions, onPromotionPress, tagSlug }: TrendingProductsProps) {
  const router = useRouter();
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Trending Products</Text>
        <Pressable 
          style={styles.viewAllContainer} 
          onPress={() => router.push(`/(home)/ProductListView/trending-products?tagSlug=${tagSlug}` as any)}
        >
          <Text style={styles.viewAll}>View All</Text>
          <AntDesign name="right" size={14} color={Colors.light.tint} />
        </Pressable>
      </View>

      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {promotions.map((promotion) => (
          <Pressable
            key={promotion.id}
            style={styles.card}
            onPress={() => onPromotionPress(promotion)}
          >
            <Image
              source={{ uri: promotion.mediaUrl }}
              style={styles.image}
              resizeMode="cover"
            />
            <View style={styles.contentContainer}>
              <Text style={styles.productName} numberOfLines={3}>
                {promotion.name}
              </Text>
            </View>
          </Pressable>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 24,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  viewAllContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  viewAll: {
    fontSize: 14,
    color: Colors.light.tint,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    paddingTop: 8,
    gap: 20,
  },
  card: {
    width: 180,
    height: 240,
    backgroundColor: 'white',
    borderRadius: 5,
    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: 140,
    backgroundColor: Colors.light.secondaryBackground,
  },
  contentContainer: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    flex: 1,
    justifyContent: 'center',
  },
  productName: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
    lineHeight: 20,
  },
}); 