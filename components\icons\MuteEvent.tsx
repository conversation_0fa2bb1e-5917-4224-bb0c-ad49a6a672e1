import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const MuteEvent = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Mute_event-1_svg__a)">
      <Path
        fill="url(#Mute_event-1_svg__b)"
        fillRule="evenodd"
        d="M.675 1.518a.596.596 0 0 1 .842-.843l1.368 1.368a3.925 3.925 0 0 1 7.039 2.385v3.504c0 .*************.654.198.197.353.27.569.27a.397.397 0 1 1 0 .795h-.27l.832.832a.596.596 0 0 1-.842.843zM2.078 4.32l5.33 5.33h-6.17a.397.397 0 0 1 0-.794c.215 0 .37-.074.567-.271a.93.93 0 0 0 .271-.654V4.428q0-.053.002-.107m2.532 6.591c0-.329.267-.596.596-.596h1.588a.596.596 0 0 1 0 1.192H5.206a.596.596 0 0 1-.596-.596"
        clipRule="evenodd"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Mute_event-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.828}
        y2={14.038}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Mute_event-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Mute_event_svg__a)"
    >
      <Path d="M11 11 1 1M8.692 8.689h1.923c-.34 0-.582-.136-.822-.376a1.28 1.28 0 0 1-.376-.906v-2.99A3.418 3.418 0 0 0 2.94 2.895M5.23 11h1.54M2.583 4.845v2.562c0 .34-.135.666-.376.906-.24.24-.482.376-.822.376h5" />
    </G>
    <Defs>
      <ClipPath id="Mute_event_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default MuteEvent;
