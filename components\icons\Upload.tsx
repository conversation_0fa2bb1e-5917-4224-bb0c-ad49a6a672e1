import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Upload = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      clipPath="url(#Upload-1_svg__a)"
    >
      <Path
        stroke="url(#Upload-1_svg__b)"
        d="M11 6.773v3.461a.77.77 0 0 1-.77.77H1.77a.77.77 0 0 1-.77-.77V6.773"
      />
      <Path stroke="url(#Upload-1_svg__c)" d="M4.077 2.99 6 1.069 7.923 2.99" />
      <Path stroke="url(#Upload-1_svg__d)" d="M6 7.99V1.069" />
    </G>
    <Defs>
      <LinearGradient
        id="Upload-1_svg__b"
        x1={6}
        x2={6}
        y1={6.262}
        y2={11.976}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Upload-1_svg__c"
        x1={6}
        x2={6}
        y1={0.836}
        y2={3.433}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Upload-1_svg__d"
        x1={6.5}
        x2={6.5}
        y1={0.232}
        y2={9.582}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Upload-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Upload_svg__a)"
    >
      <Path d="M11 6.773v3.461a.77.77 0 0 1-.77.77H1.77a.77.77 0 0 1-.77-.77V6.773M4.077 2.99 6 1.069 7.923 2.99M6 7.99V1.069" />
    </G>
    <Defs>
      <ClipPath id="Upload_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Upload;
