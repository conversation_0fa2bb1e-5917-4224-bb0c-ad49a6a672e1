import { View, Pressable, Animated, ScrollView, Platform, useWindowDimensions, Modal } from 'react-native';
import { Text, useTheme, Button as PaperButton, ActivityIndicator } from 'react-native-paper';
import { StyleSheet } from 'react-native';
import { ReactNode, useEffect } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { FileUploadItem } from './Attachments/FileUploadItem';
import * as DocumentPicker from 'expo-document-picker';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';
import { Cross } from '@/components/icons';
import { Colors, Icons } from '@/constants/DesignSystem';

interface BottomDrawerProps {
  isVisible: boolean;
  onClose: () => void;
  slideAnim: Animated.Value;
  overlayAnim: Animated.Value;
  title?: string;
  height?: number; // Height as percentage (0-1)
  children: ReactNode;
  showHeader?: boolean;
  selectedFiles: DocumentPicker.DocumentPickerAsset[];
  onFileRemove: (index: number , documentId: string) => void;
  onFileRetry?: (index: number) => void;
  hasUploadingFiles?: boolean;
  onUploadComplete?: (fileIndex: number, status: 'success' | 'error', response?: any) => void;
  isLoading?: boolean;
}

export function BottomDrawer({
  isVisible,
  onClose,
  slideAnim,
  overlayAnim,
  title = '',
  height = 0.4, // Default to 40% of screen height
  children,
  showHeader = true,
  selectedFiles,
  onFileRemove,
  onFileRetry,
  hasUploadingFiles = false,
  onUploadComplete,
  isLoading = false,
}: BottomDrawerProps) {
  const theme = useTheme();
  const { height: windowHeight } = useWindowDimensions();
  const insets = useSafeAreaInsets();

  // Calculate responsive height
  const calculateHeight = () => {
    const maxHeight = windowHeight * 0.8; // Maximum 80% of screen height
    const minHeight = windowHeight * 0.3; // Minimum 30% of screen height
    const calculatedHeight = windowHeight * height;
    const heightWithInsets = calculatedHeight - insets.bottom;
    
    // Always return at least the minimum height
    return Math.max(
      Math.min(heightWithInsets, maxHeight),
      minHeight
    );
  };

  const drawerHeight = calculateHeight();

  useEffect(() => {
    if (isVisible) {
      slideAnim.setValue(0);
      overlayAnim.setValue(0);
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(overlayAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, 
  // eslint-disable-next-line react-hooks/exhaustive-deps
  [drawerHeight, isVisible]);

  return (
    <Modal
      transparent={true}
      visible={isVisible}
      onRequestClose={() => {
        if (!hasUploadingFiles) {
          onClose();
        }
      }}
      animationType="none"
      statusBarTranslucent
    >
      <Animated.View 
        style={[
          styles.modalOverlay,
          {
            opacity: overlayAnim,
          }
        ]} 
      >
        <Pressable 
          style={styles.modalOverlay} 
          onPress={() => {
            if (!hasUploadingFiles) {
              onClose();
            }
          }}
        >
          <Animated.View 
            style={[
              styles.bottomSheet,
              {
                maxHeight: drawerHeight,
                minHeight: windowHeight * 0.3,
                paddingBottom: insets.bottom,
                transform: [{
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [drawerHeight, 0],
                  }),
                }],
              }
            ]}
          >
            {showHeader && (
              <View style={styles.bottomSheetHeader}>
                <Text variant="titleMedium">
                  {title}
                </Text>
                {!hasUploadingFiles && (
                  <Pressable 
                    onPress={onClose}
                    hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                  >
                    <Cross 
                      size={Icons.size.md} 
                      color={Colors.black} 
                    />
                  </Pressable>
                )}
              </View>
            )}
            
            <ScrollView 
              style={styles.scrollView}
              contentContainerStyle={[
                styles.scrollViewContent,
                selectedFiles.length > 0 && styles.scrollViewContentWithFiles
              ]}
              showsVerticalScrollIndicator={false}
              bounces={true}
              alwaysBounceVertical={false}
              scrollEventThrottle={16}
              keyboardShouldPersistTaps="handled"
              scrollEnabled={true}
            >
              <Pressable style={{ flex: 1 }} pointerEvents="box-none">
                {isLoading ? (
                  <View style={styles.loadingContainer}>
                    <FastPartyActivityIndicator />
                    <Text style={styles.loadingText}>Processing files...</Text>
                  </View>
                ) : selectedFiles.length === 0 ? (
                  children
                ) : (
                  <View style={styles.uploadList}>
                    {selectedFiles.map((file, index) => (
                      <FileUploadItem
                        key={`${file.uri}-${index}`}
                        file={{
                          ...file,
                          type: file.mimeType || '*/*'
                        }}
                        onRemove={(documentId) => onFileRemove(index, documentId)}
                        onRetry={() => onFileRetry?.(index)}
                        onUploadComplete={(status, response) => onUploadComplete?.(index, status, response)}
                        uploadDelay={index * 500}
                      />
                    ))}
                  </View>
                )}
              </Pressable>
            </ScrollView>

            {hasUploadingFiles && (
              <View style={styles.footer}>
                <PaperButton 
                  mode="contained"
                  onPress={onClose}
                  style={styles.doneButton}
                  labelStyle={styles.doneButtonLabel}
                >
                  Done
                </PaperButton>
              </View>
            )}
          </Animated.View>
        </Pressable>
      </Animated.View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    
  },
  bottomSheet: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    ...Platform.select({
      ios: {
        paddingTop: 15,
      },
      android: {
        paddingTop: 20,
      },
    }),
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: 5,
  },
  scrollView: {
    flexGrow: 0,
    flexShrink: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  scrollViewContentWithFiles: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    paddingBottom: Platform.OS === 'ios' ? 100 : 80,
  },
  uploadList: {
    width: '100%',
    gap: 12,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  doneButton: {
    borderRadius: 8,
  },
  doneButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
  },
}); 