import React from 'react';
import { StyleSheet, Image, Pressable } from 'react-native';
import { useRouter } from 'expo-router';

interface SpecialOffersProps {
  onPress?: () => void;
  tagSlug: string;
}

export function SpecialOffers({ onPress, tagSlug }: SpecialOffersProps) {
  const router = useRouter();
  
  return (
    <Pressable 
      style={styles.container} 
      onPress={() => router.push(`/(home)/ProductListView/special-offers?tagSlug=${tagSlug}` as any)}
    >
      <Image 
        source={require('@/assets/images/specialOffers.png')}
        style={styles.image}
        resizeMode="contain"
      />
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 24,
    marginHorizontal: 16,
    paddingBottom: 10,
  },
  image: {
    width: '100%',
    height: 120, // Adjust this height based on your image's aspect ratio
    borderRadius: 12,
  },
}); 