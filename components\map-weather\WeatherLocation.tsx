import { NewPartyDetailsResult, Weather } from '@/app/(home)/PartyDetails/NewPartyDetailsResponse.model';
import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Linking } from 'react-native';

interface WeatherLocationProps {
  party: NewPartyDetailsResult
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 8,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%', // Ensure the container takes the full width
  },
  column: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    paddingVertical: 15,
    flex: 1, // Allow the column to take available space
  },
  imageContainer: {
    marginHorizontal: 4,
  },
  image: {
    width: 50,
    height: 50,
    borderRadius: 8,
  },
  text: {
    fontFamily: 'Plus Jakarta Sans',
    fontSize: 16,
    fontWeight: 'bold',
  },
  locationText: {
    fontFamily: 'Plus Jakarta Sans',
    fontSize: 12,
    color: '#F4862B',
    fontWeight: 'bold',
    marginBottom: 8,
    maxWidth: '90%',
    flexShrink: 1, 
  },
});

const WeatherLocation: React.FC<WeatherLocationProps> = ({ party }) => {
  if (!party || !party.party.weather) {
    return (
      <View style={styles.container}>
        <Text style={styles.text}>Please select location to view weather</Text>
      </View>
    );
  }
  const weatherComponent: Weather = party.party.weather;
  const weatherIconUrl = `https:${weatherComponent.condition.icon}`;

  const handlePress = () => {
    if (weatherComponent.weatherUrl) {
      Linking.openURL(`${weatherComponent.weatherUrl}`);
    }
  };

  return (
    <TouchableOpacity style={styles.container} onPress={handlePress} activeOpacity={1}>
      <View style={styles.column}>
        <Text style={styles.text}>{`${weatherComponent.avgTempC}\u00B0C`}</Text>
        <Text
          style={styles.locationText}
          numberOfLines={2}
          ellipsizeMode="tail"
        >
          Hyderabad, Telangana
        </Text>
      </View>
      <View style={styles.imageContainer}>
        {weatherComponent.condition?.icon && (
          <Image source={{ uri: weatherIconUrl }} style={styles.image} />
        )}
      </View>
    </TouchableOpacity>
  );
};

export default WeatherLocation;