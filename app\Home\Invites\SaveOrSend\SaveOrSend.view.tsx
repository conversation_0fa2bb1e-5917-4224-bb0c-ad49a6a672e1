import React, { useState } from 'react';
import {
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Card } from 'react-native-paper';
import InitialsAvatar from '@/components/Avatar/InitialsAvatar';
import { StackScreenProps } from '@react-navigation/stack';
import { PartyDetailsRootList } from '@/components/create-event/Navigation/PartyDetailsRootList';
import { formatDateString } from '@/app/(home)/utils/reusableFunctions';
import { useMutation } from '@apollo/client';
import { CREATE_GUESTS, SEND_INVITE } from './SaveOrSend.data';
import { useToast } from '@/components/Toast';
import { Alert } from 'react-native';
import { DueDate, Hosts, Location, Cancel } from '@/components/icons';
import { Colors, Icons } from '@/constants/DesignSystem';

type SaveOrSendInviteProps = StackScreenProps<PartyDetailsRootList, 'SaveOrSendInvite'>;

const SaveOrSendInvite: React.FC<SaveOrSendInviteProps> = ({ route, navigation }) => {
  const { partyDetails, selectedContacts } = route.params;
  const [contacts, setContacts] = useState(selectedContacts || []);
  const imageUrl = partyDetails?.invitation.media[0]?.url || '';
  const dateString = String(partyDetails?.time);
  const [partyMessage, setPartyMessage] = useState<string>(partyDetails?.invitation.message || '');
  const toast = useToast();

  const [createGuests] = useMutation(CREATE_GUESTS);
  const [sendInvites] = useMutation(SEND_INVITE);

  const handleRemoveContact = (contactId: string) => {
    setContacts(contacts.filter((contact) => contact.id !== contactId));
  };

  const handleBack = () => {
    Alert.alert(
      "Are you sure?",
      "If you leave, your saved invitation will be discarded.",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Discard",
          style: "destructive",
          onPress: () => navigation.goBack(),
        },
      ]
    );
  };

  const handleSaveOrSend = async (isSave: boolean) => {
    try {
      const guests = contacts.map((contact) => {
        // Remove all special characters except '+' and spaces from the phone number
        const sanitizedPhone = contact.phone
          ? contact.phone.replace(/[^+\d]/g, '')
          : null;

        // Ensure the phone number starts with '+91'
        const formattedPhone = sanitizedPhone
          ? sanitizedPhone.startsWith('+91')
            ? sanitizedPhone
            : sanitizedPhone.startsWith('+')
              ? `+91${sanitizedPhone.slice(1)}` // Add +91 if it starts with another country code
              : `+91${sanitizedPhone}` // Add +91 if no country code is present
          : null;

        return {
          firstName: contact.firstName,
          lastName: contact.lastName,
          phone: formattedPhone,
          email: contact.email ?? '',
        };
      });

      const { data: createGuestData } = await createGuests({
        variables: {
          contacts: guests,
          partyId: partyDetails?.id,
        },
      });

      const guestIds: [string] = createGuestData.createGuestsFromContacts.result.guests.map(
        (guest: { id: string }) => guest.id
      );

      if (guestIds && createGuestData) {
        console.log('Guest IDs:', guestIds);

        const { data: sendInviteData } = await sendInvites({
          variables: {
            sendInvitationToInput: {
              addGuests: guestIds,
              invitationId: partyDetails?.invitation._id,
              save: isSave,
              message: partyMessage,
            },
          },
        });

        alert(isSave ? 'Invitation saved successfully!' : 'Invitation sent successfully!');
        navigation.popToTop();
      }
    } catch (error: any) {
      if (error.graphQLErrors && error.graphQLErrors.length > 0) {
        console.error('GraphQL Errors:', error.graphQLErrors);
      }
      if (error.networkError) {
        console.error('Network Error:', error.networkError);
      }
      console.error('Error Message:', error.message);
      alert('An error occurred while sending the invite');
    }
  };

  const renderContactItem = ({ item }: { item: any }) => {
    const phoneNumber = item.phone ?? '';

    return (
      <View style={[styles.contactRow, styles.bottomPadding]}>
        <InitialsAvatar firstName={item.firstName?.[0] ?? ''} lastName={item.lastName?.[0] ?? ''} />
        <View style={styles.contactInfo}>
          <Text style={styles.contactName}>{item.firstName} {item.lastName}</Text>
          <Text style={styles.contactNumber}>{phoneNumber}</Text>
        </View>
        <TouchableOpacity onPress={() => handleRemoveContact(item.id ?? '')}>
          <Cancel size={Icons.size.md} color={Colors.error} />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <FlatList
        data={contacts}
        renderItem={renderContactItem}
        keyExtractor={(item) => item.id ?? ''}
        style={styles.contactsList}
        ListHeaderComponent={
          <>
            <View style={styles.inviteDetails}>
              <View style={styles.row}>
                <Card.Cover source={{ uri: imageUrl }} style={styles.image} />
                <View style={styles.textContainer}>
                  <Text style={[styles.name, styles.bottomPadding]}>
                    {partyDetails?.name}
                  </Text>
                  <View style={[styles.row, styles.bottomPadding2Px]}>
                    <DueDate size={Icons.size.md} color={Colors.primary} />
                    <Text style={styles.date}>{formatDateString(dateString, 'd MMM, h:mm a')}</Text>
                  </View>
                  <View style={[styles.row, styles.bottomPadding2Px]}>
                    <Location size={Icons.size.md} color={Colors.primary} />
                    <Text style={styles.date}>{partyDetails?.venueAddress.name}</Text>
                  </View>
                  <View style={[styles.row, styles.bottomPadding]}>
                    <Hosts size={Icons.size.md} color={Colors.primary} />
                    <Text style={styles.date}>Hosted by {partyDetails?.event.mainHost.userId.firstName}</Text>
                  </View>
                </View>
              </View>
            </View>

            <TextInput
              placeholder="Write your message here"
              style={styles.messageContainer}
              multiline
              onChangeText={setPartyMessage}
              value={partyMessage}
              textAlignVertical="top"
            />
            <View style={styles.guestsContainer}>
              <Text style={styles.guestsTitle}>
                Guests ({contacts.length})
              </Text>
            </View>
          </>
        }
        nestedScrollEnabled={true}
      />
      <View style={styles.bottomBar}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Text style={styles.backButtonText}>Back</Text>
        </TouchableOpacity>
        <View style={styles.rightButtonsContainer}>
          <TouchableOpacity
            style={[styles.saveButton, contacts.length === 0 && styles.disabledButton]}
            onPress={() => handleSaveOrSend(true)}
            disabled={contacts.length === 0} // Disable if no contacts
          >
            <Text style={[styles.saveButtonText, contacts.length === 0 && styles.disabledButtonText]}>
              SAVE
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.sendButton, contacts.length === 0 && styles.disabledButton]}
            onPress={() => handleSaveOrSend(false)}
            disabled={contacts.length === 0} // Disable if no contacts
          >
            <Text style={[styles.sendButtonText, contacts.length === 0 && styles.disabledButtonText]}>
              SEND INVITE
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContent: {
    paddingBottom: 16,
  },
  inviteDetails: {
    marginBottom: 20,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  image: {
    resizeMode: 'cover',
    borderRadius: 10,
    flex: 0.4,
    backgroundColor: 'white',
  },
  textContainer: {
    flex: 0.6,
    marginLeft: 10,
  },
  name: {
    fontFamily: 'Plus Jakarta Sans',
    fontSize: 22,
    fontWeight: '500',
  },
  date: {
    fontFamily: 'Plus Jakarta Sans',
    fontSize: 14,
    color: '#000000',
    marginLeft: 5,
  },
  messageContainer: {
    paddingHorizontal: 10,
    paddingVertical: 10,
    backgroundColor: Colors.background.tertiary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.white,
    fontFamily: 'Plus Jakarta Sans',
    fontSize: 16,
    color: '#000000',
    fontWeight: '500',
    marginBottom: 20,
  },
  guestsContainer: {
    marginTop: 20,
  },
  guestsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    fontFamily: 'Plus Jakarta Sans',
  },
  contactsList: {
    padding: 10,
  },
  contactRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  contactInfo: {
    flex: 1,
    marginLeft: 10,
  },
  contactName: {
    fontSize: 16,
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: '500',
  },
  contactNumber: {
    fontSize: 14,
    fontFamily: 'Plus Jakarta Sans',
    color: '#666',
  },
  bottomPadding: {
    marginBottom: 10,
  },
  bottomPadding2Px: {
    marginBottom: 5,
  },
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 45,
    borderTopWidth: 1,
    borderColor: '#ccc',
    backgroundColor: '#fff',
  },
  backButton: {
    borderWidth: 1,
    borderColor: Colors.mediumGray,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  backButtonText: {
    color: 'black',
    fontSize: 16,
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: '500',
  },
  rightButtonsContainer: {
    flexDirection: 'row',
  },
  saveButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginHorizontal: 5,
  },
  sendButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  saveButtonText: {
    color: 'black',
    fontSize: 16,
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: '500',
  },
  sendButtonText: {
    color: 'black',
    fontSize: 16,
    fontFamily: 'Plus Jakarta Sans',
    fontWeight: '500',
  },
  disabledButton: {
    backgroundColor: '#E0E0E0',
    borderColor: '#B0B0B0',
  },
  disabledButtonText: {
    color: '#A0A0A0',
  },
});

export default SaveOrSendInvite;