import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import { MuteEvent, Notification } from '../../icons';
import { Icons, Spacing, Typography } from '@/constants/DesignSystem';

interface MuteEventButtonProps {
  isMuted: boolean;
  onPress: () => void;
  style?: any;
}

export const MuteEventButton: React.FC<MuteEventButtonProps> = ({ 
  isMuted, 
  onPress,
  style 
}) => {
  return (
    <TouchableOpacity 
      style={[styles.option, style]} 
      accessibilityRole="button" 
      accessibilityLabel={isMuted ? "Unmute Event" : "Mute Event"}
      onPress={onPress}
    >
      {isMuted ? (
        <Notification size={Icons.size.lg}  />
      ) : (
        <MuteEvent size={Icons.size.lg}  />
      )}
      <Text style={styles.optionText}>
        {isMuted ? 'Unmute Event' : 'Mute Event'}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  optionText: {
    fontSize: Typography.fontSize.md,
    marginLeft: Spacing.md,
    fontFamily: Typography.fontFamily.primary,
  },
}); 