import { gql } from '@apollo/client';

export const GET_TRAFFIC_INFO = gql`
query Traffic($input: TrafficAnalysisInput!) {
  traffic(input: $input) {
    ... on AntsyResponse {
      status
      message
      result {
        answer
        question
      }
    }
    ... on AntsyErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}
`;

export const GET_WEATHER_INFO = gql`
query Weather($input: PartyInfoInput!) {
  weather(input: $input) {
    ... on AntsyResponse {
      message
      result {
        answer
        question
      }
      status
    }
    ... on AntsyErrorResponse {
      errors {
        message
        field
      }
      message
      status
    }
  }
}
`;

export const GET_VENUE_INFO = gql`
query VenueDetails($input: PartyInfoInput!, $short: Boolean) {
  venueDetails(input: $input, short: $short) {
    ... on AntsyResponse {
      status
      message
      result {
        answer
      }
    }
    ... on AntsyErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}
`;

export const GET_PARTY_INFO = gql`
query PartyDetails($input: PartyInfoInput!) {
  partyDetails(input: $input) {
    ... on AntsyResponse {
      message
      result {
        answer
        question
      }
      status
    }
    ... on AntsyErrorResponse {
      errors {
        field
        message
      }
      message
      status
    }
  }
}
`;

export const GET_GIFT_SUGGESTIONS = gql`
query GiftSuggestions($input: PartyInfoInput!) {
  giftSuggestions(input: $input) {
    ... on AntsyResponse {
      status
      message
      result {
        answer
      }
    }
    ... on AntsyErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}
`;

export const GET_OUTFIT_SUGGESTIONS = gql`
query OutfitSuggestions($input: PartyInfoInput!) {
  outfitSuggestions(input: $input) {
    ... on AntsyResponse {
      status
      message
      result {
        answer
      }
    }
    ... on AntsyErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}
`;