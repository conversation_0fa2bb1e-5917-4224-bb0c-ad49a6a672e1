import * as React from 'react'
import { View, StyleSheet } from 'react-native'
import { useSignUp, useAuth } from '@clerk/clerk-expo'
import { useRouter } from 'expo-router'
import { TextInput, Text, Button as PaperButton } from 'react-native-paper'
import { InfoLabel } from '@/components/UI/ReusableComponents/InfoLabel'
import { SafeAreaView } from 'react-native-safe-area-context'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import * as z from 'zod'
import WelcomeBanner from '@/components/WelcomeBanner'
import { PhoneNumberInput } from '@/components/UI/ReusableComponents/PhoneNumberInput'
import { regexValidators, textConstraints } from '@/app/(home)/utils/reusableFunctions'
import { CREATE_USER } from '@/app/auth/users.data'
import { useMutation } from '@apollo/client'
import { useUserStore } from './auth/userStore'
import { userStorage } from './auth/userStorage'
import { SignUpNames } from '@/constants/displayNames'
import { Colors, Typography, Spacing, Borders } from '@/constants/DesignSystem'


const signUpSchema = z.object({
  firstName: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name cannot exceed 50 characters'),
  lastName: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name cannot exceed 50 characters'),
  emailAddress: z.string()
    .refine(val => val === '' || regexValidators.isValidEmail(val), 'Please Enter Valid Email Address')
    .optional()
    .or(z.literal('')),
  phoneNumber: z.string()
    .regex(/^\+[1-9]\d{0,4}[0-9]{10}$/, 'Phone number must be 10 digits'),
})

export default function SignUpScreen() {
  const { isLoaded, signUp, setActive } = useSignUp()
  const { getToken } = useAuth()
  const router = useRouter()
  const [emailAddress, setEmailAddress] = React.useState('')
  const [firstName, setFirstName] = React.useState('')
  const [lastName, setLastName] = React.useState('')
  const [pendingVerification, setPendingVerification] = React.useState(false)
  const [code, setCode] = React.useState('')
  const [phoneNumber, setPhoneNumber] = React.useState('')
  const [errors, setErrors] = React.useState<Record<string, string>>({})
  // State for error messages
  const [snackbarMessage, setSnackbarMessage] = React.useState('')
  const [rawPhoneNumber, setRawPhoneNumber] = React.useState('')
  const [formattedPhoneNumber, setFormattedPhoneNumber] = React.useState('')
  const [createUser] = useMutation(CREATE_USER)
  const [isResending, setIsResending] = React.useState(false)
  const [countdown, setCountdown] = React.useState(30)
  const [canResend, setCanResend] = React.useState(false)

  React.useEffect(() => {
    let timer: NodeJS.Timeout

    if (pendingVerification && countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            setCanResend(true)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }

    return () => {
      if (timer) clearInterval(timer)
    }
  }, [pendingVerification, countdown])

  const validateForm = () => {
    try {
      const formData = {
        firstName,
        lastName,
        phoneNumber,
        ...(emailAddress ? { emailAddress } : {})
      }

      signUpSchema.parse(formData)
      setErrors({})
      return true
    } catch (err) {
      // Handle sign-up validation error
      if (err instanceof z.ZodError) {
        const newErrors: Record<string, string> = {}
        err.errors.forEach((error) => {
          if (error.path) {
            newErrors[error.path[0]] = error.message
          }
        })
        setErrors(newErrors)
      }
      return false
    }
  }

  const handleInputFocus = (field: string) => {
    setErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[field]
      return newErrors
    })
  }

  const handleNameChange = (value: string, setName: (value: string) => void, maxLength: number) => {
    const sanitizedValue = textConstraints.sanitizeTextInput(value, maxLength)
    setName(sanitizedValue)
  }

  const handlePhoneNumberChange = (raw: string, formatted: string) => {
    setRawPhoneNumber(raw)
    setFormattedPhoneNumber(formatted)
    setPhoneNumber(formatted)
  }

  const onSignUpPress = async () => {
    if (!isLoaded) return

    if (!validateForm()) return

    try {
      const signUpData = {
        firstName,
        lastName,
        phoneNumber: formattedPhoneNumber,
        ...(emailAddress ? { emailAddress } : {})
      }
      await signUp.create(signUpData)
      await signUp.preparePhoneNumberVerification({ strategy: 'phone_code' })
      setPendingVerification(true)
      setCountdown(30)
      setCanResend(false)
      
    } catch (err: any) {
      let errorMessage = err.errors?.[0]?.message || 'An error occurred during sign up'
      setSnackbarMessage(errorMessage)
    }
  }

  const handleResendCode = async () => {
    if (!isLoaded || isResending) return

    try {
      setIsResending(true)
      await signUp.preparePhoneNumberVerification({ strategy: 'phone_code' })
      setSnackbarMessage('Verification code resent successfully')
      setCountdown(30)
      setCanResend(false)
    } catch (err: any) {
      // Handle error resending verification code
      setSnackbarMessage(err.message || 'Failed to resend verification code')
    } finally {
      setIsResending(false)
    }
  }

  const onPressVerify = async () => {
    if (!isLoaded) return

    try {
      const completeSignUp = await signUp.attemptPhoneNumberVerification({
        code,
      })
      if (completeSignUp.status === 'complete') {
        try {
          const createUserResponse = await createUser({
            variables: {
              input: {
                firstName: completeSignUp.firstName!,
                lastName: completeSignUp.lastName!,
                phone: completeSignUp.phoneNumber!,
                email: completeSignUp.emailAddress || null,
                externalId: completeSignUp.createdUserId!,
                isRegistered: true,
                role: ["user"]
              }
            }
          })

          const userData = createUserResponse?.data?.register?.result?.user
          if (userData) {
            await userStorage.saveUser(userData)
            useUserStore.getState().setUserData(userData)

            if (completeSignUp.createdSessionId) {
              await setActive({ session: completeSignUp.createdSessionId })
              const token = await getToken()
              if (token) {
                await userStorage.saveToken(token)
              } else {
                // Failed to get token after session activation
              }
              router.replace('/onBoarding')
            }
          }
        } catch (err) {
          // Error adding user to app database
          setSnackbarMessage('Error creating user profile')
        }
      }
    } catch (err: any) {
      // Error during verification
      setSnackbarMessage(err.message || 'Verification failed')
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView keyboardShouldPersistTaps="handled">
        <WelcomeBanner />
        <View style={styles.formContainer}>
          <InfoLabel
            message={snackbarMessage}
            visible={!!snackbarMessage}
            type="error"
            style={styles.infoLabel}
          />
          {!pendingVerification && (
            <>
              <TextInput
                mode="outlined"
                label="First Name*"
                value={firstName}
                onChangeText={(value) => handleNameChange(value, setFirstName, 50)}
                error={!!errors.firstName}
                onFocus={() => handleInputFocus('firstName')}
                outlineColor={Colors.border.medium}
                activeOutlineColor={Colors.primary}
                outlineStyle={{ borderRadius: Borders.radius.md }}
                contentStyle={{ paddingHorizontal: Spacing.sm }}
                theme={{ colors: { background: Colors.background.primary } }}
                style={styles.input}
                maxLength={50}
              />
              {errors.firstName && <Text style={styles.errorText}>{errors.firstName}</Text>}

              <TextInput
                mode="outlined"
                label="Last Name*"
                value={lastName}
                onChangeText={(value) => handleNameChange(value, setLastName, 100)}
                error={!!errors.lastName}
                onFocus={() => handleInputFocus('lastName')}
                outlineColor={Colors.border.medium}
                activeOutlineColor={Colors.primary}
                outlineStyle={{ borderRadius: Borders.radius.md }}
                contentStyle={{ paddingHorizontal: Spacing.sm }}
                theme={{ colors: { background: Colors.background.primary } }}
                style={styles.input}
                maxLength={100}
              />
              {errors.lastName && <Text style={styles.errorText}>{errors.lastName}</Text>}

              <TextInput
                mode="outlined"
                label="Email"
                autoCapitalize="none"
                value={emailAddress}
                onChangeText={setEmailAddress}
                error={!!errors.emailAddress}
                onFocus={() => handleInputFocus('emailAddress')}
                keyboardType="email-address"
                outlineColor={Colors.border.medium}
                activeOutlineColor={Colors.primary}
                outlineStyle={{ borderRadius: Borders.radius.md }}
                contentStyle={{ paddingHorizontal: Spacing.sm }}
                theme={{ colors: { background: Colors.background.primary } }}
                style={styles.input}
                maxLength={320}
              />
              {errors.emailAddress && <Text style={styles.errorText}>{errors.emailAddress}</Text>}

              <PhoneNumberInput
                value={rawPhoneNumber}
                onChangeText={handlePhoneNumberChange}
                error={!!errors.phoneNumber}
                onFocus={() => handleInputFocus('phoneNumber')}
                label="Phone Number*"
                placeholder="Enter phone number"
                mode="outlined"
                style={styles.input}
              />
              {errors.phoneNumber && <Text style={styles.errorText}>{errors.phoneNumber}</Text>}

              <View style={styles.buttonContainer}>
                <PaperButton
                  mode='contained'
                  onPress={onSignUpPress}
                  style={styles.primaryButton}
                  buttonColor={Colors.button.primary}
                  textColor={Colors.white}
                >
                  {SignUpNames.SIGN_UP}
                </PaperButton>
              </View>

              <View style={styles.buttonContainer}>
                <PaperButton
                  mode='outlined'
                  onPress={() => router.back()}
                  style={styles.secondaryButton}
                  textColor={Colors.primary}
                >
                  {SignUpNames.BACK}
                </PaperButton>
              </View>
            </>
          )}
          {pendingVerification && (
            <>
              <Text style={styles.verificationText}>Enter verification code sent to your phone</Text>
              <TextInput
                mode="outlined"
                label="Verification Code"
                value={code}
                onChangeText={setCode}
                keyboardType="number-pad"
                autoFocus={true}
                outlineColor={Colors.border.medium}
                activeOutlineColor={Colors.primary}
                outlineStyle={{ borderRadius: Borders.radius.md }}
                contentStyle={{ paddingHorizontal: Spacing.sm }}
                theme={{ colors: { background: Colors.background.primary } }}
                style={styles.input}
              />
              <View style={styles.buttonGroup}>
                <View style={styles.buttonContainer}>
                  <PaperButton
                    mode='contained'
                    onPress={onPressVerify}
                    style={styles.primaryButton}
                    buttonColor={Colors.button.primary}
                    textColor={Colors.white}
                  >
                    {SignUpNames.VERIFY_PHONE}
                  </PaperButton>
                </View>

                <View style={styles.buttonContainer}>
                  {!canResend ? (
                    <Text style={styles.countdownText}>
                      Resend code in {countdown}s
                    </Text>
                  ) : (
                    <PaperButton
                      mode='outlined'
                      onPress={handleResendCode}
                      loading={isResending}
                      disabled={isResending}
                      style={styles.secondaryButton}
                      textColor={Colors.primary}
                    >
                      Resend Code
                    </PaperButton>
                  )}
                </View>

                <View style={styles.buttonContainer}>
                  <PaperButton
                    mode='contained'
                    onPress={() => {
                      setPendingVerification(false)
                      setCountdown(30)
                      setCanResend(false)
                    }}
                    style={styles.primaryButton}
                    buttonColor={Colors.button.primary}
                    textColor={Colors.white}
                  >
                    {SignUpNames.BACK_TO_SIGN_UP}
                  </PaperButton>
                </View>
              </View>
            </>
          )}
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  infoLabel: {
    marginBottom: Spacing.md,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
    justifyContent: 'center',
    padding: Spacing.xxxl,
    alignItems: 'center',
  },
  formContainer: {
    justifyContent: 'center',
    gap: Spacing.md,
    marginTop: Spacing.xl,
    width: '100%',
  },
  input: {
    marginBottom: Spacing.xs,
  },
  errorText: {
    color: Colors.error,
    fontSize: Typography.fontSize.sm,
    marginLeft: Spacing.sm,
    marginBottom: Spacing.sm,
  },
  buttonGroup: {
    gap: Spacing.md,
    marginTop: Spacing.md,
  },
  buttonContainer: {
    justifyContent: 'center',
    width: '70%',
    alignSelf: 'center',
    marginTop: Spacing.sm,
  },
  primaryButton: {
    borderRadius: Borders.radius.pill,
    paddingHorizontal: Spacing.lg,
  },
  secondaryButton: {
    borderRadius: Borders.radius.pill,
    borderColor: Colors.primary,
    paddingHorizontal: Spacing.lg,
  },
  countdownText: {
    textAlign: 'center',
    color: Colors.text.secondary,
    marginTop: Spacing.sm,
  },
  verificationText: {
    fontSize: Typography.fontSize.md,
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
});