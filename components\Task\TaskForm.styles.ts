import { StyleSheet, Dimensions, Platform } from 'react-native';
import { sharedStyles } from './Form/Fields/Shared.styles';

const screenWidth = Dimensions.get('window').width;

export const styles = StyleSheet.create({
  upperFoamSection: {
    paddingHorizontal: screenWidth * 0.06107,
    marginTop: 20,
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  surface: {
    flex: 6,
    backgroundColor: '#fff',
  },
  contentWrapper: {
    flexGrow: 1,
    backgroundColor: '#fff',
  },
  scrollView: {
    width: '100%',
    backgroundColor: '#fff',
  },
  errorContainer: {
    padding: 16,
    backgroundColor: '#FEE2E2',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
    zIndex: 1,
  },
  taskTitleErrorText: {
    color: '#EF4444',
    fontSize: 12,
    textAlign: 'left',
    fontWeight: '500',
    bottom: 0,
  },
  partyFieldErrorText: {
    color: '#EF4444',
    fontSize: 12,
    textAlign: 'left',
    fontWeight: '500',
    zIndex: 1000,
    right: Platform.OS === 'ios' ? 57 : screenWidth * 0.18,
    marginTop: 33,
  },
  inputStyle: {
    backgroundColor: '#fff',
    marginVertical: 4,
    fontSize: 16,
    height: 72,
  },
  rowContainer: {
    ...sharedStyles.rowContainer,
  },

  saparator: {
    ...sharedStyles.saparator,
  },

  bottomButton: {
    backgroundColor: '#fff',
    borderTopColor: '#E5E7EB',
    width: '100%',
    bottom: 70,
  },

  saveButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 100,
    backgroundColor: '#E2E7FB',
    width: '100%',
    alignItems: 'center',
    height: 60,
    justifyContent: 'center',
  },

  saveButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2E52E2',
    textAlign: 'center',
  },

  bottomFoamSection: {
    paddingHorizontal: screenWidth * 0.05,
    marginTop: 1,
  },

  footer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    bottom: 0,
    height: '25%',
    backgroundColor: '#ffffff',
  },
});