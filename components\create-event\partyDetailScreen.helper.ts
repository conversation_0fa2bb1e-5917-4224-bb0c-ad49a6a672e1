import { LocationItem, VendorType } from "@/app/(home)/EventsDashboard/AddParty.models";
import { GraphQLEventTypesResponse, GraphQLLocationsResponse } from "@/components/user-events/user-events.model";

interface PartyType {
    id: string;
    name: string;
    vendorTypes: VendorType[];
}

export function transformPartyTypesResponse(response: GraphQLEventTypesResponse | undefined): PartyType[] {
    if (!response) return [];
    
    try {
      const partyTypes = response.getMdEventTypeById?.result?.mdEventType?.partyTypes || [];
      const validPartyTypes = partyTypes.filter(pt => pt && pt.id && pt.name);
      return validPartyTypes;
    } catch (error) {
      console.log('Error transforming party types response:', error);
      return [];
    }
}

export function safeTransformPartyTypesResponse(response: unknown): PartyType[] {
    try {
        if (!response || typeof response !== 'object') return [];
        const typedResponse = response as GraphQLEventTypesResponse;
        return typedResponse.getMdEventTypeById.result.mdEventType.partyTypes || [];
    } catch (error) {
        console.log('Error transforming party types response:', error);
        return [];
    }
} 

export function transformLocationsResponse(response: GraphQLLocationsResponse | undefined): LocationItem[] {
    if (!response) return [];
    
    try {
      return response.getMdServiceLocations?.result?.mdServiceLocations?.map(location => ({
        id: location.id,
        title: location.city,
      })) || [];
    } catch (error) {
      console.log('Error transforming locations response:', error);
      return [];
    }
}