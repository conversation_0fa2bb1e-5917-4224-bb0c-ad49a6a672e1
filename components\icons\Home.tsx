import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Home = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Home-1_svg__a)">
      <Path
        fill="url(#Home-1_svg__b)"
        fillRule="evenodd"
        d="M.75 5.25a.79.79 0 0 0-.25.574v4.497c0 .651.528 1.179 1.179 1.179h3.535V9.143a.786.786 0 1 1 1.572 0V11.5h3.535c.651 0 1.179-.528 1.179-1.179V5.824a.79.79 0 0 0-.25-.574L6.256.595a.39.39 0 0 0-.512 0z"
        clipRule="evenodd"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Home-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.828}
        y2={14.029}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Home-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Home_svg__a)"
    >
      <Path
        d="M11 5.954a.77.77 0 0 0-.246-.57L6 1 1.246 5.385A.77.77 0 0 0 1 5.954v4.277a.77.77 0 0 0 .77.769h8.46a.77.77 0 0 0 .77-.77zM6 11V7.923"
        strokeWidth={strokeWidth}
        fill="none"
      />
    </G>
    <Defs>
      <ClipPath id="Home_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Home;
