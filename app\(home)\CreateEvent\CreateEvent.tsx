import { useState, useRef, useMemo } from 'react';
import { View, ScrollView } from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { EventNameScreen } from '@/components/create-event/EventNameScreen';
import { EventTypeScreen } from '@/components/create-event/EventTypeScreen';
import { MultiDayScreen } from '@/components/create-event/MultiDayScreen';
import { CohostsScreen } from '@/components/create-event/CoHostScreen';
import { PartiesScreen } from '@/components/create-event/PartiesScreen';
import { ProgressBar } from '@/components/create-event/ProgressBar';
import { NavigationButtons } from '@/components/create-event/NavigationButtons';
import { CREATE_EVENT } from '@/components/user-events/user-events.data';
import { transformEventDataToMutationInput } from './CreateEvent.helper';
import { useMutation } from '@apollo/client';
import { CreateEventProvider, useCreateEvent } from './CreateEventContext';
import { useEventRefreshStore } from '@/store/eventRefreshStore';
import { useToast } from '@/components/Toast';
import PartyDetailScreen from '@/components/create-event/PartyDetailScreen';
import { Cohost } from '../EventsDashboard/AddParty.models';
import { CreateEventData } from './CreateEvent.models';
import { useNavigationStore } from '@/store/navigationStore';
import { NAV_BAR_VIEW_TYPE } from '@/constants/bottomNavTabs';
import { useEventStore } from '@/store/eventStore';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { BottomSheetModalProvider, BottomSheetBackdrop, BottomSheetScrollView, BottomSheetView } from '@gorhom/bottom-sheet';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AddCohostsForm from '@/components/event-dashboard/AddParty/AddCohostsForm';

export default function CreateEvent() {
  return (
    <CreateEventProvider>
      <CreateEventContent />
    </CreateEventProvider>
  );
}

function CreateEventContent() {
  const { eventData, setEventData } = useCreateEvent();
  const [createdEventId, setCreatedEventId] = useState<string | null>(null);
  const router = useRouter();
  const [step, setStep] = useState(1);
  const toast = useToast();
  const cohostSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ["90%", "90%"], []);

  const partyDetailRef = useRef<{
    handleSubmit: (eventId: string) => Promise<boolean>;
    getPartyDate: () => Date;
    getLocationId: () => string;
    getCohosts: () => Cohost[];
    isValid: () => boolean;
  }>(null);

  const isWeddingEvent = eventData.eventType?.name.toLowerCase().includes('wedding');

  const [createEvent] = useMutation(CREATE_EVENT);

  const setNeedsRefresh = useEventRefreshStore((state) => state.setNeedsRefresh);

  const { setViewType } = useNavigationStore();

  const setSelectedEventId = useEventStore((state) => state.setSelectedEventId);
  
  const isStepValid = () => {
    switch (step) {
      case 1:
        return eventData.name.trim().length > 0;
      case 2:
        return eventData.eventType !== null;
      case 3:
        return isWeddingEvent ? 
          eventData.parties.length > 0 : 
          eventData.isMultiDay !== null;
      case 4:
        if (isWeddingEvent) {
          return true;
        }
        if (!eventData.isMultiDay) {
          return true; // Always enable next button for party details
        }
        return true;
      case 5:
        return !eventData.isMultiDay ? eventData.locationId.length > 0 : true;
      case 6:
        return true; // Cohosts are optional
      default:
        return false;
    }
  };

  const handleSuccessfulEventCreation = (eventId: string) => {
    setViewType(NAV_BAR_VIEW_TYPE.HOST);
    setSelectedEventId(eventId);
    router.replace({
      pathname: '/(home)/(tabs)/eventsDB',
      params: { eventId }
    });
  };

  const handleNext = async () => {
    if (step < getMaxSteps()) {
      setStep(step + 1);
    } else {
      if (!eventData.isMultiDay && !isWeddingEvent) {
        try {
          if (!partyDetailRef.current) {
            toast.error('Party details not available');
            return;
          }
  
          let eventId = createdEventId;
  
          // Only create event if we don't have an ID from previous attempt
          if (!eventId) {
            const eventMutationInput = {
              ...transformEventDataToMutationInput(eventData),
              date: partyDetailRef.current.getPartyDate(),
              locationId: partyDetailRef.current.getLocationId(),
              cohosts: partyDetailRef.current.getCohosts(),
            };
  
            const eventResult = await createEvent({ 
              variables: eventMutationInput
            });
  
            if (eventResult.data?.createEvent?.errors?.length > 0) {
              const errorMessage = eventResult.data.createEvent.errors
                .map((error: { field: string; message: string }) => error.message)
                .join(', ');
              toast.error(errorMessage);
              return;
            }
  
            eventId = eventResult.data?.createEvent?.result?.event?.id;
            if (!eventId) {
              toast.error('Failed to create event');
              return;
            }
            setCreatedEventId(eventId);
          }
  
          // Try to create party with existing or new event ID
          const partyCreated = await partyDetailRef.current.handleSubmit(eventId);
  
          if (partyCreated) {
            setEventData({
              name: '',
              eventType: null,
              isMultiDay: null,
              date: null,
              locationId: '',
              locationName: '',
              area: '',
              cohosts: [],
              parties: []
            });
            setCreatedEventId(null);
            setStep(1);
            setNeedsRefresh(true);
            handleSuccessfulEventCreation(eventId);
            toast.success('Event and party created successfully');
          }
        } catch (error) {
          console.error('Error:', error);
          toast.error('Failed to create party. Please try again.');
        }
      } else {
        try {
          // Create event with collected data
          const eventMutationInput = transformEventDataToMutationInput(eventData);
          
          console.log('Event Mutation Input:', JSON.stringify(eventMutationInput, null, 2));

          const eventResult = await createEvent({ 
            variables: eventMutationInput
          });

          if (eventResult.data?.createEvent?.errors?.length > 0) {
            const errorMessage = eventResult.data.createEvent.errors
              .map((error: { field: string; message: string }) => error.message)
              .join(', ');
            toast.error(errorMessage);
            return;
          }

          const newEventId = eventResult.data?.createEvent?.result?.event?.id;
          
          // Reset form state
          setEventData({
            name: '',
            eventType: null,
            isMultiDay: null,
            date: null,
            locationId: '',
            locationName: '',
            area: '',
            cohosts: [],
            parties: []
          });
          
          setStep(1);
          setNeedsRefresh(true);
          handleSuccessfulEventCreation(newEventId);
          toast.success('Event created successfully');
        } catch (error) {
          console.error('Error:', error);
          toast.error('Failed to create event');
        }
      }
    }
  };

  const handleBack = () => {
    if (step > 1) {
      if (!isWeddingEvent && step === 6 && eventData.isMultiDay) {
        // If coming back from cohost in multi-day flow, go back to multiday question
        setStep(3);
      } else {
        setStep(step - 1);
      }
    } else {
      router.back();
    }
  };

  const getMaxSteps = () => {
    if (isWeddingEvent) {
      return 4; // Name -> Type -> Parties -> Cohost
    }
    return eventData.isMultiDay ? 4 : 4; // If multiday: Name -> Type -> MultiDay -> Cohost
                                      // If single day: Name -> Type -> MultiDay -> PartyDetail
  };

  const renderStep = () => {
    if (isWeddingEvent) {
      // Wedding flow
      switch(step) {
        case 1: return <EventNameScreen value={eventData.name} onChange={(name) => setEventData({...eventData, name})} />;
        case 2: return <EventTypeScreen value={eventData.eventType} onChange={(eventType) => setEventData({...eventData, eventType})} />;
        case 3: return <PartiesScreen selectedParties={eventData.parties} onPartiesChange={(parties) => setEventData({...eventData, parties})} cohostSheetRef={cohostSheetRef} />;
        case 4: return <CohostsScreen cohosts={eventData.cohosts} onCohostChange={(cohosts) => setEventData({...eventData, cohosts})} cohostSheetRef={cohostSheetRef} />;
      }
    } else {
      // Non-wedding flow
      switch(step) {
        case 1: return <EventNameScreen value={eventData.name} onChange={(name) => setEventData({...eventData, name})} />;
        case 2: return <EventTypeScreen value={eventData.eventType} onChange={(eventType) => setEventData({...eventData, eventType})} />;
        case 3: return <MultiDayScreen value={eventData.isMultiDay} onChange={(isMultiDay) => setEventData({...eventData, isMultiDay})} />;
        case 4:
          if (!eventData.isMultiDay) {
            return <PartyDetailScreen 
              ref={partyDetailRef}
              eventTypeId={eventData.eventType?.id}
              eventName={eventData.name}
              onPartyCreated={() => {
                setStep(1);
                setNeedsRefresh(true);
                router.replace ('/(home)/(tabs)/myEvents');
              }}
              onPartyDetailsChange={(details) => {
                setEventData((prev: CreateEventData) => {
                  const transformedCohosts = details.cohosts.map(cohost => ({
                    id: cohost.id,
                    firstName: cohost?.firstName || '',
                    lastName: cohost?.lastName || '',
                    phoneNumber: cohost?.phoneNumber || undefined,
                    email: cohost?.email || undefined
                  }));

                  return {
                    ...prev,
                    date: details.date,
                    locationId: details.locationId,
                    cohosts: transformedCohosts
                  };
                });
              }}
              cohostSheetRef={cohostSheetRef}
            />;
          }
          return <CohostsScreen cohosts={eventData.cohosts} onCohostChange={(cohosts) => setEventData({...eventData, cohosts})} cohostSheetRef={cohostSheetRef} />;
      }
    }
  };

  return (
    <>
      <Stack.Screen 
        options={{
          title: 'Create Event',
          headerShown: true,
          headerShadowVisible: false,
          headerBackButtonDisplayMode: 'minimal',
          presentation: 'card'
        }} 
      />
      <GestureHandlerRootView style={{ flex: 1 }}>
      <BottomSheetModalProvider>

        <View style={{ 
          flex: 1, 
          backgroundColor: 'white',
          position: 'relative'
        }}>
          <ScrollView 
            bounces={false}
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={{
              flexGrow: 1,
              paddingBottom: 100
            }}
          >
            {renderStep()}
          </ScrollView>

          <View style={{ 
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: 'white',
            borderTopWidth: 1,
            borderTopColor: '#f0f0f0',
            paddingHorizontal: 16,
            paddingVertical: 16,
            zIndex: 2000,
          }}>
            <ProgressBar currentStep={step} totalSteps={getMaxSteps()} />
            <NavigationButtons 
              onBack={handleBack}
              onNext={handleNext}
              isLastStep={step === getMaxSteps()}
              isNextDisabled={!isStepValid()}
            />
          </View>
          <BottomSheetModal
              ref={cohostSheetRef}
              index={1}
              snapPoints={snapPoints}
              enablePanDownToClose
              backdropComponent={(props) => (
                <BottomSheetBackdrop
                  {...props}
                  appearsOnIndex={1}
                  disappearsOnIndex={0}
                />
              )}
            >
              <BottomSheetScrollView keyboardShouldPersistTaps='handled'>
                <BottomSheetView
                  style={{
                    flex: 1,
                    marginBottom: "10%",
                    paddingBottom: "10%",
                    marginHorizontal: "4%",
                  }}
                >
                                    <AddCohostsForm
                    existingCohosts={eventData?.cohosts || []} 
                    onCohostAdd={(newCohost) => {
                      setEventData((prev: any) => ({
                        ...prev,
                        cohosts: [...prev.cohosts, newCohost]
                      }));
                      cohostSheetRef.current?.dismiss();
                    }}
                  />
                </BottomSheetView>
              </BottomSheetScrollView>
            </BottomSheetModal>
        </View>
        </BottomSheetModalProvider>
        </GestureHandlerRootView>
    </>
  );
}