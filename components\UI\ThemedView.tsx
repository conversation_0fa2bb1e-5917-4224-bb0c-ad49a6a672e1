import { View, type ViewProps } from 'react-native';
import { Colors } from '@/constants/Colors';

export type ThemedViewProps = ViewProps & {
  lightColor?: string; // Optional light color
  darkColor?: string;  // Optional dark color (ignored)
};

export function ThemedView({ style, lightColor = Colors.light.background, ...otherProps }: ThemedViewProps) {
  // Always use lightColor, ignoring darkColor
  const backgroundColor = lightColor; // Set backgroundColor to lightColor directly

  return <View style={[{ backgroundColor }, style]} {...otherProps} />;
}
