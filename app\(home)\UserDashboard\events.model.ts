// Core Event interface matching the response structure
export interface Event {
  id: string;
  name: string;
  startDate: string | null;
  endDate: string | null;
  eventType: {
    bannerImage: string;
  };
}

// Filter interface for the query
export interface EventFilter {
  presentAndUpcoming?: boolean;
}

// Response structure
export interface EventsResult {
  events: Event[];
}

// Main response interface matching GraphQL query
export interface GetEventsResponse {
  getEvents: {
    result: EventsResult;
  };
} 