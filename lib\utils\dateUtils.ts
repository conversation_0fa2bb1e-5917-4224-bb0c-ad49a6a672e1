import { differenceInMinutes } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { isToday, startOfDay, endOfDay, subDays, startOfMonth, endOfMonth } from 'date-fns';

const IST_TIMEZONE = 'Asia/Kolkata';

export function formatTimestamp(timestamp: string): string {
  try {
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) {
      console.warn('Invalid date provided to formatTimestamp:', timestamp);
      return 'Invalid date';
    }

    const now = new Date();
    const diffMinutes = differenceInMinutes(now, date);

    if (diffMinutes < 1) {
      return 'now';
    } else if (diffMinutes < 60) {
      return `${diffMinutes} min`; 
    } else if (isToday(date)) {
      return formatInTimeZone(date, IST_TIMEZONE, 'hh:mm a');
    } else {
      return formatInTimeZone(date, IST_TIMEZONE, 'dd/MM/yy');
    }
  } catch (error) {
    console.error('Error formatting timestamp:', error);
    return 'Invalid date';
  }
}

export function getDateRangeFilter(filterType: string): { start: string | null; end: string | null } {
  const now = new Date();
  let start: Date | null = null;
  let end: Date | null = null;

  switch (filterType) {
    case 'Today':
      start = startOfDay(now);
      end = endOfDay(now);
      break;
    case 'Last 7 days':
      start = startOfDay(subDays(now, 6));
      end = endOfDay(now);
      break;
    case 'Month':
      start = startOfDay(subDays(now, 30));
      end = endOfDay(now);
      break;
    default:
      return { start: null, end: null };
  }

  return {
    start: start ? start.toISOString() : null,
    end: end ? end.toISOString() : null,
  };
}