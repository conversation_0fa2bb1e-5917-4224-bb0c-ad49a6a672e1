// DueDateField.tsx
import { View, Pressable, StyleProp, ViewStyle, Platform } from 'react-native';
import { Text, Portal, Modal, Button } from 'react-native-paper';
import { dueDateFieldStyles } from './DueDateField.styles';
import { useState } from 'react';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import { useKeyboard } from '@/commons/KeyboardContext';

interface DueDateFieldProps {
  selectedDate: Date | null;
  formatDateAndTime: (date: Date) => string;
  style?: StyleProp<ViewStyle>;
  onDateChange: (date: Date | null) => void;
  onSaveDueDate?: (date: string | null) => Promise<void>;
  taskId?: string;
  isInitialLoad?: boolean;
  isEditMode?: boolean;
}

export function DueDateField({ 
  selectedDate,
  formatDateAndTime,
  style,
  onDateChange,
  onSaveDueDate,
  taskId,
  isEditMode = false
}: DueDateFieldProps) {
  const { isKeyboardActive } = useKeyboard();
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [tempDate, setTempDate] = useState<Date | null>(() => {
    if (selectedDate) return selectedDate;
    return null;
  });
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleDismiss = () => {
    setShowDatePicker(false);
    setShowTimePicker(false);
    setTempDate(selectedDate);
    setErrorMessage(null);
  };

  const handlePress = () => {
    if (isKeyboardActive) return;
    if (!tempDate) {
      const now = new Date();
      now.setHours(now.getHours() + 1, 0, 0, 0);
      setTempDate(now);
    }
    setShowDatePicker(true);
  };

  const handleAndroidDateTimeSelect = async (event: DateTimePickerEvent, selectedValue?: Date) => {
    if (event.type === 'dismissed') {
      handleDismiss();
      return;
    }

    if (Platform.OS === 'android' && !selectedValue) {
      handleDismiss();
      return;
    }

    if (showDatePicker && selectedValue) {
      setErrorMessage(null);
      const newDate = new Date(selectedValue);
      if (tempDate) {
        newDate.setHours(
          tempDate.getHours(),
          tempDate.getMinutes(),
          0,
          0
        );
      }
      setTempDate(newDate);
      setShowDatePicker(false);
      
      setTimeout(() => {
        setShowTimePicker(true);
      }, 100);
    } else if (showTimePicker && tempDate && selectedValue) {
      const newDateTime = new Date(tempDate);
      newDateTime.setHours(
        selectedValue.getHours(),
        selectedValue.getMinutes(),
        0,
        0
      );

      const now = new Date();
      if (newDateTime.toDateString() === now.toDateString() && newDateTime <= now) {
        setErrorMessage("");
        setShowTimePicker(false);
        return;
      }

      setErrorMessage(null);
      setShowTimePicker(false);
      await handleDateChange(newDateTime);
    }
  };

  const handleDateChange = async (date: Date | null) => {
    if (date) {
      const now = new Date();
      const newDate = new Date(date);

      if (newDate.toDateString() === now.toDateString() && newDate <= now) {
        newDate.setHours(now.getHours() + 1, 0, 0, 0);
      }

      setErrorMessage(null);
      setTempDate(newDate);
      const previousDate = selectedDate;
      onDateChange(newDate);
      
      if (isEditMode && onSaveDueDate && taskId) {
        try {
          await onSaveDueDate(newDate.toISOString());
        } catch (error: any) {
          console.log(error);
          onDateChange(previousDate);
          setTempDate(previousDate);
        }
      }
    }
  };

  const handleTimeChange = async (event: DateTimePickerEvent, time?: Date) => {
    if (time && tempDate) {
      const now = new Date();
      const newDateTime = new Date(tempDate);
      newDateTime.setHours(time.getHours(), time.getMinutes(), 0, 0);

      if (newDateTime.toDateString() === now.toDateString() && newDateTime <= now) {
        setErrorMessage("Please select a future time");
        return;
      }

      setErrorMessage(null);
      setShowTimePicker(false);
      await handleDateChange(newDateTime);
    }
  };

  const handleDateDone = () => {
    if (tempDate) {
      setShowDatePicker(false);
      setShowTimePicker(true);
    }
  };

  const handleTimeDone = async () => {
    if (tempDate) {
      await handleDateChange(tempDate);
    }
    setShowTimePicker(false);
  };

  const getMinimumTime = () => {
    const now = new Date();
    const tempDateStr = tempDate?.toDateString();
    const nowStr = now.toDateString();
    
    if (tempDateStr === nowStr) {
      return now;
    }
    return undefined;
  };

  const getDateLength = (date: Date | null) => {
    if (!date) return 1;
    return date.toString().length;
  }

  const handleIOSDateSelect = (event: DateTimePickerEvent, date?: Date) => {
    if (event.type === 'set' && date) {
      setErrorMessage(null);
      const newDate = new Date(date);
      if (tempDate) {
        newDate.setHours(tempDate.getHours(), tempDate.getMinutes(), 0, 0);
      } else {
        const now = new Date();
        newDate.setHours(now.getHours() + 1, 0, 0, 0);
      }
      setTempDate(newDate);
    } else if (event.type === 'dismissed') {
      handleDismiss();
    }
  };

  const handleIOSTimeSelect = async (event: DateTimePickerEvent, time?: Date) => {
    if (event.type === 'set' && time) {
      await handleTimeChange(event, time);
    } else if (event.type === 'dismissed') {
      handleDismiss();
    }
  };

  return (
    <View style={[dueDateFieldStyles.inputWrapper, style]}>
      <Pressable 
        onTouchEnd={handlePress}
        disabled={isKeyboardActive}
        style={{
          width: selectedDate ? 
            ((getDateLength(selectedDate) * 1) < 25 ? (getDateLength(selectedDate) * 10) : '95%')
            : '40%',
        }}>
        <View style={dueDateFieldStyles.iconContainer} />
        <Text style={dueDateFieldStyles.labelText}>Due date</Text>
        {!selectedDate ? (
          <Text style={dueDateFieldStyles.placeholderText}>Select</Text>
        ) : errorMessage ? (
          <Text style={[dueDateFieldStyles.errorText]}>
            {errorMessage}
          </Text>
        ) : (
          <Text style={dueDateFieldStyles.selectedDateText}>
            {formatDateAndTime(selectedDate)}
          </Text>
        )}
      </Pressable>

      <Portal>
        {Platform.OS === 'ios' ? (
          <>
            <Modal
              visible={showDatePicker}
              onDismiss={handleDismiss}
              dismissable={false}
              contentContainerStyle={dueDateFieldStyles.modalContainer}
            >
              <View style={dueDateFieldStyles.modalContent}>
                <Text variant="titleMedium" style={dueDateFieldStyles.modalTitle}>
                  Select Due Date
                </Text>
                <DateTimePicker
                  value={tempDate || new Date()}
                  mode="date"
                  display="inline"
                  minimumDate={new Date(new Date().setHours(0, 0, 0, 0))}
                  onChange={handleIOSDateSelect}
                  accentColor="#768DEC"
                />
                <View style={dueDateFieldStyles.buttonContainer}>
                  <Button 
                    mode="outlined"
                    onPress={handleDismiss}
                    style={dueDateFieldStyles.cancelButton}
                  >
                    Cancel
                  </Button>
                  <Button 
                    mode="contained"
                    onPress={handleDateDone}
                    style={dueDateFieldStyles.doneButton}
                    buttonColor="#768DEC"
                  >
                    Next
                  </Button>
                </View>
              </View>
            </Modal>

            <Modal
              visible={showTimePicker}
              onDismiss={handleDismiss}
              dismissable={false}
              contentContainerStyle={dueDateFieldStyles.modalContainer}
            >
              <View style={dueDateFieldStyles.modalContent}>
                <Text variant="titleMedium" style={dueDateFieldStyles.modalTitle}>
                  Select Due Time
                </Text>
                <DateTimePicker
                  value={tempDate || new Date()}
                  mode="time"
                  display="spinner"
                  minimumDate={getMinimumTime()}
                  onChange={handleIOSTimeSelect}
                  accentColor="#768DEC"
                  minuteInterval={1}
                />
                <View style={dueDateFieldStyles.buttonContainer}>
                  <Button 
                    mode="outlined"
                    onPress={handleDismiss}
                    style={dueDateFieldStyles.cancelButton}
                  >
                    Cancel
                  </Button>
                  <Button 
                    mode="contained"
                    onPress={handleTimeDone}
                    style={dueDateFieldStyles.doneButton}
                    buttonColor="#768DEC"
                  >
                    Done
                  </Button>
                </View>
              </View>
            </Modal>
          </>
        ) : (
          <>
            {showDatePicker && (
              <DateTimePicker
                value={tempDate || new Date()}
                mode="date"
                display="default"
                minimumDate={new Date(new Date().setHours(0, 0, 0, 0))}
                onChange={handleAndroidDateTimeSelect}
              />
            )}
            {showTimePicker && (
              <DateTimePicker
                value={tempDate || new Date()}
                mode="time"
                display="default"
                onChange={handleAndroidDateTimeSelect}
              />
            )}
          </>
        )}
      </Portal>
    </View>
  );
}

