import { gql } from "@apollo/client";

export const ADDRESS_BOOK_QUERY = gql`
query GetAddressBooks($filter: AddressBookFilterInput, $pagination: PaginationInput) {
    getAddressBooks(filter: $filter, pagination: $pagination) {
        ... on AddressBooksResponse {
        pagination {
            totalItems
            totalPages
            pageSize
            currentPage
        }
        message
        status
        result {
            addressBooks {
            id
            label
            venueAddress {
                id
                placeId
                address
                directions  
            }
            }
        }
        }
    }
}
`;