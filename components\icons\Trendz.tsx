import * as React from "react";
import Svg, { Path, Defs, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const Trendz = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      fill="url(#Trendz-1_svg__a)"
      stroke="url(#Trendz-1_svg__b)"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={0.909}
      d="M5.355 1.024c-.227-.1-.433.133-.336.3.342.608.57 1.277.672 1.972.141.98.001 2.054-.617 2.848a.26.26 0 0 1-.308.079.3.3 0 0 1-.093-.068c-.28-.296-.57-.596-.759-.964-.054-.111-.25-.288-.531 0-.68.573-1.095 1.452-1.008 2.349C2.41 9.526 4.127 10.976 6.01 11c1.92.024 3.612-1.43 3.627-3.46 0-1.281-.377-2.532-1.083-3.59a7.8 7.8 0 0 0-3.198-2.926"
    />
    <Defs>
      <LinearGradient
        id="Trendz-1_svg__a"
        x1={6}
        x2={6}
        y1={-0.207}
        y2={13.299}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Trendz-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.207}
        y2={13.299}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <Path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M5.335.869c-.234-.103-.447.137-.346.308.352.628.587 1.318.693 2.034.145 1.01 0 2.118-.637 2.937a.27.27 0 0 1-.414.012c-.288-.306-.587-.615-.782-.995-.056-.114-.257-.297-.548 0-.702.591-1.129 1.498-1.04 2.423.037 2.048 1.808 3.544 3.748 3.568 1.98.025 3.725-1.474 3.741-3.568 0-1.321-.39-2.611-1.118-3.703A8.03 8.03 0 0 0 5.335.87"
    />
        </>
      )}
    </Svg>
  );
};
export default Trendz;
