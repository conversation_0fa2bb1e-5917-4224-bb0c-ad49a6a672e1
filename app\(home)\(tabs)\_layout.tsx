import { usePathname } from 'expo-router';
import { StyleSheet } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import HomePageStack from '@/app/Home/HomePageStack';
import Photos from './photos';
import { Colors, Icons, Typography } from '@/constants/DesignSystem';
import { ActionSheetProvider } from '@expo/react-native-action-sheet';
import TrendsScreen from './trends';
import { Home, Trendz, Photos as PhotosIcon } from '@/components/icons';

export default function TabLayout() {
  const pathname = usePathname();
  const hiddenTabPaths = [
    '/Home/PartyDetails/NewPartyDetailsScreen',
    '/Home/PartyDetails/InvitesView',
    '/Home/PartyDetails/SendInvitesView',
    '/Home/PartyDetails/SaveOrSendInvite',
    '/Home/PartyDetails/RsvpScreen',
    '/Home/PartyDetails/PhotosAlbumView',
    '/Home/PartyDetails',
    '/Home/AddCircleScreen/CircleDetails',
    '/Home/AddCircleScreen/AddCircle',
    '/Home/AddCircleScreen/EditMembers',
    '/Home/AddPartyScreen/AddParty',
    '/Home/AddPartyScreen/EditParty',
    '/Home/AddPartyScreen/AddLocation',
    '/Home/AddPartyScreen/ConfirmLocation',
    '/Home/AddPartyScreen/AddressDetails',
    '/Home/AddPartyScreen/PartyInviteTemplate',,
    '/Home/PartyDetails/FlyerScreen',
  ];

  const tabBarStyle = hiddenTabPaths.includes(pathname) ? 'none' : 'flex';
  const Tab = createBottomTabNavigator();
  console.log('TabLayout pathname:', pathname);

  return (
    <ActionSheetProvider>
      <Tab.Navigator
        screenOptions={{
          headerShown: false,
          tabBarActiveTintColor: Colors.primary,
          tabBarInactiveTintColor: Colors.mediumGray,
          tabBarStyle: {
            display: tabBarStyle,
            backgroundColor: Colors.background.primary,
            borderTopColor: Colors.border.light,
          }
        }}>

        <Tab.Screen
          name="Home"
          component={HomePageStack}
          options={{
            tabBarLabel: 'Home',
            tabBarLabelStyle: styles.tabBarLabel,
            tabBarIcon: ({ focused }) =>
              focused ? (
                <Home
                  size={Icons.size.lg}
                  variant="filled"
                  gradientStartColor={Colors.primary}
                  gradientEndColor={Colors.gradient.orange}
                />
              ) : (
                <Home size={Icons.size.lg} color={Colors.mediumGray}/>
              ),
          }}
        />

        <Tab.Screen
          name="Trending"
          options={{
            headerShown: false,
            tabBarLabel: 'Trending',
            tabBarLabelStyle: styles.tabBarLabel,
            tabBarIcon: ({ focused }) => 
              focused ? (
                <Trendz
                  size={Icons.size.lg}
                  variant="filled"
                  gradientStartColor={Colors.primary}
                  gradientEndColor={Colors.gradient.orange}
                />
              ) : (
                <Trendz size={Icons.size.lg} color={Colors.mediumGray}/>
              ),
          }}>
          {() => <TrendsScreen />}
        </Tab.Screen>

        <Tab.Screen
          name="Photos"
          options={{
            headerShown: false,
            tabBarLabel: 'Photos',
            tabBarLabelStyle: styles.tabBarLabel,
            tabBarIcon: ({ focused }) =>
              focused ? (
                <PhotosIcon
                  size={Icons.size.lg}
                  variant="filled"
                  gradientStartColor={Colors.primary}
                  gradientEndColor={Colors.gradient.orange}
                />
              ) : (
                <PhotosIcon size={Icons.size.lg} color={Colors.mediumGray}/>
              ),
          }}>
          {() => <Photos />}
        </Tab.Screen>
      </Tab.Navigator>
    </ActionSheetProvider>
  );
}

const styles = StyleSheet.create({
  icon: {
    width: Icons.size.xl,
    height: Icons.size.xl,
    resizeMode: 'contain',
  },
  tabBarLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.bold,
  },
});
