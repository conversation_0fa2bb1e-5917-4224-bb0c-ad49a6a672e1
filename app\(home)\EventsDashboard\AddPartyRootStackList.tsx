export type AddPartyRootStackList = {
    AddParty: { 
        partyId?: string | undefined;
        eventGroupId?: string | undefined;
    };
    EditParty: {
        partyId?: string | undefined;
        eventGroupId?: string | undefined;
        isCohost?: boolean;
    };
    PartyInviteTemplate: undefined;
    AddLocation: {
        partyId?: string | undefined;
        eventGroupId?: string | undefined | null;
        isEditing?: boolean;
        onPressUpdate?: (params: {placeId: string, label: string, name: string, directions: string}) => void;
    };
    ConfirmLocation: {
        placeId: string;
        name: string;
        partyId?: string | undefined;
        eventGroupId?: string | undefined;
        isEditing?: boolean;
        onPressUpdate?: (params: {placeId: string, label: string, name: string, directions: string}) => void;
    };
    AddressDetails: {
        title?: string | undefined
        address: string
        additionalDetails?: string
        isEditing?: boolean
        onPressUpdate?: (params: {placeId: string, label: string, name: string, directions: string}) => void
        partyId?: string | undefined;
        eventGroupId?: string | undefined;
    };
};
