import { AddressBook } from "../EventsDashboard/AddParty";
export interface NewPartyDetailsResult {
    party: Party;
}

export interface Party {
    id: string;
    name: string;
    weather: Weather;
    time: String;
    partyType: PartyType;
    event: Event;
    venueAddress?: VenueAddress;
    addressBook?: AddressBook;
    activity: Activity[];
    rsvps: Rsvp[];
    invitation: PartyInvitation;
    userRole: string;
    muted?: boolean;
}

export interface Event {
    mainHost: MainHost;
    location: Location;
}

export interface Location {
    name: String;
    city: String;
}

export interface MainHost {
    userId: UserID;
}

export interface UserID {
    id: string;
    firstName: String;
    lastName: string;
}

export interface PartyType {
    portraitImage: string;
}

export interface Weather {
    weatherUrl: string;
    avgTempC: number;
    avgTempF: number;
    condition: Condition;
}

export interface Condition {
    icon: string;
    text: string;
}

export interface VenueAddress {
    coordinates: Coordinates;
    address: string;
    city: string;
    state: string;
    name: string;
}

export interface Coordinates {
    latitude: number;
    longitude: number;
}

export interface Activity {
    id: string;
    type: string;
    text: null | string;
    media: Media[];
    createdAt: string;
    createdBy: CreatedBy;
    reactions: Reaction[];
    parentMessageId: string;
}

export interface CreatedBy {
    firstName: string;
    lastName: null | string;
    profilePicture: null | string;
    id: string;
}

export interface Media {
    url: string;
    title: string;
    uploadedAt: Date;
    owner: Owner;
}

export interface Owner {
    role: string[];
    firstName: string;
    profilePicture: string;
}

export interface Reaction {
    reactionUnicode: string;
    updatedAt: Date;
    user: User;
    id: string;
}

export interface User {
    id: string;
}

export interface Rsvp {
    id: string;
    invitation: Invitation | null;
    guest: Guest | null;
    status: string;
    message: null;
}

export interface Guest {
    id: string;
    user: User;
    additionalGuestsCount: number
}

export interface User {
    firstName: string;
    profilePicture: null | string;
    email: null | string;
    phone: string;
    lastName: null | string;
    role: string[];
    id: string;
}

export interface Invitation {
    message: string;
}

export interface PartyInvitation {
    _id: string;
    media: InvitationMedia[];
    message: string;
    savedGuests: User[]
}

export interface InvitationMedia {
    url: string;
}


// TODO: @Sraavan - Confirm with Prashanth on 1/22


