import { Image, StyleSheet, TouchableOpacity, View } from "react-native";
import { useRouter } from "expo-router";
import { Colors, Spacing, Shadows, Borders, Icons } from '@/constants/DesignSystem';
import { Edit } from '../icons';
const defaultPartyImage = require('../../assets/images/HomePageDefaultImages/Default Images-04.png');
interface ImageTemplateProps {
  partyInfo: {
    partyType: {
      portraitImage: string;
    };
    invitation: {
      media: {
        url: string;
      }[];
    };
  };
  onImagePress?: () => void;
}

export function ImageTemplate({ partyInfo, onImagePress }: ImageTemplateProps) {
  const router = useRouter();

  const handleImagePress = () => {
    onImagePress?.();
  };

  const hasMediaUrl = partyInfo?.invitation?.media?.[0]?.url;
  const fallbackImage = partyInfo?.partyType?.portraitImage;

  return (
    <View style={styles.container}>
      <TouchableOpacity
        onPress={handleImagePress}
        activeOpacity={0.7}
        style={styles.touchable}
      >
        <Image
          source={hasMediaUrl ? { uri: hasMediaUrl } :
                 fallbackImage ? { uri: fallbackImage } :
                 defaultPartyImage}
          style={styles.partyImage}
          onError={(error) => console.log('Image loading error:', error.nativeEvent)}
          resizeMode="cover"
        />
        <TouchableOpacity onPress={handleImagePress} style={styles.editIcon}>
          <Edit size={Icons.size.md} color={Colors.secondary} />
        </TouchableOpacity>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    alignSelf: 'center',
    marginVertical: Spacing.md,
  },
  touchable: {
    alignSelf: 'center',
    ...Shadows.md,
  },
  partyImage: {
    width: 150,
    height: 200,
    borderRadius: Borders.radius.md,
  },
  editIcon: {
    position: 'absolute',
    bottom: Spacing.md,
    right: 0,
    margin: 0,
    ...Shadows.sm,
    backgroundColor: Colors.white,
    borderRadius: Borders.radius.circle,
    padding: Spacing.sm,
  }
});