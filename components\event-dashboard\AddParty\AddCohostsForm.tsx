import { StyleSheet, View } from 'react-native'
import React, { useState } from 'react'
import * as Contacts from 'expo-contacts';
import { Button, TextInput, Text } from 'react-native-paper';
import { Cohost } from '@/app/(home)/EventsDashboard/AddParty.models';
import { PhoneNumberInput } from '@/components/UI/ReusableComponents/PhoneNumberInput';
import { useToast } from '@/components/Toast/useToast';
import { regexValidators } from '@/app/(home)/utils/reusableFunctions';
import { Colors, Spacing, Shadows, Typography, Borders } from '@/constants/DesignSystem';

interface AddCohostsFormProps {
  existingCohosts: Cohost[];
  onCohostAdd: (cohost: Cohost) => void;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
}

const AddCohostsForm: React.FC<AddCohostsFormProps> = ({ existingCohosts, onCohostAdd }) => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [formattedPhoneNumber, setFormattedPhoneNumber] = useState('');
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const toast = useToast();

  const validateForm = () => {
    const errors: FormErrors = {};
    let isValid = true;

    if (!firstName.trim()) {
      errors.firstName = 'First name is required';
      isValid = false;
    }

    if (!lastName.trim()) {
      errors.lastName = 'Last name is required';
      isValid = false;
    }

    if (!phoneNumber || !regexValidators.isValidPhone(phoneNumber)) {
      errors.phoneNumber = 'Valid phone number is required';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const checkDuplicatePhone = (phone: string): boolean => {
    return existingCohosts.some(cohost => cohost.phoneNumber === phone);
  };

  const handleCustomCohostAdd = () => {
    if (!validateForm()) return;

    if (checkDuplicatePhone(formattedPhoneNumber)) {
      toast.error('A cohost with this phone number already exists');
      return;
    }

    const newCohost: Cohost = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      firstName: firstName?.trim(),
      lastName: lastName ? lastName?.trim() : '',
      phoneNumber: formattedPhoneNumber,
    };

    onCohostAdd(newCohost);
    resetForm();
  };

  const resetForm = () => {
    setFirstName('');
    setLastName('');
    setPhoneNumber('');
    setFormattedPhoneNumber('');
    setFormErrors({});
  };

  const openContacts = async () => {
    const { status } = await Contacts.requestPermissionsAsync();
    if (status === 'granted') {
      try {
        const contact = await Contacts.presentContactPickerAsync();
        if (contact) {
          const phoneNumber = contact?.phoneNumbers?.[0]?.number;

          if (!phoneNumber) {
            toast.error('Selected contact has no phone number');
            return;
          }

          if (checkDuplicatePhone(phoneNumber)) {
            toast.error('A cohost with this phone number already exists');
            return;
          }

          const newCohost: Cohost = {
            id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            firstName: contact?.firstName,
            lastName: contact?.lastName ? contact?.lastName : '',
            phoneNumber: phoneNumber,
            email: contact?.emails?.[0]?.email || '',
          };

          onCohostAdd(newCohost);
        }
      } catch (error) {
        toast.error('Error selecting contact');
        console.log('Error selecting contact:', error);
      }
    } else {
      toast.error('Contacts permission not granted');
    }
  };

  return (
    <View style={styles.container}>
      <Text
        variant="titleMedium"
        style={styles.title}
      >
        Add Co-host
      </Text>

      <View style={styles.formContainer}>
        <TextInput
          label="First Name *"
          value={firstName}
          onChangeText={setFirstName}
          error={!!formErrors.firstName}
          style={styles.input}
          mode="outlined"
          outlineColor={Colors.border.medium}
          activeOutlineColor={Colors.primary}
          outlineStyle={{ borderRadius: Borders.radius.md }}
          contentStyle={{ paddingHorizontal: Spacing.sm }}
        />
        {formErrors.firstName && (
          <Text style={styles.errorText}>{formErrors.firstName}</Text>
        )}

        <TextInput
          label="Last Name *"
          value={lastName}
          onChangeText={setLastName}
          error={!!formErrors.lastName}
          style={styles.input}
          mode="outlined"
          outlineColor={Colors.border.medium}
          activeOutlineColor={Colors.primary}
          outlineStyle={{ borderRadius: Borders.radius.md }}
          contentStyle={{ paddingHorizontal: Spacing.sm }}
        />
        {formErrors.lastName && (
          <Text style={styles.errorText}>{formErrors.lastName}</Text>
        )}

        <PhoneNumberInput
          label="Phone Number *"
          value={phoneNumber}
          onChangeText={(raw, formatted) => {
            setPhoneNumber(raw);
            setFormattedPhoneNumber(formatted);
          }}
          error={!!formErrors.phoneNumber}
          errorText={formErrors.phoneNumber}
          mode="outlined"
        />

        <Button
          mode="contained"
          onPress={handleCustomCohostAdd}
          style={styles.addButton}
          buttonColor={Colors.primary}
          textColor={Colors.white}
          rippleColor="rgba(255,255,255,0.2)"
        >
          Add Co-host
        </Button>

        <Text style={styles.orText}>OR</Text>

        <Button
          mode="outlined"
          icon="contacts"
          onPress={openContacts}
          style={styles.contactsButton}
          textColor={Colors.primary}
          buttonColor={Colors.background.primary}
          rippleColor={Colors.background.secondary}
        >
          Select from Contacts
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: Spacing.md,
  },
  title: {
    marginBottom: Spacing.md,
    color: Colors.text.primary,
    fontSize: Typography.fontSize.lg,
    fontWeight: '600',
  },
  formContainer: {
    gap: Spacing.md,
  },
  input: {
    marginBottom: Spacing.xs,
    backgroundColor: Colors.background.primary,
  },
  errorText: {
    color: Colors.error,
    fontSize: Typography.fontSize.xs,
    marginBottom: Spacing.sm,
  },
  addButton: {
    marginTop: Spacing.sm,
    borderRadius: Borders.radius.md,
  },
  orText: {
    textAlign: 'center',
    marginVertical: Spacing.md,
    color: Colors.text.secondary,
    fontSize: Typography.fontSize.md,
  },
  contactsButton: {
    marginBottom: Spacing.md,
    borderColor: Colors.primary,
    borderRadius: Borders.radius.md,
  },
});

export default AddCohostsForm;