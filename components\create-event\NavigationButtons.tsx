import React from 'react';
import { View } from 'react-native';
import { Button } from '@/components/Button';
import { CREATE_EVENT_BUTTON_TITLES } from '@/constants/createEvent.constants';

interface NavigationButtonsProps {
  onBack: () => void;
  onNext: () => void;
  isLastStep: boolean;
  isNextDisabled: boolean;
}

export function NavigationButtons({ 
  onBack, 
  onNext, 
  isLastStep, 
  isNextDisabled 
}: NavigationButtonsProps) {
  return (
    <View style={{ 
      flexDirection: 'row', 
      justifyContent: 'space-between', 
      gap: 12,
      marginTop: 16
    }}>
      <Button 
        title={CREATE_EVENT_BUTTON_TITLES.BACK}
        mode="outlined"
        onPress={onBack}
      />
      
      <Button 
        title={isLastStep ? CREATE_EVENT_BUTTON_TITLES.CREATE : CREATE_EVENT_BUTTON_TITLES.NEXT}
        mode="contained"
        onPress={onNext}
        disabled={isNextDisabled}
      />
    </View>
  );
}