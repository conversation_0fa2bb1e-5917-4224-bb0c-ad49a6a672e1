import React from 'react';
import { Modal, TouchableOpacity, StyleSheet, Dimensions, Platform } from 'react-native';
import { ThemedView } from '@/components/UI/ThemedView';
import { ThemedText } from '@/components/UI/ThemedText';
import { Colors } from '@/constants/Colors';

interface EventOptionsModalProps {
  isVisible: boolean;
  onClose: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  iconLayout: { x: number; y: number; width: number; height: number; } | null;
}

export const EventOptionsModal: React.FC<EventOptionsModalProps> = ({
  isVisible,
  onClose,
  onEdit,
  onDelete,
  iconLayout,
}) => {
  const screenWidth = Dimensions.get('window').width;
  const modalWidth = 120;
  const rightMargin = 16;
  const verticalOffset = Platform.OS === 'android' ? 0 : 8;

  const getModalPosition = () => {
    if (!iconLayout) return {};
    
    let modalLeft = iconLayout.x - modalWidth + iconLayout.width;
    
    // Ensure modal doesn't go off screen
    if (modalLeft + modalWidth > screenWidth - rightMargin) {
      modalLeft = screenWidth - modalWidth - rightMargin;
    }
    if (modalLeft < rightMargin) {
      modalLeft = rightMargin;
    }

    return {
      position: 'absolute' as const,
      left: modalLeft,
      top: iconLayout.y + iconLayout.height + verticalOffset,
    };
  };

  if (!iconLayout) return null;

  return (
    <Modal
      animationType="none"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <TouchableOpacity 
        style={styles.modalOverlay} 
        onPress={onClose} 
        activeOpacity={1}
      >
        <ThemedView style={[styles.modalContent, getModalPosition()]}>
          <TouchableOpacity 
            onPress={() => onEdit?.()} 
            style={[styles.option, {borderBottomWidth: 1, borderBottomColor: Colors.light.secondaryBackground}]}
          >
            <ThemedText type="default">Edit</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => onDelete?.()} style={styles.option}>
            <ThemedText type="default">Delete</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
  },
  modalContent: {
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    width: 120,
    shadowColor: Colors.custom.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  option: {
    paddingVertical: '6%',
    paddingHorizontal: '12%',
  }
});