import React from 'react';
import { Modal, TouchableOpacity, StyleSheet, LayoutRectangle, Dimensions, Platform } from 'react-native';
import { ThemedView } from '@/components/UI/ThemedView';
import { ThemedText } from '@/components/UI/ThemedText';
import { Colors } from '@/constants/Colors';

interface PartyOptionsModalProps {
  isVisible: boolean;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  iconLayout: LayoutRectangle | null;
}

export const PartyOptionsModal: React.FC<PartyOptionsModalProps> = ({
  isVisible,
  onClose,
  onEdit,
  onDelete,
  iconLayout,
}) => {
  const screenWidth = Dimensions.get('window').width;
  const modalWidth = 120;
  const rightMargin = 40;
  const verticalGap = Platform.OS === 'android' ? -32 : 5; // Further reduced gap for Android

  const getModalPosition = () => {
    if (!iconLayout) return {};
    
    const modalLeft = Math.min(iconLayout.x, screenWidth - modalWidth - rightMargin);
    return {
      position: 'absolute' as 'absolute',
      left: modalLeft,
      top: iconLayout.y + iconLayout.height + verticalGap,
    };
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <TouchableOpacity style={styles.modalOverlay} onPress={onClose} activeOpacity={1}>
        <ThemedView style={[styles.modalContent, getModalPosition()]}>
          <TouchableOpacity onPress={onEdit} style={[styles.option, {borderBottomWidth: 1, borderBottomColor: Colors.light.secondaryBackground}]}>
            <ThemedText type="default">Edit</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity onPress={onDelete} style={styles.option}>
            <ThemedText type="default">Delete</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
  },
  modalContent: {
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    width: 120,
    shadowColor: Colors.custom.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  option: {
    paddingVertical: '6%',
    paddingHorizontal: '12%',
  }
});
