import React from 'react';
import { TouchableOpacity, ViewStyle } from 'react-native';
import { NewComment } from '@/components/icons';

interface SecondaryButtonProps {
  text?: string;
  onPress: () => void;
  style?: ViewStyle | ViewStyle[];
}

const SecondaryButton: React.FC<SecondaryButtonProps> = ({ onPress, style }) => {

  return (
    <TouchableOpacity onPress={onPress} activeOpacity={1}>
          <NewComment size={42} />
    </TouchableOpacity>
  );
};

export default SecondaryButton;
