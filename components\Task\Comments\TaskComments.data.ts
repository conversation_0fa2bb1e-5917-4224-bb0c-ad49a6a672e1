import { gql } from "@apollo/client";

export const CREATE_TASK_COMMENT = gql`
mutation CreateTaskComment($taskId: ID!, $input: CommentInput!) {
  createTaskComment(taskId: $taskId, input: $input) {
    ... on CommentResponse {
      result {
        comment {
          id
          content
        }
      }
    }
    ... on CommentErrorResponse {
      errors {
        field
        message
      }
    }
  }
}
`

export const UPDATE_TASK_COMMENT = gql`
mutation UpdateTaskComment($taskId: ID!, $commentId: ID!, $input: CommentUpdateInput!) {
  updateTaskComment(taskId: $taskId, commentId: $commentId, input: $input) {
    ... on CommentResponse {
      result {
        comment {
          id
          content
          reactions {
            id
            reactionUnicode
            user {
              id
              firstName
              lastName
            }
          }
        }
      }
    }
    ... on CommentErrorResponse {
      errors {
        field
        message
      }
    }
  }
}`

export const GET_TASK_COMMENTS_BY_TASK_ID = gql`
  query GetTaskById($getTaskByIdId: ID!) {
    getTaskById(id: $getTaskByIdId) {
      ... on TaskResponse {
        result {
          task {
            comments {
              id
              content
              mentions {
                id
                firstName
                lastName
                isActive
              }
              createdBy {
                id
                firstName
                lastName
              }
              createdAt
              reactions {
                id
                reactionUnicode
                user {
                  id
                  firstName
                  lastName
                  isActive
                }
              }
            }
            id
          }
        }
      }
    }
  }
`;

export const GET_EVENT_COLLABORATORS = gql`
query GetEventCollaborators($eventId: ID!) {
  getEventCollaborators(eventId: $eventId) {
    ... on EventCollaboratorsResponse {
      result {
        collaborators {
          id
          firstName
          lastName
        }
      }
    }
  }
} 
  `
export const GET_TASK_COLLABORATORS = gql`
query GetTaskCollaborators($filter: TaskCollaboratorFilterInput) {
  getTaskCollaborators(filter: $filter) {
    ... on TaskCollaboratorsResponse {
      result {
        taskCollaborators {
          id
          user {
            id
            firstName
            lastName
          }
        }
      }
    }
    ... on TaskCollaboratorErrorResponse {
      errors {
        field
        message
      }
    }
  }
}
  `


export const CREATE_TASK_COLLABORATORS = gql`
mutation CreateTaskCollaborator($taskId: ID!, $userIds: [ID!]!) {
  createTaskCollaborator(taskId: $taskId, userIds: $userIds) {
    ... on TaskCollaboratorResponse {
      result {
        taskCollaborator {
          id
          user {
            id
            firstName
            lastName
          }
        }
      }
    }
    ... on TaskCollaboratorErrorResponse {
      errors {
        field
        message
      }
    }
  }
}
`

export const DELETE_TASK_REACTION = gql`
mutation DeleteReaction($deleteReactionId: ID!) {
  deleteReaction(id: $deleteReactionId) {
    ... on ReactionResponse {
      result {
        reaction {
          id
          reactionUnicode
          user {
            id
            firstName
            lastName
          }
        }
      }
    }
    ... on ReactionErrorResponse {
      errors {
        field
        message
      }
    }
  }
}`

export const CREATE_TASK_REACTION = gql`
mutation CreateReaction($input: ReactionInput!) {
  createReaction(input: $input) {
    ... on ReactionResponse {
      result {
        reaction {
          id
          reactionUnicode
          user {
            id
            firstName
            lastName
          }
        }
      }
    }
    ... on ReactionErrorResponse {
      errors {
        field
        message
      }
    }
  }
}
  `

export const DELETE_TASK_COLLABORATOR = gql `
mutation DeleteTaskCollaborator($deleteTaskCollaboratorId: ID!) {
  deleteTaskCollaborator(id: $deleteTaskCollaboratorId) {
    ... on TaskCollaboratorResponse {
      result {
        taskCollaborator {
          id
          user {
            id
          }
        }
      }
    }
    ... on TaskCollaboratorErrorResponse {
      errors {
        field
        message
      }
    }
  }
}
`
