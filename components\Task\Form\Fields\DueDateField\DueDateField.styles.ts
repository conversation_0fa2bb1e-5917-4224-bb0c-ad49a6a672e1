import { StyleSheet, Platform } from 'react-native';
import { sharedStyles } from '../Shared.styles';

export const dueDateFieldStyles = StyleSheet.create({
  inputWrapper: {
    flex: 1,
    backgroundColor: '#fff',
    height: 45,
    paddingVertical: 0,
    flexGrow: 1,
    
  },

  labelText: {
    ...sharedStyles.label,
  },
  iconContainer: {
    position: 'absolute',
    left: 50,
    textAlign: 'right',
  },
  
  placeholderText: {
    ...sharedStyles.placeholder,
  },
  selectedDateText: {
    ...sharedStyles.value,
  },

  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  modalContent: {
    width: '100%',
    alignItems: 'center',
  },

  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },

  pickerTitle: {
    fontSize: 17,
    fontWeight: '600',
  },
  
  picker: {
    height: 200,
    width: '100%',
  },

  iconCircle: {
    width: 32,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },

  modalContainer: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 12,
    alignItems: 'center',
  },

  modalTitle: {
    marginBottom: 20,
  },

  doneButton: {
    width: '100%',
    flex: 1,
    marginLeft: 8,
  },

  errorText: {
    color: '#EF4444',
    fontSize: 12,
    textAlign: 'right',
    fontWeight: '500',
    top: -24,
    zIndex: 10,
    paddingLeft: 60,
    flexWrap: 'wrap',
    paddingTop: 22,
    right: Platform.OS === 'ios' ? 66 : 75,
    width: 200,
  },

  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 16,
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },

});