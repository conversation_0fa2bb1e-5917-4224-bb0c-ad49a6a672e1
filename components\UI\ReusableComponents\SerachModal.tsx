import React from 'react';
import { ScrollView } from 'react-native';
import { Modal, Searchbar, List, useTheme, Text } from 'react-native-paper';

interface SearchableItem {
  id: string;
  title?: string;
  city?: string;
  [key: string]: any;
}

interface WithSearchModalProps<T> {
  visible: boolean;
  onDismiss: () => void;
  onSelect: (item: T) => void;
  searchPlaceholder?: string;
  items: T[];
  getSearchKey?: (item: T) => string;
  renderItem?: (item: T, onSelect: (item: T) => void) => React.ReactNode;
  noOptionsText?: string;
}

export function SearchModal<T extends SearchableItem>() {
  return function SearchModal({
    visible,
    onDismiss,
    onSelect,
    searchPlaceholder = "Search",
    items,
    getSearchKey = (item) => item?.title || '',
    renderItem,
    noOptionsText,
  }: WithSearchModalProps<T>) {
    const theme = useTheme();
    const [searchQuery, setSearchQuery] = React.useState('');

    const filteredItems = React.useMemo(() => 
      items.filter(item => 
        getSearchKey(item)?.toLowerCase()?.includes(searchQuery?.toLowerCase())
      ),
    [items, searchQuery, getSearchKey]);

    const defaultRenderItem = (item: T, onSelect: (item: T) => void) => (
      <List.Item
        key={item?.id}
        title={item?.title }
        onPress={() => {
          onSelect(item);
          onDismiss();
        }}
      />
    );

    return (
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={{ 
          backgroundColor: theme.colors.background,
          padding: 20,
          margin: 20,
          borderRadius: 8
        }}
      >
        <Searchbar
          placeholder={searchPlaceholder}
          onChangeText={setSearchQuery}
          value={searchQuery}
        />
        <ScrollView style={{ maxHeight: 400 }}>
          {filteredItems.length === 0 && <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, textAlign: 'center', margin: 8, padding: 8 }}>{noOptionsText}</Text>}
          {filteredItems.map(item => 
            renderItem 
              ? renderItem(item, onSelect)
              : defaultRenderItem(item, onSelect)
          )}
        </ScrollView>
      </Modal>
    );
  };
}