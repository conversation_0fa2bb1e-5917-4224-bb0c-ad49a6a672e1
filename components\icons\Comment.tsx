import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Comment = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Comment-1_svg__a)">
      <Path
        fill="url(#Comment-1_svg__b)"
        d="M3.804 10.493a5 5 0 1 0-1.966-1.722m1.966 1.722L1 11l.838-2.229m1.966 1.722h.004ZM1.838 8.771V8.77Z"
      />
      <Path
        stroke="url(#Comment-1_svg__c)"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={0.962}
        d="M3.804 10.493a5 5 0 1 0-1.966-1.722m1.966 1.722L1 11l.838-2.229m1.966 1.722h.004M1.838 8.77V8.77"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Comment-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.207}
        y2={13.299}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Comment-1_svg__c"
        x1={6}
        x2={6}
        y1={-0.207}
        y2={13.299}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Comment-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G clipPath="url(#Comment_svg__a)">
      <Path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M3.804 10.493a5 5 0 1 0-1.966-1.722m1.966 1.722L1 11l.838-2.229m1.966 1.722h.004M1.838 8.77V8.77"
      />
    </G>
    <Defs>
      <ClipPath id="Comment_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Comment;
