import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';

export const ALLOWED_DOCUMENT_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];
  
  export interface TaskAttachment {
    __typename?: "Document";
    description?: string | null;
    documentType?: string;
    documentUrl: string;
    name: string;
    id?: string;
  }

  function getIconForFileType(file: DocumentPicker.DocumentPickerAsset | TaskAttachment): string {
    // Handle TaskAttachment type
    if ('documentUrl' in file && file.documentUrl) {
      const urlExtension = file.documentUrl.split('.').pop()?.toLowerCase();
      
      switch (urlExtension) {
        case 'pdf':
          return 'file-pdf-box';
        case 'doc':
        case 'docx':
          return 'file-word';
        case 'xls':
        case 'xlsx':
          return 'file-excel';
      }
    }

    // Handle DocumentPickerAsset type
    if ('mimeType' in file && file.mimeType) {
      if (file.mimeType.startsWith('image/')) {
        return 'image';
      }
      
      if (file.mimeType.startsWith('video/')) {
        return 'video';
      }

      if (file.mimeType.includes('wordprocessingml.document') || file.mimeType.includes('msword')) {
        return 'file-word';
      }

      if (file.mimeType.includes('spreadsheetml.sheet') || file.mimeType.includes('ms-excel')) {
        return 'file-excel';
      }

      if (file.mimeType.includes('pdf')) {
        return 'file-pdf-box';
      }
    }
    
    // Fallback to name extension
    const nameExtension = file.name?.split('.').pop()?.toLowerCase();
    
    switch (nameExtension) {
      case 'pdf':
        return 'file-pdf-box';
      case 'doc':
      case 'docx':
        return 'file-word';
      case 'xls':
      case 'xlsx':
        return 'file-excel';
      default:
        return 'file-document-outline';
    }
  }

  function getColorForFileType(file: DocumentPicker.DocumentPickerAsset | TaskAttachment): string {
    if ('mimeType' in file && file.mimeType?.startsWith('image/')) {
      return '#4CAF50'; // Green
    }

    if ('mimeType' in file && file.mimeType?.startsWith('video/')) {
      return '#FF9800'; // Orange for videos
    }

    if ('documentType' in file) {
      const docType = file.documentType?.toLowerCase() || '';
      if (docType.includes('image')) return '#4CAF50'; // Green
      if (docType.includes('video')) return '#FF9800'; // Orange
    }
    
    const extension = file.name?.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return '#F44336'; // Red
      case 'doc':
      case 'docx':
        return '#2196F3'; // Blue
      case 'xls':
      case 'xlsx':
        return '#4CAF50'; // Green
      default:
        return '#9E9E9E'; // Grey
    }
  }

  function getMimeTypeFromUrl(url: string): string {
    if (url.endsWith('.docx')) {
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    }
    if (url.endsWith('.doc')) {
      return 'application/msword';
    }
    if (url.endsWith('.pdf')) {
      return 'application/pdf';
    }
    if (url.endsWith('.xlsx')) {
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    }
    if (url.endsWith('.xls')) {
      return 'application/vnd.ms-excel';
    }
    return 'application/octet-stream';
  }

  export function getFileTypeIcon(file: DocumentPicker.DocumentPickerAsset | TaskAttachment) {
    // Ensure we have a valid file object
    if (!file) {
      return {
        icon: 'file-document-outline',
        color: '#9E9E9E'
      };
    }

    // For TaskAttachment type, determine MIME type from URL
    if ('documentUrl' in file) {
      const mimeType = getMimeTypeFromUrl(file.documentUrl);
      return {
        icon: getIconForFileType({ ...file, mimeType }),
        color: getColorForFileType({ ...file, mimeType })
      };
    }

    return {
      icon: getIconForFileType(file),
      color: getColorForFileType(file)
    };
  }

  export function prepareFilesForUpload(files: DocumentPicker.DocumentPickerAsset[]) {
    if (!files.length) return {};
    
    // Format for GraphQL Upload type
    const formattedFiles = files.map(file => ({
      name: file.name,
      type: file.mimeType || 'application/octet-stream',
      uri: file.uri,
      size: file.size || 0
    }));

    return {
      containerName: "tasks",  
      files: formattedFiles
    };
  }

  export const MAX_FILE_SIZE_MB = 20;
  export const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

  export function validateFile(file: DocumentPicker.DocumentPickerAsset): {
    isValid: boolean;
    error?: string;
  } {
    if (file.size && file.size > MAX_FILE_SIZE_BYTES) {
      return {
        isValid: false,
        error: `File size exceeds ${MAX_FILE_SIZE_MB}MB limit`
      };
    }
    
    return { isValid: true };
  }

  export async function getFileSize(uri: string): Promise<number> {
    try {
      const fileInfo = await FileSystem.getInfoAsync(uri, { size: true });
      return fileInfo.exists ? (fileInfo as FileSystem.FileInfo & { size: number }).size : 0;
    } catch (error) {
      console.warn('Error getting file size:', error);
      return 0;
    }
  }
  export const getMimeType = (uri: string): string => {
    // Documents
    if (uri.endsWith('.pdf')) return 'application/pdf';
    if (uri.endsWith('.doc')) return 'application/msword';
    if (uri.endsWith('.docx')) return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    if (uri.endsWith('.xls')) return 'application/vnd.ms-excel';
    if (uri.endsWith('.xlsx')) return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    if (uri.endsWith('.ppt')) return 'application/vnd.ms-powerpoint';
    if (uri.endsWith('.pptx')) return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
    if (uri.endsWith('.txt')) return 'text/plain';
    if (uri.endsWith('.csv')) return 'text/csv';
    if (uri.endsWith('.rtf')) return 'application/rtf';

    // Images
    if (uri.endsWith('.jpg') || uri.endsWith('.jpeg')) return 'image/jpeg';
    if (uri.endsWith('.png')) return 'image/png';
    if (uri.endsWith('.gif')) return 'image/gif';
    if (uri.endsWith('.webp')) return 'image/webp';
    if (uri.endsWith('.svg')) return 'image/svg+xml';
    if (uri.endsWith('.heic')) return 'image/heic';

    // Videos
    if (uri.endsWith('.mp4')) return 'video/mp4';
    if (uri.endsWith('.mov')) return 'video/quicktime';
    if (uri.endsWith('.avi')) return 'video/x-msvideo';
    if (uri.endsWith('.wmv')) return 'video/x-ms-wmv';
    if (uri.endsWith('.mkv')) return 'video/x-matroska';
    if (uri.endsWith('.webm')) return 'video/webm';

    // Audio
    if (uri.endsWith('.mp3')) return 'audio/mpeg';
    if (uri.endsWith('.wav')) return 'audio/wav';
    if (uri.endsWith('.m4a')) return 'audio/mp4';
    if (uri.endsWith('.aac')) return 'audio/aac';

    // Archives
    if (uri.endsWith('.zip')) return 'application/zip';
    if (uri.endsWith('.rar')) return 'application/x-rar-compressed';
    if (uri.endsWith('.7z')) return 'application/x-7z-compressed';

    return 'application/octet-stream'; // Fallback for unknown types
  };
  