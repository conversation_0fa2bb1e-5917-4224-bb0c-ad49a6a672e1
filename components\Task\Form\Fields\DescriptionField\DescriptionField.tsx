import { Platform, View } from 'react-native';
import { Text } from 'react-native-paper';
import { styles } from './DescriptionField.styles';
import { TextModal } from '@/components/Task/Form/ReusableComponents/TextModal';
import { useState, useRef } from 'react';
import { Pressable } from 'react-native';
import { useToast } from '@/components/Toast/useToast';
import { useKeyboard } from '@/commons/KeyboardContext';
import { Icons, Colors } from '@/constants/DesignSystem';
import { Add } from '@/components/icons';

interface DescriptionFieldProps {
  value: string;
  onChangeText: (text: string) => void;
  onSaveDescription?: (description: string | null) => Promise<void>;
  taskId?: string;
}

export function DescriptionField({ 
  value, 
  onChangeText,
  onSaveDescription,
  taskId 
}: DescriptionFieldProps) {
  const [modalVisible, setModalVisible] = useState(false);
  const [tempValue, setTempValue] = useState(value);
  const toast = useToast();
  const [textHeight, setTextHeight] = useState(0);
  const { isKeyboardActive } = useKeyboard();

  const touchStartTime = useRef<number>(0);
  const touchStartY = useRef<number>(0);
  const PRESS_DELAY = 150; // milliseconds
  const SCROLL_THRESHOLD = 3; // pixels

  const handleTouchStart = (event: any) => {
    touchStartTime.current = Date.now();
    touchStartY.current = event.nativeEvent.pageY;
  };

  const handleTouchEnd = (event: any) => {
    if (isKeyboardActive) return;
    
    const touchEndTime = Date.now();
    const touchEndY = event.nativeEvent.pageY;
    const timeDiff = touchEndTime - touchStartTime.current;
    const scrollDiff = Math.abs(touchEndY - touchStartY.current);

    if (timeDiff < PRESS_DELAY && scrollDiff < SCROLL_THRESHOLD) {
      setModalVisible(true);
    }
  };

  const handleSave = async () => {
    onChangeText(tempValue);
    setModalVisible(false);
    
    if (onSaveDescription && taskId) {
      try {
        await onSaveDescription(tempValue || null);
      } catch (error: any) {
        console.log(error);
        onChangeText(value);
        toast.error('Failed to update description');
      }
    }
  };

  const handleCancel = () => {
    setTempValue(value);
    setModalVisible(false);
  };

  return (
    <View style={[styles.descriptionSection, { minHeight: textHeight }]}>
      <Pressable 
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        disabled={isKeyboardActive}
        style={[{
          width: value ? '100%' : '30%',
          maxWidth: '100%'
        }]}
      >
        <Text style={styles.descriptionSectionTitle}>
          Description
        </Text>
        <View style={styles.row}>
          {value ? (
            <Text 
              style={styles.descriptionInput}
              numberOfLines={undefined}
              onLayout={(event) => {
                setTextHeight(event.nativeEvent.layout.height);
              }}
            >
              {value}
            </Text>
          ) : (
            <View style={styles.placeholderContainer}>
              <Add size={Icons.size.md} color={Colors.text.secondary} />
              <Text style={styles.placeholder}>
                Description
              </Text>
            </View>
          )}
        </View>
      </Pressable>

      <TextModal
        visible={modalVisible}
        value={tempValue}
        onChangeText={setTempValue}
        onCancel={handleCancel}
        onSave={handleSave}
        inputTheme={{
          colors: {
            primary: '#0A0A0B',
            background: '#ffffff',
          },
          zIndex: 1000,
        }}
        placeholder="Add description"
        maxLength={255}
        numberOfLines={Platform.OS === 'ios' ? 16 : 80}
        isDescription={true}
      />
    </View>
  );
}    