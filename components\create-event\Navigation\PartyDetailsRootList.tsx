import { NewPartyDetailsResult, Reaction, Rsvp } from "@/app/(home)/PartyDetails/NewPartyDetailsResponse.model";
import { MapLocationProps } from "@/components/map-weather/MapLocation";
import { Party } from "@/app/(home)/PartyDetails/NewPartyDetailsResponse.model";
import * as Contacts from "expo-contacts";
import { LiveLocation } from "@/components/map-weather/MapData/MapLiveLocations.model";
import { TextEditorProps } from "@/components/TextEditors/EditorComponent";

export type PartyDetailsRootList = {
  NewPartyDetailsScreen: { partyId: string | undefined, isHosting?: boolean|undefined, rsvp?: string | undefined , fromSearch?: boolean | undefined, fromCreate?: boolean | undefined, showSuccessAnimation?: boolean };
  InvitesView: { partyDetails: Party | undefined };
  PhotosAlbumView: { albumId: string | undefined; albumType: string };
  TasksScreen: undefined;
  FullScreenMapView: { mapProps: MapLocationProps }
  PartyInviteTemplate: { partyId: string | undefined }
  SendReminders: { locations: LiveLocation[] | undefined, partyId: string | undefined };
  SendInvitesView: { partyDetails: Party | undefined }
  SaveOrSendInvite: { partyDetails: Party | undefined, selectedContacts: Contacts.Contact[] | undefined }
  AppsView: undefined;
  RsvpScreen: { partyId: string | undefined, fromSearch?: boolean | undefined };
  ReactionsCard: {reactions: Reaction[]}
  KeyboardEditor: {props:TextEditorProps};
  FlyerScreen: {
    image: string;
    date: string;
    venue: string;
    name: string;
    address: string;
    partyId: string;
  };
};
