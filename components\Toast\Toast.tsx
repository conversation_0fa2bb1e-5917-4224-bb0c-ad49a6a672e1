import React from 'react';
import RNToast, { BaseToast, ErrorToast } from 'react-native-toast-message';

const toastConfig = {
  success: (props: any) => (
    <BaseToast
      {...props}
      style={{
        borderLeftColor: '#00C851',
        height: 60,
        zIndex: 9999,
        position: 'relative',
        bottom: 0
      }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
      text1Style={{
        fontSize: 16,
        fontWeight: '500'
      }}
      text2Style={{
        fontSize: 14
      }}
    />
  ),
  error: (props: any) => (
    <ErrorToast
      {...props}
      style={{
        borderLeftColor: '#ff4444',
        height: 60,
        zIndex: 9999,
        position: 'relative',
        bottom: 0
      }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
      text1Style={{
        fontSize: 16,
        fontWeight: '500'
      }}
      text2Style={{
        fontSize: 14
      }}
    />
  )
};

export function Toast() {
  return (
    <RNToast 
      config={toastConfig} 
      position="bottom"
      bottomOffset={80}  // Adjust this value based on your needs
      visibilityTime={3000}
    />
  );
}