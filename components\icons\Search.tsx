import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Search = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      clipPath="url(#Search-1_svg__a)"
    >
      <Path
        stroke="url(#Search-1_svg__b)"
        d="M5.308 9.115a3.808 3.808 0 1 0 0-7.615 3.808 3.808 0 0 0 0 7.615"
      />
      <Path stroke="url(#Search-1_svg__c)" d="M10.5 10.5 8.077 8.077" />
    </G>
    <Defs>
      <LinearGradient
        id="Search-1_svg__b"
        x1={5.308}
        x2={5.308}
        y1={0.581}
        y2={10.866}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <LinearGradient
        id="Search-1_svg__c"
        x1={9.289}
        x2={9.289}
        y1={7.784}
        y2={11.057}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Search-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Search_svg__a)"
    >
      <Path d="M5.23 9.462A4.23 4.23 0 1 0 5.23 1a4.23 4.23 0 0 0 0 8.462M11 11 8.308 8.308" />
    </G>
    <Defs>
      <ClipPath id="Search_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Search;
