import React from 'react';
import { View, Platform, StyleSheet } from 'react-native';
import { StatusBar as RNStatusBar } from 'react-native';
import { Colors } from '@/constants/Colors';

interface DarkStatusBarProps {
  barStyle?: 'light-content' | 'dark-content';
  backgroundColor?: string;
}

export function DarkStatusBar({ 
  barStyle = 'light-content',
  backgroundColor = Colors.custom.semiTransparentBlack 
}: DarkStatusBarProps) {
  return (
    <>
      {Platform.OS === 'ios' && (
        <View style={styles.statusBarBackground} />
      )}
      
      <RNStatusBar 
        barStyle={barStyle}
        translucent={true}
        backgroundColor={Platform.OS === 'android' ? backgroundColor : 'transparent'}
      />
    </>
  );
}

const styles = StyleSheet.create({
  statusBarBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 47,
    backgroundColor: Colors.custom.semiTransparentBlack,
    zIndex: 999,
  },
});