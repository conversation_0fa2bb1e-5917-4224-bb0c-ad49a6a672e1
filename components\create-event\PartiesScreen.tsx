import React from 'react';
import { View } from 'react-native';
import { Text, Chip } from 'react-native-paper';
import { useCreateEvent } from '../../app/(home)/CreateEvent/CreateEventContext';
import { Party } from '../../app/(home)/CreateEvent/CreateEvent.models';
import { BottomSheetModal } from '@gorhom/bottom-sheet';

interface PartiesScreenProps {
  selectedParties: Party[];
  onPartiesChange: (parties: Party[]) => void;
  cohostSheetRef: React.RefObject<BottomSheetModal>;  
}

export function PartiesScreen({ selectedParties, onPartiesChange, cohostSheetRef }: PartiesScreenProps) {
  const { eventData } = useCreateEvent();
  const availablePartyTypes = eventData.eventType?.partyTypes || [];

  const toggleParty = (party: Party) => {
    const isSelected = selectedParties.some(p => p.id === party.id);
    if (isSelected) {
      onPartiesChange(selectedParties.filter(p => p.id !== party.id));
    } else {
      onPartiesChange([...selectedParties, party]);
    }
  };

  return (
    <View style={{ padding: 16 }}>
      <Text variant="headlineMedium" style={{ marginBottom: 16 }}>
        Select Parties
      </Text>
      
      {availablePartyTypes.length > 0 ? (
        <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8 }}>
          {availablePartyTypes.map((party) => (
            <Chip
              key={party.id}
              selected={selectedParties.some(p => p.id === party.id)}
              onPress={() => toggleParty(party)}
              style={{ marginBottom: 4 }}
            >
              {party.name}
            </Chip>
          ))}
        </View>
      ) : (
        <View style={{ alignItems: 'center', padding: 20 }}>
          <Text variant="bodyLarge" style={{ textAlign: 'center', color: 'gray' }}>
            No party types available for this event type.
          </Text>
          <Text variant="bodyMedium" style={{ textAlign: 'center', color: 'gray', marginTop: 8 }}>
            Please contact the administrator to add party types.
          </Text>
        </View>
      )}
    </View>
  );
}