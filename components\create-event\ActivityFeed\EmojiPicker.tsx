import React from 'react';
import { View, StyleSheet } from 'react-native';
import {StatusBar} from 'expo-status-bar'
import EmojiPicker from 'rn-emoji-picker';
import {emojis} from "rn-emoji-picker/dist/data"
import { Emoji } from 'rn-emoji-picker/dist/interfaces';
import { Colors, Icons } from '@/constants/DesignSystem';
import { Emoji as EmojiIcon } from '@/components/icons';

interface EmojiPickerProps {
  onEmojiSelected: (emoji: Emoji) => void;
  visible: boolean;
  onClose: () => void;
}

const CustomEmojiPicker: React.FC<EmojiPickerProps> = ({ onEmojiSelected, visible, onClose }) => {
  if (!visible) return null;

  return (
    <View style={styles.container}>
      <EmojiIcon size={Icons.size.md} color={Colors.primary}/>
    </View>
  );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#000',
        paddingTop: 50
    },
});

export default CustomEmojiPicker;