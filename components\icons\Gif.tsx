import * as React from "react";
import Svg, { <PERSON>, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Gif = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path
      fill="url(#GIF-1_svg__a)"
      fillRule="evenodd"
      d="M9.518 2.147c1.095 0 1.982.887 1.982 1.982v3.4a1.98 1.98 0 0 1-1.982 1.982H2.482A1.98 1.98 0 0 1 .5 7.529v-3.4c0-1.095.887-1.982 1.982-1.982zm-2.88 5.547A.396.396 0 0 0 6.64 6.9h-.338V4.757h.338a.396.396 0 0 0 0-.793H5.171a.396.396 0 1 0 0 .793h.337v2.144h-.337a.396.396 0 1 0 0 .793h1.468m-4.389-2.6c0-.186.152-.337.338-.337h.49c.146 0 .272.094.318.225a.396.396 0 0 0 .748-.264 1.13 1.13 0 0 0-1.066-.754h-.49a1.13 1.13 0 0 0-1.13 1.13v1.47c0 .624.506 1.13 1.13 1.13h.49a1.13 1.13 0 0 0 1.13-1.13v-.49a.396.396 0 0 0-.396-.397h-.734a.396.396 0 1 0 0 .793h.337v.093a.34.34 0 0 1-.337.338h-.49a.34.34 0 0 1-.338-.338zm5.544-.733c0-.22.177-.397.396-.397h1.958a.396.396 0 1 1 0 .793H8.586v.553h1.316a.396.396 0 1 1 0 .793H8.586v1.194a.396.396 0 0 1-.793 0z"
      clipRule="evenodd"
    />
    <Defs>
      <LinearGradient
        id="GIF-1_svg__a"
        x1={6}
        x2={6}
        y1={1.258}
        y2={11.204}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#GIF_svg__a)"
    >
      <Path d="M10.6 7.106V3.775c0-.858-.695-1.554-1.553-1.554H2.154C1.296 2.221.6 2.917.6 3.775v3.331c0 .858.696 1.553 1.554 1.553h6.893c.858 0 1.553-.695 1.553-1.553M4.824 4.077h1.363m0 2.727H4.824M5.505 4.077v2.727" />
      <Path d="M3.082 5.668h.682v.454a.68.68 0 0 1-.682.682h-.455a.68.68 0 0 1-.681-.682V4.76c0-.377.305-.682.681-.682h.455c.297 0 .55.19.643.455M7.452 6.804V4.077H9.27M7.452 5.327h1.59" />
    </G>
    <Defs>
      <ClipPath id="GIF_svg__a">
        <Path fill="#fff" d="M0 0h11.2v11.2H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Gif;
