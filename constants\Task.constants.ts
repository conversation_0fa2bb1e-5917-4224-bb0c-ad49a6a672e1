export const TaskNames = {
    TASK_NAME: 'Task Name',
    TASK_CREATED: 'Task created successfully',
    TASK_CREATION_FAILED: 'Failed to create task',
    USER_NOT_AUTHENTICATED: 'User not authenticated',
    FETCH_PARTIES_ERROR: 'Failed to fetch parties. Please try again.',
    FETCH_ASSIGNEES_ERROR: 'Failed to fetch assignees. Please try again.',
    UNEXPECTED_ERROR: 'An unexpected error occurred. Check console for details.',
    TASK_NAME_REQUIRED: 'Task name is required',
    PARTY_REQUIRED: 'Party is required',
    SAVE_BUTTON_LABEL: 'Save',
    TASK_UPDATED: 'Task updated successfully',
    TASK_UPDATE_FAILED: 'Failed to update task',
};

export const TaskStatus = {
    IN_PROGRESS: 'IN_PROGRESS',
    CLOSED: 'CLOSED',
    COMPLETED: 'COMPLETED',
} as const;

export const TaskPagination = {
    DEFAULT_LIMIT: 100,
    DEFAULT_SKIP: 0,
} as const;

export const TaskFormLayout = {
    KEYBOARD_OFFSET_IOS: 80,
    KEYBOARD_OFFSET_ANDROID: 100,
    BOTTOM_SHEET_SNAP_POINTS: ['90%', '90%'],
    BOTTOM_SHEET_MARGIN_BOTTOM: '10%',
    BOTTOM_SHEET_PADDING_BOTTOM: '10%',
    BOTTOM_SHEET_MARGIN_HORIZONTAL: '4%',
} as const;

export const TaskValidation = {
    TASK_NAME_MIN_LENGTH: 1,
    TASK_NAME_MAX_LENGTH: 100,
} as const;