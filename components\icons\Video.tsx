import * as React from "react";
import Svg, {
  Path,
  Defs,
  LinearGradient,
  Stop,
} from "react-native-svg";
import { CommonProps } from ".";

const Video = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <Path d="M10.7008 3.37503L8.42307 4.38465V3.17311C8.42307 2.95889 8.33797 2.75346 8.1865 2.60198C8.03503 2.45052 7.82959 2.36542 7.61538 2.36542H1.55769C1.34348 2.36542 1.13804 2.45052 0.986567 2.60198C0.835096 2.75346 0.75 2.95889 0.75 3.17311V8.82695C0.75 9.04115 0.835096 9.24663 0.986567 9.39807C1.13804 9.54951 1.34348 9.63464 1.55769 9.63464H7.61538C7.82959 9.63464 8.03503 9.54951 8.1865 9.39807C8.33797 9.24663 8.42307 9.04115 8.42307 8.82695V7.61541L10.7008 8.62503C10.7621 8.64869 10.8285 8.65709 10.8938 8.64926C10.9591 8.6415 11.0217 8.61792 11.0758 8.58044C11.1299 8.54305 11.1741 8.49297 11.2044 8.43457C11.2348 8.37617 11.2505 8.31123 11.25 8.24541V3.75465C11.2505 3.68883 11.2348 3.62389 11.2044 3.56549C11.1741 3.5071 11.1299 3.457 11.0758 3.41958C11.0217 3.38216 10.9591 3.35854 10.8938 3.35077C10.8285 3.34301 10.7621 3.35133 10.7008 3.37503Z" fill="url(#Video-1_svg__a)" stroke="url(#Video-1_svg__b)" strokeLinecap="round" strokeLinejoin="round"/>
          <Defs>
            <LinearGradient id="Video-1_svg__a" x1="6" y1="1.4881" x2="6" y2="11.3057" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
            <LinearGradient id="Video-1_svg__b" x1="6" y1="1.4881" x2="6" y2="11.3057" gradientUnits="userSpaceOnUse">
              <Stop stopColor={gradientStartColor}/>
              <Stop offset="1" stopColor={gradientEndColor}/>
            </LinearGradient>
          </Defs>
        </>
      ) : (
        <Path d="M10.7008 3.37503L8.42307 4.38465V3.17311C8.42307 2.95889 8.33797 2.75346 8.1865 2.60198C8.03503 2.45052 7.82959 2.36542 7.61538 2.36542H1.55769C1.34348 2.36542 1.13804 2.45052 0.986567 2.60198C0.835096 2.75346 0.75 2.95889 0.75 3.17311V8.82695C0.75 9.04115 0.835096 9.24663 0.986567 9.39807C1.13804 9.54951 1.34348 9.63464 1.55769 9.63464H7.61538C7.82959 9.63464 8.03503 9.54951 8.1865 9.39807C8.33797 9.24663 8.42307 9.04115 8.42307 8.82695V7.61541L10.7008 8.62503C10.7621 8.64869 10.8285 8.65709 10.8938 8.64926C10.9591 8.6415 11.0217 8.61792 11.0758 8.58044C11.1299 8.54305 11.1741 8.49297 11.2044 8.43457C11.2348 8.37617 11.2505 8.31123 11.25 8.24541V3.75465C11.2505 3.68883 11.2348 3.62389 11.2044 3.56549C11.1741 3.5071 11.1299 3.457 11.0758 3.41958C11.0217 3.38216 10.9591 3.35854 10.8938 3.35077C10.8285 3.34301 10.7621 3.35133 10.7008 3.37503Z" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
      )}
    </Svg>
  );
};
export default Video;
