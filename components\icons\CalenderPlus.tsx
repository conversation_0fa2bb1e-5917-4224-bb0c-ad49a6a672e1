import * as React from "react";
import Svg, {
  G,
  <PERSON>,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Rect,
} from "react-native-svg";
import { CommonProps } from ".";

const CalenderPlus = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#CalenderPlus-1_svg__a)">
            <Path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M3.25 0.5C3.68393 0.5 4.03571 0.851776 4.03571 1.28571V2.07143H7.96436V1.28571C7.96436 0.851776 8.31614 0.5 8.75008 0.5C9.18403 0.5 9.53579 0.851776 9.53579 1.28571V2.07143H10.3214C10.9723 2.07143 11.5 2.59909 11.5 3.25V10.3214C11.5 10.9723 10.9723 11.5 10.3214 11.5H1.67857C1.02766 11.5 0.5 10.9723 0.5 10.3214V3.25C0.5 2.59909 1.02766 2.07143 1.67857 2.07143H2.46429V1.28571C2.46429 0.851776 2.81607 0.5 3.25 0.5ZM3.44643 6.78581C3.44643 6.46036 3.71026 6.19652 4.03571 6.19652H5.41071V4.82152C5.41071 4.49607 5.67455 4.23224 6 4.23224C6.32545 4.23224 6.58929 4.49607 6.58929 4.82152V6.19652H7.96429C8.28974 6.19652 8.55357 6.46036 8.55357 6.78581C8.55357 7.11127 8.28974 7.37509 7.96429 7.37509H6.58929V8.75008C6.58929 9.07552 6.32545 9.33936 6 9.33936C5.67455 9.33936 5.41071 9.07552 5.41071 8.75008V7.37509H4.03571C3.71026 7.37509 3.44643 7.11127 3.44643 6.78581Z"
              fill="url(#CalenderPlus-1_svg__b)"
            />
          </G>
          <Defs>
            <LinearGradient
              id="CalenderPlus-1_svg__b"
              x1="6"
              y1="-0.827586"
              x2="6"
              y2="14.0287"
              gradientUnits="userSpaceOnUse"
            >
              <Stop stopColor={gradientStartColor} />
              <Stop offset="1" stopColor={gradientEndColor} />
            </LinearGradient>
            <ClipPath id="CalenderPlus-1_svg__a">
              <Rect width={12} height={12} fill="#fff" />
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G clipPath="url(#CalenderPlus_svg__a)">
            <Path d="M1.55769 1.96155C1.34348 1.96155 1.13804 2.04665 0.986567 2.19811C0.835096 2.34959 0.75 2.55502 0.75 2.76924V10.4423C0.75 10.6565 0.835096 10.862 0.986567 11.0134C1.13804 11.1649 1.34348 11.25 1.55769 11.25H10.4423C10.6565 11.25 10.862 11.1649 11.0134 11.0134C11.1649 10.862 11.25 10.6565 11.25 10.4423V2.76924C11.25 2.55502 11.1649 2.34959 11.0134 2.19811C10.862 2.04665 10.6565 1.96155 10.4423 1.96155H8.82692" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M3.1731 0.75V3.17308" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M8.8269 0.75V3.17308" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M3.1731 1.96155H7.21156" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M8.0193 6.80774H3.98083" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
            <Path d="M6 4.78845V8.82691" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round"/>
          </G>
          <Defs>
            <ClipPath id="CalenderPlus_svg__a">
              <Rect width="12" height="12" fill="#fff" />
            </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  );
};
export default CalenderPlus;
