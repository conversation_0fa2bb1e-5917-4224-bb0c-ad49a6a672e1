import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, Pressable, KeyboardAvoidingView, Platform, Image, Linking, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useLazyQuery } from '@apollo/client';
import { GET_TRAFFIC_INFO, GET_WEATHER_INFO, GET_GIFT_SUGGESTIONS, GET_OUTFIT_SUGGESTIONS } from '@/graphql/queries/AskAntsy';
import * as Location from 'expo-location';
import { useToast } from '@/components/Toast';
import { useVenueInfo } from '@/hooks/useVenueInfo';
import { Spacing, Colors, Typography, Borders } from '@/constants/DesignSystem';

interface AskAntsyModalProps {
  isVisible: boolean;
  onClose: () => void;
  partyId: string;
  onQueryResponse: (response: { data: string | null; loading: boolean; type?: string }) => void;
}

const AskAntsyModal: React.FC<AskAntsyModalProps> = ({
  isVisible,
  onClose,
  partyId,
  onQueryResponse,
}) => {
  const toast = useToast();
  const [getTrafficInfo] = useLazyQuery(GET_TRAFFIC_INFO);
  const [getWeatherInfo] = useLazyQuery(GET_WEATHER_INFO);
  const [getGiftSuggestions] = useLazyQuery(GET_GIFT_SUGGESTIONS);
  const [getDressCode] = useLazyQuery(GET_OUTFIT_SUGGESTIONS);
  const { fetchVenueInfo } = useVenueInfo(partyId);

  const handleEnableLocationServices = async () => {
    if (Platform.OS === 'ios') {
      await Linking.openURL('app-settings:');
    } else {
      await Linking.openSettings();
    }
  };

  async function handleTrafficPress() {
    onClose();
    try {
      const locationEnabled = await Location.hasServicesEnabledAsync();
      if (!locationEnabled) {
        Alert.alert(
          'Location Services Required',
          'Please enable location services to get traffic information.',
          [
            {
              text: 'Cancel',
              style: 'cancel',
            },
            {
              text: 'Open Settings',
              onPress: handleEnableLocationServices,
            },
          ],
          { cancelable: true }
        );
        return;
      }

      const foregroundStatus = await Location.getForegroundPermissionsAsync();

      if (!foregroundStatus.canAskAgain) {
        Alert.alert(
          'Location Permission Required',
          'Please enable location access in your device settings to get traffic information.',
          [
            {
              text: 'Cancel',
              style: 'cancel',
            },
            {
              text: 'Open Settings',
              onPress: handleEnableLocationServices,
            },
          ],
          { cancelable: true }
        );
        return;
      }

      if (foregroundStatus.status !== 'granted') {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(
            'Location Permission Required',
            'To get traffic information, please allow location access in your device settings.',
            [
              {
                text: 'Cancel',
                style: 'cancel',
              },
              {
                text: 'Open Settings',
                onPress: handleEnableLocationServices,
              },
            ],
            { cancelable: true }
          );
          return;
        }
      }

      onQueryResponse({ data: null, loading: true, type: 'traffic' });

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
        mayShowUserSettingsDialog: true,
      });

      if (!location?.coords?.latitude || !location?.coords?.longitude) {
        throw new Error('Invalid location coordinates received');
      }

      const { data, error } = await getTrafficInfo({ 
        variables: { 
          input: { 
            partyId,
            lat: location.coords.latitude,
            lng: location.coords.longitude
          } 
        },
        fetchPolicy: 'network-only'
      });

      if (error) {
        throw new Error('Failed to fetch traffic information');
      }

      if (!data?.traffic?.result?.answer) {
        throw new Error('Invalid response from traffic service');
      }
      
      onQueryResponse({ 
        data: data.traffic.result.answer, 
        loading: false,
        type: 'traffic'
      });
    } catch (error) {
      onQueryResponse({ data: null, loading: false, type: 'traffic' });
      
      if (error instanceof Error) {
        if (error.message.includes('permission')) {
          Alert.alert(
            'Location Permission Required',
            'To get traffic information, please allow location access in your device settings.',
            [
              {
                text: 'Cancel',
                style: 'cancel',
              },
              {
                text: 'Open Settings',
                onPress: handleEnableLocationServices,
              },
            ],
            { cancelable: true }
          );
        } else {
          toast.error('Failed to get traffic information. Please try again.');
        }
      } else {
        toast.error('An unexpected error occurred. Please try again.');
      }
    }
  }

  async function handleWeatherPress() {
    onClose();
    try {
      onQueryResponse({ data: null, loading: true, type: 'weather' });
      const { data } = await getWeatherInfo({ variables: { input: { partyId } }, fetchPolicy: 'network-only' });
      onQueryResponse({ 
        data: data.weather.result.answer, 
        loading: false,
        type: 'weather'
      });
    } catch (error) {
      onQueryResponse({ data: null, loading: false, type: 'weather' });
      toast.error('Failed to get weather information');
    }
  }

  async function handleVenuePress() {
    onClose();
    await fetchVenueInfo(true, onQueryResponse);
  }

  async function handleGiftSuggestionsPress() {
    onClose();
    try {
      onQueryResponse({ data: null, loading: true, type: 'gifts' });
      const { data } = await getGiftSuggestions({ variables: { input: { partyId } }, fetchPolicy: 'network-only' });
      onQueryResponse({ 
        data: data.giftSuggestions.result.answer, 
        loading: false,
        type: 'gifts'
      });
    } catch (error) {
      onQueryResponse({ data: null, loading: false, type: 'gifts' });
      toast.error('Failed to get gift suggestions');
    }
  }

  async function handleDressCodePress() {
    onClose();
    try {
      onQueryResponse({ data: null, loading: true, type: 'dresscode' });
      const { data } = await getDressCode({ variables: { input: { partyId } }, fetchPolicy: 'network-only' });
      onQueryResponse({ 
        data: data.outfitSuggestions.result.answer, 
        loading: false,
        type: 'dresscode'
      });
    } catch (error) {
      onQueryResponse({ data: null, loading: false, type: 'dresscode' });
      toast.error('Failed to get dress code suggestions');
    }
  }



  return (
    <Modal
      visible={isVisible}
      animationType='none'
      transparent={true}
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <Pressable 
        style={styles.overlay} 
        onPress={onClose}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={styles.halfSheetContainer}
        >
          <Pressable 
            style={styles.halfSheet} 
            onPress={(e) => e.stopPropagation()}
          >
            <LinearGradient
              colors={[Colors.tertiary, Colors.primary]}
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
              style={styles.modalHeaderContainer}
            >
              <View style={styles.modalHeader}>
                <Image 
                  source={require('@/assets/images/AskAntsy/antsy.png')} 
                  style={styles.modalAntsyIcon}
                  resizeMode="contain"
                />
                <Text style={styles.modalTitle}>Antsy here, what can i help you with ?</Text>
              </View>
            </LinearGradient>
            <View style={styles.modalBody}>
              <View style={styles.optionsRow}>
                <View style={styles.optionItem}>
                  <TouchableOpacity 
                    onPress={handleTrafficPress}
                    activeOpacity={0.7}
                  >
                    <View style={styles.optionCircle}>
                      <Image 
                        source={require('@/assets/images/AskAntsy/traffic.png')} 
                        style={styles.optionIcon}
                        resizeMode="contain"
                      />
                    </View>
                  </TouchableOpacity>
                  <Text style={styles.optionLabel}>Traffic</Text>
                </View>
                <View style={styles.optionItem}>
                  <TouchableOpacity 
                    onPress={handleWeatherPress}
                    activeOpacity={0.7}
                  >
                    <View style={styles.optionCircle}>
                      <Image 
                        source={require('@/assets/images/AskAntsy/weather.png')} 
                        style={styles.optionIcon}
                        resizeMode="contain"
                      />
                    </View>
                  </TouchableOpacity>
                  <Text style={styles.optionLabel}>Weather</Text>
                </View>
                <View style={styles.optionItem}>
                  <TouchableOpacity 
                    onPress={handleVenuePress}
                    activeOpacity={0.7}
                  >
                    <View style={styles.optionCircle}>
                      <Image 
                        source={require('@/assets/images/AskAntsy/location.png')} 
                        style={styles.optionIcon}
                        resizeMode="contain"
                      />
                    </View>
                  </TouchableOpacity>
                  <Text style={styles.optionLabel}>Venue Details</Text>
                </View>
                <View style={styles.optionItem}>
                  <TouchableOpacity 
                    onPress={handleGiftSuggestionsPress}
                    activeOpacity={0.7}
                  >
                    <View style={styles.optionCircle}>
                      <Image 
                        source={require('@/assets/images/AskAntsy/gift.png')} 
                        style={styles.optionIcon}
                        resizeMode="contain"
                      />
                    </View>
                  </TouchableOpacity>
                  <Text style={styles.optionLabel}>Gift{`\n`}Suggestions</Text>
                </View>
                <View style={styles.optionItem}>
                  <TouchableOpacity 
                    onPress={handleDressCodePress}
                    activeOpacity={0.7}
                  >
                    <View style={styles.optionCircle}>
                      <Image 
                        source={require('@/assets/images/AskAntsy/attire.png')} 
                        style={styles.optionIcon}
                        resizeMode="contain"
                      />
                    </View>
                  </TouchableOpacity>
                  <Text style={styles.optionLabel}>Dress Code</Text>
                </View>
              </View>
            </View>
          </Pressable>
        </KeyboardAvoidingView>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  halfSheetContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  halfSheet: {
    backgroundColor: Colors.background.primary,
    height: '50%',
    overflow: 'hidden',
    borderTopLeftRadius: Borders.radius.lg,
    borderTopRightRadius: Borders.radius.lg,
  },
  modalHeaderContainer: {
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  modalAntsyIcon: {
    width: 48,
    height: 48,
    marginRight: Spacing.sm,
  },
  modalTitle: {
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.md,
    color: Colors.text.secondary,
    fontWeight: Typography.fontWeight.semibold,
    flex: 1,
  },
  modalBody: {
    backgroundColor: Colors.background.primary,
    flex: 1,
    flexWrap: 'wrap',
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.xl,
  },
  optionsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-start',
    marginTop: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  optionItem: {
    alignItems: 'center',
    width: '33%',
    marginBottom: Spacing.xl,
  },
  optionCircle: {
    width: 72,
    height: 72,
    borderRadius: Borders.radius.circle,
    backgroundColor: Colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  optionIcon: {
    width: 60,
    height: 60,
  },
  optionLabel: {
    fontFamily: Typography.fontFamily.primary,
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginTop: Spacing.xs,
  },
});

export default AskAntsyModal; 