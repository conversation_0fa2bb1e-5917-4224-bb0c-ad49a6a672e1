import { Provider as PaperProvider } from 'react-native-paper';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { ArrowLeft } from 'lucide-react-native';
import { PartyDetailsRootList } from './PartyDetailsRootList';
import NewPartyDetailsScreen from '../NewPartyDetailsScreen';
import PartyInviteTemplates from '@/app/(home)/PartyInviteTemplates/PartyInviteTemplates';
import InvitesView from '@/app/Home/Invites/InvitesView';
import SendReminders from '@/components/map-weather/MapData/LiveLocation/SendReminders';
import SendNewInvites from '@/app/Home/Invites/NewInvitesView';
import SaveOrSendInvite from '@/app/Home/Invites/SaveOrSend/SaveOrSend.view';
import AppsView from '@/components/Apps/AppsView';
import { Platform} from 'react-native';
import TasksScreen from '@/app/(home)/(tabs)/tasks';
import CreateTaskRoute from '@/app/(home)/Task/CreateTask';
import RsvpScreen from '@/app/Home/Rsvp/RsvpScreen';
import PhotosAlbumView from '@/app/(home)/photosAlbumView/photosAlbumView';
import LiveLocationMap from '@/components/map-weather/MapData/LiveLocationMap';
import ReactionsView from '@/app/Home/Reactions/ReactionsView';
import TextEditor from '@/components/TextEditors/EditorComponent';

import FlyerScreen from '../FlyerScreen';
const Stack = createNativeStackNavigator<PartyDetailsRootList>();

const PartyDetailsStack = () => {
  return (
    <PaperProvider>
      <Stack.Navigator
        screenOptions={{
          headerTintColor: 'black',
          headerBackButtonDisplayMode: 'minimal',
        }}
      >
        <Stack.Screen
          name="NewPartyDetailsScreen"
          component={NewPartyDetailsScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name='FullScreenMapView'
          component={LiveLocationMap}
          options={{
            title: 'Live Location Tracking',
          }}
        />
        <Stack.Screen
          name='InvitesView'
          component={InvitesView}
          options={{
            title: 'Invite',
          }}
        />
        <Stack.Screen
          name='PartyInviteTemplate'
          component={PartyInviteTemplates}
        />
        <Stack.Screen
          name='SendInvitesView'
          component={SendNewInvites}
          options={{
            title: 'Invite',
          }}
        />
        <Stack.Screen
          name='SaveOrSendInvite'
          component={SaveOrSendInvite}
          options={{
            title: 'Invite',
          }}
        />
        <Stack.Screen
          name="AppsView"
          component={AppsView}
          options={{
            title: 'Apps',
            presentation: 'transparentModal',
            headerShown: false,
          }}
        />
        <Stack.Screen
          name='KeyboardEditor'
          component={TextEditor}
          options={{
            title: '',
            presentation: 'transparentModal',
            headerShown: false,
          }}
        />

        <Stack.Screen
          name="RsvpScreen"
          component={RsvpScreen}
          options={{
            headerShown: false,
            
          }}
        />
        <Stack.Screen
          name={'PhotosAlbumView'}
          component={PhotosAlbumView}
        />
        <Stack.Screen
          name='TasksScreen'
          component={TasksScreen}
        />
        <Stack.Screen
          name='CreateTask'
          component={CreateTaskRoute}
          options={{
            title: 'Create Task',
          }}
        />
        <Stack.Screen
          name='SendReminders'
          component={SendReminders}
          options={{
            title: undefined,
            presentation: 'transparentModal',
          }}
        />
        <Stack.Screen
          name='ReactionsCard'
          component={ReactionsView}
          options={{
            title: 'Apps',
            presentation: 'transparentModal',
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="FlyerScreen"
          component={FlyerScreen}
          options={{
            title: '',
            headerShown: true,
          }}
        />

      </Stack.Navigator>
    </PaperProvider>
  );
};

export default PartyDetailsStack;
