import { create } from 'zustand';

interface VenueDetails {
    isNew: boolean,
    placeId: string;
    name: string;
    title?: string;
    directions?: string;
    venueAddressId?: string
}

interface VenueStore {
  venueDetails: VenueDetails | null;
  setVenueDetails: (details: VenueDetails) => void;
  clearVenueDetails: () => void;
}

export const useVenueStore = create<VenueStore>((set) => ({
  venueDetails: null,
  setVenueDetails: (details) => set({ venueDetails: details }),
  clearVenueDetails: () => set({ venueDetails: null }),
}));