import * as React from "react";
import Svg, { <PERSON>, <PERSON>, Circle, Defs, <PERSON><PERSON><PERSON><PERSON>, LinearGradient, Stop } from "react-native-svg";
import { CommonProps } from ".";

const Calender = ({
  size,
  color,
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' ? (
        <>
          <G clipPath="url(#Calender-1_svg__a)">
            <Path
              fill={hasGradient ? "url(#Calender-1_svg__b)" : color}
              d="M8.75.5c.434 0 .786.352.786.786v.785h.785c.651 0 1.179.528 1.179 1.179v7.071c0 .651-.528 1.179-1.179 1.179H1.68C1.028 11.5.5 10.972.5 10.321V3.25c0-.65.528-1.179 1.179-1.179h.785v-.785a.786.786 0 0 1 1.572 0v.785h3.929v-.785c0-.434.351-.786.785-.786M2.817 5.607a.943.943 0 1 0 0 1.886.943.943 0 0 0 0-1.886m3.261 0a.943.943 0 1 0 0 1.886.943.943 0 0 0 0-1.886m3.26 0a.943.943 0 1 0 .001 1.886.943.943 0 0 0 0-1.886"
            />
          </G>
          <Defs>
            <LinearGradient
              id="Calender-1_svg__b"
              x1={6}
              x2={6}
              y1={-0.828}
              y2={14.029}
              gradientUnits="userSpaceOnUse"
            >
              <Stop stopColor={gradientStartColor} />
              <Stop offset={1} stopColor={gradientEndColor} />
            </LinearGradient>
            <ClipPath id="Calender-1_svg__a">
              <Path fill="#fff" d="M0 0h12v12H0z" />
            </ClipPath>
          </Defs>
        </>
      ) : (
        <>
          <G stroke={color} clipPath="url(#Calender_svg__a)">
            <Path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M1.77 2.154a.77.77 0 0 0-.77.77v7.307a.77.77 0 0 0 .77.769h8.46a.77.77 0 0 0 .77-.77V2.924a.77.77 0 0 0-.77-.77H8.693M3.308 1v2.308M8.692 1v2.308M3.308 2.154h3.846"
            />
            <Circle cx={3.231} cy={6.692} r={0.692} />
            <Circle cx={6} cy={6.692} r={0.692} />
            <Circle cx={8.769} cy={6.692} r={0.692} />
          </G>
          <Defs>
            <ClipPath id="Calender_svg__a">
              <Path fill="#fff" d="M0 0h12v12H0z" />
            </ClipPath>
          </Defs>
        </>
      )}
    </Svg>
  )
};
export default Calender;
