import gql from 'graphql-tag';


export const UPDATE_INVITATION_RSVP  = gql`
mutation UpdateInvitationRSVP(
 $input: InvitationRSVPInput!,
 $updateInvitationRsvpId: ID!, 
 $updateUserInput2: UserUpdateInput!) 
 {
    updateInvitationRSVP(input: $input, id: $updateInvitationRsvpId) {
      ... on InvitationRSVPResponse {
        message
      }
      ... on InvitationRSVPErrorResponse {
        message
      }
    }
    updateUser(input: $updateUserInput2) {
      ... on UserResponse {
        message
      }
    }
  }`