import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { Colors } from '@/constants/Colors';
import { VendorDetails } from '@/constants/displayNames';
interface PriceRangeProps {
    priceRange: string;
}

export function PriceRange({ priceRange }: PriceRangeProps) {
    return (
        <View style={styles.content}>
            <Text style={styles.title}>{VendorDetails.PRICE_RANGE}</Text>
            <Text style={styles.description}>{priceRange}</Text>
        </View>
    );
}

const styles = StyleSheet.create({
    content: {
        paddingTop: '4%',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
        marginTop: 16,
    },
    description: {
        fontSize: 24,
        lineHeight: 24,
        color: Colors.light.textSecondary,
    },
});