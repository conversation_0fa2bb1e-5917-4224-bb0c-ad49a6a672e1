import * as React from "react";
import Svg, { <PERSON>, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Emoji = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Emoji-1_svg__a)">
      <Path
        fill="url(#Emoji-1_svg__b)"
        fillRule="evenodd"
        d="M11.5 6.667a5.5 5.5 0 1 0-11 0 5.5 5.5 0 0 0 11 0m-2.797-1.61c0 .444-.36.804-.803.805h-.002a.805.805 0 0 1-.002-1.61h.002c.445 0 .805.36.805.805m-3.796 0c0 .444-.36.804-.803.805h-.002a.805.805 0 0 1-.002-1.61h.002c.445 0 .805.36.805.805M3.792 7.264a.491.491 0 0 0-.948.255 3.271 3.271 0 0 0 6.315 0 .491.491 0 0 0-.948-.255 2.289 2.289 0 0 1-4.419 0"
        clipRule="evenodd"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Emoji-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.161}
        y2={14.695}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Emoji-1_svg__a">
        <Path fill="#fff" d="M0 .667h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Emoji_svg__a)"
    >
      <Path d="M6 11A5 5 0 1 0 6 1a5 5 0 0 0 0 10" />
      <Path d="M3.461 6.77C3.846 8.153 5.385 9 6.77 8.614c.846-.307 1.539-1 1.77-1.846M4.27 4.846a.192.192 0 0 1 0-.384M4.27 4.846a.192.192 0 1 0 0-.384M7.73 4.846a.192.192 0 1 1 0-.384M7.73 4.846a.192.192 0 1 0 0-.384" />
    </G>
    <Defs>
      <ClipPath id="Emoji_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Emoji;
