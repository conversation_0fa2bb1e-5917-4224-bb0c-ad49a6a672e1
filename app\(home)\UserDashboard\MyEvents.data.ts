import gql from 'graphql-tag';

export const GET_EVENTS_BY_USER_ID = gql`
query EventsResponse($filter: EventFilterInput, $pagination: PaginationInput) {
  getUserEvents(filter: $filter, pagination: $pagination) {
    ... on EventsResponse {
      status
      message
      pagination {
        totalItems
        totalPages
        pageSize
        currentPage
      }
      result {
        events {
          id
          eventType {
            bannerImage
          }
          name
          startDate
          endDate
          status
        }
      }
    }
    ... on EventErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}`

export const GET_ALL_EVENTS = gql `
query EventsResponse($filter: EventFilterInput, $pagination: PaginationInput) {
  getUserEvents(filter: $filter, pagination: $pagination) {
    ... on EventsResponse {
      status
      message
      pagination {
        totalItems
        totalPages
        pageSize
        currentPage
      }
      result {
        events {
          id
          eventType {
            bannerImage
          }
          name
          status
          parties {
            id
            name
            time
          }
        }
      }
    }
    ... on EventErrorResponse {
      status
      message
      errors {
        field
        message
      }
    }
  }
}`
export  const GET_HOME_PAGE_EVENTS = gql`
query GetEvents($filter: EventFilterInput) {
  getEvents(filter: $filter) {
    ... on EventsResponse {
      result {
        events {
          name
          id
          startDate
          endDate
          eventType {
            bannerImage
          }
        }
      }
    }
  }
}
`;