import * as React from "react";
import Svg, { Path, G, Defs, LinearGradient, Stop, ClipPath } from "react-native-svg";
import { CommonProps } from ".";

const Support = ({
  size = 12,
  color = "#000",
  gradientStartColor,
  gradientEndColor,
  variant = 'outline',
  strokeWidth = 1,
  ...props
}: CommonProps) => {
  const hasGradient = !!(gradientStartColor && gradientEndColor);

  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      {...props}
    >
      {variant === 'filled' && hasGradient ? (
        <>
          <G clipPath="url(#Support-1_svg__a)">
      <Path
        fill="url(#Support-1_svg__b)"
        fillRule="evenodd"
        d="M4.833.5a4.333 4.333 0 0 0-3.61 6.73L.646 8.778a.236.236 0 0 0 .262.314l2.03-.365q.303.148.623.246A4.518 4.518 0 0 1 8.979 3.56 4.33 4.33 0 0 0 4.832.5m5.632 4.964a3.536 3.536 0 1 0-.953 5.678l1.58.285a.236.236 0 0 0 .263-.315L10.91 9.92a3.536 3.536 0 0 0-.445-4.455"
        clipRule="evenodd"
      />
    </G>
    <Defs>
      <LinearGradient
        id="Support-1_svg__b"
        x1={6}
        x2={6}
        y1={-0.828}
        y2={14.029}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={gradientStartColor} />
        <Stop offset={1} stopColor={gradientEndColor} />
      </LinearGradient>
      <ClipPath id="Support-1_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      ) : (
        <>
         <G
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#Support_svg__a)"
    >
      <Path d="M7.73 4.462a3.269 3.269 0 0 1 2.724 5.077L11 11l-1.838-.33A3.268 3.268 0 1 1 7.73 4.462" />
      <Path d="M8.2 2.547A4.03 4.03 0 0 0 1 5.039a4 4 0 0 0 .677 2.238L1 9.077l1.63-.292" />
    </G>
    <Defs>
      <ClipPath id="Support_svg__a">
        <Path fill="#fff" d="M0 0h12v12H0z" />
      </ClipPath>
    </Defs>
        </>
      )}
    </Svg>
  );
};
export default Support;
