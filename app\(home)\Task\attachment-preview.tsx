import { useRouter } from 'expo-router';
import { AttachmentPreviewScreen } from '../../../components/Task/Attachments/AttachmentPreviewScreen';
import { Alert, View } from 'react-native';
import { Text } from 'react-native-paper';
import { useAttachmentPreviewStore } from '../../../components/Task/Attachments/attachmentPreview.store';
import { useState, useCallback, useMemo, useRef } from 'react';
import { getMimeType } from '../../../components/Task/Attachments/Attachments.utils';
import * as FileSystem from 'expo-file-system';
import { useLazyQuery, useMutation } from '@apollo/client';
import { GET_DOCUMENT_BY_ID } from './Task.data';
import { useUserStore } from '@/app/auth/userStore';
import { AttachmentFile } from '../../../components/Task/Attachments/Attachments.types';
import { useLocalSearchParams } from 'expo-router';
import { BottomSheetModal, BottomSheetBackdrop, BottomSheetView } from '@gorhom/bottom-sheet';
import * as Sharing from 'expo-sharing';
import { 
  REMOVE_MEDIA_FROM_MEDIA_FOLDER, 
  GET_MEDIA_FOLDER_BY_ID, 
  GET_FAVORITE_MEDIA, 
  GET_ARCHIVES_MEDIA,
  REMOVE_MEDIA_FROM_FAVORITES,
  PERMANENTLY_DELETE_MEDIA
} from '../photosAlbumView/photosAlbum.data';
import * as MediaLibrary from 'expo-media-library';
import { RouteProp } from '@react-navigation/native';
import { useRoute } from '@react-navigation/native';
import { Colors, Typography, Spacing } from '../../../constants/DesignSystem';
import { useToast } from '@/components/Toast/useToast';

export default function AttachmentPreviewRoute() {
  const router = useRouter();
  const { selectedFile, combinedAttachmentsByTask } = useAttachmentPreviewStore();
  const [getDocument, { data }] = useLazyQuery(GET_DOCUMENT_BY_ID);
  const detailsBottomSheetRef = useRef<BottomSheetModal>(null);
  const detailsSnapPoints = useMemo(() => ['35%', '50%'], []);
  const userData = useUserStore((state) => state.userData);
  const [currentFileIndex, setCurrentFileIndex] = useState(0);
  const params = useLocalSearchParams<{ taskId?: string }>();
  const [fileDetails, setFileDetails] = useState({
    name: '',
    type: '',
    size: '',
    uploadedBy: '',
    isBackedUp: true
  });
  const route = useRoute<RouteProp<Record<string, { albumType?: string }>, string>>();
  const { albumType } = route.params || {};
  const [removeMediaFromMediaFolder] = useMutation(REMOVE_MEDIA_FROM_MEDIA_FOLDER);
  const [unfavoriteMedia] = useMutation(REMOVE_MEDIA_FROM_FAVORITES);
  const [permanentlyDeleteMedia] = useMutation(PERMANENTLY_DELETE_MEDIA);
  const toast = useToast();

  // Get all attachments
  const allAttachments = useMemo(() => {
    if (!params.taskId || !combinedAttachmentsByTask[params.taskId]) return [];
    const { existing, uploading } = combinedAttachmentsByTask[params.taskId];
    return [...existing, ...uploading];
  }, [params.taskId, combinedAttachmentsByTask]);

  // Get current active file
  const activeFile = useMemo(() => {
    return currentFileIndex === 0 ? selectedFile : allAttachments[currentFileIndex];
  }, [selectedFile, allAttachments, currentFileIndex]);

  const handleFileChange = useCallback(async (index: number, newFile: AttachmentFile) => {
    // Close details drawer when changing files
    detailsBottomSheetRef.current?.dismiss();
    setCurrentFileIndex(index);
  }, []);

  const handleViewDetails = async () => {
    if (!activeFile) return;

    const fileUri = 'documentUrl' in activeFile ? activeFile.documentUrl : activeFile.uri;
    const fileType = getMimeType(fileUri);
    
    // Skip document API call for photos from photosAlbumView
    if ('documentUrl' in activeFile && activeFile.documentUrl && !activeFile.id?.startsWith('doc_')) {
      const fileSize = 'size' in activeFile ? Number(activeFile.size) : 0;
      const owner = 'owner' in activeFile ? activeFile.owner as { firstName?: string; lastName?: string } : undefined;
      setFileDetails({
        name: activeFile.name,
        type: fileType,
        size: fileSize ? `${(fileSize / (1024 * 1024)).toFixed(2)}mb` : '2mb',
        uploadedBy: owner 
          ? `${owner.firstName || ''} ${owner.lastName || ''}`.trim()
          : userData?.firstName + ' ' + (userData?.lastName || ''),
        isBackedUp: true
      });
      detailsBottomSheetRef.current?.present();
      return;
    }
    
    if('id' in activeFile && activeFile.id) {
      const { data: documentData } = await getDocument({ 
        variables: { 
          getDocumentByIdId: activeFile.id 
        }
      });

      if (documentData?.getDocumentById?.result?.document) {
        const document = documentData.getDocumentById.result.document;
        const createdBy = document.createdBy;
        const uploadedBy = createdBy ? `${createdBy.firstName || ''} ${createdBy.lastName || ''}`.trim() : '';
        
        // For server files (TaskAttachment)
        const fileSize = document.size || 0;
        setFileDetails({
          name: document.name,
          type: fileType,
          size: fileSize ? `${(fileSize / (1024 * 1024)).toFixed(2)}mb` : '2mb',
          uploadedBy,
          isBackedUp: true
        });
        detailsBottomSheetRef.current?.present();
        return;
      }
    }

    // For local files (DocumentPickerAsset)
    const fileSize = 'size' in activeFile ? activeFile.size : 0;
    const owner = 'owner' in activeFile ? activeFile.owner as { firstName?: string; lastName?: string } : undefined;
    setFileDetails({
      name: activeFile.name,
      type: fileType,
      size: fileSize ? `${(fileSize / (1024 * 1024)).toFixed(2)}mb` : '2mb',
      uploadedBy: owner 
        ? `${owner.firstName || ''} ${owner.lastName || ''}`.trim()
        : userData?.firstName + ' ' + (userData?.lastName || ''),
      isBackedUp: true
    });
    detailsBottomSheetRef.current?.present();
  };

  if (!selectedFile) {
    return (
      <View style={{ 
        flex: 1, 
        justifyContent: 'center', 
        alignItems: 'center',
        backgroundColor: Colors.background.secondary 
      }}>
        <Text style={{ 
          color: Colors.text.secondary,
          fontSize: Typography.fontSize.md,
          fontFamily: Typography.fontFamily.primary
        }}>
          No file selected for preview
        </Text>
      </View>
    );
  }

  const handleShare = async () => {
    try {
      const urlString = 'documentUrl' in selectedFile! ? selectedFile!.documentUrl : selectedFile!.uri;
      const originalDisplayNameFromSelection = selectedFile!.name;

      if (urlString.startsWith('file://')) {
        if (!(await Sharing.isAvailableAsync())) {
          Alert.alert('Error', 'Sharing is not available on this device.');
          return;
        }
        await Sharing.shareAsync(urlString, {
          dialogTitle: `Share ${originalDisplayNameFromSelection}`,
          mimeType: getMimeType(urlString),
        });
      } else {
        try {
          let fileNameFromUrl = urlString.substring(urlString.lastIndexOf('/') + 1).split('?')[0];
          
          if (!fileNameFromUrl || fileNameFromUrl.includes('=')) { 
             fileNameFromUrl = originalDisplayNameFromSelection;
          }
          if (!fileNameFromUrl) {
            fileNameFromUrl = "shared_file"; 
          }

          const parts = fileNameFromUrl.split('.');
          const fileExtension = parts.length > 1 ? (parts.pop() || '').toLowerCase() : '';
          
          let baseName = parts.join('.');
          if (baseName === '' && fileNameFromUrl.startsWith('.') && fileExtension !== '') {
          }
          
          const sanitizedBaseName = baseName.replace(/[^a-zA-Z0-9-_]/g, '_') || 'downloaded_file';
          
          const finalFileNameForCache = fileExtension ? `${sanitizedBaseName}.${fileExtension}` : sanitizedBaseName;
          
          const localUri = `${FileSystem.cacheDirectory}${finalFileNameForCache}`;

          const downloadResult = await FileSystem.downloadAsync(urlString, localUri);

          if (downloadResult.status === 200) {
            if (!(await Sharing.isAvailableAsync())) {
              Alert.alert('Error', 'Sharing is not available on this device.');
              return;
            }
            await Sharing.shareAsync(downloadResult.uri, {
              dialogTitle: `Share ${fileNameFromUrl}`,
              mimeType: downloadResult.mimeType || getMimeType(downloadResult.uri),
            });
          } else {
            Alert.alert('Download Failed', `Could not download the file (status: ${downloadResult.status}).`);
          }
        } catch (downloadError: any) {
          Alert.alert('Error Sharing File', downloadError.message || 'An error occurred while preparing the file for sharing.');
        }
      }
    } catch (error: any) {
      const errorMessage = error?.message || 'Could not share the file due to an unexpected error.';
      Alert.alert('Error Sharing File', errorMessage);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete File',
      'Are you sure you want to delete this file?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: async () => {
            if (!selectedFile) {
              toast.error('No file selected for deletion.');
              return;
            }

            if (!('id' in selectedFile && selectedFile.id)) {
              toast.success('This is a local file and not on the server. Removing from preview.');
              router.back();
              return;
            }

            const mediaIdToDelete = selectedFile.id;

            if (!params.taskId) {
              toast.error('Cannot delete file: Context (Task ID) for folder is missing.');
              return;
            }
            const folderId = params.taskId;

            try {
              if (albumType === 'FAVORITES') {
                // For favorites album, use unfavorite mutation
                const response = await unfavoriteMedia({
                  variables: {
                    unfavoriteMediaId: mediaIdToDelete
                  }
                });

                if (response.errors) {
                  toast.error('Failed to remove photo from favorites. Please try again.');
                  return;
                }
                router.replace({
                  pathname: '/(home)/photosAlbumView/photosAlbumView',
                  params: { 
                    albumType: 'FAVORITES',
                    deleteSuccess: 'true',
                    message: 'Photo removed from favorites successfully.'
                  }
                });
              } else if (albumType === 'DELETED') {
                // For deleted album, use permanent delete mutation
                const response = await permanentlyDeleteMedia({
                  variables: {
                    deleteMediaId: mediaIdToDelete
                  }
                });

                const errors = response.data?.deleteMedia?.errors;
                if (errors?.length > 0) {
                  const errorMessage = errors[0].message || 'Failed to delete photo';
                  toast.error(errorMessage);
                  return;
                }
                router.replace({
                  pathname: '/(home)/photosAlbumView/photosAlbumView',
                  params: { 
                    albumType: 'DELETED',
                    deleteSuccess: 'true',
                    message: 'Photo permanently deleted successfully.'
                  }
                });
              } else {
                // For regular albums, use removeMediaFromFolder mutation
                const response = await removeMediaFromMediaFolder({
                  variables: {
                    mediaId: mediaIdToDelete,
                    mediaFolderId: folderId
                  }
                });

                const errors = response.data?.removeMediaFromMediaFolder?.errors;
                const authError = errors?.find((error: { field: string }) => error.field === 'authorization');

                if (authError) {
                  toast.error('You are not authorized to delete this file.');
                  return;
                }
                router.replace({
                  pathname: '/(home)/photosAlbumView/photosAlbumView',
                  params: { 
                    albumId: folderId,
                    deleteSuccess: 'true',
                    message: 'Photo deleted successfully.'
                  }
                });
              }
            } catch (error: any) {
              console.error(`Exception during delete for mediaId ${mediaIdToDelete}:`, error);
              const displayMessage = error.message || 'An unexpected error occurred. Please check your connection and try again.';
              toast.error(displayMessage);
            }
          },
          style: 'destructive',
        },
      ],
      { cancelable: true }
    );
  };

  const handleDownload = async () => {
    if (!selectedFile) return;

    try {
      const urlString = 'documentUrl' in selectedFile ? selectedFile.documentUrl : selectedFile.uri;
      if (!urlString) {
        Alert.alert('Error', 'No valid URL found for download');
        return;
      }
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Permission to access media library was denied');
        return;
      }

      let fileExtension = '';
      if (selectedFile.name) {
        const parts = selectedFile.name.split('.');
        fileExtension = parts.length > 1 ? `.${parts.pop()}` : '';
      } else if (urlString.includes('.')) {
        const parts = urlString.split('.');
        fileExtension = `.${parts.pop()?.split('?')[0]}`;
      }

      if (!fileExtension) {
        const mimeType = getMimeType(urlString);
        if (mimeType) {
          const mimeToExt: { [key: string]: string } = {
            'image/jpeg': '.jpg',
            'image/png': '.png',
            'image/gif': '.gif',
            'application/pdf': '.pdf',
            'application/msword': '.doc',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
            'application/vnd.ms-excel': '.xls',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
            'text/plain': '.txt',
          };
          fileExtension = mimeToExt[mimeType] || '.file';
        } else {
          fileExtension = '.file';
        }
      }

      const baseName = selectedFile.name?.split('.')[0] || 'downloaded_file';
      const filename = `${baseName}${fileExtension}`;
      const fileUri = `${FileSystem.documentDirectory}${filename}`;
      
      const downloadResult = await FileSystem.downloadAsync(urlString, fileUri);
      
      if (downloadResult.status === 200) {
        await MediaLibrary.saveToLibraryAsync(downloadResult.uri);
        await FileSystem.deleteAsync(fileUri, { idempotent: true });
        Alert.alert('Success', 'File downloaded successfully!');
      } else {
        Alert.alert('Download Failed', 'Could not download the file');
      }
    } catch (error: any) {
      console.error('Download error:', error);
      Alert.alert('Error', error.message || 'Failed to download file');
    }
  };

  const renderFileDetails = () => (
    <View style={{ 
      paddingVertical: Spacing.lg, 
      paddingHorizontal: Spacing.sm,
      backgroundColor: Colors.background.secondary 
    }}>
      <View style={{ gap: Spacing.sm }}>
        <View style={{ 
          flexDirection: 'row', 
          rowGap: Spacing.sm, 
          justifyContent: 'flex-start' 
        }}>
          <Text style={{ 
            color: Colors.text.secondary,
            fontSize: Typography.fontSize.lg,
            fontFamily: Typography.fontFamily.primary
          }}>Name: </Text>
          <Text style={{ 
            color: Colors.text.secondary,
            fontSize: Typography.fontSize.lg,
            fontFamily: Typography.fontFamily.primary
          }}>{fileDetails.name}</Text>
        </View>

        <View style={{ 
          flexDirection: 'row', 
          rowGap: Spacing.sm, 
          justifyContent: 'flex-start' 
        }}>
          <Text style={{ 
            color: Colors.text.secondary,
            fontSize: Typography.fontSize.lg,
            fontFamily: Typography.fontFamily.primary
          }}>Type: </Text>
          <Text style={{ 
            color: Colors.text.secondary,
            fontSize: Typography.fontSize.lg,
            fontFamily: Typography.fontFamily.primary
          }}>{fileDetails.type}</Text>
        </View>

        <View style={{ 
          flexDirection: 'row', 
          rowGap: Spacing.sm, 
          justifyContent: 'flex-start' 
        }}>
          <Text style={{ 
            color: Colors.text.secondary,
            fontSize: Typography.fontSize.lg,
            fontFamily: Typography.fontFamily.primary
          }}>Size: </Text>
          <Text style={{ 
            color: Colors.text.secondary,
            fontSize: Typography.fontSize.lg,
            fontFamily: Typography.fontFamily.primary
          }}>{fileDetails.size}</Text>
        </View>

        <View style={{ 
          flexDirection: 'row', 
          rowGap: Spacing.sm, 
          justifyContent: 'flex-start' 
        }}>
          <Text style={{ 
            color: Colors.text.secondary,
            fontSize: Typography.fontSize.lg,
            fontFamily: Typography.fontFamily.primary
          }}>Uploaded by: </Text>
          <Text style={{ 
            color: Colors.text.secondary,
            fontSize: Typography.fontSize.lg,
            fontFamily: Typography.fontFamily.primary
          }}>{fileDetails.uploadedBy}</Text>
        </View>
      </View>
    </View>
  );

  return (
    <>{albumType === 'DELETED' ? (
      <AttachmentPreviewScreen
      file={selectedFile}
      onDelete={handleDelete}
      onViewDetails={handleViewDetails}
      onFileChange={handleFileChange}
    />
    ) : (
      <AttachmentPreviewScreen
      file={selectedFile}
      onDelete={handleDelete}
      onShare={handleShare}
      onViewDetails={handleViewDetails}
      onFileChange={handleFileChange}
      onDownload={handleDownload}
    />
    )
    }
     
     
    
     
      <BottomSheetModal
        ref={detailsBottomSheetRef}
        index={1}
        snapPoints={detailsSnapPoints}
        enablePanDownToClose
        backdropComponent={(props) => (
          <BottomSheetBackdrop
            {...props}
            appearsOnIndex={1}
            disappearsOnIndex={0}
          />
        )}
      >
        <BottomSheetView
          style={{
            flex: 1,
            marginBottom: "0%",
            paddingBottom: "0%",
            marginHorizontal: Spacing.md,
            backgroundColor: Colors.background.primary
          }}
        >
          {renderFileDetails()}
        </BottomSheetView>
      </BottomSheetModal>
     
    </>
  );
} 