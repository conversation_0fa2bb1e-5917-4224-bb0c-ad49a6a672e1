import Toast from 'react-native-toast-message';

export interface ToastOptions {
  duration?: number;
  position?: 'top' | 'bottom';
}

export interface ToastInstance {
  success: (message: string, options?: ToastOptions) => void;
  error: (message: string, options?: ToastOptions) => void;
}

export function useToast(): ToastInstance {
  const defaultOptions: ToastOptions = {
    duration: 3000,
    position: 'bottom'
  };

  const show = (type: 'success' | 'error', message: string, options?: ToastOptions) => {
    Toast.show({
      type,
      text1: type === 'success' ? 'Success' : 'Error',
      text2: message,
      ...defaultOptions,
      ...options,
    });
  };

  return {
    success: (message: string, options?: ToastOptions) => show('success', message, options),
    error: (message: string, options?: ToastOptions) => show('error', message, options),
  };
}