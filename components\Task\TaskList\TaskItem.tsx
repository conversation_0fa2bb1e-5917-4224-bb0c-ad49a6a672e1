import { View, Pressable } from 'react-native';
import { Surface, Text, Portal, Dialog } from 'react-native-paper';
import { useState, useEffect } from 'react';
import { formatDate, isPast } from 'date-fns';
import { Colors } from '@/constants/Colors';
import { styles } from '@/app/(home)/Task/tasks.styles';
import { Task } from './types';
import { Swipeable } from 'react-native-gesture-handler';
import { StyleSheet } from 'react-native';
import { useIsFocused } from '@react-navigation/native';
import { useMutation } from '@apollo/client';
import { UPDATE_TASK } from '@/app/(home)/Task/Task.data';
import { DateTimePickerModal } from '@/components/UI/ReusableComponents/DateTimePickerModal';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';
import { getAssigneeName } from '@/app/(home)/utils/reusableFunctions';
import { Icons, Colors as ColorsDesignSystem } from '@/constants/DesignSystem';
import { DueDate, Delete, Check, Circle } from '@/components/icons';

interface TaskItemProps {
  task: Task;
  onPress?: () => void;
  onDelete?: (taskId: string) => void;
  refetchTask?: () => Promise<any>;
}

const swipeStyles = StyleSheet.create({
  deleteContainer: {
    backgroundColor: '#BF2231',
    width: 80,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  }
});

export function TaskItem({ task, onPress, onDelete, refetchTask }: TaskItemProps) {
  const [updateTask] = useMutation(UPDATE_TASK);
  const [taskStatus, setTaskStatus] = useState(task.status);
  const [isOverdue, setIsOverdue] = useState(
    task.dueDate ? isPast(new Date(task.dueDate)) && task.status === 'IN_PROGRESS' : false
  );
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const isFocused = useIsFocused();
  const [tempDate, setTempDate] = useState<Date>(
    task.dueDate ? new Date(task.dueDate) : new Date()
  );
  const [isLoading, setIsLoading] = useState(false);
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);

  useEffect(() => {
    if (!task.dueDate || task.status !== 'IN_PROGRESS') return;

    const checkOverdueStatus = () => {
      if (!task.dueDate) return;

      const dueDate = new Date(task.dueDate);
      const isPastDue = isPast(dueDate);
      setIsOverdue(isPastDue);
    };

    checkOverdueStatus();

    const interval = setInterval(checkOverdueStatus, 60000);

    return () => clearInterval(interval);
  }, [task.dueDate, task.status]);

  useEffect(() => {
    if (isFocused) {
      setTaskStatus(task.status);
    }
  }, [isFocused, task.status]);

  const handleDeletePress = () => {
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = () => {
    setShowDeleteDialog(false);
    onDelete?.(task.id);
  };

  const handleDeleteCancel = () => {
    setShowDeleteDialog(false);
  };

  const renderRightActions = () => {
    return (
      <Pressable
        style={swipeStyles.deleteContainer}
        onPress={handleDeletePress}
      >
        <Delete size={24} color={ColorsDesignSystem.text.primary} />
      </Pressable>
    );
  };

  const handleStatusChange = async () => {
    const newStatus = taskStatus === 'IN_PROGRESS' ? 'COMPLETED' : 'IN_PROGRESS';

    try {
      await updateTask({
        variables: {
          updateTaskId: task.id,
          input: {
            status: newStatus,
          }
        },
        update: (cache) => {
          cache.modify({
            id: cache.identify({ __typename: 'Task', id: task.id }),
            fields: {
              status: () => newStatus
            }
          });
        }
      });

      setTaskStatus(newStatus);
      if (refetchTask) {
        await refetchTask();
      }
    } catch (error) {
      console.error('Failed to update task status:', error);
    }
  };

  const isCheckboxEnabled = ['IN_PROGRESS', 'COMPLETED', 'CLOSED'].includes(taskStatus);

  const getStatusColor = (status: string) => {
    if (isOverdue && status === 'IN_PROGRESS') {
      return Colors.light.taskStatusOverdue;
    }

    if (isOverdue && status === 'IN_PROGRESS') {
      return Colors.light.taskStatusOverdue;
    }

    switch (status) {
      case 'IN_PROGRESS':
        return Colors.light.taskStatusInProgress;
      case 'COMPLETED':
        return Colors.light.taskStatusCompleted;
      case 'CLOSED':
        return Colors.light.taskStatusClosed;
      default:
        return Colors.light.taskCheckboxUnchecked;
    }
  };

  const getCheckboxColor = (status: string, isChecked: boolean) => {
    if (!isChecked) {
      return Colors.light.taskCheckboxUnchecked;
    }
    return getStatusColor(status);
  };

  const isChecked = taskStatus === 'CLOSED' || taskStatus === 'COMPLETED';

  const getCheckboxStyle = (status: string) => {
    if (status === 'COMPLETED') {
      return {
        borderColor: Colors.light.taskCompletedBorder,
        backgroundColor: Colors.light.taskCompletedBackground,
      };
    }
    if (status === 'CLOSED') {
      return {
        borderColor: getStatusColor(status),
        backgroundColor: Colors.light.background,
      };
    }
    return {};
  };

  const handleDueDatePress = () => {
    setTempDate(task.dueDate ? new Date(task.dueDate) : new Date());
    setShowDateTimePicker(true);
  };

  const handleDateTimeChange = async (dateTime: Date) => {
    setIsLoading(true);

    try {
      await updateTask({
        variables: {
          updateTaskId: task.id,
          input: {
            dueDate: dateTime.toISOString(),
          },
        },
        update: (cache) => {
          cache.modify({
            id: cache.identify({ __typename: 'Task', id: task.id }),
            fields: {
              dueDate: () => dateTime.toISOString(),
            },
          });
        },
      });

      if (refetchTask) {
        await refetchTask();
      }
    } catch (error) {
      console.error('Failed to update task due date:', error);
    } finally {
      setIsLoading(false);
      setShowDateTimePicker(false);
    }
  };


  return (
    <>
      <Swipeable
        renderRightActions={renderRightActions}
        rightThreshold={40}
      >
        <Surface style={styles.taskItem} mode="flat">
          <View style={styles.taskMainContent}>
            <Pressable
              onPress={handleStatusChange}
              style={[
                styles.checkboxContainer,
                !isCheckboxEnabled && styles.disabledCheckbox
              ]}
              disabled={!isCheckboxEnabled}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <View style={styles.checkboxContainer}>
                {isChecked ? (
                  <View style={[
                    styles.checkedCircle,
                    getCheckboxStyle(taskStatus)
                  ]}>
                    <Check
                      size={Icons.size.md}
                      color={taskStatus === 'COMPLETED' ? '#0B7B69' : getStatusColor(taskStatus)}
                      style={styles.checkMark}
                    />
                  </View>
                ) : (
                  <Circle
                    size={Icons.size.md}
                    color={getCheckboxColor(taskStatus, isChecked)}
                  />
                )}
              </View>
            </Pressable>

            <Pressable
              onPress={onPress}
              style={[
                styles.taskContent,
                { flex: 1 }
              ]}
              hitSlop={{ top: 10, bottom: 10 }}
            >
              <Text
                style={[
                  styles.taskTitle,
                  taskStatus === 'CLOSED' && styles.closedTaskTitle
                ]}
                numberOfLines={2}
              >
                {task.title}
              </Text>
              {task.assignedTo && task.assignedTo.length > 0 && (
                <View style={styles.taskMeta}>
                  <Text style={[
                    styles.assigneeText,
                    taskStatus === 'CLOSED' && styles.closedAssigneeText
                  ]}>
                    {`${getAssigneeName(task.assignedTo[0])}`}
                  </Text>
                </View>
              )}
            </Pressable>
          </View>

          <Pressable
            onPress={handleDueDatePress}
            style={[styles.dateContainer, {
              minWidth: task.dueDate ? 85 : 26,
            }]}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            {task.dueDate ? (
              <Text style={[
                styles.dateTag,
                { backgroundColor: getStatusColor(taskStatus),
                },
                taskStatus === 'CLOSED' && styles.closedDateTag
              ]}>
                {formatDate(task.dueDate, 'MMM dd, yy')}
              </Text>
            ) : (
              <DueDate
                size={Icons.size.md}
                color={ColorsDesignSystem.text.secondary}
                style={styles.calendarIcon}
              />
            )}
          </Pressable>
        </Surface>
      </Swipeable>

      <Portal>
        <DateTimePickerModal
          visible={showDateTimePicker}
          onDismiss={() => setShowDateTimePicker(false)}
          value={tempDate}
          onChange={(date) => {
            handleDateTimeChange(date);
          }}
          title="Select Due Date & Time"
          minimumDate={new Date()}
        />

        {isLoading && (
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              justifyContent: 'center',
              alignItems: 'center',
              zIndex: 9999,
            }}
          >
            <FastPartyActivityIndicator />
          </View>
        )}

        <Dialog
          visible={showDeleteDialog}
          onDismiss={handleDeleteCancel}
          style={styles.deleteDialog}
        >
          <Dialog.Content
            style={styles.deleteDialogContent}
          >
            <Text style={styles.deleteDialogText}>Do you want to delete task ?</Text>
          </Dialog.Content>
          <Dialog.Actions style={styles.deleteDialogActions}>
            <Pressable
              onPress={handleDeleteConfirm}
              style={styles.deleteYesDialogButton}
            >
              <Text style={styles.deleteYesButtonText}>Yes</Text>
            </Pressable>
            <View style={styles.deleteDialogSeparator} />
            <Pressable
              onPress={handleDeleteCancel}
              style={styles.deleteNoDialogButton}
            >
              <Text style={styles.deleteNoButtonText}>No</Text>
            </Pressable>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </>
  );
}