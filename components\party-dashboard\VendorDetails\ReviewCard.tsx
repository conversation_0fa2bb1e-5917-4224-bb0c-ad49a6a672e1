import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, Pressable } from 'react-native';
import { Star } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';
import { getFormattedDate } from '@/app/(home)/utils/reusableFunctions';

interface Review {
  rating: number;
  comment: string;
  userId: string;
  userName: string;
  userImage: string;
  createdAt: string;
}

interface ReviewCardProps {
  review: Review;
}

export function ReviewCard({ review }: ReviewCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const CHARACTER_LIMIT = 120;
  const shouldTruncate = review.comment.length > CHARACTER_LIMIT;

  const handleCommentPress = () => {
    if (shouldTruncate) {
      setIsExpanded(!isExpanded);
    }
  };

  const displayText = shouldTruncate && !isExpanded
    ? `${review.comment.slice(0, CHARACTER_LIMIT)}`
    : review.comment;

  const renderStars = () => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Star
          key={i}
          color={Colors.light.text}
          size={16}
          fill={i <= review.rating ? Colors.light.text : 'transparent'}
        />
      );
    }
    return stars;
  };

  return (
    <View style={[
      styles.card,
      isExpanded && styles.expandedCard
    ]}>
      <View style={styles.header}>
        <View style={styles.userInfo}>
          {review.userImage && (
            <Image source={{ uri: review.userImage }} style={styles.userImage} />
          )}
          <View style={styles.userTextContainer}>
            <Text style={styles.userName} numberOfLines={2}>{review.userName}</Text>
            <View style={{ flexDirection: 'row', gap: 4, marginTop: 4 }}>
              <View style={styles.starContainer}>{renderStars()}</View>
              <Text style={styles.date}>{getFormattedDate(review.createdAt)}</Text>
            </View>
          </View>
        </View>
      </View>
      <Pressable onPress={handleCommentPress}>
        <Text style={styles.comment}>
          {displayText}
          {shouldTruncate && !isExpanded && (
            <Text style={styles.readMore}> ... Read more</Text>
          )}
        </Text>
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    padding: 16,
    marginRight: 16,
    width: 250,
    minHeight: 180, // Fixed minimum height
    maxHeight: 180, // Same as minHeight when not expanded
    shadowColor: Colors.custom.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  expandedCard: {
    maxHeight: 400, // Maximum height when expanded
    height: 'auto', // Allow content to determine height up to maxHeight
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  userTextContainer: {
    flex: 1,
  },
  userImage: {
    width: 44,
    height: 44,
    borderRadius: 20,
    marginRight: 12,
  },
  userName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: Colors.light.text,
    flexWrap: 'wrap',
  },
  starContainer: {
    flexDirection: 'row',
    gap: 4,
  },
  date: {
    fontSize: 12,
    color: Colors.light.text,
    marginLeft: 4,
    alignSelf: 'center',
  },
  comment: {
    marginTop: 8,
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  readMore: {
    color: Colors.light.tint,
    fontWeight: '600',
  },
});