import { useEffect, useRef, useState } from 'react';
import { EventSourcePolyfill } from 'event-source-polyfill';
import { userStorage } from '@/app/auth/userStorage';
import { Platform } from 'react-native';

/**
 * Configuration options for the EventStream
 */
export interface EventStreamOptions {
  /**
   * Whether to automatically reconnect on connection loss
   * @default true
   */
  autoReconnect?: boolean;
  
  /**
   * Delay in milliseconds before attempting to reconnect
   * @default 3000
   */
  reconnectDelay?: number;
  
  /**
   * Maximum number of reconnection attempts
   * @default 5
   */
  maxReconnectAttempts?: number;
  
  /**
   * Custom headers to include with the request
   */
  headers?: Record<string, string>;
  
  /**
   * Event types to listen for (in addition to the default 'message' event)
   * @default []
   */
  eventTypes?: string[];
  
  /**
   * Whether to include credentials with the request
   * @default true
   */
  withCredentials?: boolean;
}

/**
 * Default options for the EventStream
 */
const DEFAULT_OPTIONS: EventStreamOptions = {
  autoReconnect: true,
  reconnectDelay: 3000,
  maxReconnectAttempts: 5,
  eventTypes: [],
  withCredentials: true,
};

/**
 * Creates an EventSource connection to receive server-sent events
 * 
 * @param url The URL to connect to
 * @param onMessage Callback function to handle incoming messages
 * @param options Configuration options
 * @returns An object with methods to control the connection
 */
export function createEventStream(
  url: string,
  onMessage: (data: any, event?: string) => void,
  options: EventStreamOptions = {}
) {
  // Merge default options with provided options
  const config = { ...DEFAULT_OPTIONS, ...options };
  let eventSource: EventSourcePolyfill | null = null;
  let reconnectAttempts = 0;
  let reconnectTimeout: NodeJS.Timeout | null = null;
  let isConnected = false;
  let isClosed = false;
  
  /**
   * Initialize the EventSource connection
   */
  const connect = async () => {
    if (eventSource || isClosed) return;
    
    try {
      // Get authentication token
      const token = await userStorage.getToken();
      
      // Create headers with authentication
      const headers: Record<string, string> = {
        ...(config.headers || {}),
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      };
      
      // Create EventSource connection
      eventSource = new EventSourcePolyfill(url, {
        withCredentials: config.withCredentials,
        headers,
      });
      
      // Set up event handlers
      eventSource.onopen = () => {
        isConnected = true;
        reconnectAttempts = 0;
      };
      
      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          onMessage(data);
        } catch (error) {
          onMessage(event.data);
        }
      };
      
      // Set up custom event handlers
      if (config.eventTypes && config.eventTypes.length > 0) {
        config.eventTypes.forEach((eventType) => {
          eventSource?.addEventListener(eventType, (event: MessageEvent) => {
            try {
              const data = JSON.parse(event.data);
              onMessage(data, eventType);
            } catch (error) {
              onMessage(event.data, eventType);
            }
          });
        });
      }
      
      eventSource.onerror = (error) => {
        isConnected = false;
        
        // Attempt to reconnect if enabled
        if (config.autoReconnect && !isClosed) {
          if (reconnectAttempts < (config.maxReconnectAttempts || 5)) {
            reconnectTimeout = setTimeout(() => {
              reconnectAttempts++;
              close();
              connect();
            }, config.reconnectDelay);
          }
        }
      };
    } catch (error) {
      console.error('Error creating EventSource connection:', error);
    }
  };
  
  /**
   * Close the EventSource connection
   */
  const close = () => {
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout);
      reconnectTimeout = null;
    }
    
    if (eventSource) {
      eventSource.close();
      eventSource = null;
    }
    
    isConnected = false;
  };
  
  /**
   * Permanently close the connection and prevent reconnection
   */
  const destroy = () => {
    isClosed = true;
    close();
  };
  
  return {
    connect,
    close,
    destroy,
    isConnected: () => isConnected,
  };
}

/**
 * React hook for using server-sent events in components
 * 
 * @param resourceId The resource ID to listen for events on
 * @param onEvent Callback function to handle incoming events
 * @param options Configuration options
 */
export function useEventStream(
  resourceId: string,
  onEvent: (data: any, event?: string) => void,
  options: EventStreamOptions = {}
) {
  const [isConnected, setIsConnected] = useState(false);
  const eventStreamRef = useRef<ReturnType<typeof createEventStream> | null>(null);
  
  useEffect(() => {
    if (!resourceId) return;
    
    // Create the base URL for the event stream
    const baseUrl = process.env.EXPO_PUBLIC_API_URL || '';
    const eventStreamUrl = `${baseUrl}/events/${resourceId}`;
    
    // Create the event stream
    const eventStream = createEventStream(
      eventStreamUrl,
      onEvent,
      options
    );
    
    eventStreamRef.current = eventStream;
    
    // Connect to the event stream
    eventStream.connect();
    
    // Update connection status
    const intervalId = setInterval(() => {
      setIsConnected(eventStream.isConnected());
    }, 1000);
    
    // Clean up on unmount
    return () => {
      clearInterval(intervalId);
      if (eventStreamRef.current) {
        eventStreamRef.current.destroy();
      }
    };
  }, [resourceId, options]);
  
  return {
    isConnected,
    close: () => eventStreamRef.current?.close(),
    reconnect: () => {
      if (eventStreamRef.current) {
        eventStreamRef.current.close();
        eventStreamRef.current.connect();
      }
    },
  };
}
