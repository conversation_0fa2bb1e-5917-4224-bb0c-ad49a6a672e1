import React from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Linking,
    Image,
    Modal,
    Pressable,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';

// Define the app keys as a Type
type AppStoreKey = 'Rapido' | 'Uber' | 'Ola' | 'BlinkIt' | 'Zomato' | 'Swiggy';

const travelApps = [
    { name: 'Rapido', icon: require('../../assets/images/PartyDetailsAssets/rapido.png'), url: 'rapido://' },
    { name: 'Uber', icon: require('../../assets/images/PartyDetailsAssets/uber.png'), url: 'uber://' },
    { name: 'Ola', icon: require('../../assets/images/PartyDetailsAssets/ola.png'), url: 'ola://' },
];

const foodApps = [
    { name: 'BlinkIt', icon: require('../../assets/images/PartyDetailsAssets/blinkit.png'), url: 'blinkit://' },
    { name: '<PERSON><PERSON><PERSON>', icon: require('../../assets/images/PartyDetailsAssets/zomato.png'), url: 'zomato://' },
    { name: 'Swiggy', icon: require('../../assets/images/PartyDetailsAssets/swiggy.png'), url: 'swiggy://' },
];

const appStoreLinks: Record<AppStoreKey, { ios: string; android: string }> = {
    Rapido: {
        ios: 'https://apps.apple.com/in/app/rapido-bike-taxi-auto-cabs/id1198464606',
        android: 'https://play.google.com/store/apps/details?id=com.rapido.passenger&hl=en_IN',
    },
    Uber: {
        ios: 'https://apps.apple.com/in/app/uber-request-a-ride/id368677368',
        android: 'https://play.google.com/store/apps/details?id=com.ubercab',
    },
    Ola: {
        ios: 'https://apps.apple.com/in/app/ola-book-cab-auto-bike-taxi/id539179365',
        android: 'https://play.google.com/store/apps/details?id=com.olacabs.customer&hl=en_IN',
    },
    BlinkIt: {
        ios: 'https://apps.apple.com/in/app/blinkit-grocery-in-10-minutes/id960335206',
        android: 'https://play.google.com/store/apps/details?id=com.grofers.customerapp&hl=en_IN',
    },
    Zomato: {
        ios: 'https://apps.apple.com/in/app/zomato-food-delivery-dining/id434613896',
        android: 'https://play.google.com/store/apps/details?id=com.application.zomato',
    },
    Swiggy: {
        ios: 'https://apps.apple.com/in/app/swiggy-food-instamart-dineout/id989540920',
        android: 'https://play.google.com/store/apps/details?id=in.swiggy.android',
    },
};

const AppsView: React.FC<{ isVisible: boolean; onClose: () => void }> = ({ isVisible, onClose }) => {
    const navigation = useNavigation();
    const openApp = async (app: AppStoreKey, url: string) => {
        try {
            await Linking.openURL(url);
        } catch {
            const storeLink = Platform.OS === 'ios' ? appStoreLinks[app].ios : appStoreLinks[app].android;
            if (storeLink) {
                await Linking.openURL(storeLink);
            } else {
                console.error(`No store link found for app: ${app}`);
            }
        }
    };

    const handlePressOutside = () => {
        navigation.goBack();
    };
    return (
        <Modal
            visible={isVisible}
            animationType="slide"
            transparent={true}
            onRequestClose={handlePressOutside}
        >
            <Pressable style={styles.overlay} onPress={handlePressOutside}>
                <KeyboardAvoidingView
                    behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                    style={styles.halfSheetContainer}
                >
                    <Pressable style={styles.halfSheet} onPress={() => { }}>
                        <Text style={styles.sectionTitle}>Travel Apps</Text>
                        <View style={styles.appRow}>
                            {travelApps.map((app, index) => (
                                <TouchableOpacity
                                    key={index}
                                    style={styles.appContainer}
                                    onPress={() => openApp(app.name as AppStoreKey, app.url)}
                                >
                                    <Image source={app.icon} style={styles.appIcon} />
                                    <Text style={styles.appText}>{app.name}</Text>
                                </TouchableOpacity>
                            ))}
                        </View>

                        <Text style={styles.sectionTitle}>Food Apps</Text>
                        <View style={styles.appRow}>
                            {foodApps.map((app, index) => (
                                <TouchableOpacity
                                    key={index}
                                    style={styles.appContainer}
                                    onPress={() => openApp(app.name as AppStoreKey, app.url)}
                                >
                                    <Image source={app.icon} style={styles.appIcon} />
                                    <Text style={styles.appText}>{app.name}</Text>
                                </TouchableOpacity>
                            ))}
                        </View>
                    </Pressable>
                </KeyboardAvoidingView>
            </Pressable>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: 'transparent',
        shadowOffset: { width: 1, height: 1 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
    },
    halfSheetContainer: {
        flex: 1,
        justifyContent: 'flex-end',
    },
    halfSheet: {
        backgroundColor: '#fff',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        padding: 20,
        paddingBottom: 40,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 10,
        color: '#333',
    },
    appRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 20,
    },
    appContainer: {
        alignItems: 'center',
        width: 80,
    },
    appIcon: {
        width: 50,
        height: 50,
        marginBottom: 5,
        borderRadius: 8,
    },
    appText: {
        fontSize: 14,
        color: '#333',
        textAlign: 'center',
    },
});

export default AppsView;
