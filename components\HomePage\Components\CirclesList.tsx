import { HomeRootStackList } from '@/app/Home/HomeNavigation';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { View, Text, Pressable, Image, StyleSheet } from 'react-native';
import { useEffect, useState } from 'react';
import { Colors, Typography, Spacing, Borders, Icons } from '@/constants/DesignSystem';
import { FastPartyActivityIndicator } from '@/components/FastPartyActivityIndicator';
import { Add, Next, Previous } from '@/components/icons';

interface CirclesListProps {
    circles: any[];
    isLoading?: boolean;
    error?: Error | null;
    onFetchMore?: () => void;
    isRefetching: boolean;
}

interface EventCircleProps {
    name: string;
    url: string;
    id: string;
}

const styles = StyleSheet.create({
    container: {
        marginBottom: Spacing.md,
    },
    headerContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginHorizontal: Spacing.lg,
    },
    sectionTitle: {
        fontFamily: Typography.fontFamily.primary,
        fontWeight: Typography.fontWeight.semibold,
        fontSize: Typography.fontSize.lg,
        marginTop: Spacing.xs,
        color: Colors.text.secondary,
    },
    navigationControls: {
        flexDirection: 'row',
        padding: Spacing.xs,
        gap: Spacing.md,
    },
    navigationButton: {
        padding: Spacing.xs,
    },
    circlesContent: {
        marginTop: Spacing.md,
        alignItems: 'center',
    },
    circlesRow: {
        flexDirection: 'row',
        justifyContent: 'center',
        gap: Spacing.lg,
        marginBottom: Spacing.md,
    },
    loadingContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 260,
        marginBottom: Spacing.lg,
    },
    errorText: {
        fontFamily: 'Plus Jakarta Sans',
        color: Colors.error,
    },
    newCircleContainer: {
        width: 100,
        height: 100,
        borderRadius: 50,
        backgroundColor: Colors.background.secondary,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: Borders.width.thin,
        borderColor: Colors.primary,
        borderStyle: 'dashed',
    },
    circleContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        marginHorizontal: Spacing.xs,
    },
    circleButton: {
        width: 100,
        height: 100,
        borderRadius: 50,
        overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Colors.background.secondary,
    },
    circleButtonWithImage: {
        width: 100,
        height: 100,
        borderRadius: 50,
        overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Colors.background.secondary,
        borderWidth: Borders.width.thin,
        borderColor: Colors.border.light,
    },
    circleImage: {
        width: 100,
        height: 100,
    },
    circleInitials: {
        fontSize: Typography.fontSize.xxl,
        fontWeight: Typography.fontWeight.regular,
        color: Colors.primary,
    },
    circleName: {
        fontFamily: Typography.fontFamily.primary,
        fontWeight: Typography.fontWeight.semibold,
        fontSize: Typography.fontSize.md,
        marginTop: Spacing.xs,
        color: Colors.text.secondary,
    },
});

export function CirclesList({ circles, isLoading, error, onFetchMore, isRefetching }: CirclesListProps) {
    const navigation = useNavigation<NavigationProp<HomeRootStackList>>();
    const [page, setPage] = useState(0);
    const circlesPerPage = 5;
    const totalPages = Math.ceil(circles?.length / circlesPerPage);
    const currentCircles = circles?.slice(page * circlesPerPage, (page + 1) * circlesPerPage);

    useEffect(() => {
        if (circles?.length > 0 && isRefetching) {
          setPage(0);
        }
      }, [isRefetching]);


    if (isLoading) {
        return <FastPartyActivityIndicator />;
    }

    if (error) {
        console.error(error);
        return (
            <View style={styles.loadingContainer}>
                <Text style={styles.errorText}>
                    Unable to load circles
                </Text>
            </View>
        );
    }



    const handlePressRight = async () => {
        setPage((prevPage) => Math.min(totalPages - 1, prevPage + 1));
        onFetchMore?.();
    };

    const handleNavigateToCreateCircle = () => {
        navigation.navigate('AddCircleScreen');
    }

    const handlePressNavigateCircleDetails = (id: string) => {
        // Using any to bypass type checking for the navigation
        // This is a workaround for the type error with nested navigation
        (navigation as any).navigate('AddCircleScreen', {
            screen: 'CircleDetails',
            params: { circleId: id },
        });
    }


    const NewCircleCard = () => (
        <View style={styles.circleContainer}>
            <Pressable
                style={styles.newCircleContainer}
                onPress={handleNavigateToCreateCircle}
            >
                <Add size={Icons.size.lg} color={Colors.primary} />
            </Pressable>
            <Text style={styles.circleName}>
                New Circle
            </Text>
        </View>
    );

    const EventCircle: React.FC<EventCircleProps> = ({ name, url, id }) => {
        const getInitials = (name: string) => {
            const nameParts = name?.trim()?.split(' ');
            const initials = nameParts?.length > 1
                ? `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase()
                : `${nameParts[0][0]}`.toUpperCase();
            return initials;
        };

        return (
            <View style={styles.circleContainer}>
                <Pressable
                    style={url ? styles.circleButtonWithImage : styles.circleButton}
                    onPress={() => handlePressNavigateCircleDetails(id)}
                >
                    {url ? (
                        <Image
                            source={{ uri: url }}
                            style={styles.circleImage}
                            resizeMode="cover"
                        />
                    ) : (
                        <Text style={styles.circleInitials}>
                            {getInitials(name)}
                        </Text>
                    )}
                </Pressable>
                <Text style={styles.circleName}>
                    {name.length > 15 ? `${name.substring(0, 11)}...` : name}
                </Text>
            </View>
        );
    };

    return (
        <View style={styles.container}>
            <View style={styles.headerContainer}>
                <Text style={styles.sectionTitle}>My Circles</Text>
                <View style={styles.navigationControls}>
                    <Pressable
                        onPress={() => setPage(Math.max(0, page - 1))}
                        disabled={page === 0}
                        style={styles.navigationButton}
                    >
                        <Previous
                            color={page === 0 ? Colors.button.disabled : Colors.secondary}
                            size={Icons.size.lg}
                        />
                    </Pressable>
                    <Pressable
                        onPress={handlePressRight}
                        disabled={page >= totalPages - 1}
                        style={styles.navigationButton}
                    >
                        <Next
                            color={page >= totalPages - 1 ? Colors.button.disabled : Colors.secondary}
                            size={Icons.size.lg}
                        />
                    </Pressable>
                </View>
            </View>

            <View style={styles.circlesContent}>
                {circles?.length === 0 ? (
                    <NewCircleCard />
                ) : (
                    <>
                        <View style={styles.circlesRow}>
                            <NewCircleCard />
                            {currentCircles?.slice(0, 2).map((circle, index) => (
                                <EventCircle key={index} name={circle.name} url={circle?.imageUrl} id={circle?.id} />
                            ))}
                        </View>

                        {currentCircles?.length > 2 && (
                            <View style={styles.circlesRow}>
                                {currentCircles?.slice(2, 5).map((circle, index) => (
                                    <EventCircle key={index} name={circle.name} url={circle?.imageUrl} id={circle?.id} />
                                ))}
                            </View>
                        )}
                    </>
                )}
            </View>
        </View>
    );
}
